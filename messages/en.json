{"global": {"Login": "<PERSON><PERSON>", "SignUp": "Sign Up", "GetStartedNow": "Get Started Now", "ContactUs": "Contact Us", "loading": "loading...", "Loading": "Loading...", "Back": "Back", "Cancel": "Cancel", "Continue": "Continue", "Proceed": "Proceed", "Finish": "Finish", "Reset": "Reset", "Send": "Send", "LearnMore": "Learn More", "ai-wk": "ai-wk", "errorMessage": {"title": "Something went wrong!", "Tryagain": "Try again"}, "toast": {"SubmittedSuccessfully": "Submitted successfully!", "SubmissionFailed": "Submission failed", "LoginSuccessful": "Login successful!"}, "KeyFeatures": "Key Features"}, "HeaderBlack": {"Models": "Models", "OurModels": "Our Models"}, "SiteFooter": {"nav": {"TermsofUse": "Terms of Use", "PrivacyPolicy": "Privacy Policy", "Disclaimer": "Disclaimer", "RefundPolicy": "Refund Policy"}, "copy": "Copyright"}, "ConnectTelegram": {"linkTelegram": "Link Telegram", "checkingStatus": "Checking status...", "unlinkTelegram": "Unlink Telegram", "unlinkSuccess": "Telegram unlinked successfully!", "unlinkFailed": "Failed to unlink Telegram", "unlinkDialogTitle": "Unlink Telegram", "unlinkDialogBody": "Are you sure you want to unlink your Telegram account? You can always link your Telegram again with the link Telegram button", "cancel": "Cancel", "confirm": "Confirm", "unlinking": "Unlinking..."}, "Tutorials": {"Page": {"breadcrumbAiWk": "AI-WK", "breadcrumbTutorials": "Tutorials", "mainTitle": "Get the most out of the models", "mainSubtitle": "Watch how to use and best scenario videos for the best output", "noVideos": "No videos available in this section"}, "VideoLinks": {"categories": {"aboutAiWk": "About AI-wk", "orion": "Orion", "hermesX": "<PERSON><PERSON>", "hermesC": "<PERSON><PERSON>", "otherModels": "Other Models"}, "videos": {"introductionToAiWk": "Introduction to ai-wk", "orionHowToUse": "Orion: How to Use", "bestUseScenarioForOrion": "Best Use Scenario For Orion", "hermesXHowToUse": "Hermes X: How to Use", "bestUseScenarioForHermesX": "Best Use Scenario For Hermes X", "hermesCHowToUse": "Hermes C: How to Use", "bestUseScenarioForHermesC": "Best Use Scenario For Hermes C"}, "durations": {"introductionToAiWk": "2:07", "orionHowToUse": "2:11", "bestUseScenarioForOrion": "1:50", "hermesXHowToUse": "1:47", "bestUseScenarioForHermesX": "1:52", "hermesCHowToUse": "1:45", "bestUseScenarioForHermesC": "1:13"}}}, "HeaderModels": {"returnToAiWk": "Return to ai-wk", "aiWk": "ai-wk", "needHelp": "Need Help?"}, "Form": {"fullname": {"text": "Your Name", "required": "Full name is required"}, "email": {"text": "Email Address", "required": "Email is required", "error": "Please enter a valid email", "dataFormatError": "Invalid email format"}, "company": {"text": "Company/Fund Name", "required": "Company name is required"}, "website": {"text": "Website", "required": "Website is required"}, "business": {"text": "Nature of Your Business", "required": "Please select the nature of your business", "options": {"placeholder": "Select...", "FinancialServices": "Financial Services", "Technology": "Technology", "Healthcare": "Healthcare", "Education": "Education", "Other": "Other"}}, "collaboration": {"text": "Proposed Area of Collaboration", "required": "Please select an area of collaboration", "options": {"placeholder": "Select...", "ProductIntegration": "Product Integration", "Distribution": "Distribution", "CoMarketing": "Co-Marketing", "DataPartnership": "Data Partnership", "Other": "Other"}}, "message": {"text": "Message", "required": "Message is required"}, "industry": {"text": "Industry", "required": "Industry is required"}, "workflow": {"text": "Describe your workflow or use cases", "required": "Please describe your workflow or use case is required"}, "teamSize": {"text": "Team Size"}, "qualified": {"text": "Are you a qualified investor?", "required": "Please specify if you are a qualified investor", "options": {"placeholder": "Select...", "Yes": "Yes", "No": "No"}}, "investorType": {"text": "Type of Investor", "required": "Please select your investor type", "options": {"placeholder": "Select...", "Angel": "Angel", "VentureCapital": "Venture Capital", "Corporate": "Corporate", "FamilyOffice": "Family Office", "Other": "Other"}}, "synergies": {"text": "Do you bring any strategic synergies?", "required": "Please describe any strategic synergies"}, "role": {"text": "Role", "required": "Please select a role", "options": {"placeholder": "Select a role", "AIEngineer": "AI Engineer", "SalesConsultant": "Sales Consultant", "Other": "Other"}}, "coverLetter": {"text": "Cover Letter", "required": "Message is required"}, "resume": {"text": "Resume", "required": "Resume is required", "fileTypeError": "Only PDF or DOC/DOCX files are allowed"}, "organization": {"text": "Organization/Affiliation", "required": "Organization is required"}, "inquiryType": {"text": "Type of Inquiry", "required": "Please select the type of inquiry", "options": {"placeholder": "Select...", "MediaInterview": "Media Interview", "SpeakingEngagement": "Speaking Engagement", "GeneralInquiry": "General Inquiry"}}, "password": {"text": "Password", "required": "This field is required", "minError": "Password must be at least 8 characters", "containNumberError": "Must contain a number", "containLowercaseLetter": "Must contain a lowercase letter", "containUppercaseLetter": "Must contain an uppercase letter", "containSpecialCharacter": "Must contain a special character"}, "FromDate": {"text": "From Date"}, "ToDate": {"text": "To Date"}, "Button": {"Sending": "Sending...", "SendMessage": "Send Message", "Logging": "Logging in…", "Login": "<PERSON><PERSON>", "SigningUp": "Signing Up...", "SignUp": "Sign Up", "Updating": "Updating...", "SetUpProfile": "Set Up Profile", "Generating": "Generating...", "Generate": "Generate", "SaveDetails": "Save Details", "EditDetails": "Edit Details", "Saving": "Saving...", "SaveChanges": "Yes, save changes", "ConfirmTicket": "Confirm & Send Ticket", "Submit": "Submit", "Cancelling": "Cancelling...", "CancelSubscription": "Cancel Subscription", "Submitting": "Submitting...", "Processing": "Processing...", "AgreeAndContinue": "Agree and Continue", "Filter": "Filter", "SendRequest": "Send Request", "Uploading": "Uploading...", "MadePayment": "I've made payment"}, "Login": {"email": {"text": "Email"}}, "SignUp": {"firstName": {"text": "First Name", "required": "First Name is required"}, "lastName": {"text": "Last Name", "required": "Last Name is required"}, "email": {"text": "Email"}}}, "Table": {"Page": "Page", "of": "of", "Previous": "Previous", "Next": "Next", "Date": "Date", "Amount": "Amount", "Type": "Type", "Narration": "Narration"}, "DashboardNav": {"Home": "Home", "Credits": "Credits", "Settings": "Settings"}, "Home": {"title": "Hello world!", "Header": {"models": {"title": "Our Models", "Hermes": {"name": "<PERSON><PERSON>", "description": "Market Monitor"}, "Orion": {"name": "Orion", "description": "Equity Analyst"}, "Olympus": {"name": "Olympus", "description": "Market Simulation"}, "Luca": {"name": "Luca", "description": "Accounting"}, "Freddie": {"name": "<PERSON>", "description": "Rec<PERSON>er"}, "Yumi": {"name": "<PERSON><PERSON>", "description": "Customer Service"}, "CustomModels": {"name": "Custom Models", "description": "For Your Business"}}, "nav": {"Models": "Models", "Pricing": "Pricing", "AboutUs": "About Us", "ContactUs": "Contact Us"}}, "HeroSection": {"subTitle": "Explore our various models", "title": {"line1": "Purpose-Built", "line2": "AI Work Models", "line3": "for Professionals"}, "description": "Powering analysis, execution, and decision-making — always on, always efficient."}, "OurAIModels": {"title": "Our AI Work Models, Ready to Deploy", "description": "Choose from expert-built models that perform specific business tasks with speed and accuracy.", "tabs": {"Olympus": {"tabLabel": "Market Simulation", "label": "Olympus", "title": "Market Simulator, forecasting trends and testing strategies before execution"}, "MarketMonitor": {"tabLabel": "Market Monitor", "label": "<PERSON><PERSON>", "title": "Market Monitor, tracking global financial markets for trading opportunities 24/7"}, "EquityAnalyst": {"tabLabel": "Equity Analyst", "label": "Orion", "title": "Equity Analyst, delivering deep market insights for informed investment decisions"}, "Accountant": {"tabLabel": "Accountant", "label": "Luca", "title": "Accountant, automating bookkeeping and financial reporting with accuracy"}, "Recruiter": {"tabLabel": "Rec<PERSON>er", "label": "<PERSON>", "title": "Recruiter, identifying top talent and streamlining the hiring process"}, "CustomerService": {"tabLabel": "Customer Service", "label": "<PERSON><PERSON>", "title": "Customer Concierge, with warmth, speed, and precision"}}}, "AIBusiness": {"subTitle": "Customization", "title": {"line1": "Tailor-made", "line2": "AI Work Models", "line3": "for Your Business"}, "description": {"part1": "Every business is different — ", "part2": "we craft custom AI work models", "part3": "to meet your business needs."}, "LearnMore": "Learn More"}, "StatisticsSection": {"subTitle": "Statistics", "hermescChartText": "Hermes C chart below shows capital growth from a constant 100 investment using live signals. Explore the Active Alpha Zone!", "legendCharts": {"rawStrategyReturn": "Raw strategy return", "passiveReturnToClose": "Passive return to close", "activeReturnToMax": "Active return to max", "activeAlphaZone": "Active Alpha Zone", "activeAlphaZoneTooltip": "AAZ represents the range of potential return between a passive hold-to-close strategy and the maximum achievable return before the next session close. It reflects the profit window available to experienced traders who actively manage their positions with timely entries and exits. Capturing value within this zone requires skillful execution, market awareness, and discretion—beyond simply reacting to alerts."}, "title": {"line1": "Our models are built to ", "line2": "deliver results"}, "models": {"Orion": {"name": "Orion", "leftMetric": {"title": "Time Saved.", "description": "Orion generates reports in 2 minutes per ticker vs. 4 hours manually saving nearly a full workday per ticker."}, "rightMetric": {"title": "Cheaper per report.", "description": "A junior equity analyst earning $80,000/year typically produces analysis for 2 tickers/day costing $170 per ticker. Orion delivers the same for just $10 per ticker. Over 500 tickers annually, that's a cost drop from $60,000 to $5,000—saving your team ~$55,000 a year."}}, "HermesX": {"name": "HermesX", "leftMetric": {"title": "Hermes X provides full 24/7 market monitoring.", "value": "168 Hours/Week.", "description": "Something that would take a rotating team of 3-4 analysts. It auto-scans news, filters noise, and sends alerts within seconds, delivering hundreds of insights weekly without breaking a sweat."}, "rightMetric": {"title": "Cost Reduction.", "description": "Replacing a $180,000/year human monitoring team, Hermes X delivers the same 24/7 coverage for just $120/year per user. That's over $179,000 saved annually—without compromising speed or accuracy."}}, "Olympus": {"name": "Olympus", "leftMetric": {"title": "Time Saved.", "description": "Olympus replaces a 10-person, 4-hour manual simulation with a fully automated 1-hour process—saving 40 human-hours without sacrificing depth or detail."}, "rightMetric": {"title": "Cheaper Per Simulation.", "description": "Olympus runs full-scale simulations for just $100—compared to $1,200 using a 10-person human team. That's over $1,100 saved per run with no oss in insight or speed."}}}}, "AIWorkModelsClosingHero": {"title1": "Try AI Work Models", "title2": "For Your Business"}}, "Pricing": {"CreditsPower": {"subTitle": "Pricing", "title": {"line1": "Credits power", "line2": "everything on ai-wk"}, "Credit": "Credit", "currencyMetadata": {"USD": "United States Dollar", "EUR": "Euro", "GBP": "British Pounds", "SGD": "Singapore Dollars", "AUD": "Australian Dollars", "CAD": "Canadian Dollars", "JPY": "Japanese Yen", "CNY": "Chinese Yuan", "HKD": "Hong Kong Dollars"}, "list": {"item1": {"title": "Big Picture Thinking:", "description": "1 Credit= 1 USD / USDC / USDT"}, "item2": {"title": "Methodical Precision:", "description": "Use Credits to access all AI models "}, "item3": {"title": "Flawless Execution:", "description": "No hidden fees or fluctuating rates"}}}, "EarnMore": {"title": "Earn More as you Grow", "description": {"part1": "Top up more, get rewarded instantly. Bonus credits apply only", "part2": "to the incremental top-up within each tier"}, "tiers": {"header": {"Tier": "Tier", "TopUp": "Top-Up (12 Months Rolling)", "BonusRate": "Bonus Rate"}, "prompt": "Bonus is only applied to amount within each tier", "Basic": {"name": "Basic", "range": "<$1000", "bonusRate": "0%"}, "Pro": {"name": "Pro", "range": "$1000 - $4,999", "bonusRate": "10%"}, "VIP": {"name": "VIP", "range": "$5,000 - $9,999", "bonusRate": "20%"}, "SVIP": {"name": "SVIP", "range": "$10,000+", "bonusRate": "30%"}}}, "CreditsCharge": {"title": "Credits Charges per Model", "creditModels": {"Orion": {"name": "Orion", "description": "Equity Analyst", "charges": {"option1": "10 Credits/Ticker"}}, "HermesX": {"name": "HermesX", "description": "Market Monitor", "charges": {"option1": "20 Credits/Month"}}, "Freddie": {"name": "<PERSON>", "description": "Rec<PERSON>er", "charges": {"option1": "20 Credits/50 Candidates"}}, "Luca": {"name": "Luca", "description": "Accountant", "charges": {"option1": "30 Credits/Month/Company (Basic)", "option2": "60 Credits/Month/Company (Premium)"}}, "Olympus": {"name": "Olympus", "description": "Market Simulator", "charges": {"option1": "100 Credits/Simulation Run"}}, "HermesC": {"name": "<PERSON><PERSON>", "description": "Trader", "charges": {"option1": "1000 Credits/Month"}}}}, "FAQs": {"title": "FAQs (Frequently Asked Questions)", "faqs": {"q1": {"question": "How do I check my current tier?", "answer": {"paragraph1": "In the Membership dashboard, together with details of past transactions"}}, "q2": {"question": "Do Credits Expire?", "answer": {"paragraph1": "Credits expire in 5 years, giving members sufficient time to use them"}}, "q3": {"question": "Can I get a refund on unused credits?", "answer": {"label": "NO, but credits can be used for any services provided by ai-wk. Please refer to our ", "RefundPolicy": "Refund Policy"}}, "q4": {"question": "What Payment methods are supported?", "answer": {"paragraph1": "We support a wide range of payments ranging from credit cards, QR codes, stablecoins and bank wire transfers."}}, "q5": {"question": "How do custom solutions work?", "answer": {"paragraph1": "In the customization section, fill the details of your request, and our team will get in contact for a quote or a follow-up meeting for more details."}}}}}, "AboutUs": {"OurVision": {"subTitle": "Our Vision", "title": "The Hybrid Work Model is the Future", "content": {"paragraph1": "At ai-wk.com, we believe the future of work combines the creativity of people with the precision and speed of AI. We're building a world where AI teammates integrate seamlessly into human teams—delivering faster, smarter, and more scalable outcomes.", "paragraph2": "Our mission is to empower businesses and individuals to thrive in this transformation. Through education, training, and access to AI work models, we help people adapt, grow, and lead in the new era of productivity."}}, "InvestmentBankerSection": {"title": "An Investment Banker's Approach to AI", "description1": "Founded by", "description2": ", a former Goldman Sachs investment banker, ai-wk.com brings Wall Street discipline to AI innovation.", "name": "<PERSON>", "list": {"item1": {"title": "Big Picture Thinking:", "description": "Our AI teammates are designed for full workflows, not one-off tasks."}, "item2": {"title": "Methodical Precision:", "description": "Every model is structured, refined, and built with an obsession for detail."}, "item3": {"title": "Flawless Execution:", "description": "From financial models to technical deployments, quality and consistency drive everything we do."}, "item4": {"title": "Trust & Commitment:", "description": "We operate with long-term thinking, deep expertise, and integrity."}}}, "OurCulture": {"title": "Our Culture", "description": "We're modern. We're global. We're built for results.", "cultureCards": {"alt": "Modern office with AI visualization overlays", "TechForward": {"title": "Tech-forward by design", "boldText": "Tech-forward by design", "normalText": "engineered for efficiency at every layer"}, "ClientCentric": {"title": "", "boldText": "Client-centric by default ", "normalText": "— your objectives define our roadmap"}, "PrivacyFirst": {"title": "", "boldText": "Privacy-first mindset", "normalText": "we use AI responsibly & respect for your data"}, "Multicultural": {"title": "Multicultural and distributed team", "boldText": "Multicultural and distributed team", "normalText": "working across time zones and disciplines"}, "CloudNative": {"title": "Cloud-native, agile, and output-focused", "boldText": "Cloud-native, agile, and output-focused", "normalText": "built for speed and resilience"}}}, "PurposeBuilt": {"subTitle": "Purpose Built", "title": "Born from Alpharithm Investments", "description": {"text1": "ai-wk.com is developed by", "name": "Alpharithm Investments", "text2": "as a showcase of what AI teammates can do. Each AI work model is purpose-built, continuously trained, and rigorously tested to execute professional tasks with speed and accuracy — from generating trading signals to producing in-depth research. This is where you can experience the future of work — powered by intelligent, capable AI teammates. And once you've seen what our models can do, we want to partner with existing companies and professionals to help elevate your game through tailored AI integration and adoption."}}}, "ContactUs": {"ContactUsSection": {"subTitle": "Contact Us", "title": "We would love to hear from you!", "description": "Please select the category that best fits your inquiry. All messages will be routed to the relevant team through our internal system, and we'll get back to you as soon as possible. For further assistance you can reach us at "}, "Tabs": {"CustomModels": {"value": "custom-models", "label": "Custom Models", "pane": {"title": "Companies or professionals who want to collaborate with us to build customized AI models tailored to their specific workflows, industry needs, or internal systems.", "description": "We work with selected clients to co-develop AI teammates that automate and elevate specialized workflows — from financial analysis to customer service, internal reporting, compliance, and more.", "form": {"description": "Tell us about your business needs, pain points, and how you envision AI helping you work smarter. We'll follow up to discuss scope and feasibility.", "problemText": "What's the problem you want to solve with AI?", "toolsText": "Current tools or system used"}}}, "Support": {"value": "support", "label": "Support", "pane": {"title": "Please log in to your member dashboard to contact our support team. All inquiries must be submitted through the in-platform support form to ensure proper authentication and response tracking.", "description": "Existing users of ai-wk.com who need help with their account, payments, or AI models."}}, "Career": {"value": "career", "label": "Career", "pane": {"title": "We're always on the lookout for exceptional people to join us. If you're passionate about building the future of work, explore the career opportunities with us. We are looking for AI Engineers and Sales Consultants (commission-based) to join us at the moment.", "description": "We are continuously hiring in the following areas:", "areas": {"Engineers": {"title": "AI / Software Engineers", "description": "(Tech Leads, Senior Engineers, Junior Engineers)"}, "Sales": {"title": "Sales Representatives", "description": "(Covering different sectors and geographic regions)"}}}}, "Investments": {"value": "investment", "label": "Investments", "pane": {"title": {"paragraph1": "We raise capital to scale our business annually. We are currently preparing for our angel round, and welcome inquiries from investors who meet qualified investor criteria.", "paragraph2": "Strategic investors who bring synergistic value to our product, distribution, or infrastructure will be prioritized."}, "form": {"description": "Please submit your details via the form below. Meetings will be arranged selectively and handled directly by our CEO."}}}, "Partnership": {"value": "partnership", "label": "Partnership", "pane": {"title": "We are open to meaningful partnerships across sectors including:", "description": "If you're exploring a win-win opportunity with us, we'd love to hear more.", "list": {"item1": "Financial platforms and brokerages", "item2": "SaaS and data integration tools", "item3": "Distribution partners or B2B channels"}, "form": {"description": "Tell us about your company and your proposed collaboration:", "valueText": "What value do you envision in this partnership?"}}}, "Media": {"value": "media", "label": "Media & General Inquiries", "pane": {"title": "Please use this section if your message doesn't fall into the categories above. We welcome thoughtful inquiries and will route them to the right team internally.", "form": {"description": "Use this form for interview requests, speaking engagements, or general contact."}}}}}, "UserLogin": {"title": "Welcome back", "description": "Let’s continue from where you stopped", "ForgotPassword": "Forgot Password?", "OR": "OR", "LogWithGoogle": "Log in with Google", "DoHaveAccount": "Don’t have an account?", "SignUpHere": "Sign Up Here"}, "SignUp": {"title": "Sign up to create your Account", "description": "Fill the form below to get started or use any of the other options available.", "OR": "OR", "SignupWithGoogle": "Sign up with Google", "AlreadyHaveAccount": "Already have an account?", "LoginHere": "Login Here"}, "Hermes": {"LucaLandingHeroSection": {"title": "Hermes: Seeking Trading Opportunities 24/7", "description": "Hermes is a family of AI models inspired by <PERSON><PERSON>, the Greek God of Trade, built to monitor trading catalysts and generate actionable insights. Each Hermes model is specialized for a different domain and purpose. We currently offer two models for subscription:"}, "LucaGitHub": {"title": {"text1": "Hermes X scans global news 24/7, delivering only", "text2": "“market-moving insights.”", "text3": "It helps traders stay ahead of key catalysts without wasting time on irrelevant noise."}}, "LucaCTA": {"title": "Stay ahead of market-moving news!"}, "HowLucaWorks": {"title": {"line1": "<PERSON>", "line2": "Works..."}, "steps": {"step1": {"title": "Subscribe to <PERSON><PERSON>", "description": "Choose from monthly or annual plans."}, "step2": {"title": "AI Monitors Market Events", "description": "Scans global news continuously."}, "step3": {"title": "Filtered News Al<PERSON>s <PERSON>", "description": "Only high-impact events reach your Telegram."}, "step4": {"title": "Trader Reviews & Acts", "description": "Uses AI insights to inform trading decisions."}}, "ImportantNote": "Important Note: <PERSON><PERSON> enhances decision-making but does not replace human judgment. Traders should always verify news sources before acting."}, "LucaComparison": {"Hermes": "<PERSON><PERSON>", "vs": "vs", "Traditional": "Traditional News Feeds", "HumanMonitoring": "Human Monitoring", "leftComparisons": {"comparison1": {"title": "Noise Reduction", "description": "– Only alerts on events that truly matter."}, "comparison2": {"title": "AI-Powered Insights", "description": "– Goes beyond headlines to explain trading impact"}, "comparison3": {"title": "Multi-Source Validation", "description": "– Pulls from elite agencies and verified voices."}}, "rightComparisons": {"comparison1": {"title": "24/7 Coverage", "description": "– Never misses a crucial event, even when traders are offline"}, "comparison2": {"title": "Lower Overhead", "description": "– Replace teams of analysts with one AI assistant."}, "comparison3": {"title": "More Focused Attention", "description": "– Frees traders from distraction."}}}, "LucaKeyFeatures": {"title": "Hermes X: AI-Powered Market News Monitor", "description": {"line1": "Hermes X scans and analyzes macroeconomic and major company news 24/7 to detect", "line2": "market-moving events. It filters irrelevant noise and delivers only high-impact alerts."}, "features": {"feature1": {"title": "Real-Time Market Monitoring", "description": "Operates round-the-clock to capture real-time news developments."}, "feature2": {"title": "AI-Powered Filtering", "description": "Filters out non-essential news, focusing on events with market-moving potential."}, "feature3": {"title": "Multi-Agent Analysis", "description": "Uses collaborative AI agents for catalyst analysis."}, "feature4": {"title": "Instant Delivery", "description": "Sends alerts directly to users' Telegram accounts."}}}, "LucaFAQ": {"title": "FAQs (Frequently Asked Questions)", "faqs": {"q1": {"question": "What are the pricing plans for Hermes X?", "answer": {"line1": "• Monthly Plan – 20 credits/month", "line2": "• API Access – 100 credits/month", "line3": "Subscription applies to a single Telegram ID"}}, "q2": {"question": "What are the pricing plans for Hermes C?", "answer": {"line1": "• Standard subscription – 1000 credits/month", "line2": "• API Access – 2000 credits/month"}}, "q3": {"question": "Can Hermes be Customized?", "answer": {"line1": "Users can customize the model and its output to fit their specific needs. Our AI engineers can build tailored AI solutions for your workflow. Looking for a customized solution? Contact us to tailor Orion to your specific needs.", "ContactUs": "Contact Us to get started"}}}}, "LucaKeyFeatures2": {"title": "Hermes C: Company-Specific Catalyst Monitor", "description": "Hermes C tracks and analyzes company-specific news, developments, and disclosures. It generates trade ideas based on a multi-factor framework that includes fundamentals, technicals, catalysts, financials, and sentiment. Ideal for users seeking structured analysis for directional trades.", "features": {"feature1": {"title": "Real-Time Market Monitoring", "description": "Monitors stocks for pricing moving events."}, "feature2": {"title": "AI-Powered Filtering", "description": "Applies proprietary AI logic to suggest high-confidence trade setups."}, "feature3": {"title": "Multi-Agent Analysis", "description": "Supports both discretionary and programmatic trading strategies."}, "feature4": {"title": "Instant Delivery", "description": "Strategies are interpretable and can be validated with historical rationale."}}}, "HermesXLiteSection": {"title": "Hermes X Lite is Free on X", "description": "Stay connected with us on X (formerly known as Twitter) to receive timely and pertinent alerts as they happen. Our updates are designed to keep you informed in real time, ensuring you never miss out on important news and announcements.", "ClickToFollow": "Click here to Follow"}}, "Orion": {"LucaLandingHeroSection": {"title": "Orion: Professional-Grade Equity Analyst", "description": "Orion is a professional-grade AI equity analyst that delivers deep fundamental, technical, and sentiment analysis using financial reports, market data, analyst research, earnings call transcripts, and key news catalysts."}, "LucaGitHub": {"title": {"text1": "The result? A", "text2": "high-quality investment report", "text3": "with scoring across fundamental, technical, and sentiment-based factors—helping investors make informed decisions with confidence."}}, "LucaCTA": {"title": {"text1": "Get professional", "text2": "grade investment", "text3": "insights in minutes!"}}, "HowLucaWorks": {"title": {"line1": "How Orion", "line2": "Works..."}, "description": "Users must upload their own third-party research reports, as these are copyrighted and not provided by the platform.", "steps": {"step1": {"title": "Input Stock Ticker(s)", "description": "Enter a company's ticker symbol or upload a list."}, "step2": {"title": "AI-Powered Analysis", "description": "Orion scans and processes high-quality data sources."}, "step3": {"title": "Professional Report Generated", "description": "Receive a structured investment report with key scores and insights."}, "step4": {"title": "Instant Delivery", "description": "Reports are sent directly to your email or Telegram."}}}, "LucaComparison": {"Orion": "Orion", "vs": "vs", "StockInformation": "Stock Information Portals", "HumanAnalysts": "Human Analysts", "leftComparisons": {"comparison1": {"title": "Higher-Quality Data", "description": "– Uses professional reports, not public or user-generated content"}, "comparison2": {"title": "Comprehensive Analysis", "description": "– Covers financial, operational, and market factors"}, "comparison3": {"title": "AI-Driven Expertise", "description": "– Multiple specialized AI models work together for maximum accuracy."}}, "rightComparisons": {"comparison1": {"title": "Faster Reports", "description": "– Get deep research in minutes instead of days."}, "comparison2": {"title": "Lower Costs", "description": "– A fraction of the cost of hiring analysts"}, "comparison3": {"title": "Unbiased & Consistent", "description": "– Pure data-driven insights, no subjective opinions."}, "comparison4": {"title": "Enhances Decision-Making", "description": "– Complements human judgment with structured research."}}}, "LucaSmartAccounting": {"title": {"line1": "Who is <PERSON>", "line2": "for?"}, "features": {"Professional": {"title": "Professional Investors", "description": "seeking quick, unbiased investment assessments"}, "HedgeFunds": {"title": "Hedge Funds & Asset Managers", "description": "conducting in-depth research."}, "Investment": {"title": "Investment Firms", "description": "looking to enhance or replace internal research teams."}, "Analysts": {"title": "Analysts & Traders", "description": "who need a comprehensive review of shortlisted stocks."}}}, "LucaKeyFeatures": {"features": {"feature1": {"title": "Aggregates High-Quality Data", "description": "Financial reports, earnings calls, market trends, and more."}, "feature2": {"title": "Multi-Factor Scoring System", "description": "Fundamental, Technical, Valuation, Analyst Sentiment, Catalyst, and Overall Score."}, "feature3": {"title": "AI-Driven Team", "description": "Not a single AI, but a collaborative system of specialized models."}, "feature4": {"title": "Customizable Reports", "description": "Tailor insights to your specific investment needs."}}}, "LucaFAQ": {"title": "FAQs (Frequently Asked Questions)", "faqs": {"q1": {"question": "What are the pricing plans for Orion?", "answer": {"line1": "• $10 per ticker."}}, "q2": {"question": "Can Orion be Customized?", "answer": {"line1": "Need custom features? Contact us to tailor Orion to your specific business needs.", "ContactUs": "Contact Us"}}}}}, "Olympus": {"LucaLandingHeroSection": {"title": "Olympus: AI-Powered Market Simulation", "description": "Olympus is an advanced simulation model designed to analyze equity market trading scenarios. This model employs multiple AI agents that role-play different market participants such as: Hedge funds, Long-only funds, retail investors, companies and influencers"}, "LucaGitHub": {"title": {"text1": "Agents interact in real-time, offering insights into market dynamics and decision-making strategies. The result is a", "text2": "comprehensive “<PERSON>’s View”", "text3": "report detailing actions, return profiles, and reasoning."}}, "LucaCTA": {"title": {"text1": "Unlock AI-driven", "text2": "market simulations", "text3": "today!"}}, "HowLucaWorks": {"title": {"line1": "How Olympus", "line2": "Works..."}, "steps": {"step1": {"title": "Select Market Conditions", "description": "Choose the background, events, and market participants."}, "step2": {"title": "Set AI Agent Strategies", "description": "Configure personalities, investment styles, and objectives."}, "step3": {"title": "Run the Simulation", "description": "Watch how different participants interact in real time."}, "step4": {"title": "Receive a Detailed Report", "description": "A full “God’s View” report covering agent actions, returns, assessments, and key success factors. Customize report focus areas based on your needs."}}}, "LucaComparison": {"Olympus": "Olympus", "vs": "vs", "Humans": "Humans", "comparisons": {"comparison1": {"title": "Lower Cost", "description": "– AI simulations are significantly cheaper than hiring human participants."}, "comparison2": {"title": "Faster Execution", "description": "– Simulations run in minutes, not weeks."}, "comparison3": {"title": "Higher Accuracy", "description": "– AI agents follow strict strategies and investment objectives."}, "comparison4": {"title": "Defined Human Bias", "description": "– AI operates according to your setting of personalities, biases and other human traits."}}}, "LucaSmartAccounting": {"title": {"line1": "Who benefits from", "line2": "Olympus?"}, "features": {"Traders": {"title": "Traders & Investors", "description": "looking to gain a deeper understanding of market behavior."}, "Hedge": {"title": "Hedge Funds & Asset Managers", "description": "test reactions to specific market events."}, "Academics": {"title": "Academics & Researchers", "description": "studying market dynamics and investor psychology"}, "Companies": {"title": "Companies & Policymakers", "description": "to analyze potential responses to regulations or corporate actions."}}}, "LucaKeyFeatures": {"features": {"feature1": {"title": "Realistic Market Simulations", "description": "AI-driven agents mimic real investor behaviors."}, "feature2": {"title": "Comprehensive Market Reports", "description": "Insights into strategies, performance, and decision-making."}, "feature3": {"title": "Customizable Scenarios", "description": "Tailor simulations for specific market conditions."}, "feature4": {"title": "Fast, Unbiased, and Cost-Effective", "description": "More efficient and accurate than human-driven simulations."}}}, "OlympusFAQ": {"title": "FAQs (Frequently Asked Questions)", "faqs": {"q1": {"question": "What are the pricing plans for Olympus?", "answer": "• 100 credits per simulation."}, "q2": {"question": "Can Olympus be Customized?", "answer": {"line1": "Need custom features? Contact us to tailor Olympus to your specific business needs.", "ContactUs": "Contact Us"}}}}}, "Luca": {"LucaLandingHeroSection": {"title": "Luca: Your AI Accountant for Effortless Bookkeeping & Financial Reports", "description": "<PERSON> is an AI-powered accounting assistant designed to streamline bookkeeping, financial record-keeping, and reporting."}, "LucaGitHub": {"title": {"text1": "<PERSON> turns simple messages like", "text2": "“I paid a $30 fee to GitHub from Bank A”", "text3": "into structured financial records, updating statements in real-time with attached invoices and receipts."}}, "LucaCTA": {"title": {"text1": "Ready to make your", "text2": "accounting", "text3": "smarter?"}}, "HowLucaWorks": {"title": {"line1": "How Luca", "line2": "Works..."}, "steps": {"step1": {"title": "Input Financial Transactions", "description": "Enter transactions via text message or manual entry."}, "step2": {"title": "Automated Entry Creation", "description": "Luca categorizes and records transactions instantly."}, "step3": {"title": "Real-Time Financial Reports", "description": "Balance sheets, income statements, and cash flow updates in real-time."}, "step4": {"title": "Attach Supporting Documents", "description": "Upload invoices and receipts for easy reference."}}}, "LucaComparison": {"Luca": "Luca", "vs": "vs", "Humans": "Humans", "TraditionalSoftware": "Traditional Software", "leftComparisons": {"comparison1": {"title": "Lower Cost", "description": "– A fraction of the price of a full-time or part-time accountant."}, "comparison2": {"title": "Faster Processing", "description": "– Instantly records and updates transactions."}, "comparison3": {"title": "24/7 Availability", "description": "– No scheduling required; <PERSON> is always ready."}, "comparison4": {"title": "Consistent & Error-Free", "description": "– Reduces human errors and maintains accuracy."}}, "rightComparisons": {"comparison1": {"title": "Conversational & Intuitive", "description": "– Uses simple text commands instead of complex manual inputs."}, "comparison2": {"title": "Integrated Document Management", "description": "– Seamlessly links invoices and receipts to entries."}, "comparison3": {"title": "Real-Time Updates", "description": "– Eliminates delays in generating financial statements."}}}, "LucaSmartAccounting": {"title": {"line1": "Smart Accounting", "line2": "for Everyone"}, "features": {"Small": {"title": " business owners", "description": "looking for an affordable way to manage finances without hiring an accountant"}, "Freelancers": {"title": "Freelancers & self-employed", "description": "needing real-time financial tracking."}, "Professional": {"title": "Professional accountants", "description": "seeking to enhance productivity and manage multiple clients efficiently. "}, "Companies": {"title": "Companies", "description": "requiring structured financial records for tax reporting, auditing, and planning."}}}, "LucaKeyFeatures": {"features": {"feature1": {"title": "Automated Bookkeeping", "description": "Converts text inputs into structured accounting records."}, "feature2": {"title": "Real-Time Financial Reporting", "description": "Instantly updates records and generates reports."}, "feature3": {"title": "Flexible Input", "description": "Manual entry options for users who prefer hands-on control."}, "feature4": {"title": "Document & Receipt Management", "description": "Attach invoices, receipts, and supporting files."}}}, "LucaFAQ": {"title": "FAQs (Frequently Asked Questions)", "faqs": {"q1": {"question": "What are the pricing plans for <PERSON>?", "answer": {"line1": "• Standard Plan – 30 credits/month Get full access to Luca&apos;s AI-powered bookkeeping and financial reporting. (No document storage included.)", "line2": "• Premium Plan – 60 credits/month Enjoy all Standard Plan features plus secure document storage for invoices and receipts."}}, "q2": {"question": "Can Luca be Customized?", "answer": {"line1": "• Need custom features? Contact us to tailor <PERSON> to your specific business needs.", "ContactUs": "Contact Us"}}}}}, "Freddie": {"LucaLandingHeroSection": {"title": "Freddie: AI-<PERSON><PERSON>c<PERSON>er for End-to-End Hiring", "description": "<PERSON> is a professional-grade AI recruiter designed to handle the entire recruitment process, from drafting job postings to onboarding new hires. It assists with sourcing, evaluating, and communicating with candidates, providing an end-to-end talent acquisition solution."}, "LucaGitHub": {"title": {"text1": "<PERSON> streamlines the", "text2": "entire recruitment journey,", "text3": "from job posting to onboarding, helping teams to focus on hiring smarter and faster instead of getting stuck in endless screening, testing, and communication tasks"}}, "LucaCTA": {"title": {"text1": "Start hiring efficiently", "text2": "and smartly"}}, "HowLucaWorks": {"title": {"line1": "How <PERSON>", "line2": "Works..."}, "steps": {"step1": {"title": "Post job on your preferred platform", "description": "Freddie drafts optimized listings for LinkedIn, Indeed, and others. "}, "step2": {"title": "Resume based screening by <PERSON>", "description": "Automatically screens resumes based on experience, skills, and fit."}, "step3": {"title": "Collect candidate preferences", "description": "Gathers pay, notice, availability, and leave from candidates."}, "step4": {"title": "Smart testing & ranking", "description": "Sends tests, scores candidates, and ranks by job fit."}, "step5": {"title": "Compare & shortlist", "description": "View ranked candidates with summaries and red flags highlighted."}, "step6": {"title": "Communicate & hire", "description": "Schedule interviews, send offers, and manage onboarding seamlessly."}, "step7": {"title": "Verify backgrounds", "description": "<PERSON> runs checks on education, work history, and identity."}}}, "LucaComparison": {"Freddie": "<PERSON>", "vs": "vs", "Hiring": "Hiring Websites and Platforms", "HumanHiring": "Human Hiring Managers", "leftComparisons": {"comparison1": {"title": "Complete Workflow", "description": "– Manages the full recruitment cycle, not just job listing."}, "comparison2": {"title": "Smarter Screening", "description": "–  Goes beyond basic resume filters with tailored screening and testing."}, "comparison3": {"title": "Personalized Communication", "description": "– Maintain consistent, professional interaction with candidates."}}, "rightComparisons": {"comparison1": {"title": "Faster Process", "description": "– Rapid candidate review, evaluation, and communication."}, "comparison2": {"title": "Lower Cost", "description": "– Delivers recruitment support at a fraction of human HR expense."}, "comparison3": {"title": "Always Available", "description": "– Works 24/7 without downtime or bottlenecks."}}}, "LucaSmartAccounting": {"title": {"line1": "Who is <PERSON>", "line2": "for?"}, "features": {"Small": {"title": "Small business owners", "description": "with teams under 20 who need to hire without an in-house HR"}, "Growing": {"title": "Growing startups and expanding companies", "description": "Scale hiring effortlessly with Freddie's automated sourcing, screening, and scheduling—all without bloating your team."}, "HRManagers": {"title": "HR managers", "description": "Free up time by automating job posts, resume screening, and first-round filtering with <PERSON>."}, "Professional": {"title": "Professional recruiters", "description": "Process candidates faster. <PERSON> boosts your throughput with intelligent filtering and round-the-clock screening."}}}, "LucaKeyFeatures": {"features": {"feature1": {"title": "Job Posting Creation", "description": "Drafts high impact, role specific job Ads"}, "feature2": {"title": "Candidate Screening", "description": "Uses dynamic questionnaires and information gathering to pre-screen applicants"}, "feature3": {"title": "Skill Testing and Evaluation", "description": "Organizes tests and evaluations across multiple performance dimensions."}, "feature4": {"title": "Decision Support", "description": "Provides structured comparisons, charts, scoring metrics, and side-by-side analysis."}}}, "LucaFAQ": {"title": "FAQs (Frequently Asked Questions)", "faqs": {"q1": {"question": "What are the pricing plans for Freddie?", "answer": {"line1": "• Per Job Posting – 50 credits", "line2": "Each job posting includes full-cycle support from sourcing to onboarding."}}, "q2": {"question": "Can Freddie be Customized?", "answer": {"line1": "Freddie can be tailored to match company-specific branding, documents, and processes. Looking for a customized solution? Contact us to refine <PERSON> for your organization.", "ContactUs": "Contact Us"}}}}}, "Yumi": {"LucaLandingHeroSection": {"title": "Yumi: 24/7 Customer Service Concierge", "description": "<PERSON><PERSON> is an AI-powered customer service specialist designed to handle support with speed, intelligence, and empathy serving as both the first point of contact for users and powerful internal assistant for support team."}, "LucaCTA": {"title": {"text1": "Bring home", "text2": "your company's premium", "text3": "concierge experience."}}, "LucaGitHub": {"title": {"text1": "A smart AI powered support agent that handles both", "text2": "user facing and internal support", "text3": "ensuring every interaction is fast, helpful and fully documented."}}, "LucaKeyFeatures": {"features": {"feature1": {"title": "24/7 Chat & Email Concierge", "description": ""}, "feature2": {"title": "Intelligent Prioritization and Smart Escalation", "description": ""}, "feature3": {"title": "Auto-Fill Support Forms for Users", "description": ""}, "feature4": {"title": "Full Log of Conversations, Internal Notes & Resolution Steps", "description": ""}}}, "LucaSmartAccounting": {"title": {"line1": "Who is <PERSON><PERSON>", "line2": "for?"}, "features": {"SMEs": {"title": "SMEs", "description": "• looking to enhance support without hiring large teams."}, "Startups": {"title": "Startups & lean teams", "description": "• aiming to automate Tier 1 support for cost efficiency."}, "Webapps": {"title": "Webapps and SaaS platforms", "description": "• needing uninterrupted, scalable customer interaction."}, "Founders": {"title": "Founders & product teams", "description": "• who want to stay focused on growth while delegating support to an intelligent, always-available assistant."}}}, "LucaComparison": {"Yumi": "<PERSON><PERSON>", "vs": "vs", "TraditionalChatbots": "Traditional Chatbots", "HumanOnly": "Human Only Support Teams", "leftComparisons": {"comparison1": {"title": "Smarter Routing", "description": "– Escalates to the right person or team no dead ends"}, "comparison2": {"title": "Priority-Aware", "description": "– Ranks and tags issues by urgency and impact."}, "comparison3": {"title": "Structured Input", "description": "– Collects user context and issue details before escalating."}, "comparison4": {"title": "Auto-Fill Forms", "description": "– Prepares support requests so users only need to review and confirm.."}, "comparison5": {"title": "Deep Integration", "description": "– Works with internal tools to keep records and statuses organized."}}, "rightComparisons": {"comparison1": {"title": "Always On", "description": "– Zero downtime, instant replies at any hour."}, "comparison2": {"title": "Scalable", "description": "– <PERSON>les 100s of enquiries without bottlenecks."}, "comparison3": {"title": "Cost-Effective", "description": "– Reduces headcount needs for repetitive tasks."}, "comparison4": {"title": "Data Driven", "description": "– Her logs help refine support workflows and identify weak points."}}}, "TryYumi": {"subTitle": "Get Started With <PERSON><PERSON>", "title": "Try Out Yumi", "description": "Test <PERSON><PERSON> right from our Help section she handles live chats, form fills, and real-time support.Want more? Try a version you can train with your own data, then book a demo to explore her full power.", "TryYumiNow": "<PERSON>"}, "LucaFAQ": {"title": "FAQs (Frequently Asked Questions)", "faqs": {"q1": {"question": "What is subscription like for <PERSON><PERSON>?", "answer": {"description": "Yumi is delivered as a modular AI customer service system under a flexible subscription model, which includes:", "line1": "Customization to match your brand and workflows", "line2": "Integration with your webapp or customer platform", "line3": "Access to both the client-facing support bot and internal UI", "line4": "Continuous updates and tuning based on real usage data"}}}}}, "CustomModel": {"LucaLandingHeroSection": {"title": "Customized Work Models: Tailored AI Solutions for Your Business", "description": "We create custom AI solutions designed around your unique workflows because we know one-size-fits-all doesn't cut it. Our ready-to-deploy models transform everyday processes like data analysis evaluating, decision-making, and automation into smart, AI-powered systems. Whether you're a small businessor a growing enterprise, you can harness the power of AI without needing to hire engineers or build an internal AI team."}, "LucaGitHub": {"title": {"text1": "<PERSON> significantly shortens the process from ideas to PowerPoint, allowing users to", "text2": "focus on creativity and strategic insights", "text3": "instead of being consumed by tedious tasks such as copy-pasting and formatting."}}, "HowLucaWorks": {"title": {"line1": "How", "line2": "Customization works"}, "steps": {"step1": {"title": "Understand Your Workflow", "description": "We map out your full inputs, steps, stakeholders, and output."}, "step2": {"title": "Integrate Inputs", "description": "Accepts both public and internal data, structured or unstructured."}, "step3": {"title": "Design Processing Logic", "description": "Define rules, procedures, safety checks, and analysis methods."}, "step4": {"title": "Customize Output", "description": "Supports generation of documents, reports, charts, dashboards, and social media content."}, "step5": {"title": "Deploy and Iterate", "description": "Initial deployment followed by user feedback and refinement."}}}, "LucaComparison": {"CustomizedModels": "Customized Models", "vs": "vs", "GenericAITools": "Generic AI Tools", "leftComparisons": {"comparison1": {"title": "Cost Effective", "description": "– No need for long-term engineering or data science hiring."}, "comparison2": {"title": "Rapid Deployment", "description": "– Faster time-to-value compared to internal development"}, "comparison3": {"title": "Domain Expertise Integration", "description": "– We incorporate your staff's knowledge directly into the model."}}, "rightComparisons": {"comparison1": {"title": "Tailored to Your Business", "description": "– Customized logic and workflows versus broad-use templates."}, "comparison2": {"title": "Higher Relevance and Precision", "description": "– Models built to align with your specific data, goals, and formats."}, "comparison3": {"title": "Better Compliance and Governance", "description": "– Built-in constraints to match internal standards."}}}, "LucaSmartAccounting": {"title": {"line1": "Who needs customized", "line2": "work models?"}, "features": {"Small": {"title": "Small and mid-sized companies", "description": "wanting to integrate AI without full-time AI specialists."}, "Business": {"title": "Business owners", "description": "looking to convert repetitive, manual workflows into automated processes."}, "Department": {"title": "Department heads and team leads", "description": "seeking efficiency in operational tasks like reporting, review, or content generation."}, "Consultants": {"title": "Consultants or professional service firms", "description": "needing tailored, branded client deliverables."}}}, "LucaKeyFeatures": {"features": {"feature1": {"title": "Workflow-Based Design", "description": "We analyze your entire process: inputs, logic, and output requirements."}, "feature2": {"title": "Multimodal Input Handling", "description": "Structured or unstructured data, public sources, or internal systems."}, "feature3": {"title": "Custom Processing Logic", "description": "Built-in company-specific guidelines, rules, and compliance parameters."}, "feature4": {"title": "Flexible Output Formats", "description": "Delivery in PPT, PDF, Word, Excel, CSV, or social media content formats."}, "feature5": {"title": "Ongoing Collaboration", "description": "We work with your team to align with your real-world workflow expectations."}}}, "LucaFAQ": {"title": "FAQs (Frequently Asked Questions)", "faqs": {"q1": {"question": "What is the pricing for customization?", "answer": {"line1": "• We charge case-by-case, based on complexity and workload", "line2": "Contact us to discuss your needs and receive a tailored proposal.", "ContactUs": "Contact Us"}}}}}, "DashboardHome": {"Hi": "Hi", "Description": "Access our full range of models for any task you need to tackle today.", "Cards": {"ModelManagement": {"Heading": "Model Management", "Sub": "Models Used"}, "Subscriptions": {"Heading": "Subscriptions", "Sub": "Running Subscription"}, "Credits": {"Heading": "Credits", "Sub": "Credit Balance", "Redeem": "Redeem perks"}}, "title": "Home", "RecentlyUsed": "Recently Used", "NoRecentModels": "No recent models used", "AllModels": "All Models", "DashboardHeader": {"MyModels": "My Models", "OtherBusinesses": "Other Businesses", "LogOut": "Log out", "NeedHelp": "Need Help?", "tutorials": "Tutorials", "toast": {"notLoadProfile": "Could not load your profile", "notLoadModels": "Could not load models", "notLoadRecently": "Could not load recently used models"}, "Organization": {"title": "New Organization", "description": "Please assign a name to your new organization. This organization will maintain its own settings and preferences, distinct from those of other organizations associated with your account.", "form": {"name": {"text": "Enter Organization Name"}}, "CreateOrganization": "Create Organization"}, "cards": {"HermesX": {"tabLabel": "Market Monitor", "label": "Free to add and view past alerts. Subscribe to real-time alerts to start paying.", "title": "Market News Monitor, tracking global financial movements and opportunities 24/7"}, "HermesC": {"tabLabel": "Market Monitor", "label": "Free to add and view past alerts and performance. Charges apply only if you subscribe to real-time alerts.", "title": "Market News Monitor, tracking global financial movements and opportunities 24/7"}, "Luca": {"tabLabel": "Accountant", "label": "30 Credits/month per company for basic accounting services.", "title": "Accountant, automating bookkeeping and financial reporting with accuracy"}, "Freddie": {"tabLabel": "HR", "label": "Free for one job and up to 20 candidates. Pay 20 Credits for every additional 50 candidates.", "title": "Recruiter, identifying top talent and streamlining the hiring process"}, "Orion": {"tabLabel": "Equity Analyst", "label": "Free to add. Only pay when you process tickers.", "title": "Equity Analyst, delivering deep market insights for informed investment decisions"}, "Olympus": {"tabLabel": "Market Simulation", "label": "Free to add. Each simulation costs 100 Credits.", "title": "Market Simulator, forecasting trends and testing strategies before execution"}, "Yumi": {"tabLabel": "Customer Concierge", "label": "Experience how <PERSON><PERSON> handles user interactions across chat and email, fully integrated with an internal UI for support teams. Access costs 30 credits and is valid for 3 days of testing", "title": "Customer Concierge, enhancing user experience with personalized support"}}}, "Welcome": {"title": "Welcome <PERSON>", "ContinueToDashboard": "Continue to Dashboard", "slides": {"Welcome": {"title": "Welcome to ai-wk", "description": "Explore different business models that streamline your workflow and suit your unique working style, while also enhancing productivity and collaboration."}, "Credits": {"title": "Credits & Pricing", "description": "Just thought to let you know that we charge a small fee per usage of each model. To top up or understand more click HERE to begin"}}}, "Profile": {"title": " Complete your profile", "description": "Finish setting up your profile to access the best of us all. Provide your name and email address and gain access to all the models available.", "toast": {"ProfileUpdated": "Profile updated!", "UpdateFailed": "Update failed"}}, "ModelCard": {"IHaveAgreed": "I have read and agreed to:", "The": "The", "TermsOfUse": "Terms of Use", "Disclaimer": "Disclaimer", "AddToMyModel": "Add to my model", "toast": {"UpdateFailed": "Update failed", "TermsFailed": "Terms failed"}}}, "DashboardCredits": {"title": "Credits", "CreditsLeft": "Credits Left", "Credits": "Credits", "RedeemPerks": "<PERSON><PERSON><PERSON>", "PaymentMethod": "Payment method and Subscription", "TransactionHistory": "Transaction History", "BillStatement": "Bill statement", "toast": {"NotLoadExchangeRates": "Could not load exchange rates"}, "TransactionTable": {"PayAsYouGo": "Pay-As-You-Go", "NumberOfCredits": "Number of Credits", "EnterAmount": "Enter amount", "PayWithUSDCard": "Pay With USD Card", "PayWithStableCoins": "Pay With Stable Coins", "PayWithBank": "Pay With Bank", "WalletAddress": "Wallet Address", "CopyAddress": "Copy Address", "TopUpWallet": "Top Up Wallet", "ForPayment": "For Payment:", "SelectCoinType": "Select Coin Type", "ScantoPay": "<PERSON>an to Pay", "pay": {"text1": "$1", "text2": "per 1 Credits"}, "payUSDT": {"T": "T", "text1": "USDT", "text2": "1 USDT = 1 Credit"}, "payUSDC": {"C": "C", "text1": "USDC", "text2": "1 USDC = 1 Credit"}, "toast": {"notLoadTransactionHistory": "Could not load your transaction history", "WalletAddressSavedSuccessfully": "Wallet address saved successfully", "FailedToSave": "Failed to save wallet address", "AddressCopied": "Address copied to clipboard!", "PaymentProcessCompleted": "Payment process completed"}, "walletAddressDescription": "Please provide your Wallet ID for us to charge the credits amount from. We will not deduct more than you request.", "scanToPayDescription": "Please scan the QR-Code below to initiate payment for your credits.", "NoFundingRecord": {"text": "No funding record", "description": "We have no record of topping up yet"}}, "TopUp": {"title": "Top Up", "EmailSent": "<PERSON><PERSON>", "SelectPaymentMethod": "Select Payment Method", "TopUpWallet": "Top Up Wallet", "CryptoTopUp": "Crypto Top Up", "BankTransfer": "Bank Transfer", "SelectCoinType": "Select Coin Type", "WalletAddress": "Wallet Address", "Payment": "Payment", "AccountDetailsRequestSent": {"title": "Account Details Request Sent", "description": "Your request has been sent and an email containing the bank account details will be sent to your email address."}, "Processing": "Processing...", "ProceedToPay": "Proceed to pay", "CreditsToAdd": "Credits to add", "AmountToAdd": "Amount to add", "AutoRenewMonthly": "Auto renew monthly", "PaymentMethod": {"text": "Payment Method", "usd": "Credit/Debit Card (Multi-currency)", "stable": "Crypto Wallet (USDT/USDC)", "bank": "Bank Transfer (for payment over $1000)", "yedpay": "Wechat/UnionPay (HKD)"}, "CoinSelection": {"T": "T", "USDT": {"text": "USDT", "description": "1 USDT = 1 Credit"}, "C": "C", "USDC": {"text": "USDC", "description": "1 USDC = 1 Credit"}}, "WalletAddressForm": {"description": "Please provide your Wallet ID for us to track your payment to us and credit your account accurately, otherwise the fund will be lost.", "info": {"text1": "Please use Ethereum Mainnet", "text2": "network."}, "WalletAddress": {"text": "Wallet Address", "description": "Enter your wallet address"}}, "ScanToPay": {"text": "<PERSON>an to Pay", "CopyAddress": "Copy Address", "Confirming": "Confirming...", "ConfirmPayment": "Confirm payment", "contents": {"line1": "Please scan the QR-Code below to initiate payment for your credits.", "line2": {"text1": "Only send USDC or USDT from the", "text2": "ERC-20", "text3": "network to this address. Using a wallet on a different network may result in loss of funds"}, "line3": "After payment please input the transaction hash to confirm your payment"}, "TransactionHash": {"text": "Transaction Hash", "description": "Enter the transaction hash from your payment"}}, "CryptoTopUpForm": {"AmountToAdd": {"description": "1 {currentCoin} = 1 Credit"}}, "BankTransferForm": {"Note": {"text1": "Note:", "text2": "For Bank Wire Transfer a request would be sent to verify your account and then an email containing the receiving bank account details will be sent to you for payment."}}, "BankDetails": {"text": "Bank Details", "description": "View the details below for your bank wire transfer", "BankName": {"text": "Bank Name:", "description": "DBS BANK (HONG KONG) LTD"}, "AccountNumber": {"text": "Account Number:", "description": "*********"}, "AccountName": {"text": "Account Name:", "description": "Alpharithm Investments Limited"}, "SWIFTCode": {"text": "SWIFT Code:", "description": "DHBKHKHH"}, "BankCode": {"text": "Bank Code:", "description": "016"}, "Branch": {"text": "Branch:", "description": "478"}, "Address": {"text": "Address:", "description": "G/F, The Center, 99 Queen's Road Central, Central, Hong Kong"}, "UploadInfo": {"description": "Made the transfer? Upload your transfer slip or receipt below", "MB": "MB - Click to change", "notes": {"note1": "Upload your transfer slip here", "note2": "PDFs, JPEG with a maximum size of 5mb"}}}, "toast": {"notLoadExchangeRates": "Could not load exchange rates", "PaymentFailed": "Payment failed", "WalletAddressSavedSuccessfully": "Wallet address saved successfully", "FailedToSave": "Failed to save wallet address", "AddressCopied": "Address copied to clipboard!", "PleaseEnterAddress": "Please enter a wallet address", "NotSuccessful": "Not successful", "ReceiptUploaded": "Receipt uploaded successfully!", "UploadFailed": "Upload failed", "EnterTransactionHash": "Please enter a transaction hash", "CopiedToClipboard": "{label} copied to clipboard!", "UploadPDForJPEGFile": "Please upload a PDF or JPEG file", "FileSizeLessThan5MB": "File size must be less than 5MB", "SelectFileToUpload": "Please select a file to upload"}}, "InvoiceModal": {"GenerateStatement": "Generate Statement", "toast": {"DownloadFailed": "Download failed"}}, "RedeemParkModal": {"RedeemSuccessful": "Redeem Successful", "ViewCredits": "View Credits", "creditAmount": {"text1": "You have successfully redeemed", "text2": "worth of credits!"}, "EnterCode": {"text": "Enter Code", "description": "Enter code to redeem gift"}, "RedeemPerks": {"title": "<PERSON><PERSON><PERSON>", "description": "Got a code? Use it here to get your credit rewards."}, "toast": {"Successful": "Successful!", "SubscriptionFailed": "Subscription failed", "PleaseEnterCode": "Please enter a code"}}, "Subscribe": {"title": "Credits", "NoActiveSubscriptionsFound": "No active subscriptions found", "Subscriptions": "Subscriptions", "subscription": "subscription", "$": "$", "per": "/ per", "BillingStarted": "Billing Started:", "NextBillingCycle": "Next Billing Cycle:", "RemainingDays": "Remaining Days:", "Cancelled": "Cancelled", "NoActivePaymentMethodFound": "No active payment method found", "PaymentMethod": "Payment Method", "Default": "<PERSON><PERSON><PERSON>", "SetAsDefault": "Set as default", "SetDefault": {"title": "Set As Default?", "description": "Are you sure you want to set this payment method as your default?", "cancelText": "Cancel", "confirmText": "Yes, Set As Default", "loadingText": "Submitting..."}, "CancelSubscription": {"title": "Cancel Subscription", "description": "Are you sure you want to cancel your subscription? You will lose access to premium features immediately.", "cancelText": "No, Keep It", "confirmText": "Yes, Cancel It", "loadingText": "Cancelling..."}, "SubText": {"stripe": "For payment with your cards through Stripe", "bankWire": "For payment greater than $1000", "crypto": "Your saved wallet information", "yedpay": "For payments in HKD", "default": "usd"}, "toast": {"notLoadSubscriptions": "Could not load subscriptions", "SubscriptionCancelled": "Subscription cancelled successfully", "FailedToCancel": "Failed to cancel subscription", "notLoadPayment": "Could not load payment", "PaymentSetAsDefault": "Payment method set as default", "FailedToSet": "Failed to set as default"}}}, "DashboardSettings": {"title": "Settings", "tabs": {"Account": {"title": "Account", "BASIC": "BASIC", "ChangeProfilePicture": "Change Profile Picture", "currentlyOnEditMode": "You are currently on edit mode", "YourDetails": {"title": "Your Details", "description": "Update your account settings. Set your preferred language and timezone."}, "TelegramID": {"text": "Telegram ID", "ChangeID": "Change ID", "description": "Your Telegram ID linked to various models. You can also link your app by searching @{botName} in your Telegram app and choose /link_account"}, "LanguagesAndTimeZone": {"title": "Languages & Time Zone", "description": "Set your preferred language and timezone.", "searchLanguage": "Search language..."}, "Language": "Language", "NoLanguageFound": "No language found.", "TimeZone": "Time Zone", "SearchTimeZone": "Search time zone...", "SelectTimezone": "Select timezone...", "NoTimeZoneFound": "No time zone found.", "SignInMethod": {"title": "Sign-in Method", "description": "Your method of sign in"}, "SignedInWithEmail": "Signed in with <PERSON><PERSON>", "SignedInWithGoogle": "Signed in with Google", "ChangePassword": "Change Password", "OldPassword": "Old Password", "NewPassword": "New Password", "ConfirmNewPassword": "Confirm New Password", "toast": {"ProfileUpdated": "Profile updated!", "UpdateFailed": "Update failed", "notLoadProfile": "Could not load your profile", "FailedLoadTimezones": "Failed to load timezones", "PasswordChanged": "Password changed successfully!"}, "SaveChangesModal": {"title": "Save Changes?", "description": "Do you want to save the changes you made to your account details?"}}, "Organization": {"title": "Organization", "pane": {}}, "Security": {"title": "Security", "pane": {}}, "API": {"title": "API", "pane": {}}}}, "DashboardSupport": {"title": {"text1": "Need Help? Talk to", "text2": "<PERSON><PERSON>"}, "description": "Get started with a simple prompt", "TicketHistory": {"text": "Your Ticket History", "title": "Tickets History", "Resolved": "Resolved", "Pending": "Pending", "NoResultsFound": "No results found", "toast": {"notLoadTickets": "Could not load tickets", "notLoadTicketDetails": "Could not load ticket details"}, "tableHeader": {"ID": "ID", "Date": "Date", "IssueType": "Issue Type", "Status": "Status", "PriorityLevel": "Priority Level"}, "TicketDetail": {"FullName": "Full Name", "Email": "Email", "Issue": "Issue", "Priority": "Priority", "Status": "Status", "Subject": "Subject", "Description": "Description", "Attachments": "Attachments"}}, "FailedToCreateTicket": "Failed to create ticket. Please try again.", "messages": {"InputTooLarge": "Input too large. Please reduce content size and try again.", "SomethingWentWrong": "Something went wrong. Please try again.", "TicketCreated": "Ticket created successfully"}, "TicketForm": {"title": "Ticket Form", "AddAttachment": "Add Attachment", "RemoveFile": "Remove file", "IssueTypes": {"BusinessAndPartnerships": "Business & Partnerships", "illingAndPayments": "Billing & Payments", "UsingOurModels": "Using our Models", "CustomizationRequests": "Customization Requests", "TechnicalIssues": "Technical Issues & Bugs", "AccountHelp": "Account Help", "Media": "Media & Brand Requests", "Careers": "Careers and Opportunities", "GeneralQuestions": "General Questions or Feedback", "Other": "Other or Private Concern"}, "FullName": {"text": "Full Name", "description": "Your name"}, "IssueType": {"text": "Issue type", "description": "Select an issue type"}, "PriorityLevel": {"text": "Priority Level", "description": "Select a priority level"}, "Subject": {"text": "Subject", "description": "Subject of ticket"}, "Chat": {"text": "Type your message..."}, "Attachments": "Attachments", "DescribeYourIssue": "Describe your issue"}}, "ERICBAI": {"meta": {"title": "<PERSON> | Founder & CEO of Alpharithm Investments", "description": "<PERSON> is the founder of Alpharithm Investments, a leading AI-powered investment platform. Former MD at Goldman Sachs and HSBC, with 20+ years in global finance."}, "foundersCorner": "Founder's Corner", "name": "<PERSON>", "title": "Founder and CEO Alpharithm Investments", "bio": {"paragraph1": "<PERSON> founded Alpharithm Investments in March 2024, leveraging over two decades of experience as a senior investment banker. He has held leadership roles at top-tier financial institutions including Goldman Sachs, Credit Suisse, Rothschild, HSBC, and BNP Paribas, across global financial hubs such as London, Paris, and Hong Kong.", "paragraph2": "<PERSON> was appointed Managing Director at Goldman Sachs in the Class of 2013 and served as the Greater China Head of Financial Institutions. He was later appointed Global Head of Financial Institutions at HSBC. His career has been defined by a strong track record in capital markets and M&A, advising global and Chinese financial institutions on strategic transactions.", "paragraph3": "<PERSON> holds an MBA from the prestigious ESSEC Business School in France and is a CFA Charterholder. Fluent in English, French, and Mandarin, he operates effectively across global markets and complex cross-border environments. <PERSON> played a pivotal role in funding and IPOs for several fintech unicorns which sparked his deep interest in the transformative power of technology. That journey led to the founding of Alpharithm Investments, where <PERSON> is pioneering the development of AI-based investment and trading models aimed at achieving sustained alpha and risk-adjusted performance.", "paragraph4": "Under <PERSON>'s leadership, Alpharithm has grown from an investment-focused initiative into a broader AI powerhouse. The company has expanded its capabilities beyond asset management to deliver AI-driven solutions in recruitment, customer service, accounting, and more—redefining how businesses access and apply high-quality work through automation. <PERSON>'s belief in the revolutionary potential of artificial intelligence to reshape industries underpins Alpharithm's vision and operating model.", "paragraph5": "He brings unmatched domain expertise in financial fundamentals, capital raising, and execution. His institutional relationships also provide Alpharithm with a natural client base, supporting its go-to-market strategy for both investment and enterprise AI products.", "goldParagraph1": "<PERSON> was appointed", "goldParagraph2": " in the Class of 2013 and served as the Greater China Head of Financial Institutions. He was later appointed Global Head of Financial Institutions at HSBC. His career has been defined by a strong track record in", "goldParagraph3": " advising global and Chinese financial institutions on strategic transactions."}, "links": {"goldmanSachs": "Managing Director at Goldman Sachs", "capitalMarkets": "capital markets and M&A"}, "dialog": {"title": "Transactions", "description": "In his illustrious banking career, <PERSON> has executed major capital markets and M&A transactions, including:", "sections": {"ipos": {"title": "IPOs", "items": ["Lufax", "OneConnect", "<PERSON> Good Doctor", "CPIC H-share", "PICC Group H-share", "Galaxy Securities H-share", "Huatai Securities", "CICC", "Orient Securities", "GTJA Securities H-share"]}, "placements": {"title": "Placements and Rights Issues:", "items": ["Ping An OneConnect pre-IPO", "Ping An HealthKonnect pre-IPO", "Lufax B-round and C-round", "<PERSON> An Good Doctor A-round", "BOC rights issue", "ICBC H-share block trade", "China Merchants Bank rights issue."]}, "debt": {"title": "Debt and Preference Shares:", "items": ["BOC offshore/onshore preference shares", "China Life subdebt", "CDB Leasing bond issuances", "First Basel III compliant capital issuance in China by Binhai Bank."]}, "acquisitions": {"title": "Acquisitions", "items": ["Lufax acquisition of Puhui Finance", "Telstra's sale of Autohome stake to Ping An", "Ping An Group's acquisition of Shenzhen Development Bank"]}, "strategicInvestments": {"title": "Strategic Investments", "items": ["Barclays's acquisition of ABSA Bank", "Erste Bank's acquisition of Romanian Commercial Bank", "KKR's acquisition of Morgan Stanley's stake in CICC", "AMP's investment in China Life Pension."]}}}}, "TOS": {"title": "Terms of Use", "Date": "Effective Date: May 2025", "description": "Welcome to ai-wk.com (“Site”), a platform that provides AI-powered work models for business professionals (“Services”). By accessing or using this Site and our Services, you agree to comply with and be bound by these Terms of Use (“Terms”). If you do not agree, please do not use our Site or Services.", "features": {"feature1": {"title": " 1. Eligibility", "description": "You must be at least 18 years old and have the legal capacity to enter into a binding contract. By using this Site, you represent and warrant that you meet these requirements."}, "feature2": {"title": "2. Use of Services", "description": "You may access and use our Services for lawful business purposes only. You agree not to:", "list": {"item1": "Use our content for payments or distribution except in accordance with our license.", "item2": "Attempt to test the vulnerability of any service or breach security measures.", "item3": "Share your account credentials or access tokens with third parties.", "item4": "Use the Services in violation of applicable law or regulations."}}, "feature3": {"title": "3. Intellectual Property", "description": "All content and materials provided on our website or through our Services are protected by intellectual property rights. All creative content, text, graphics, and design is the property of our company or our partners. You may not use, copy, reproduce, modify, translate, license or otherwise share content without written permission."}, "feature4": {"title": "4. Account and Payment", "description": {"paragraph1": "Some features require account registration. You agree to provide accurate information and keep it up to date.", "paragraph2": "Payment terms will be detailed separately. Subscription renewals will be based on current payment terms and are non-refundable unless specified otherwise."}}, "feature5": {"title": "5. <PERSON>laimer", "description": "Our AI models are designed to assist, not replace, workforce. Our AI may capture professional confidential information as data examples. To avoid this:", "list": {"item1": "Do not rely solely on AI outputs as absolute truth.", "item2": "Review all AI-generated content for accuracy and appropriateness.", "item3": "Do not enter sensitive data or any content generated by the Services.", "item4": "Know that safety filters can occasionally block legitimate content inputs."}}, "feature6": {"title": "6. Third-Party Services", "description": "This Site may link to third-party services (e.g., Stripe). We claim no responsibility for the content, terms of service offered by such services."}, "feature7": {"title": "7. Termination", "description": "We may suspend or terminate your access to the Services at any time, with or without notice. You may also request termination by contacting us."}, "feature8": {"title": "8. Limitation of Liability", "description": "To the maximum extent permitted by law, we shall not be liable for any indirect, incidental, special, consequential, or punitive damages arising out of these Services."}, "feature9": {"title": "9. Indemnification", "description": "You agree to indemnify and hold harmless us, our and its affiliates, officers, and employees from any claims, liabilities, and expenses arising out of your use of our Services, your breach of these Terms or conduct, your use that is not covered by our grants, negligence or willful misconduct."}, "feature10": {"title": "10. Changes of Terms", "description": "We may update these Terms from time to time. Your continued use of the Site after changes means you accept the revised Terms. We will notify users of material changes."}, "feature11": {"title": "11. Governing Law", "description": "These Terms are governed by the laws of Cayman Islands, without regard to its conflicts of laws principles."}, "feature12": {"title": "12. Contact Us", "description": "If you have any questions regarding these Terms, please contact us:"}}}, "PrivacyPolicy": {"title": "Privacy Policy", "Date": "Effective Date: May 2025", "description": {"line1": "This Privacy Policy explains how ai-wk.com (“Site”) collects, uses, discloses, and protects your personal information when you visit our website and use our services.", "line2": "By accessing or using our services, you agree to the terms of this Privacy Policy."}, "features": {"feature1": {"title": "1. Information We Collect", "listA": {"title": "a. Information You Provide:", "item1": "Name, email address, phone number, and company (if applicable)", "item2": "Billing and payment information", "item3": "Personal and Usage Information (processed via third-party payment providers)", "item4": "Support inquiries and other communications"}, "listB": {"title": "b. Information We Collect Automatically:", "item1": "IP address, browser type, and device information", "item2": "Usage data including pages visited, time spent on the site", "item3": "Cookies and similar tracking technologies"}}, "feature2": {"title": "2. How We Use Your Information", "list": {"item1": "To provide, operate, and maintain our services", "item2": "To improve our user experience", "item3": "To communicate with you, including updates and support", "item4": "To process transactions and manage your account", "item5": "To improve our platform and enhance user experience", "item6": "To comply with legal obligations and enforce our Terms of Use"}}, "feature3": {"title": "3. Sharing and Disclosure", "description": "We do not sell your personal data. We may share your information with:", "list": {"item1": "Service providers that help operate the platform (e.g., cloud hosting, payment processing, analytics)", "item2": "Legal or regulatory authorities (as required by law or to protect our rights)", "item3": "Successors in the event of a merger, acquisition, or sale of assets"}}, "feature4": {"title": "4. Data Security", "description": "We implement reasonable administrative, technical, and physical safeguards to protect your personal information. However, no method of transmission over the internet or electronic storage is 100% secure."}, "feature5": {"title": "5. Data Retention", "description": "We retain personal information only as long as necessary for legitimate business purposes or as required by law."}, "feature6": {"title": "6. Your Rights and Choices", "description": "Depending on your location, you may have rights to:", "list": {"item1": "Access, correct, or delete your personal data", "item2": "Opt-out of marketing communications", "item3": "Withdraw consent for marketing communications", "item4": "File a complaint with a data protection authority"}, "info": "You can exercise these rights by contacting <NAME_EMAIL>."}, "feature7": {"title": "7. <PERSON><PERSON>", "description": "We use cookies and similar technologies to improve your browsing experience, analyze traffic, and personalize content. You can manage your preferences through your browser settings."}, "feature8": {"title": "8. International Users", "description": "Our services may be hosted globally. If you are accessing from a jurisdiction with data protection laws (e.g., GDPR), we will comply with applicable regulations. While we may try to be subject to all international data protection laws, we still make reasonable efforts to comply with local laws to handle data privacy, transactions, and support our customers. Our team is not European."}, "feature9": {"title": "9. Changes to This Policy", "description": "We may update this Privacy Policy from time to time. The updated version will be posted on this page with a revised effective date. Continued use of our services indicates your acceptance of the updated policy."}, "feature10": {"title": "10. Contact Us", "description": "For questions or concerns regarding these Terms, please contact us:"}}}, "AIDisclaimer": {"title": "AI Model Disclaimer", "Date": "Effective Date: 1 May 2025", "description": {"text1": "This disclaimer applies to all users of AI-powered work models offered through ai-wk.com, including but not limited to models named", "text2": "<PERSON><PERSON>", "text3": "and", "text4": "Orion"}, "features": {"feature1": {"title": "1. No Investment Advice", "description": "The content and outputs generated by our AI models are provided for general informational purposes only. They do not constitute investment, financial, legal, or any other form of professional advice. You should not rely solely on AI-generated content to make investment or business decisions."}, "feature2": {"title": "2. Limitations of AI-Generated Content", "description": "Our models produce results based on data inputs and algorithms that may be incomplete, inaccurate, or outdated. As a result:", "list": {"item1": "The analysis and recommendations may contain errors or biases", "item2": "The data used may be partial or contextually insufficient", "item3": "The content may not reflect current market conditions or individual circumstances"}, "info": "We do not guarantee the accuracy, completeness, or reliability of any AI-generated information."}, "feature3": {"title": "3. User Responsibility", "description": "Users are solely responsible for evaluating the risks and appropriateness of using AI-generated outputs. We strongly recommend consulting licensed professionals before making any decisions based on such content."}, "feature4": {"title": "4. Limitation of Liability", "description": {"paragraph1": "ai-wk.com and its affiliates shall not be liable for any direct, indirect, incidental, or consequential damages, including financial losses, resulting from your use or reliance on AI-generated outputs.", "paragraph2": "Use of our Services constitutes your acceptance of this disclaimer."}}, "feature5": {"title": "5. Contact Us", "description": "For questions regarding this disclaimer, contact us:"}}}, "RefundPolicy": {"title": "Refund Policy", "description": "At ai-wk.com, we are committed to delivering high-quality AI work models and services to our users. Please review our refund policy below.", "features": {"feature1": {"title": "1. Digital Products and Credits", "description": {"paragraph1": "All purchases of AI work model credits and paid services are non-refundable once the credits have been used or the services accessed.", "paragraph2": "We do offer refunds under the following conditions:"}, "list": {"item1": "A user is charged in error", "item2": "A user accidentally purchases multiple packages in a short period", "item3": "A user is unable to access the service due to a verified technical issue on our end"}}, "feature2": {"title": "2. Eligibility for Refunds", "description": "To be eligible for a refund, you must:", "list": {"item1": "Submit a refund request within 7 days of the transaction", "item2": "Provide your transaction ID and a brief description of the issue", "item3": "Ensure the credits or services in question have not been used"}, "info": "We reserve the right to refuse refund requests that do not meet the above conditions."}, "feature3": {"title": "3. How to Request a Refund", "description": {"text1": "Please submit a support form request at ai-wk.com or contact our support team at", "text2": "with the following information:"}, "list": {"item1": "Your full name", "item2": "Registered email address", "item3": "Date and time of purchase", "item4": "Transaction ID", "item5": "Reason for refund request"}, "info": "We aim to review and respond to all refund requests within 3-5 business days."}, "feature4": {"title": "4. Final Notes", "description": "All refund decisions are made at the sole discretion of Alpharithm Investments Limited, and approved refunds will be processed to the original payment method."}}}, "DashboardHermesC": {"title": {"line1": "Hermes C: Stay Ahead with Real-Time", "line2": "Stock Events"}, "TrialCountdownBanner": {"countdown": "{days}d {hours}h {minutes}m {seconds}s left for your free trial. Subscribe now to enjoy beyond your trial"}, "description": "Send full analysis to your finger tips.", "AlertsTable": {"title": "Previous Alerts", "Latest": "Latest", "delayed": "24 hours delayed", "UTC": "UTC", "NYT": "NYT", "UserTime": "User time", "TimeZone": "Time Zone", "ViewPerformance": "View Performance", "Filter": "Filter", "Button": {"Loading": "Loading...", "EnquireNow": "Enquire Now", "SubscribeNow": "Subscribe Now", "StartFreeTrial": "Start Free Trial"}, "toast": {"notLoadAlerts": "Could not load alerts", "notLoadSavedFilters": "Could not load saved filters", "notLoadSubscriptionStatus": "Could not load subscription status"}, "Table": {"ViewChart": "View Chart", "RawStrategyReturn": {"text": "Raw strategy return", "description": "This represents the return from a raw strategy set by AI with no human inputs. It is based on entry at the AI recommended entry price, with exit occurring either when the take-profit level is hit or at the close of the next regular trading session, whichever comes first."}, "PassiveReturnClose": {"text": "Passive return to close", "description": "This measures the return from the time of the alert to the next regular session close, assuming a passive position is taken at the average price of the alert bar and held through that period."}, "ActiveReturnToMax": {"text": "Active return to max", "description": "measures the maximum short-term return achievable after the alert, calculated from the alert bar's average price to the highest price reached before the next session close. Achieving this return assumes active trade management and timely profit-taking decisions, reflecting the max profit an experienced trader could realistically capture with skillful execution."}, "NoResultsFound": "No results found", "Header": {"Time": "Time", "Ticker": "Ticker", "Event": "Event", "Direction": "Direction", "Confidence": "Confidence", "Condition": "Condition"}}}, "EnquireFlow": {"Loading": "Loading...", "ImportantDisclaimers": {"title": "Important Disclaimers", "description": "<PERSON>mes C is not a financial advisor. It is a general information processing system designed to automate part of your investment workflow. Users must keep in mind:", "contents": {"line1": {"text1": "No investment advice is provided.", "text2": "Hermes should not be relied upon to make investment decisions without human judgment."}, "line2": {"text1": "Hermes may be misled by incomplete or false news articles.", "text2": "News signals are only as good as their inputs. Users must verify the validity of the news and signal."}, "line3": {"text1": "Users must supply their own data sources.", "text2": "Due to licensing constraints, we do not bundle third-party news or market data feeds."}, "line4": {"text1": "Not for retail investors.", "text2": "This tool is intended only for qualified professionals with the expertise to interpret its outputs."}}}, "ScheduleMeeting": {"title": "Next Steps: Schedule a Meeting", "description": "To ensure this system fits your needs and investment setup, we do not offer instant subscriptions. Please click below to request a meeting with our team!"}, "Organization": {"text": "Organization", "description": "Name of your fund, company, or family office"}, "TypeOfInvestor": {"text": "Type of Investor", "description": "e.g hedge fund, family office, prop trading desk"}, "Country": {"text": "Country", "description": "Jurisdiction of your operation"}, "toast": {"DisclaimerAccepted": "Disclaimer accepted successfully!", "FailedToAccept": "Failed to accept disclaimer", "MeetingSuccessfully": "Meeting request submitted successfully!", "FailedToSave": "Failed to save meeting request"}}, "FilterAlerts": {"Reset": "Reset to default", "LoadingFilters": "Loading your filters...", "FilterTable": "Filter Table", "Direction": {"text": "Direction", "description": "Select multiple directions where applicable", "long": "<PERSON>", "short": "Short"}, "Condition": {"text": "Condition", "description": "Select multiple conditions where applicable", "normal": "Normal", "breakthrough": "Breakthrough"}, "Confidence": {"text": "Confidence", "description": "Select multiple confidence levels where applicable"}, "toast": {"notLoadSavedFilters": "Could not load saved filters", "FailedToSaveFilters": "Failed to save filters"}}, "Subscribe": {"title": "Subscribe to Receive the Latest <PERSON><PERSON>s", "description": "To keep you instantly updated, we'll send alerts directly to your connected Telegram ID. This subscription ensures you never miss an important notification. Select a subscription type and Click the \"Proceed\" button below to activate your Telegram alerts and stay in the loop.", "toast": {"Successful": "Successful!", "SubscriptionFailed": "Subscription failed"}, "Monthly": {"text": "Monthly", "description": "1,000 Credits"}, "Annually": {"text": "Annually", "description": "12,000 Credits"}}, "FreeTrial": {"title": "Start 5 Days Free Trial", "description": "Enjoy a 5-day free trial. You'll need to subscribe after the trial to continue receiving Hermes C alerts.", "StartFreeTrial": "Start Free Trial", "toast": {"TrialStartedSuccessfully": "Trial started successfully!", "notStartTrial": "Could not start trial"}}, "Performance": {"Title": "Performance", "Header": {"line1": "Hermes C: Stay Ahead with Real-Time", "line2": "Stock Events", "description": "Send full analysis to your finger tips."}, "ViewGraph": {"loading": "Loading...", "strategyReturn": "Strategy Return", "passiveReturnToClose": "Passive return to close", "passiveReturnToMax": "Passive return to max", "averageReturns": "Average returns"}, "ViewChart": {"loading": "Loading..."}, "Table": {"Time": "Time (NYT)", "NumberOfAlerts": "Number of alerts", "LongPositions": "Long Positions %", "NormalConditions": "Normal Conditions %", "RawStrategyReturn": "Raw strategy return", "PassiveReturnToClose": "Passive return to close", "ActiveReturnToMax": "Active return to max", "NoResultsFound": "No results found", "UserTime": "User time", "NYT": "NYT", "Previous": "Previous", "Next": "Next", "Page": "Page", "of": "of", "Filter": "Filter", "Period": {"Daily": "Daily", "Weekly": "Weekly", "Monthly": "Monthly"}}, "FilterAlertsModalHermesC": {"filterTable": "Filter Table", "reset": "Reset to default", "loading": "Loading your filters...", "direction": {"text": "Direction", "description": "Select multiple directions where applicable", "Long": "<PERSON>", "Short": "Short"}, "condition": {"text": "Condition", "description": "Select multiple conditions where applicable", "normal": "Normal", "breakthrough": "Breakthrough"}, "confidence": {"text": "Confidence", "description": "Select multiple confidence levels where applicable"}, "cancel": "Cancel", "apply": "Filter"}, "PerformanceChart": {"constantCapital": "Constant Capital", "compounding": "Compounding", "legend": {"rawStrategyReturn": "Raw strategy return", "passiveReturnToClose": "Passive return to close", "activeReturnToMax": "Active return to max", "activeAlphaZone": "Active Alpha Zone", "activeAlphaZoneTooltip": "AAZ represents the range of potential return between a passive hold-to-close strategy and the maximum achievable return before the next session close. It reflects the profit window available to experienced traders who actively manage their positions with timely entries and exits. Capturing value within this zone requires skillful execution, market awareness, and discretion—beyond simply reacting to alerts."}, "customTooltip": {"date": "Date: {label}"}, "accumulativePerformance": "Accumulative Performances at Average Daily Returns"}, "SubscriptionFlowModal": {"disclaimer": {"title": "Important Disclaimers", "desc1": "<PERSON>mes C is not a financial advisor. It is a general information processing system designed to automate part of your investment workflow. Users must keep in mind:", "noAdvice": "No investment advice is provided.", "noAdviceDesc": "Hermes should not be relied upon to make investment decisions without human judgment.", "misled": "Hermes may be misled by incomplete or false news articles.", "misledDesc": "News signals are only as good as their inputs. Users must verify the validity of the news and signal.", "ownData": "Users must supply their own data sources.", "ownDataDesc": "Due to licensing constraints, we do not bundle third-party news or market data feeds.", "notRetail": "Not for retail investors.", "notRetailDesc": "This tool is intended only for qualified professionals with the expertise to interpret its outputs.", "cancel": "Cancel", "agree": "Agree and Continue", "processing": "Processing..."}, "meeting": {"title": "Next Steps: Schedule a Meeting", "description": "To ensure this system fits your needs and investment setup, we do not offer instant subscriptions. Please click below to request a meeting with our team!", "organization": "Organization", "organizationPlaceholder": "Name of your fund, company, or family office", "typeOfInvestor": "Type of Investor", "typeOfInvestorPlaceholder": "e.g hedge fund, family office, prop trading desk", "country": "Country", "countryPlaceholder": "Jurisdiction of your operation", "message": "Message", "messagePlaceholder": "Brief description of your interest or any questions", "cancel": "Cancel", "submit": "Submit", "submitting": "Submitting..."}}, "SubscribeModalHermesC": {"title": "Subscribe to Receive the Latest <PERSON><PERSON>s", "description": "To keep you instantly updated, we'll send alerts directly to your connected Telegram ID. This subscription ensures you never miss an important notification. Select a subscription type and Click the \"Proceed\" button below to activate your Telegram alerts and stay in the loop.", "monthly": "Monthly", "monthlyCredits": "1,000 Credits", "annually": "Annually", "annualCredits": "12,000 Credits", "cancel": "Cancel", "proceed": "Proceed", "success": "Successful!", "error": "Subscription failed"}}}, "DashboardHermesX": {"title": {"line1": "Hermes X: Stay Ahead with Real-Time", "line2": "Market-Moving Alerts"}, "Social": {"Follow1": "<PERSON><PERSON> X <PERSON>", "Follow2": "Follow us on X for top-tier alerts at no cost."}, "description": "Delivered instantly to your Telegram, 24/7. Check out a delayed sample below.", "AlertsTable": {"title": "Previous Alerts", "Latest": "Latest", "delayed": "24 hours delayed", "UTC": "UTC", "NYT": "NYT", "UserTime": "User time", "ViewPerformance": "View Performance", "TelegramPreferences": "Telegram Preferences", "Filter": "Filter", "Button": {"Loading": "Loading...", "SubscribeNow": "Subscribe Now", "StartFreeTrial": "Start Free Trial", "FilterTable": "Filter Table"}, "toast": {"notLoadAlerts": "Could not load alerts", "notLoadSavedFilters": "Could not load saved filters", "notLoadSubscriptionStatus": "Could not load subscription status"}, "Table": {"ViewChart": "View Chart", "PreviewWarning": {"line1": "Once subscribed, you'll receive alerts in real time with no delay on your telegram account and you can also view them here in this table as well.", "line2": "This preview shows past alerts with a built-in delay so you can get a feel for the signal quality."}, "Next": "Next", "Previous": "Previous", "Page": "Page", "of": "of", "NoResultsFound": "No results found", "Header": {"Time": "Time", "TweetText": "News / Tweets", "Alert": "<PERSON><PERSON>", "Category": "Category", "Assets": "Assets", "Region": "Region", "Direction": "Direction", "Significance": "Significance", "AffectedAssets": "Affected Assets"}}}, "SubscribeFlow": {"Loading": "Loading...", "toast": {"DisclaimerAccepted": "Disclaimer accepted successfully!", "FailedToAccept": "Failed to accept disclaimer", "MeetingSuccessfully": "Meeting request submitted successfully!", "FailedToSave": "Failed to save meeting request"}}, "FilterAlerts": {"FilterTable": "Filter Table", "Reset": "Reset to default", "LoadingFilters": "Loading your preferences...", "Category": {"text": "Category", "description": "Select multiple categories were applicable", "Market": "Market", "Sector": "Sector", "Company": "Company"}, "Significance": {"text": "Significance", "description": "Select multiple options were applicable", "High": "High", "Medium": "Medium", "Low": "Low"}, "Assets": {"text": "Assets", "description": "Select one or more assets where applicable", "Equities": "Equities", "Bonds": "<PERSON><PERSON>", "FX": "FX", "Commodities": "Commodities", "Crypto": "Crypto", "Private": "Private"}, "Region": {"text": "Region", "description": "Select multiple options were applicable", "US": "US", "China": "China", "Japan": "Japan", "India": "India", "UK": "UK", "Germany": "Germany", "Saudi Arabia": "Saudi Arabia", "Canada": "Canada", "Australia": "Australia", "Southeast Asia": "Southeast Asia", "Latin America": "Latin America", "EU": "EU", "Global": "Global"}, "CheckBox": "Customize this filter for your telegram alerts", "Button": {"Cancel": "Cancel", "Filter": "Filter", "Saving": "Saving..."}, "toast": {"notLoadSavedFilters": "Could not load saved preferences", "FailedToSaveFilters": "Failed to save preferences"}}, "Modals": {"WelcomeModal": {"title": "Welcome to HermesX", "description": "We're glad to have you on board. Here, you'll get real-time alerts, helpful insights, and tools designed to keep you ahead. To get started, connect your Telegram account below - your experience just got a lot better, we're just a chat away.", "continue": "Continue to Dashboard"}, "TelegramSetupModal": {"title": "Link your Telegram", "description": "Please link your telegram to connect you to the HermesX Alert bot, to provide you with top alerts.", "button": "Set Up Profile", "success": "Successful!", "error": "Update failed"}, "TelegramConfirmationModal": {"title": "Your Telegram ID will be connected to the Hermes Bot", "description": "We will connect your already registered Telegram ID on the membership dashboard to the Hermes bot.\nIf you want to change your Telegram ID you can always do it in the membership dashboard settings.", "continue": "Continue"}, "SubscribeModal": {"title": "Subscribe to Receive the Latest <PERSON><PERSON>s", "description": "To keep you instantly updated, we'll send alerts directly to your connected Telegram ID. This subscription ensures you never miss an important notification. Select a subscription type and Click the \"Proceed\" button below to activate your Telegram alerts and stay in the loop.", "monthly": "Monthly", "monthlyCredits": "20 Credits", "annually": "Annually", "annualCredits": "240 Credits", "cancel": "Cancel", "proceed": "Proceed", "success": "Successful!", "error": "Subscription failed"}, "SubscriptionFlowModal": {"connectTelegram": {"title": "Connect your Telegram", "description": "Please link your telegram to connect you to the HermesX Alert bot, to provide you with top alerts.", "linkTelegram": "Link Telegram", "checkingStatus": "Checking status..."}, "subscribe": {"title": "Subscribe to Receive the Latest <PERSON><PERSON>s", "description": "To keep you instantly updated, we'll send alerts directly to your connected Telegram ID. This subscription ensures you never miss an important notification. Select a subscription type and Click the \"Proceed\" button below to activate your Telegram alerts and stay in the loop.", "monthly": "Monthly", "monthlyCredits": "20 Credits", "annually": "Annually", "annualCredits": "240 Credits", "cancel": "Cancel", "proceed": "Proceed"}, "preferences": {"title": "Telegram Preferences", "reset": "Reset to default", "loading": "Loading your preferences...", "category": {"text": "Category", "description": "Select multiple categories were applicable"}, "significance": {"text": "Significance", "description": "Select multiple options were applicable"}, "assets": {"text": "Assets", "description": "Select one or more assets where applicable"}, "region": {"text": "Region", "description": "Select multiple options were applicable"}, "cancel": "Cancel", "apply": "Apply Preferences", "saving": "Saving..."}}, "TelegramPreferencesModal": {"title": "Telegram Preferences", "reset": "Reset to default", "loading": "Loading your preferences...", "category": {"text": "Category", "description": "Select multiple categories were applicable"}, "significance": {"text": "Significance", "description": "Select multiple options were applicable"}, "assets": {"text": "Assets", "description": "Select one or more assets where applicable"}, "region": {"text": "Region", "description": "Select multiple options were applicable"}, "cancel": "Cancel", "apply": "Apply Preferences", "saving": "Saving..."}, "DisclaimerModal": {"title": "AI Model Disclaimer", "sections": {"noAdvice": {"title": "1. No Investment Advice", "text": "The content and outputs generated by our AI models are provided for general informational purposes only. They do not constitute investment advice, legal or any other form of professional advice. You should not rely solely on AI-generated content to make investment or business decisions."}, "limitations": {"title": "2. Limitations of AI-Generated Content", "text": "Our models produce results based on data inputs and algorithms that may be incomplete, inaccurate, or outdated. As a result:", "list": {"line1": "The analysis and recommendations may contain errors or biases", "line2": "The data used may be partial or contextually insufficient", "line3": "The content may not reflect current market conditions or individual circumstances"}, "footer": "We do not guarantee the accuracy, completeness, or reliability of any AI-generated information."}, "userResponsibility": {"title": "3. User Responsibility", "text": "Users are solely responsible for evaluating the risks and appropriateness of using [...]"}}, "cancel": "Cancel", "agree": "Agree and Continue"}, "ExpandTweetModal": {"close": "Close"}, "ExpandTruthsocialModal": {"title": "Truth Social Post"}}}, "DashboardOrion": {"Form": {"validationApiError": "Validation API error", "noJobIdReturned": "No job_id returned from API", "unexpectedError": "Unexpected error", "couldNotLoadModels": "Could not load models", "failedToFetchTelegramId": "Failed to fetch Telegram ID", "telegramIdSaved": "Telegram ID saved successfully", "failedToSaveTelegramId": "Failed to save Telegram ID", "errorSavingTelegramId": "Error saving Telegram ID", "errorInSaveTelegramIdFunction": "Error in saveTelegramId function: {error}", "pleaseSelectAtLeastOneTickerForUpload": "Please select tickers first", "pdfDocDocxTxtOnly": "PDF, DOCX, DOC, TXT with a maximum size of 10mb", "noFilesSelected": "No files selected", "errorExtractingTickers": "Error extracting tickers: {error}", "pleaseSelectTickersBeforeUploading": "Please select tickers first", "pleaseSelectTickersBeforeUploadingEarnings": "Please select tickers first", "pleaseSelectTickersBeforeUploadingOther": "Please select tickers first", "generateReportTitle": "Generate your report", "generateReportDescription": "Provide your tickers below and your report will be delivered to you in a bit.", "tickersLabel": "Tickers", "tickersSubLabel": "(U.S Markets Only)", "uploadTickers": "Upload Tickers", "selectTickersPlaceholder": "Select Tickers", "removeTicker": "Remove ticker", "uploadResearchReportsLabel": "Upload Research Reports", "optional": "(Optional)", "researchReportsTooltip": "We do not provide third-party research reports. Users are responsible for obtaining these reports independently and uploading them here. Please ensure that all reports are acquired in compliance with applicable regulations and proper protocols.", "viewUploads": "View Uploads", "uploadDocumentButton": "Click to upload your document", "uploadDocumentDescription": "PDF, DOCX, DOC, TXT with a maximum size of 10mb", "tickersNotFound": "{tickers} not found", "uploadEarningsLabel": "Upload Latest Earnings Releases", "earningsTooltip": "We will retrieve the latest filings from the SEC for you. However, some companies may publish their earnings results on their websites before filing with the SEC. In such cases, users can obtain the results directly from the company's site and upload them here.", "uploadOtherDocumentsLabel": "Upload Other Documents for Analysis", "otherDocumentsTooltip": "In addition to the default documents and information we provide, users may upload additional documents here for processing.", "otherDocumentsDescription": "PDFs, DOCX, DOC, TXT with a maximum size of 10mb", "deliveryOptionsLabel": "Delivery Options", "selectDeliveryPlaceholder": "Select a method to receive your report", "emailOption": "Email Address", "telegramOption": "Telegram", "emailLabel": "Email Address", "emailPlaceholder": "Enter your email address", "generatedReportsNote": "Generated reports will always be available in your account.", "secFilingCheckbox": "Send me the latest SEC filing and management presentation, if available.", "generateReportButton": "Generate Report", "generatingReportButton": "Generate Report...", "reportDelivered": "The report is delivered to your {method} !", "tickersFound": "Tickers Found", "tickersNotRecognized": "Tickers not recognized", "cancel": "Cancel", "finish": "Finish", "searchPlaceholder": "Search for tickers e.g., AAPL,META,NVDA", "searchButton": "Search", "searchError": "Search error", "tickerLimitExceeded": "Ticker Limit exceeded. Please reduce to 10 or fewer tickers.", "enterTickersInstruction": "Enter all the tickers you are searching for using comma or space to separate them", "pleaseSelectTickersFirst": "Please select tickers first", "pleaseSelectAtLeastOneTicker": "Please select at least one ticker", "pleaseSelectDownloadOption": "Please select a download option", "pleaseEnterEmail": "Please enter an email address", "pleaseEnterValidEmail": "Please enter a valid email address", "pleaseEnterTelegramId": "Please enter a Telegram ID", "checkingStatus": "Checking status...", "pleaseLinkTelegram": "Please link your telegram first", "generateReportConfirmTitle": "Generate Report", "generateReportConfirmDescription": "Are you sure you want to generate this report? This action will cost you.", "dontAskAgain": "Don't ask again"}, "UploadTickers": {"uploadTickersTitle": "Upload Tickers", "clickToUploadDocument": "Click to upload your document (.csv)", "tickerLimit": "Ticker Limit:", "selectedFile": "Selected file:", "validating": "Validating...", "limitExceeded": "Limit exceeded. Please reduce to 10 or fewer tickers.", "failedToReadCSV": "Failed to read CSV file", "noFileSelected": "No file selected", "notCSVFile": "is not a CSV file", "pleaseFixValidationErrors": "Please fix validation errors before uploading", "uploadSuccessful": "Upload successful", "uploadFailed": "Upload failed", "uploadButton": "Upload", "uploadingButton": "Uploading...", "useCSVTemplate": "Use CSV Template"}, "UploadSheet": {"uploadResearchReportsTitle": "Upload Research Reports", "uploading": "Uploading", "clickToUploadDocument": "Click to upload your document", "uploadDocumentDescription": "PDF, DOCX, DOC, TXT with a maximum size of 10mb", "uploadsSectionTitle": "Uploads", "selectedFile": "Selected file:", "cancel": "Cancel", "finish": "Finish", "searchForTickersPlaceholder": "Search for tickers", "noMatchingTickersFound": "No matching tickers found"}, "Tutorials": {"breadcrumbOrion": "Orion", "breadcrumbTutorials": "Tutorials", "watchTutorials": "Watch Tutorials", "getTheMostOutOfModels": "Get the most out of the models", "watchHowToUse": "Watch how to use and best scenario videos for the best output", "noVideosAvailable": "No videos available in this section"}, "TelegramModal": {"saveTelegramIdTitle": "Save Telegram ID", "saveTelegramIdDescription": "We noticed that you have added a new Telegram ID. Would you like to save this ID for further usage?", "replaceExistingIdWarning": "This would replace any existing ID already saved.", "noDontSave": "No, don't save", "yesSaveId": "Yes, Save Id", "saving": "Saving..."}, "ReportHistory": {"reportsHistoryTitle": "Reports History", "reportsHistoryDescription": "You can leave this page without interrupting the processing. Report will be delivered to you when ready", "loadingReports": "Loading reports...", "noReportsFound": "No reports found", "wantToKnow": "Want to know what the reports look like?", "reportSample": "Report Sample", "noTickers": "No tickers", "failed": "Failed", "showingRows": "Showing Rows", "previous": "Previous", "next": "Next", "share": "Share", "email": "Email", "telegramId": "Telegram ID", "download": "Download", "progressFailed": "Failed", "progressPercent": "{percent}%", "enterReceivingEmailTitle": "Enter Receiving Email Address", "enterEmailPlaceholder": "Enter your email address", "useRegisteredAddress": "Use Registered address", "cancel": "Cancel", "send": "Send", "enterReceivingTelegramTitle": "Enter Receiving Telegram ID", "enterTelegramPlaceholder": "Enter your telegram id", "pleaseEnterEmail": "Please enter an email address", "pleaseEnterValidEmail": "Please enter a valid email address", "pleaseEnterTelegramId": "Please enter a Telegram ID", "noJobIdForEmail": "No job ID available for email", "noJobIdForDownload": "No job ID available for download", "reportSentToEmail": "Report sent to email successfully", "reportSentToTelegram": "Report sent to Telegram successfully", "reportDownloaded": "Report downloaded successfully", "reportGenerationComplete": "Report generation complete!", "reportGenerationFailed": "Report generation failed"}, "Onboard": {"orionTutorialTitle": "Orion Tutorial", "welcomeToOrion": "Welcome to Orion", "welcomeDescription": "We're glad to have you on board. Generate reports for different companies using their tickers. Generate as many reports as you need and get the insights you require to make the right business decisions.", "watchHowToVideo": "Watch \"How To\" Video", "continueToDashboard": "Continue to Dashboard", "previousVideo": "Previous Video", "watchLater": "Watch Later", "continueDashboard": "Continue Dashboard", "nextVideo": "Next Video: {title}"}, "LoadingModal": {"loadingDialogTitle": "loading dialog", "generating": "Generating", "downloadingReport": "Downloading Report"}, "Header": {"orion": "Orion", "home": "Home", "reports": "Reports", "returnToAiWk": "Return to ai-wk", "aiWk": "ai-wk", "needHelp": "Need Help?", "tutorials": "Tutorials"}}, "DashboardYumiSandbox": {"InternalLogPanel": {"noLogs": "No internal logs found."}, "SandboxHomePage": {"needHelp": "Need Help? Talk to", "getStartedPrompt": "Get started with a simple prompt", "loading": "Loading...", "ticketCreated": "Ticket created successfully"}, "SandboxCallCenterPage": {"callCenterHeader": "Call Center", "callCenterDescription": "is capable of operating a fully autonomous AI-powered call center. Use the options below to simulate a conversation with an AI business specialist at the call center.", "presetUsersBelow": "Call using preset users below:", "callUsing": "Call using:", "InitiateCall": "Initiate Call", "callingAs": "Calling as {firstname} {lastname}", "retry": "Retry", "callEnded": "Call ended", "callDroppedByServer": "Call dropped by server", "callEndedWithError": "Call ended with error", "connecting": "Connecting...", "callPickedUp": "Call picked up", "wsError": "WebSocket error", "error": "Error"}, "SandboxHeader": {"returnToAiWk": "Return to ai-wk", "aiWk": "ai-wk", "needHelp": "Need Help?", "testUser": "Test User:"}, "SandboxSubscribeModal": {"title": "Yumi Sandbox", "description": "Start using <PERSON><PERSON> to support your customers as they engage with your services. For just 30 credits, you’ll get 3 days of full access to explore the sandbox and see <PERSON><PERSON> in action.", "thirtyCredits": "30 Credits", "forThreeDays": "For 3 Days", "termsOfUse": "Terms of Use", "disclaimer": "Disclaimer", "returnToAiWk": "Return to ai-wk", "agreeAndContinue": "Agree and Continue", "agreementPrefix": "I've read and agree to the", "and": "and"}, "SandboxSubscribeModalWrapper": {"success": "Successful!", "subscriptionFailed": "Subscription failed"}, "SupportPage": {"ticketDetailsPanel": {"fullName": "Full Name", "email": "Email", "issue": "Issue", "status": {"label": "Status", "resolved": "Resolved", "pending": "Pending"}, "priority": "Priority", "ticketId": "Ticket ID", "subject": "Subject", "description": "Description", "additionalInfo": "Additional Info", "attachments": "Attachments:", "emailAttachments": "Email Attachments:", "attachmentLabel": "Attachment {number}", "download": "Download", "downloadError": "Failed to download attachment.", "imageAlt": "image"}, "table": {"sn": "S/N", "date": "Date", "customer": "Customer", "email": "Email Address", "issueType": "Issue Type", "status": "Status", "priority": "Priority"}, "type": {"GI": "General Inquiry", "PSQ": "Product or Service Questions", "OTI": "Order or Transaction Issues", "BPQ": "Billing & Payment Questions", "AAH": "Account or Access Help", "TSE": "Technical Support or Errors", "FS": "Feedback or Suggestions", "PSC": "Private or Sensitive Concerns"}, "error": "Failed to load tickets.", "status": {"resolved": "Resolved", "pending": "Pending", "unknown": "Unknown"}, "priority": {"unknown": "Unknown"}, "header": "Internal Support", "tabs": {"all": "All Tickets", "pending": "Pending", "resolved": "Resolved"}, "summary": {"all": "All Tickets", "pending": "Pending Tickets", "resolved": "Resolved Tickets", "total": "Total number of tickets raised", "pendingTotal": "Total number of pending tickets", "resolvedTotal": "Total number of resolved tickets"}, "dateRange": {"select": "Select Date Range", "from": "From", "to": "To", "cancel": "Cancel", "apply": "Apply"}, "pagination": {"pageInfo": "Page {page} of {totalPages}", "rowsPerPage": "Rows per page:", "previous": "Previous", "next": "Next"}, "filterBar": {"searchPlaceholder": "Search name, email…", "period": "Period", "from": "From", "to": "To", "status": "Status", "priority": "Priority", "periodOptions": {"All Time": "All Time", "Today": "Today", "Last 7 Days": "Last 7 Days", "This Month": "This Month", "Custom…": "Custom…"}, "statusOptions": {"All": "All", "Pending": "Pending", "Resolved": "Resolved"}, "priorityOptions": {"All": "All"}, "dateRangePicker": {"chooseRange": "Choose date range", "rangeLabel": "{from} – {to}"}, "allTicketsTable": {"header": "All Tickets"}}, "transactionTable": {"table": {"dateTime": "Date & Time", "amount": "Amount", "type": "Type", "narration": "Narration"}}, "transactionHistoryTab": {"custom": {"from": "From", "to": "To"}, "error": "Could not load transactions.", "showingRows": "Showing {count} of {total} rows"}, "transactionFilterBar": {"period": "Period", "model": "Model", "periodOptions": {"All Time": "All Time", "Last 7 Days": "Last 7 Days", "This Month": "This Month", "Custom…": "Custom…"}, "modelOptions": {"All": "All", "Orion": "Orion", "Hermes": "<PERSON><PERSON>", "Olympus": "Olympus"}}, "ticketsContent": {"selectTicketPrompt": "Select a ticket to view details.", "error": {"couldNotLoadTicket": "Could not load ticket details."}, "tabs": {"details": "Ticket Details", "emailLog": "<PERSON><PERSON>", "internalLogs": "Internal Logs"}, "referIssue": "Refer Issue", "referDialog": {"title": "Refer Issue", "description": "Select who you want to refer this to and state your reason.", "referTo": "Refer to", "selectWho": "Select who to refer to", "failedToLoad": "Failed to load escalation members.", "reason": "Reason", "reasonPlaceholder": "Enter a reason for referring this issue", "referIssue": "Refer Issue", "confirmTitle": "Confirm Referral", "confirmDescription": "Are you sure you want to refer this issue to:", "to": "To:", "referring": "Referring...", "confirm": "Confirm"}, "roles": {"CustomerSupportLead": "Customer Support Lead", "OperationsTeam": "Operations Team", "TechnicalTeam": "Technical Team", "ProductTeam": "Product Team", "MarketingOrCommunications": "Marketing or Communications", "Leadership": "Leadership", "PeopleAndCulture": "People & Culture (HR)", "FinanceAndBilling": "Finance & Billing", "ComplianceOrLegal": "Compliance or Legal", "CEO": "CEO"}, "toast": {"ticketReferred": "Ticket referred successfully!", "failedToRefer": "Failed to refer ticket.", "ticketResolved": "Ticket resolved successfully!"}, "resolveDialog": {"resolved": "Resolved", "resolving": "Resolving...", "resolveTicket": "Resolve Ticket", "title": "Confirm Resolve", "description": "Are you sure this ticket has been resolved for the customer? This action cannot be undone.", "confirm": "Yes, Resolve"}}}, "SupportTicketDetailPage": {"notFound": "Customer info not found", "breadcrumb": {"internalSupport": "Internal Support"}}, "SetupPage": {"finishSetupNotice": "Finish setting up your concierge to access the sandbox.", "proactiveText": "You are a proactive, warm, and helpful concierge. You speak clearly and concisely, always aiming to guide users with confidence and kindness. You stay professional yet approachable, avoiding jargon and adapting to the user’s level. Your tone is calm, respectful, and attentive, making users feel supported and understood.", "conciergeSetup": "Concierge Setup", "knowledgeBaseTab": "Knowledge base", "customizationTab": "Customization", "editCustomization": "Edit Customization", "customizationTabSetup": "Set up your concierge details.", "customizationTabDescription": "Update your concierge settings below. All fields are optional.", "customizationUpdated": "Customization updated!", "uploadedFiles": "Uploaded Files", "uploadedDocuments": "Uploaded Documents", "updateCustomization": "Update Customization", "updating": "Updating...", "cancelEdit": "Cancel Edit", "saveAndSetup": "Save & Setup Concierge", "saving": "Saving...", "uploadInfo": "Upload your company information below and we'll set up your concierge based on the information", "organizationName": "Organization Name", "organizationNamePlaceholder": "Organization Q", "conciergeName": "Concierge Name", "conciergeNamePlaceholder": "<PERSON><PERSON>", "language": "Language", "languagePlaceholder": "Select Language", "personalityPrompt": "Personality Prompt", "personalityPromptPlaceholder": "<PERSON><PERSON> is polite", "cancel": "Cancel", "continue": "Continue", "successToast": "Concierge setup successful!", "customizationUpdatedToast": "Customization updated!", "failedToCreate": "Failed to create concierge", "failedToUpdate": "Failed to update customization", "alreadyCreated": "You have already created a sandbox environment. Please use your existing sandbox or contact support if you need to reset it.", "somethingWentWrong": "Something went wrong", "upload": "Drag and drop here or upload from your device", "uploadSub": "Supported files: DOC, DOCX, PDF. Max size: 5mb.", "noFileSelected": "No file selected", "changeCon": "Click to change concierge avatar", "table": {"documentName": "Document Name", "sn": "S/N", "fileName": "File Name", "size": "Size"}}, "TestChecklistPage": {"title": "Sandbox Testing Checklist", "checklist": {"chatFunctionality": {"title": "1. Chat Functionality", "goal": "Goal: Ensure {concierge} understands user input and responds using the knowledge base and priority settings.", "askGeneral": "Ask general questions that {concierge} should answer using her trained knowledge base", "testSimilarity": "Test out word/phrase similarity—think \"version\", \"help\", \"AI PM\", and variations"}, "ticketCreation": {"title": "2. Ticket Creation via Cha<PERSON>", "goal": "Goal: Test {concierge}'s ability to automatically log support tickets based on chat request.", "confirmTicket": "Confirm {concierge} raises the ticket for you with relevant details (issue type, priority, brief, summary, etc.)", "checkConfirmation": "Check for confirmation response from {concierge} (e.g., \"Your ticket has been submitted\")"}, "ticketCreationEmail": {"title": "3. Ticket Creation via Email", "goal": "Goal: Verify {concierge} handles incoming support emails and turns them into tickets.", "sendSupportRequest": "Use your ai-wk login email to send a support <NAME_EMAIL>", "observeReply": "Observe {concierge}'s automated reply confirming receipt and creation of a new ticket", "checkConfirmationEmail": "Check if ticket confirmation email is sent"}, "ticketCreationCall": {"title": "4. Ticket Creation via Call Centre", "goal": "Goal: Validate that {concierge} can generate support tickets based on voice call interactions.", "sendSupportRequest": "Initiate browser call by clicking 'Initiate Call' and describe an issue during the call ", "observeReply": "{concierge} should be able to transcribe the request and raise a ticket with key details", "checkConfirmationEmail": "Confirm receipt of ticket in the Internal Support UI"}, "internalSupportView": {"title": "5. Internal Support System View", "goal": "Goal: Review how logged issues appear to internal support teams.", "checkDashboard": "Check the internal support dashboard", "locateTickets": "Locate your submitted tickets (via email/chat)", "verifyMetadata": "Verify metadata: user, contact info, issue type, source (chat/email), timestamp, message logs", "checkAttachments": "Check whether attachments (if any) or logs are properly stored"}, "escalationReferral": {"title": "6. Escalation / Referral", "goal": "Goal: Test internal escalation and referral workflow.", "selectIssue": "Select a logged issue", "referInterface": "Use the interface to refer it to the backend, AI engineer, or management", "confirmAssignee": "Confirm the assignee and status update in the system"}, "markResolved": {"title": "7. Marking an Issue as Resolved", "goal": "Goal: Ensure resolution process is smooth and properly logged.", "markResolved": "Mark the test issue as Resolved", "confirmTimestamp": "Confirm resolution timestamp and final notes are recorded"}}}, "ProfileHeader": {"creditActions": "Credit Actions", "creditDialog": {"title": "Add/Subtract Credits", "description": "Enter an amount or use the buttons to adjust this customer's balance.", "currentBalance": "Current Balance", "amountLabel": "Amount to add or subtract", "increment": "+", "decrement": "−", "narration": "Narration", "saveChange": "Save Change"}, "confirmDialog": {"title": "Confirm Changes", "description": "Are you sure you want to {action} {amount} credits?", "add": "add", "subtract": "subtract", "confirm": "Yes, Confirm"}, "actions": {"cancel": "Cancel", "updating": "Updating..."}, "toast": {"suspendSuccess": "Customer suspended successfully!", "suspendError": "Failed to suspend customer.", "reactivateSuccess": "Account reactivated successfully!", "reactivateError": "Failed to reactivate account.", "resetSuccess": "Reset link sent successfully", "resetError": "Failed to send reset link", "creditsUpdated": "Credits updated!", "updateCreditsError": "Failed to update credits"}}, "CustomersTab": {"details": "User Details", "transactions": "Transaction History", "chat": "<PERSON><PERSON>", "tickets": "Tickets"}, "CustomerInfoCard": {"loading": "Loading customer info...", "notFound": "Customer info not found.", "fullName": "Full Name", "email": "Email", "availableCredits": "Available Credits", "ticketsRaised": "Tickets Raised", "accountStatus": "Account Status", "active": "Active"}, "ChatWindow": {"agentJoined": "Human Agent joined the conversation at {time}", "agentResolved": "Human Agent resolved the conversation at {time}"}, "ChatMessage": {"user": "User", "yumi": "<PERSON><PERSON>"}, "ChatLogTab": {"noSessions": "No chat sessions found", "noHistory": "This user has no chat history yet.", "selectSession": "Select a chat session", "chatTab": "<PERSON><PERSON>", "callTab": "Call Tab"}, "Sidebar": {"title": "Yumi-Sandbox", "setup": "Setup", "chatWithYumi": "Chat with", "internalSupport": "Internal Support", "testChecklist": "Test Checklist", "callCenter": "Call Center"}, "Layout": {"setup": "Setup", "chatWithYumi": "Chat with", "internalSupport": "Internal Support", "testChecklist": "Test Checklist", "callCenter": "Call Center", "sandbox": "Sandbox"}, "TicketForm": {"title": "Ticket Form", "fullName": "Full Name", "yourName": "Your name", "emailAddress": "Email Address", "emailPlaceholder": "<EMAIL>", "issueType": "Issue type", "selectIssueType": "Select an issue type", "priorityLevel": "Priority Level", "selectPriorityLevel": "Select a priority level", "subject": "Subject", "subjectPlaceholder": "Subject of ticket", "attachments": "Attachments", "addAttachment": "Add Attachment", "removeFile": "Remove file", "message": "Message", "describeYourIssue": "Describe your issue", "reset": "Reset", "confirmAndSend": "Confirm & Send Ticket", "submit": "Submit", "issueTypeList": {"GI": "General Inquiry", "PSQ": "Product or Service Questions", "OTI": "Order or Transaction Issues", "BPQ": "Billing & Payment Questions", "AAH": "Account or Access Help", "TSE": "Technical Support or Errors", "FS": "Feedback or Suggestions", "PSC": "Private or Sensitive Concerns"}}, "ChatBox": {"placeholder": "Type your message...", "addAttachment": "Add Attachment", "removeFile": "Remove file", "send": "Send"}}}