{"global": {"Login": "登录", "SignUp": "注册", "GetStartedNow": "立即开始", "ContactUs": "联系我们", "loading": "加载中...", "Loading": "加载中...", "Back": "返回", "Cancel": "取消", "Continue": "继续", "Proceed": "继续", "Finish": "完成", "Reset": "重置", "Send": "发送", "LearnMore": "了解更多", "ai-wk": "ai-wk", "errorMessage": {"title": "出了点问题！", "Tryagain": "请重试"}, "toast": {"SubmittedSuccessfully": "提交成功！", "SubmissionFailed": "提交失败", "LoginSuccessful": "登录成功！"}, "KeyFeatures": "核心功能"}, "HeaderBlack": {"Models": "模型", "OurModels": "我们的模型"}, "SiteFooter": {"nav": {"TermsofUse": "使用条款", "PrivacyPolicy": "隐私政策", "Disclaimer": "免责声明", "RefundPolicy": "退款政策"}, "copy": "版权"}, "ConnectTelegram": {"linkTelegram": "绑定Telegram", "checkingStatus": "正在检查状态...", "unlinkTelegram": "解除绑定Telegram", "unlinkSuccess": "成功解除绑定Telegram！", "unlinkFailed": "解除绑定Telegram 失败", "unlinkDialogTitle": "解除绑定Telegram", "unlinkDialogBody": "确定要解除绑定Telegram 账户吗？您可以随时通过“绑定Telegram”按钮重新绑定。", "cancel": "取消", "confirm": "确认", "unlinking": "正在解除绑定..."}, "Tutorials": {"Page": {"breadcrumbAiWk": "AI-WK", "breadcrumbTutorials": "使用教程", "mainTitle": "充分发挥模型效能", "mainSubtitle": "观看使用方法和最佳应用场景的视频，获取最佳输出效果", "noVideos": "本节暂无视频"}, "VideoLinks": {"categories": {"aboutAiWk": "关于 AI-wk", "orion": "Orion 模型", "hermesX": "Hermes X 模型", "hermesC": "Her<PERSON> C 模型", "otherModels": "其他模型"}, "videos": {"introductionToAiWk": "AI-wk 简介", "orionHowToUse": "如何使用Orion", "bestUseScenarioForOrion": "Orion最佳应用场景", "hermesXHowToUse": "如何使用Hermes X", "bestUseScenarioForHermesX": "Hermes X最佳应用场景", "hermesCHowToUse": "如何使用Hermes C", "bestUseScenarioForHermesC": "Hermes C 最佳应用场景"}, "durations": {"introductionToAiWk": "2:07", "orionHowToUse": "2:11", "bestUseScenarioForOrion": "1:50", "hermesXHowToUse": "1:47", "bestUseScenarioForHermesX": "1:52", "hermesCHowToUse": "1:45", "bestUseScenarioForHermesC": "1:13"}}}, "HeaderModels": {"returnToAiWk": "返回ai-wk", "aiWk": "ai-wk", "needHelp": "需要帮助？"}, "Form": {"fullname": {"text": "您的姓名", "required": "姓名为必填项"}, "email": {"text": "电子邮箱地址", "required": "邮箱为必填项", "error": "请输入有效的邮箱地址", "dataFormatError": "邮箱格式无效"}, "company": {"text": "公司/基金名称", "required": "公司名称为必填项"}, "website": {"text": "公司网址", "required": "网址为必填项"}, "business": {"text": "您的业务性质", "required": "请选择业务性质", "options": {"placeholder": "请选择...", "FinancialServices": "金融服务", "Technology": "科技", "Healthcare": "医疗健康", "Education": "教育", "Other": "其他"}}, "collaboration": {"text": "拟合作方向", "required": "请选择合作方向", "options": {"placeholder": "请选择...", "ProductIntegration": "产品整合", "Distribution": "渠道分销", "CoMarketing": "联合营销", "DataPartnership": "数据合作", "Other": "其他"}}, "message": {"text": "留言信息", "required": "留言不能为空"}, "industry": {"text": "所属行业", "required": "行业为必填项"}, "workflow": {"text": "请描述您的工作流程或使用场景", "required": "请描述您的工作流程或使用场景"}, "teamSize": {"text": "团队规模"}, "qualified": {"text": "您是合格投资者吗？", "required": "请说明您是否为合格投资者", "options": {"placeholder": "请选择...", "Yes": "是", "No": "否"}}, "investorType": {"text": "投资者类型", "required": "请选择您的投资者类型", "options": {"placeholder": "请选择...", "Angel": "天使投资人", "VentureCapital": "风险投资", "Corporate": "企业投资", "FamilyOffice": "家族办公室", "Other": "其他"}}, "synergies": {"text": "您能带来哪些战略协同？", "required": "请描述您能带来的战略协同"}, "role": {"text": "角色", "required": "请选择角色", "options": {"placeholder": "请选择角色", "AIEngineer": "AI工程师", "SalesConsultant": "销售顾问", "Other": "其他"}}, "coverLetter": {"text": "求职信", "required": "留言不能为空"}, "resume": {"text": "简历", "required": "简历为必填项", "fileTypeError": "仅支持上传 PDF 或 DOC/DOCX 文件"}, "organization": {"text": "所属组织/单位", "required": "组织名称为必填项"}, "inquiryType": {"text": "咨询类型", "required": "请选择咨询类型", "options": {"placeholder": "请选择...", "MediaInterview": "媒体采访", "SpeakingEngagement": "演讲活动", "GeneralInquiry": "一般咨询"}}, "password": {"text": "密码", "required": "此字段为必填项", "minError": "密码至少需要8个字符", "containNumberError": "必须包含数字", "containLowercaseLetter": "必须包含小写字母", "containUppercaseLetter": "必须包含大写字母", "containSpecialCharacter": "必须包含特殊字符"}, "FromDate": {"text": "开始日期"}, "ToDate": {"text": "结束日期"}, "Button": {"Sending": "发送中...", "SendMessage": "发送信息", "Logging": "正在登录...", "Login": "登录", "SigningUp": "注册中...", "SignUp": "注册", "Updating": "更新中...", "SetUpProfile": "设置个人资料", "Generating": "生成中...", "Generate": "生成", "SaveDetails": "保存详情", "EditDetails": "编辑详情", "Saving": "保存中...", "SaveChanges": "是的，保存更改", "ConfirmTicket": "确认并提交工单", "Submit": "提交", "Cancelling": "取消中...", "CancelSubscription": "取消订阅", "Submitting": "提交中...", "Processing": "处理中...", "AgreeAndContinue": "同意并继续", "Filter": "筛选", "SendRequest": "发送请求", "Uploading": "上传中...", "MadePayment": "我已完成付款"}, "Login": {"email": {"text": "邮箱"}}, "SignUp": {"firstName": {"text": "名", "required": "名为必填项"}, "lastName": {"text": "姓", "required": "姓为必填项"}, "email": {"text": "邮箱"}}}, "Table": {"Page": "第", "of": "共", "Previous": "上一页", "Next": "下一页", "Date": "日期", "Amount": "金额", "Type": "类型", "Narration": "备注"}, "DashboardNav": {"Home": "首页", "Credits": "代币", "Settings": "设置"}, "Home": {"title": "你好，世界！", "Header": {"models": {"title": "我们的模型", "Hermes": {"name": "<PERSON><PERSON>", "description": "市场监测"}, "Orion": {"name": "Orion", "description": "股票分析师"}, "Olympus": {"name": "Olympus", "description": "市场模拟"}, "Luca": {"name": "Luca", "description": "会计"}, "Freddie": {"name": "<PERSON>", "description": "招聘官"}, "Yumi": {"name": "<PERSON><PERSON>", "description": "客户服务"}, "CustomModels": {"name": "定制模型", "description": "为您的企业量身打造"}}, "nav": {"Models": "模型", "Pricing": "定价", "AboutUs": "关于我们", "ContactUs": "联系我们"}}, "HeroSection": {"subTitle": "探索我们的模型", "title": {"line1": "", "line2": "专为专业人士打造的", "line3": "AI工作模型"}, "description": "助力分析、执行与决策 — 全天候、高效率。"}, "OurAIModels": {"title": "即开即用的AI工作模型", "description": "由专家构建、可快速执行特定业务任务的模型", "tabs": {"Olympus": {"tabLabel": "市场模拟", "label": "Olympus", "title": "市场模拟器：在执行前预测趋势并测试策略"}, "MarketMonitor": {"tabLabel": "市场监测", "label": "<PERSON><PERSON>", "title": "市场监测器：全天候追踪全球金融市场，发现交易机会"}, "EquityAnalyst": {"tabLabel": "股票分析师", "label": "Orion", "title": "股票分析师：提供深入市场见解，辅助投资决策"}, "Accountant": {"tabLabel": "会计", "label": "Luca", "title": "会计：自动化账务处理与财务报告，确保准确性"}, "Recruiter": {"tabLabel": "招聘官", "label": "<PERSON>", "title": "招聘官：识别顶尖人才，简化招聘流程"}, "CustomerService": {"tabLabel": "客户服务", "label": "<PERSON><PERSON>", "title": "客户顾问：服务快捷、热情、专业"}}}, "AIBusiness": {"subTitle": "定制服务", "title": {"line1": "为您的企业", "line2": "量身定制的", "line3": "AI工作模型"}, "description": {"part1": "每家企业都是独特的 —— ", "part2": "我们为您定制AI工作模型，", "part3": "满足您的业务需求。"}, "LearnMore": "了解更多"}, "StatisticsSection": {"subTitle": "数据统计", "hermescChartText": "以下图表展示了在Hermes C 实时信号驱动下，固定本金投资所带来的资本增长。让我们一起探索主动阿尔法区间！", "legendCharts": {"rawStrategyReturn": "原始策略回报", "passiveReturnToClose": "持仓至收盘回报", "activeReturnToMax": "主动最大回报", "activeAlphaZone": "主动阿尔法区间", "activeAlphaZoneTooltip": "该区域代表被动持仓与最大回报之间的利润空间。体现了熟练交易者通过时机把握、技巧与判断力可以实现的交易利润。"}, "title": {"line1": "我们的模型助您", "line2": "成效卓著"}, "models": {"Orion": {"name": "Orion", "leftMetric": {"title": "节省时间。", "description": "Orion每只股票生成报告仅需2分钟，而手动需4小时，每只股票节省近一天工时。"}, "rightMetric": {"title": "节省成本。", "description": "一个年薪8万美元的初级分析师每天分析2只股票，单报告成本约170美元。而Orion仅需10美元，每年分析500只股票可将成本从6万美元降至5000美元，年节省约5.5万美元。"}}, "HermesX": {"name": "HermesX", "leftMetric": {"title": "Hermes X实现全天候市场监测。", "value": "每周168小时", "description": "无需轮班监控团队，自动扫描新闻、过滤噪音、数秒内发送预警，每周生成数百条洞察，轻松高效。"}, "rightMetric": {"title": "成本大幅降低。", "description": "替代年薪18万美元的人工监测团队，Hermes X每位用户每年仅120美元，年节省超17.9万美元，且无损速度与准确性。"}}, "Olympus": {"name": "Olympus", "leftMetric": {"title": "节省时间。", "description": "Olympus将10人团队耗时4小时的手动模拟压缩为1小时自动流程，节省40小时人力。"}, "rightMetric": {"title": "成本更低。", "description": "一次完整模拟仅需100美元，相比人工团队需1200美元，每次节省超1100美元，且不影响洞察力或速度。"}}}}, "AIWorkModelsClosingHero": {"title1": "尝试AI工作模型", "title2": "为您的企业赋能"}}, "Pricing": {"CreditsPower": {"subTitle": "定价", "title": {"line1": "代币购买", "line2": "ai-wk平台上的所有模型"}, "Credit": "代币", "currencyMetadata": {"USD": "美元", "EUR": "欧元", "GBP": "英镑", "SGD": "新元", "AUD": "澳元", "CAD": "加元", "JPY": "日元", "CNY": "人民币", "HKD": "港币"}, "list": {"item1": {"title": "价格", "description": "1 代币 = 1 美元 / USDC / USDT"}, "item2": {"title": "简单", "description": "使用代币访问所有AI模型"}, "item3": {"title": "无隐藏费用：", "description": "无隐藏费用或汇率波动"}}}, "EarnMore": {"title": "成长越快，赚得越多", "description": {"part1": "充值越多，奖励越高。", "part2": "奖励代币适用于每个等级内的增量金额。"}, "tiers": {"header": {"Tier": "等级", "TopUp": "充值额（12个月累计）", "BonusRate": "奖励比例"}, "prompt": "奖励仅适用于每个等级内的新增充值部分", "Basic": {"name": "基础", "range": "<$1000", "bonusRate": "0%"}, "Pro": {"name": "专业", "range": "$1000 - $4,999", "bonusRate": "10%"}, "VIP": {"name": "VIP", "range": "$5,000 - $9,999", "bonusRate": "20%"}, "SVIP": {"name": "SVIP", "range": "$10,000+", "bonusRate": "30%"}}}, "CreditsCharge": {"title": "各模型代币定价标准", "creditModels": {"Orion": {"name": "Orion", "description": "股票分析师", "charges": {"option1": "10 代币 / 股票"}}, "HermesX": {"name": "<PERSON><PERSON>", "description": "市场监测", "charges": {"option1": "20 代币 / 月"}}, "Freddie": {"name": "<PERSON>", "description": "招聘官", "charges": {"option1": "20 代币 / 50应征者"}}, "Luca": {"name": "Luca", "description": "会计", "charges": {"option1": "30 代币 / 月 / 公司（基础版）", "option2": "60 代币 / 月 / 公司（高级版）"}}, "Olympus": {"name": "Olympus", "description": "市场模拟器", "charges": {"option1": "100 代币 / 每次模拟"}}, "HermesC": {"name": "<PERSON><PERSON>", "description": "交易员", "charges": {"option1": "1000 代币 / 月"}}}}, "FAQs": {"title": "常见问题解答", "faqs": {"q1": {"question": "我如何查看当前会员代币等级？", "answer": {"paragraph1": "在会员控制面板中，可查看详细的充值与使用记录"}}, "q2": {"question": "代币会过期吗？", "answer": {"paragraph1": "代币有效期为5年，确保有充足时间使用"}}, "q3": {"question": "未使用的代币可以退款吗？", "answer": {"label": "一般来说不可以，但代币可用于平台上的任意服务。详情请参阅我们的 ", "RefundPolicy": "退款政策"}}, "q4": {"question": "支持哪些支付方式？", "answer": {"paragraph1": "支持信用卡、二维码支付、稳定币和银行电汇等多种支付方式。"}}, "q5": {"question": "定制服务如何计费？", "answer": {"paragraph1": "在定制服务板块填写需求详情，我们团队会联系您确认方案与报价。"}}}}}, "AboutUs": {"OurVision": {"subTitle": "我们的愿景", "title": "混合工作模式是未来趋势", "content": {"paragraph1": "在 ai-wk.com，我们相信工作的未来是人类创造力与AI精准速度的结合。我们正在打造一个AI队友无缝融入人类团队的世界 —— 构建更快、更聪明、更具规模的混合团队。", "paragraph2": "我们的使命是赋能企业和个人在这场转型中获益。通过教育、培训和AI工作模型的使用，我们帮助人们适应、成长，并在新时代中成为高效生产力的引领者。"}}, "InvestmentBankerSection": {"title": "投行思维推动AI创新", "description1": "由前高盛投行家", "description2": "创立，ai-wk.com将华尔街的专业精神带入AI时代。", "name": "<PERSON>", "list": {"item1": {"title": "大局思维：", "description": "我们的AI队友服务于战略思维、完整流程，而非单个任务。"}, "item2": {"title": "严谨精准：", "description": "每个模型都经过结构化设计、持续优化，追求极致细节。"}, "item3": {"title": "执行力强：", "description": "从金融建模到技术落地，始终坚持质量与一致性。"}, "item4": {"title": "信任与承诺：", "description": "我们以长期视角、专业专注与诚信运营。"}}}, "OurCulture": {"title": "我们的文化", "description": "现代、全球化和结果导向", "cultureCards": {"alt": "AI应用公司", "TechForward": {"title": "科技驱动的设计", "boldText": "科技驱动的设计", "normalText": "在每一层级上追求效率"}, "ClientCentric": {"title": "", "boldText": "以客户为中心", "normalText": "您的目标决定我们的产品路线"}, "PrivacyFirst": {"title": "", "boldText": "注重隐私", "normalText": "我们负责任地使用AI，尊重您的数据"}, "Multicultural": {"title": "多元文化与全球分布", "boldText": "多元文化与全球分布", "normalText": "跨时区跨学科协同工作"}, "CloudNative": {"title": "云原生、敏捷、高效", "boldText": "云原生、敏捷、高效", "normalText": "为速度与韧性而生"}}}, "PurposeBuilt": {"subTitle": "为使命而生", "title": "Alpharithm Investments倾力打造", "description": {"text1": "ai-wk.com由", "name": "Alpharithm Investments", "text2": "开发，以展示AI队友的无限潜力。每一个AI工作模型都按专业标准打造，持续训练，严格测试 —— 从生成交易信号到撰写研究报告，皆能胜任。欢迎您体验AI驱动的未来办公模式。我们也期待与企业和专业人士合作，共同提升工作效率，实现AI深度集成。"}}}, "ContactUs": {"ContactUsSection": {"subTitle": "联系我们", "title": "我们期待您的来信！", "description": "请选择最符合您咨询目的的类别。所有消息将通过我们的内部系统发送至相关团队，我们会尽快回复。如需进一步协助，您也可以通过以下方式联系我们："}, "Tabs": {"CustomModels": {"value": "custom-models", "label": "定制模型", "pane": {"title": "欢迎希望与我们合作共同开发专属AI模型的企业与专业人士，适配您的工作流程、行业需求或内部系统。", "description": "我们与精选客户共同打造AI队友，自动化并优化专业流程 —— 涵盖金融分析、客户服务、内部报告、合规管理等。", "form": {"description": "请告诉我们您的业务需求、痛点，以及您期望AI如何帮助提升效率。我们将安排后续沟通。", "problemText": "您希望通过AI解决什么问题？", "toolsText": "目前使用的工具或系统"}}}, "Support": {"value": "support", "label": "用户支持", "pane": {"title": "请登录会员控制面板，通过平台内支持表单提交咨询，以确保身份验证与跟进记录。", "description": "如需账户、支付或AI模型相关帮助请登入与Yumi客服沟通。"}}, "Career": {"value": "career", "label": "加入我们", "pane": {"title": "我们欢迎热衷于打造未来工作方式的优秀人才。当前我们正在招聘AI工程师与销售顾问团队。", "description": "我们持续在以下领域招聘：", "areas": {"Engineers": {"title": "AI / 软件工程师", "description": "（技术主管、高级工程师、初级工程师）"}, "Sales": {"title": "销售代表", "description": "（涵盖多个行业与区域市场）"}}}}, "Investments": {"value": "investment", "label": "投资合作", "pane": {"title": {"paragraph1": "我们每年筹集资金以扩大业务，目前正筹备天使轮融资，欢迎符合资格的投资者联系。", "paragraph2": "具备战略协同价值的投资人（如产品、渠道、基础设施方面）将被优先考虑。"}, "form": {"description": "请通过下方表单提交您的信息。我们将有选择地安排由CEO亲自洽谈的会议。"}}}, "Partnership": {"value": "partnership", "label": "合作伙伴", "pane": {"title": "我们欢迎来自以下领域的战略合作：", "description": "如果您正在探索与我们共赢的机会，欢迎联系我们。", "list": {"item1": "金融平台与券商", "item2": "SaaS与数据集成工具", "item3": "渠道合作伙伴或B2B销售方"}, "form": {"description": "请介绍您的公司以及合作设想：", "valueText": "您期望在本次合作中实现的价值"}}}, "Media": {"value": "media", "label": "媒体与其他咨询", "pane": {"title": "如果您的消息不属于上述类别，请选择此项。我们欢迎有深度的问题，并会转交至相应团队。", "form": {"description": "适用于采访邀请、演讲邀约或一般联系。"}}}}}, "UserLogin": {"title": "欢迎回来", "description": "让我们从您上次中断的地方继续", "ForgotPassword": "忘记密码？", "OR": "或", "LogWithGoogle": "使用 Google 登录", "DoHaveAccount": "还没有账户？", "SignUpHere": "点击这里注册"}, "SignUp": {"title": "注册并创建账户", "description": "填写下方表单即可开始，或选择其他注册方式。", "OR": "或", "SignupWithGoogle": "使用 Google 注册", "AlreadyHaveAccount": "已有账户？", "LoginHere": "点击这里登录"}, "Hermes": {"LucaLandingHeroSection": {"title": "Hermes：<br />全天候捕捉交易机会", "description": "Hermes是受希腊贸易之神Hermes启发的AI模型系列，专注监测交易催化事件并生成可操作交易信号。每个模型均有独立用途。目前两个模型可提供订阅："}, "LucaGitHub": {"title": {"text1": "Hermes X 全天扫描全球新闻，", "text2": "“传递影响市场的关键新闻”。", "text3": "帮助交易员领先掌握关键市场驱动信息，拒绝冗余干扰。"}}, "LucaCTA": {"title": "领先掌握市场动向！"}, "HowLucaWorks": {"title": {"line1": "Hermes 是如何", "line2": "工作的……"}, "steps": {"step1": {"title": "订阅 Hermes", "description": "选择月付或年付计划。"}, "step2": {"title": "AI 监测市场事件", "description": "全天候扫描全球新闻动态。"}, "step3": {"title": "过滤后即时推送", "description": "只有高影响事件会推送至 Telegram"}, "step4": {"title": "交易员查看并执行", "description": "借助AI洞察做出交易决策"}}, "ImportantNote": "重要提示：Hermes 旨在辅助决策，不代替人类判断。交易前请验证消息来源。"}, "LucaComparison": {"Hermes": "<PERSON><PERSON>", "vs": "对比", "Traditional": "传统新闻推送", "HumanMonitoring": "人工监测", "leftComparisons": {"comparison1": {"title": "减少噪音", "description": "—— 仅推送真正重要的事件"}, "comparison2": {"title": "AI驱动洞察", "description": "—— 不止标题，更解释交易影响"}, "comparison3": {"title": "多来源验证", "description": "—— 汇聚权威媒体与可信消息源"}}, "rightComparisons": {"comparison1": {"title": "全天候覆盖", "description": "—— 关键事件不再错过，即使离线"}, "comparison2": {"title": "更低成本", "description": "—— 用一个AI助手替代分析团队"}, "comparison3": {"title": "专注更聚焦", "description": "—— 让交易员远离无效干扰"}}}, "LucaKeyFeatures": {"title": "Hermes X：AI驱动的市场新闻监控", "description": {"line1": "Hermes X 实时扫描分析宏观与公司新闻，", "line2": "探测市场催化事件。过滤冗余噪音，仅推送高影响动态。"}, "features": {"feature1": {"title": "实时市场监控", "description": "全天候运行，捕捉第一时间新闻"}, "feature2": {"title": "AI过滤算法", "description": "聚焦市场催化事件，去除无关信息"}, "feature3": {"title": "多智能体协作", "description": "多个AI队友协同判断新闻的影响"}, "feature4": {"title": "即时推送", "description": "将预警推送至用户Telegram账户"}}}, "LucaFAQ": {"title": "<div class=\"md:w-[2em] sm:w-auto\">常见问题</div>", "faqs": {"q1": {"question": "Hermes X 的定价方案是什么？", "answer": {"line1": "• 月付方案 – 每月 20 代币", "line2": "• API 接口 – 每月 100 代币", "line3": "订阅与Telegram账号绑定"}}, "q2": {"question": "Hermes C 的定价方案是什么？", "answer": {"line1": "• 标准订阅 – 每月 1000 代币", "line2": "• API 接口 – 每月 2000 代币"}}, "q3": {"question": "Hermes 可以定制吗？", "answer": {"line1": "可以根据您的需求定制模型与输出。我们的AI工程师可以为您的流程开发专属模型。想定制？联系我们。", "ContactUs": "立即联系"}}}}, "LucaKeyFeatures2": {"title": "Hermes C：公司催化事件监测器", "description": "Hermes C 专注跟踪公司公告、新闻、信披等催化信息。通过多因子框架（基本面、技术面、事件线、财务、情绪）生成交易建议，快速对可交易事件进行结构化分析。", "features": {"feature1": {"title": "实时公司监控", "description": "监控公司新闻，识别价格驱动事件"}, "feature2": {"title": "AI驱动推荐", "description": "使用专有算法，推送高信心交易机会"}, "feature3": {"title": "多策略支持", "description": "支持主观与程序化交易策略"}, "feature4": {"title": "结果可解释", "description": "提供历史依据与推理逻辑，方便验证"}}}, "HermesXLiteSection": {"title": "Hermes X Lite 免费开放", "description": "关注我们的X账号，即可免费实时接收重要市场消息更新。我们致力于第一时间推送关键资讯，助您决策领先一步。", "ClickToFollow": "点击关注"}}, "Orion": {"LucaLandingHeroSection": {"title": "Orion：<br />专业级股票分析师", "description": "Orion 是一款专业级的AI股票分析师，结合财报、市场数据、分析师研究、电话会议纪要和新闻催化，提供深度基本面、技术面和情绪分析。"}, "LucaGitHub": {"title": {"text1": "结果？", "text2": "一份高质量投资报告，", "text3": "覆盖基本面、技术面与情绪评分，帮助投资者做出自信决策。"}}, "LucaCTA": {"title": {"text1": "几分钟内获取", "text2": "专业级", "text3": "投资洞察！"}}, "HowLucaWorks": {"title": {"line1": "Orion 如何", "line2": "运作……"}, "description": "用户需上传自有第三方研究报告，平台不能提供版权资料。", "steps": {"step1": {"title": "输入股票代码", "description": "输入公司代码或上传列表"}, "step2": {"title": "AI 分析引擎", "description": "Orion 扫描并处理高质量数据源"}, "step3": {"title": "生成专业报告", "description": "生成结构化投资报告，包含关键评分与洞察"}, "step4": {"title": "即时送达", "description": "报告将通过邮箱或 Telegram 发送"}}}, "LucaComparison": {"Orion": "Orion", "vs": "对比", "StockInformation": "股票信息网站", "HumanAnalysts": "人类分析师", "leftComparisons": {"comparison1": {"title": "更高质量数据", "description": "—— 使用专业报告，而非公开或用户生成内容"}, "comparison2": {"title": "全面分析", "description": "—— 覆盖财务、运营与市场因素"}, "comparison3": {"title": "AI专业协作", "description": "—— 多个专业AI模型协同分析，实现最大精度"}}, "rightComparisons": {"comparison1": {"title": "更快输出", "description": "—— 几分钟完成深度研究，而非数小时"}, "comparison2": {"title": "更低成本", "description": "—— 成本仅为聘请分析师的一小部分"}, "comparison3": {"title": "无偏见与一致性", "description": "—— 纯数据驱动，无主观意见"}, "comparison4": {"title": "提升决策质量", "description": "—— 结构化研究辅助人类判断"}}}, "LucaSmartAccounting": {"title": {"line1": "Orion", "line2": "适合谁使用？"}, "features": {"Professional": {"title": "专业投资者", "description": "寻求快速、客观的投资评估"}, "HedgeFunds": {"title": "对冲基金与资产管理人", "description": "开展深度研究"}, "Investment": {"title": "投资公司", "description": "希望增强或替代内部研究团队"}, "Analysts": {"title": "分析师与交易员", "description": "需要快速全面审视候选股票"}}}, "LucaKeyFeatures": {"features": {"feature1": {"title": "聚合高质量数据", "description": "包含财报、研报、电话会议、市场趋势等"}, "feature2": {"title": "多因子评分系统", "description": "涵盖基本面、技术面、估值、情绪、催化与总评分"}, "feature3": {"title": "AI协作团队", "description": "非单一模型，而是多个专业AI队员组成的协作系统"}, "feature4": {"title": "报告可定制", "description": "根据投资需求调整洞察重点和参数"}}}, "LucaFAQ": {"title": "<div class=\"md:w-[2em] sm:w-auto\">常见问题</div>", "faqs": {"q1": {"question": "Orion 的定价方案？", "answer": {"line1": "• 每只股票分析 $10"}}, "q2": {"question": "可以定制 Orion 吗？", "answer": {"line1": "需要定制功能？欢迎联系我们，根据业务需求定制 Orion。", "ContactUs": "联系我们"}}}}}, "Olympus": {"LucaLandingHeroSection": {"title": "Olympus：<br />AI驱动的市场模拟器", "description": "Olympus 是一款市场模拟模型，用于分析股票市场交易情境。该模型由多个AI代理扮演不同市场参与者角色，如对冲基金、长期基金、散户、公司及意见领袖。"}, "LucaGitHub": {"title": {"text1": "智能代理实时互动，并对市场动态产生反应、做出投资决策。", "text2": "最终生成一份全面的“上帝视角报告”，", "text3": "详细刨析代理的操作、收益表现与逻辑依据。"}}, "LucaCTA": {"title": {"text1": "立即体验", "text2": "AI驱动的", "text3": "市场模拟！"}}, "HowLucaWorks": {"title": {"line1": "Olympus 如何", "line2": "运作……"}, "steps": {"step1": {"title": "选择市场条件", "description": "设定股票、时段及市场参与者"}, "step2": {"title": "设定AI策略", "description": "配置角色性格、投资风格与目标"}, "step3": {"title": "运行模拟", "description": "观察各参与者实时互动"}, "step4": {"title": "获取详细报告", "description": "生成完整的“上帝视角”报告，涵盖行为、收益、评估及成功关键点。可根据需求定制关注重点。"}}}, "LucaComparison": {"Olympus": "Olympus", "vs": "对比", "Humans": "人工模拟", "comparisons": {"comparison1": {"title": "成本更低", "description": "—— AI模拟远低于人工模拟成本"}, "comparison2": {"title": "执行更快", "description": "—— 模拟运行仅需约一个小时，而非几周"}, "comparison3": {"title": "精度更高", "description": "—— AI严格执行策略与目标"}, "comparison4": {"title": "可控人类偏见", "description": "—— 可根据设定控制AI的人格与偏好"}}}, "LucaSmartAccounting": {"title": {"line1": "Olympus", "line2": "适合谁使用？"}, "features": {"Traders": {"title": "交易员与投资者", "description": "希望深入理解市场行为和交易策略有效性"}, "Hedge": {"title": "对冲基金与资产管理人", "description": "测试特定市场事件的反应"}, "Academics": {"title": "学术机构与研究人员", "description": "研究市场动态与投资者心理"}, "Companies": {"title": "企业与政策制定者", "description": "分析法规或公司行为的潜在反应"}}}, "LucaKeyFeatures": {"features": {"feature1": {"title": "真实的市场模拟", "description": "AI代理仿真真实投资者行为"}, "feature2": {"title": "全面的市场报告", "description": "洞察策略、表现与决策路径"}, "feature3": {"title": "可定制模拟情境", "description": "根据特定需求定制模拟参数"}, "feature4": {"title": "快速、客观、低成本", "description": "比人工模拟更高效、更精确"}}}, "OlympusFAQ": {"title": "<div class=\"md:w-[2em] sm:w-auto\">常见问题</div>", "faqs": {"q1": {"question": "Olympus 的定价方案？", "answer": "• 每次模拟 100 积分"}, "q2": {"question": "Olympus 可以定制吗？", "answer": {"line1": "需要定制功能？联系我们以满足您的业务需求。", "ContactUs": "联系我们"}}}}}, "Luca": {"LucaLandingHeroSection": {"title": "Luca：<br />轻松记账与财务报表的AI会计", "description": "Luca 是一款由AI驱动的会计助手，旨在简化记账、财务记录和报表生成。"}, "LucaGitHub": {"title": {"text1": "Luca 可将类似", "text2": "“我从平安银行向阿里云服务支付了30元费用”", "text3": "这样的信息转化为结构化财务记录，并实时更新报表，附带发票与收据。"}}, "LucaCTA": {"title": {"text1": "让您的", "text2": "财务管理更智能，", "text3": "从现在开始！"}}, "HowLucaWorks": {"title": {"line1": "<PERSON> 如何", "line2": "运作……"}, "steps": {"step1": {"title": "输入财务交易", "description": "通过文字或手动输入交易信息。"}, "step2": {"title": "自动生成账目", "description": "Luca 自动分类并记录交易。"}, "step3": {"title": "实时财务报表", "description": "资产负债表、利润表与现金流报表实时更新。"}, "step4": {"title": "附加支持文件", "description": "上传发票与收据以便查阅。"}}}, "LucaComparison": {"Luca": "Luca", "vs": "对比", "Humans": "人工会计", "TraditionalSoftware": "传统软件", "leftComparisons": {"comparison1": {"title": "成本更低", "description": "—— 远低于全职或兼职会计的费用。"}, "comparison2": {"title": "处理更快", "description": "—— 交易即时录入与更新。"}, "comparison3": {"title": "全天候可用", "description": "—— 无需排班，<PERSON> 随时在线。"}, "comparison4": {"title": "一致性强，误差少", "description": "—— 降低人为错误，提升准确性。"}}, "rightComparisons": {"comparison1": {"title": "对话式操作直观易用", "description": "—— 使用文字指令代替繁琐输入。"}, "comparison2": {"title": "集成文档管理", "description": "—— 自动关联发票与收据。"}, "comparison3": {"title": "实时更新", "description": "—— 消除报表延迟。"}}}, "LucaSmartAccounting": {"title": {"line1": "智能会计，", "line2": "人人可用"}, "features": {"Small": {"title": "小型企业主", "description": "无需聘请会计也能轻松管理财务。"}, "Freelancers": {"title": "自由职业者与个体户", "description": "实现财务实时跟踪。"}, "Professional": {"title": "专业会计师", "description": "提升工作效率，同时管理多个客户。"}, "Companies": {"title": "公司企业", "description": "需按需生成合规的报表用于税务、审计与财务规划。"}}}, "LucaKeyFeatures": {"features": {"feature1": {"title": "自动化记账", "description": "将文字输入转为结构化账目记录。"}, "feature2": {"title": "实时财务报告", "description": "记录实时更新，自动生成报表。"}, "feature3": {"title": "灵活输入方式", "description": "支持手动输入，适合希望手动控制的用户。"}, "feature4": {"title": "文档与收据管理", "description": "上传发票、收据与辅助文件。"}}}, "LucaFAQ": {"title": "<div class=\"md:w-[2em] sm:w-auto\">常见问题</div>", "faqs": {"q1": {"question": "Luca 的定价方案？", "answer": {"line1": "• 标准方案 – 每月 30 代币：访问 Luca 的AI记账与报表功能（不含文件存储）。", "line2": "• 高级方案 – 每月 60 代币：包含文档存储功能。"}}, "q2": {"question": "Luca 可以定制吗？", "answer": {"line1": "• 需要定制功能？欢迎联系我们根据业务需求打造专属 Luca。", "ContactUs": "联系我们"}}}}}, "Freddie": {"LucaLandingHeroSection": {"title": "Freddie：<br />端到端的AI招聘助手", "description": "Freddie 是一款专业级AI招聘官，可处理从岗位发布到新员工入职的全流程招聘任务。它协助人才筛选、评估与沟通，提供一站式招聘解决方案。"}, "LucaGitHub": {"title": {"text1": "Freddie 精简整个", "text2": "招聘流程，", "text3": "从岗位发布到入职，让团队专注高效招聘，而不是被筛选、测试与沟通环节拖慢进度。"}}, "LucaCTA": {"title": {"text1": "立即开始高效、", "text2": "智能招聘"}}, "HowLucaWorks": {"title": {"line1": "<PERSON> 如何", "line2": "运作……"}, "steps": {"step1": {"title": "发布岗位至首选平台", "description": "Freddie 为 LinkedIn、Indeed 等平台撰写优化后的岗位描述。"}, "step2": {"title": "自动筛选简历", "description": "根据经验、技能和匹配度自动筛选候选人。"}, "step3": {"title": "收集候选人偏好", "description": "收集薪酬预期、到岗时间、可用性与请假情况。"}, "step4": {"title": "智能测试与评分", "description": "发送测试题，对候选人打分并按适配度排名。"}, "step5": {"title": "比对与筛选", "description": "查看候选人评分摘要，自动标注风险点。"}, "step6": {"title": "沟通与录用", "description": "安排面试、发送Offer，并完成入职流程。"}, "step7": {"title": "背景验证", "description": "Freddie 验证学历、工作经历与身份信息。"}}}, "LucaComparison": {"Freddie": "<PERSON>", "vs": "对比", "Hiring": "招聘网站与平台", "HumanHiring": "人力招聘经理", "leftComparisons": {"comparison1": {"title": "完整流程管理", "description": "—— 管理整个招聘周期，而非仅发布岗位。"}, "comparison2": {"title": "更智能的筛选", "description": "—— 不止看简历，还支持个性化测试与过滤。"}, "comparison3": {"title": "个性化沟通", "description": "—— 与候选人保持一致、专业的互动体验。"}}, "rightComparisons": {"comparison1": {"title": "流程更快", "description": "—— 快速完成候选人筛选、评估与沟通。"}, "comparison2": {"title": "成本更低", "description": "—— 支持完整招聘流程，费用远低于人力HR。"}, "comparison3": {"title": "永不离线", "description": "—— 7x24全天候工作，无瓶颈。"}}}, "LucaSmartAccounting": {"title": {"line1": "谁适合使用", "line2": "<PERSON>？"}, "features": {"Small": {"title": "小型企业主", "description": "团队小于20人，无专职HR，也能高效招聘。"}, "Growing": {"title": "成长型公司与初创企业", "description": "通过Freddie自动完成人才获取、筛选与面试排程，无需扩大HR团队。"}, "HRManagers": {"title": "人力资源经理", "description": "通过自动化简历筛选、岗位发布与初筛，节省宝贵时间。"}, "Professional": {"title": "专业招聘顾问", "description": "提升候选人处理效率，Freddie 支持智能过滤与全时筛选。"}}}, "LucaKeyFeatures": {"features": {"feature1": {"title": "岗位发布生成", "description": "自动撰写高质量、岗位匹配的招聘文案。"}, "feature2": {"title": "候选人筛选", "description": "通过问卷与信息采集自动预筛。"}, "feature3": {"title": "技能测试与评估", "description": "涵盖多个维度的测试与打分。"}, "feature4": {"title": "决策支持系统", "description": "提供结构化比较、图表、评分与对比分析。"}}}, "LucaFAQ": {"title": "<div class=\"md:w-[2em] sm:w-auto\">常见问题</div>", "faqs": {"q1": {"question": "Freddie 的定价方案？", "answer": {"line1": "• 前20应聘者的评估免费，之后20 代币可处理额外50应聘者", "line2": "包含从候选人获取到入职的完整支持。"}}, "q2": {"question": "Freddie 可以定制吗？", "answer": {"line1": "Freddie 可根据公司品牌、文档与流程定制。如需个性化版本，欢迎联系我们定制专属解决方案。", "ContactUs": "联系我们"}}}}}, "Yumi": {"LucaLandingHeroSection": {"title": "Yu<PERSON>：<br />全天候AI客户服务专员", "description": "Yumi 是一款由AI驱动的客户服务专家，具备快速响应、智能理解与贴心服务能力，既是用户的第一接触点，也是支持团队的强力助手。"}, "LucaCTA": {"title": {"text1": "为您的公司带来", "text2": "高端客户", "text3": "服务体验"}}, "LucaGitHub": {"title": {"text1": "一位智能AI客服代表，", "text2": "可同时处理用户咨询与内部支持，", "text3": "确保每次互动快速、有效、全程记录。"}}, "LucaKeyFeatures": {"features": {"feature1": {"title": "全天候聊天与邮件回复服务", "description": "24/7随时为客户提供响应。"}, "feature2": {"title": "智能优先级识别与升级", "description": "可判断问题紧急性并转交合适内部支持人员。"}, "feature3": {"title": "自动填写支持表单", "description": "为用户自动填写请求信息，提升效率。"}, "feature4": {"title": "完整记录对话与解决路径", "description": "包括内部备注与客服处理步骤。"}}}, "LucaSmartAccounting": {"title": {"line1": "谁适合使用", "line2": "<PERSON><PERSON>？"}, "features": {"SMEs": {"title": "中小企业", "description": "希望提升客服效率，但又不想扩充人手。"}, "Startups": {"title": "初创公司与精简团队", "description": "希望自动化基础客户支持，节省成本。"}, "Webapps": {"title": "Web应用与SaaS平台", "description": "需要稳定且可扩展的客户沟通渠道。"}, "Founders": {"title": "创始人与产品团队", "description": "希望专注业务增长，将客服交给智能助手。"}}}, "LucaComparison": {"Yumi": "<PERSON><PERSON>", "vs": "对比", "TraditionalChatbots": "传统聊天机器人", "HumanOnly": "纯人工客服团队", "leftComparisons": {"comparison1": {"title": "更智能的分流", "description": "—— 可转交至正确的人员或团队，避免卡死。"}, "comparison2": {"title": "识别优先级", "description": "—— 根据紧急性与影响程度标记问题。"}, "comparison3": {"title": "结构化输入", "description": "—— 升级前先收集用户背景与问题详情。"}, "comparison4": {"title": "自动填写表单", "description": "—— 支持表格预填，仅需用户确认。"}, "comparison5": {"title": "深度集成", "description": "—— 可连接企业内部人工客服系统，实现人机无缝合作。"}}, "rightComparisons": {"comparison1": {"title": "始终在线", "description": "—— 零宕机，随时响应用户问题。"}, "comparison2": {"title": "高扩展性", "description": "—— 可同时处理上百个客户请求。"}, "comparison3": {"title": "成本效益高", "description": "—— 大幅减少重复客服所需人手。"}, "comparison4": {"title": "数据驱动优化", "description": "—— 通过日志数据改进支持流程并发现问题。"}}}, "TryYumi": {"subTitle": "开始体验 Yumi", "title": "试用 Yumi", "description": "您可以在帮助中心直接试用 Yumi，体验其实时聊天、表单填写与支持能力。想要更深入的版本？试试Yumi沙盒，上传自有数据、训练属于你的客服AI，并体验全部前后台功能。", "TryYumiNow": "立即试用 Yumi沙盒"}, "LucaFAQ": {"title": "<div class=\"md:w-[2em] sm:w-auto\">常见问题</div>", "faqs": {"q1": {"question": "Yumi 的订阅模式如何？", "answer": {"description": "Yumi 属于定制化模型，需要和客户的系统多点衔接，包含：", "line1": "适配您的品牌和流程", "line2": "集成至您的网站平台", "line3": "支持您的用户端与内部端界面", "line4": "基于您的数据持续更新与优化"}}}}}, "CustomModel": {"LucaLandingHeroSection": {"title": "定制化工作模型：为您的企业量身打造的AI方案", "description": "我们根据您的独特工作流程打造AI解决方案，因为我们知道千篇一律并不适用。我们的即开即用模型可将日常流程如数据分析、评估、决策与自动化转化为智能AI系统。无论是小型企业还是成长型公司，您都能无需招聘工程师或组建AI团队，就释放AI的强大能力。"}, "LucaGitHub": {"title": {"text1": "Doug 大幅缩短从想法到 PPT 的路径，帮助用户", "text2": "专注创意与战略洞察，", "text3": "而不是陷入复制粘贴与格式调整等繁琐任务。"}}, "HowLucaWorks": {"title": {"line1": "定制流程", "line2": "如何运行"}, "steps": {"step1": {"title": "理解您的工作流程", "description": "我们梳理完整的输入、步骤、参与者与输出。"}, "step2": {"title": "整合输入数据", "description": "可接收结构化或非结构化的公开或内部数据。"}, "step3": {"title": "设计处理逻辑", "description": "定义规则、流程、安全校验与分析方法。"}, "step4": {"title": "定制输出形式", "description": "支持生成文档、报表、图表、仪表盘及社交媒体内容等。"}, "step5": {"title": "部署与迭代", "description": "初步上线后根据用户反馈不断优化。"}}}, "LucaComparison": {"CustomizedModels": "定制化模型", "vs": "对比", "GenericAITools": "通用AI工具", "leftComparisons": {"comparison1": {"title": "性价比高", "description": "—— 无需长期雇佣工程师或数据科学家。"}, "comparison2": {"title": "快速部署", "description": "—— 相较内部开发更快实现价值落地。"}, "comparison3": {"title": "融合行业知识", "description": "—— 可将贵团队的专业经验注入模型中。"}}, "rightComparisons": {"comparison1": {"title": "贴合业务", "description": "—— 定制逻辑与流程，非通用模板。"}, "comparison2": {"title": "相关性与精准度更高", "description": "—— 根据您的数据、目标与格式量身构建。"}, "comparison3": {"title": "更佳的合规性与控制", "description": "—— 内嵌公司规则与管控标准。"}}}, "LucaSmartAccounting": {"title": {"line1": "谁需要定制化", "line2": "工作模型？"}, "features": {"Small": {"title": "中小企业", "description": "希望在无需专职AI人员的情况下引入AI能力。"}, "Business": {"title": "企业主", "description": "希望将重复性工作流程转化为自动化执行。"}, "Department": {"title": "部门负责人与团队主管", "description": "希望提升报告、审核、内容等日常运营效率。"}, "Consultants": {"title": "顾问与专业服务机构", "description": "需要个性化、品牌化的客户交付内容。"}}}, "LucaKeyFeatures": {"features": {"feature1": {"title": "基于流程的设计", "description": "我们分析您的输入、逻辑与输出要求。"}, "feature2": {"title": "多模态输入处理", "description": "支持结构化或非结构化、外部或内部数据。"}, "feature3": {"title": "自定义处理逻辑", "description": "内置企业规则、流程与合规限制。"}, "feature4": {"title": "灵活输出格式", "description": "支持输出为PPT、PDF、Word、Excel、CSV或社媒格式。"}, "feature5": {"title": "持续协作优化", "description": "与您的团队持续配合，确保符合真实工作场景。"}}}, "LucaFAQ": {"title": "<div class=\"md:w-[2em] sm:w-auto\">常见问题</div>", "faqs": {"q1": {"question": "定制化服务如何计费？", "answer": {"line1": "• 按项目复杂度与工作量报价", "line2": "欢迎联系我们详细沟通需求并获取专属方案。", "ContactUs": "联系我们"}}}}}, "DashboardHome": {"Hi": "您好", "Description": "使用我们全系列模型，助您轻松完成今日任务。", "Cards": {"ModelManagement": {"Heading": "模型管理", "Sub": "已使用模型"}, "Subscriptions": {"Heading": "订阅管理", "Sub": "当前订阅"}, "Credits": {"Heading": "代币", "Sub": "代币余额", "Redeem": "代币兑换码"}}, "title": "首页", "RecentlyUsed": "最近使用", "NoRecentModels": "暂无最近使用模型", "AllModels": "全部模型", "DashboardHeader": {"MyModels": "我的模型", "OtherBusinesses": "其他企业", "LogOut": "退出登录", "NeedHelp": "需要帮助？", "tutorials": "使用教程", "toast": {"notLoadProfile": "无法加载您的个人资料", "notLoadModels": "无法加载模型", "notLoadRecently": "无法加载最近使用记录"}, "Organization": {"title": "新建团队", "description": "请为您的新团队命名。该团队将拥有独立的设置与偏好，与您账户下的其他团队互不影响。", "form": {"name": {"text": "输入团队名称"}}, "CreateOrganization": "创建团队"}, "cards": {"HermesX": {"tabLabel": "市场监测", "label": "免费添加并查看历史提醒。订阅实时提醒后才开始计费。", "title": "市场宏观新闻监测器，全天候追踪全球金融动态与机会"}, "HermesC": {"tabLabel": "交易员", "label": "免费添加、查看历史提醒和绩效。仅在订阅实时提醒后才收费。", "title": "股票交易机会监测器，全天候追踪交易机会事件"}, "Luca": {"tabLabel": "会计", "label": "每家公司每月30代币，提供基础会计服务", "title": "会计助手，精准自动完成记账与财务报表生成"}, "Freddie": {"tabLabel": "人力资源", "label": " 可免费发布一个职位并评估最多20位应聘者。每增加50位应聘者需支付20代币。", "title": "招聘助手，识别顶尖人才，简化招聘流程"}, "Orion": {"tabLabel": "股票分析", "label": "免费添加。仅在处理股票代码时付费。", "title": "股票分析师，提供深度市场洞察，辅助投资决策"}, "Olympus": {"tabLabel": "市场模拟", "label": " 免费添加，每次模拟需支付100代币。", "title": "市场模拟器，预测趋势并测试策略"}, "Yumi": {"tabLabel": "客户服务", "label": "体验 Yumi 如何通过聊天和电子邮件处理用户互动，全面整合到支持团队的内部界面中。访问需花费30代币，有效期为3天测试时间。", "title": "客户服务管家，为用户提供个性化支持体验"}}}, "Welcome": {"title": "欢迎页", "ContinueToDashboard": "进入控制台", "slides": {"Welcome": {"title": "欢迎使用 ai-wk", "description": "探索多种可提升工作流程、提升效率与协作的业务模型，匹配您的独特工作方式。"}, "Credits": {"title": "代币与定价", "description": "我们按模型使用计费。如需充值或了解详情，请点击这里开始。"}}}, "Profile": {"title": "完善个人资料", "description": "完成个人资料设置，解锁所有可用模型。提供姓名与邮箱即可开始使用。", "toast": {"ProfileUpdated": "个人资料已更新！", "UpdateFailed": "更新失败"}}, "ModelCard": {"IHaveAgreed": "我已阅读并同意：", "The": "", "TermsOfUse": "使用条款", "Disclaimer": "免责声明", "AddToMyModel": "添加至我的模型", "toast": {"UpdateFailed": "更新失败", "TermsFailed": "条款确认失败"}}}, "DashboardCredits": {"title": "代币", "CreditsLeft": "剩余代币", "Credits": "代币", "RedeemPerks": "兑换权益", "PaymentMethod": "付款方式与订阅", "TransactionHistory": "交易记录", "BillStatement": "账单明细", "toast": {"NotLoadExchangeRates": "无法加载汇率"}, "TransactionTable": {"PayAsYouGo": "按次付费", "NumberOfCredits": "代币数量", "EnterAmount": "输入金额", "PayWithUSDCard": "使用银行卡支付", "PayWithStableCoins": "使用稳定币支付", "PayWithBank": "银行转账", "WalletAddress": "钱包地址", "CopyAddress": "复制地址", "TopUpWallet": "充值钱包", "ForPayment": "付款信息：", "SelectCoinType": "选择币种", "ScantoPay": "扫码支付", "pay": {"text1": "$1", "text2": "兑换 1 代币"}, "payUSDT": {"T": "T", "text1": "USDT", "text2": "1 USDT = 1 代币"}, "payUSDC": {"C": "C", "text1": "USDC", "text2": "1 USDC = 1 代币"}, "toast": {"notLoadTransactionHistory": "无法加载交易记录", "WalletAddressSavedSuccessfully": "钱包地址保存成功", "FailedToSave": "保存钱包地址失败", "AddressCopied": "地址已复制到剪贴板！", "PaymentProcessCompleted": "支付完成"}, "walletAddressDescription": "请输入您的钱包地址，我们将从中扣除所需代币，不会超额扣款。", "scanToPayDescription": "请扫描下方二维码完成代币支付。", "NoFundingRecord": {"text": "暂无充值记录", "description": "我们尚未记录任何充值操作"}}, "TopUp": {"title": "充值", "EmailSent": "邮件已发送", "SelectPaymentMethod": "选择付款方式", "TopUpWallet": "充值钱包", "CryptoTopUp": "加密货币充值", "BankTransfer": "银行转账", "SelectCoinType": "选择币种", "WalletAddress": "钱包地址", "Payment": "付款", "AccountDetailsRequestSent": {"title": "账户信息请求已提交", "description": "我们已向您的邮箱发送银行账户信息。"}, "Processing": "处理中...", "ProceedToPay": "继续付款", "CreditsToAdd": "充值代币", "AmountToAdd": "充值金额", "AutoRenewMonthly": "每月自动续费", "PaymentMethod": {"text": "付款方式与订阅", "usd": "银行卡（多币种）", "stable": "加密钱包（USDT/USDC）", "bank": "银行转账（适用于金额超过$1000）", "yedpay": "微信 / QR（港币）"}, "CoinSelection": {"T": "T", "USDT": {"text": "USDT", "description": "1 USDT = 1 代币"}, "C": "C", "USDC": {"text": "USDC", "description": "1 USDC = 1 代币"}}, "WalletAddressForm": {"description": "请提供您的钱包地址以便我们准确追踪您的付款并充值到您的账户，否则可能导致资金丢失。", "info": {"text1": "请使用以太坊主网", "text2": " (Ethereum mainnet)"}, "WalletAddress": {"text": "钱包地址", "description": "请输入您的钱包地址"}}, "ScanToPay": {"text": "扫码支付", "CopyAddress": "复制地址", "Confirming": "确认中...", "ConfirmPayment": "确认付款", "contents": {"line1": "请扫描以下二维码开始支付代币。", "line2": {"text1": "请仅通过", "text2": "ERC-20", "text3": "网络发送 USDC 或 USDT 到此地址。使用其他网络的钱包可能导致资金丢失。"}, "line3": "付款后请输入交易哈希 (transaction hash) 以确认您的付款。"}, "TransactionHash": {"text": "交易哈希", "description": "请输入付款交易哈希"}}, "CryptoTopUpForm": {"AmountToAdd": {"description": "1 {currentCoin} = 1 代币"}}, "BankTransferForm": {"Note": {"text1": "注意：", "text2": "银行转账需先提交要求，我们将通过邮件发送收款银行账户信息供您付款。"}}, "BankDetails": {"text": "银行账户信息", "description": "请查看以下银行转账信息", "BankName": {"text": "银行名称：", "description": "DBS BANK (HONG KONG) LTD"}, "AccountNumber": {"text": "账号：", "description": "*********"}, "AccountName": {"text": "账户名称：", "description": "Alpharithm Investments Limited"}, "SWIFTCode": {"text": "SWIFT 代码：", "description": "DHBKHKHH"}, "BankCode": {"text": "银行代码：", "description": "016"}, "Branch": {"text": "分行代码：", "description": "478"}, "Address": {"text": "地址：", "description": "G/F, The Center, 99 Queen's Road Central, Central, Hong Kong"}, "UploadInfo": {"description": "已完成转账？请上传您的转账截图或回单", "MB": "MB - 点击更换文件", "notes": {"note1": "在此上传您的转账凭证", "note2": "支持 PDF、JPEG，文件大小不超过 5MB"}}}, "toast": {"notLoadExchangeRates": "无法加载汇率", "PaymentFailed": "付款失败", "WalletAddressSavedSuccessfully": "钱包地址保存成功", "FailedToSave": "保存钱包地址失败", "AddressCopied": "地址已复制到剪贴板！", "PleaseEnterAddress": "请输入钱包地址", "NotSuccessful": "未成功", "ReceiptUploaded": "转账凭证上传成功！", "UploadFailed": "上传失败", "EnterTransactionHash": "请输入交易哈希(transaction hash)", "CopiedToClipboard": "{label} 已复制到剪贴板！", "UploadPDForJPEGFile": "请上传 PDF 或 JPEG 文件", "FileSizeLessThan5MB": "文件大小必须小于 5MB", "SelectFileToUpload": "请选择要上传的文件"}}, "InvoiceModal": {"GenerateStatement": "生成账单", "toast": {"DownloadFailed": "下载失败"}}, "RedeemParkModal": {"RedeemSuccessful": "兑换成功", "ViewCredits": "查看代币", "creditAmount": {"text1": "您已成功兑换 ", "text2": " 代币奖励！"}, "EnterCode": {"text": "输入兑换码", "description": "输入兑换码以领取奖励"}, "RedeemPerks": {"title": "兑换奖励", "description": "有兑换码？在此输入，即可获取代币奖励。"}, "toast": {"Successful": "兑换成功！", "SubscriptionFailed": "订阅失败", "PleaseEnterCode": "请输入兑换码"}}, "Subscribe": {"title": "代币", "NoActiveSubscriptionsFound": "未找到有效订阅", "Subscriptions": "订阅列表", "subscription": "订阅", "$": "$", "per": "/ 每", "BillingStarted": "计费开始时间：", "NextBillingCycle": "下一个计费周期：", "RemainingDays": "剩余天数：", "Cancelled": "已取消", "NoActivePaymentMethodFound": "未找到有效的支付方式", "PaymentMethod": "支付方式", "Default": "默认", "SetAsDefault": "设为默认", "SetDefault": {"title": "设为默认？", "description": "您确定要将此支付方式设为默认吗？", "cancelText": "取消", "confirmText": "是的，设为默认", "loadingText": "提交中..."}, "CancelSubscription": {"title": "取消订阅", "description": "您确定要取消订阅吗？取消后将立即失去高级功能访问权限。", "cancelText": "不，保留订阅", "confirmText": "是的，取消订阅", "loadingText": "取消中..."}, "SubText": {"stripe": "使用 Stripe 信用卡支付", "bankWire": "用于大于 $1000 的银行转账", "crypto": "您保存的钱包信息", "yedpay": "用于港币支付", "default": "美元"}, "toast": {"notLoadSubscriptions": "无法加载订阅信息", "SubscriptionCancelled": "订阅已成功取消", "FailedToCancel": "取消订阅失败", "notLoadPayment": "无法加载支付信息", "PaymentSetAsDefault": "已设为默认支付方式", "FailedToSet": "设为默认失败"}}}, "DashboardSettings": {"title": "设置", "tabs": {"Account": {"title": "账户", "BASIC": "基础", "ChangeProfilePicture": "更改头像", "currentlyOnEditMode": "您当前处于编辑模式", "YourDetails": {"title": "个人信息", "description": "更新您的账户设置、偏好语言与时区。"}, "TelegramID": {"text": "Telegram ID", "ChangeID": "更改 ID", "description": "您的 Telegram ID 与多个模型绑定。您也可以在 Telegram 应用中搜索 @{botName} 并选择 /link_account 来绑定账户。"}, "LanguagesAndTimeZone": {"title": "语言与时区", "description": "设置您的偏好语言与时区。", "searchLanguage": "搜索语言..."}, "Language": "语言", "NoLanguageFound": "未找到可用语言。", "TimeZone": "时区", "SearchTimeZone": "搜索时区…", "SelectTimezone": "选择时区…", "NoTimeZoneFound": "未找到可用时区。", "SignInMethod": {"title": "登录方式", "description": "您使用的登录方式"}, "SignedInWithEmail": "通过邮箱登录", "SignedInWithGoogle": "通过 Google 登录", "ChangePassword": "更改密码", "OldPassword": "旧密码", "NewPassword": "新密码", "ConfirmNewPassword": "确认新密码", "toast": {"ProfileUpdated": "个人资料已更新！", "UpdateFailed": "更新失败", "notLoadProfile": "无法加载您的个人资料", "FailedLoadTimezones": "加载时区失败", "PasswordChanged": "密码修改成功！"}, "SaveChangesModal": {"title": "保存更改？", "description": "您是否要保存对账户信息所做的更改？"}}, "Organization": {"title": "团队", "pane": {}}, "Security": {"title": "安全", "pane": {}}, "API": {"title": "API", "pane": {}}}}, "DashboardSupport": {"title": {"text1": "需要帮助？", "text2": "与Yumi 聊聊"}, "description": "请输入问题", "TicketHistory": {"text": "您的工单记录", "title": "工单历史", "Resolved": "已解决", "Pending": "待处理", "NoResultsFound": "未找到结果", "toast": {"notLoadTickets": "无法加载工单", "notLoadTicketDetails": "无法加载工单详情"}, "tableHeader": {"ID": "编号", "Date": "日期", "IssueType": "问题类型", "Status": "状态", "PriorityLevel": "优先级"}, "TicketDetail": {"FullName": "姓名", "Email": "邮箱", "Issue": "问题", "Priority": "优先级", "Status": "状态", "Subject": "主题", "Description": "问题描述", "Attachments": "附件"}}, "FailedToCreateTicket": "创建工单失败，请重试。", "messages": {"InputTooLarge": "输入内容过大，请减少文字后重试。", "SomethingWentWrong": "出错了，请稍后再试。", "TicketCreated": "工单创建成功"}, "TicketForm": {"title": "提交工单", "AddAttachment": "添加附件", "RemoveFile": "移除文件", "IssueTypes": {"BusinessAndPartnerships": "商务与合作", "illingAndPayments": "账单与付款", "UsingOurModels": "模型使用", "CustomizationRequests": "定制请求", "TechnicalIssues": "技术问题与漏洞", "AccountHelp": "账户相关问题", "Media": "媒体与品牌请求", "Careers": "招聘与职业机会", "GeneralQuestions": "常规问题或反馈", "Other": "其他或隐私问题"}, "FullName": {"text": "姓名", "description": "请输入您的姓名"}, "IssueType": {"text": "问题类型", "description": "请选择问题类型"}, "PriorityLevel": {"text": "优先级", "description": "请选择优先级"}, "Subject": {"text": "主题", "description": "请输入工单主题"}, "Chat": {"text": "请输入您的消息..."}, "Attachments": "附件", "DescribeYourIssue": "请详细描述您的问题"}}, "ERICBAI": {"meta": {"title": "柏崇俊 | Alpharithm 创始人兼首席执行官", "description": "柏崇俊，Alpharithm Investments 创始人兼 CEO，曾任高盛与汇丰董事总经理，拥有 20+ 年全球投行经验，现专注 AI 驱动的投资与自动化。"}, "foundersCorner": "创始人简介", "name": "柏崇俊", "title": "Alpharithm Investments 创始人兼首席执行官", "bio": {"paragraph1": "柏崇俊（<PERSON>）于2024年3月创立 Alpharithm Investments，凭借其超过二十年的资深投行经验开启了这项事业。他曾在高盛、瑞信、罗斯柴尔德、汇丰及法国巴黎银行等顶级金融机构担任领导职位，工作足迹遍及伦敦、巴黎及香港等全球金融中心。", "paragraph2": "柏崇俊于2013年被任命为高盛董事总经理，并担任大中华区金融机构业务主管。此后，他又出任汇丰银行全球金融机构业务主管。在其职业生涯中，他长期专注于资本市场及并购领域，积累了丰富的跨境并购与战略交易经验，尤其擅长为全球及中国的金融机构提供战略咨询。", "paragraph3": "他毕业于法国著名的 ESSEC 高等商学院，拥有 MBA 学位，并持有 CFA 特许金融分析师资格。柏崇俊精通英语、法语和普通话，能够在全球市场与复杂的跨境环境中高效运作。他曾主导多个金融科技独角兽的融资与 IPO 项目，由此激发了他对科技变革力量的浓厚兴趣。这段经历最终促使他创立 Alpharithm Investments，致力于打造基于人工智能的投资与交易模型，以实现持续超额收益与风险调整后的优异表现。", "paragraph4": "在柏崇俊的领导下，Alpharithm 已从一个专注于投资 AI 的项目成长为更广泛的 AI 平台。公司业务已扩展至招聘、客户服务、会计等多个领域，提供自动化驱动的 AI 解决方案，正在重塑企业获取和应用高质量专业服务的方式。他坚信人工智能将颠覆多个行业，而这一信念正是 Alpharithm 愿景与运营模式的核心。", "paragraph5": "他在财务分析、资本募集及执行方面拥有深厚的专业能力，其长期积累的机构客户关系也为 Alpharithm 的投资类与企业级 AI 产品提供了天然的市场基础，助力公司的市场拓展战略。", "goldParagraph1": "柏崇俊于2013年被任命为", "goldParagraph2": "，并担任大中华区金融机构业务主管。随后，他又出任汇丰银行全球金融机构业务主管。在其职业生涯中，他专注于", "goldParagraph3": "，为全球及中国的金融机构提供战略咨询。"}, "links": {"goldmanSachs": "高盛董事总经理", "capitalMarkets": "资本市场与并购"}, "dialog": {"title": "交易经历", "description": "在其卓越的投行生涯中，柏崇俊曾主导多个重要的资本市场及并购项目，包括：", "sections": {"ipos": {"title": "首次公开募股（IPO）：", "items": ["陆金所", "壹账通", "平安好医生", "中国太保H股", "中国人保集团H股", "银河证券H股", "华泰证券", "中金公司", "东方证券", "国泰君安证券H股"]}, "placements": {"title": "配售与供股：", "items": ["平安壹账通 Pre-IPO", "平安健康医疗科技 Pre-IPO", "陆金所 B轮与 C轮融资", "平安好医生 A轮融资", "中国银行供股", "工商银行 H股大宗交易", "招商银行供股"]}, "debt": {"title": "债务及优先股融资：", "items": ["中国银行境内外优先股发行", "中国人寿次级债发行", "国银租赁债券发行", "滨海银行发行中国首笔符合巴塞尔协议III的资本工具"]}, "acquisitions": {"title": "并购交易：", "items": ["陆金所收购普惠金融", "澳洲电讯向平安出售汽车之家股份", "平安集团收购深圳发展银行"]}, "strategicInvestments": {"title": "战略投资：", "items": ["巴克莱银行收购南非 ABSA 银行", "奥地利 Erste Bank 收购罗马尼亚商业银行", "KKR 收购摩根士丹利在中金公司的持股", "AMP 投资中国人寿养老保险公司"]}}}}, "TOS": {"title": "使用条款", "Date": "生效日期：2025年5月", "description": "欢迎访问 ai-wk.com（“本网站”），这是一个为专业人士提供AI工作模型的平台（“服务”）。访问或使用本网站及我们的服务即表示您同意遵守并受本《使用条款》（“条款”）的约束。如您不同意，请勿使用本网站或我们的服务。", "features": {"feature1": {"title": "1. 使用资格", "description": "您必须年满18岁，并具有签署具有法律约束力合同的能力。使用本网站即表示您声明并保证符合上述条件。"}, "feature2": {"title": "2. 服务使用", "description": "您只能将我们的服务用于合法的商业目的。您同意不：", "list": {"item1": "未经授权使用我们的内容进行付费或分发。", "item2": "尝试测试服务的安全性或破坏安全机制。", "item3": "与他人共享账户凭据或访问令牌。", "item4": "以违反适用法律或法规的方式使用本服务。"}}, "feature3": {"title": "3. 知识产权", "description": "我们网站或服务中提供的所有内容和资料均受知识产权保护。所有创意内容、文本、图形和设计均为我方或合作方所有。未经书面许可，您不得使用、复制、改编、翻译、授权或传播任何内容。"}, "feature4": {"title": "4. 账户与付款", "description": {"paragraph1": "部分功能需要注册账户。您需提供真实准确的信息，并保持更新。", "paragraph2": "付款条款将另行说明。订阅续费按当前付款条款执行，除非另有说明，否则不予退款。"}}, "feature5": {"title": "5. <PERSON>免责声明", "description": "我们的AI模型用于辅助工作，而非替代人工。AI模型可能会处理包含专业机密的信息。为避免风险：", "list": {"item1": "请勿完全依赖AI输出内容。", "item2": "使用前请自行验证AI生成内容的准确性与适用性。", "item3": "请勿输入敏感信息或任何由AI生成的敏感内容。", "item4": "请注意安全机制可能会屏蔽部分合法内容。"}}, "feature6": {"title": "6. 第三方服务", "description": "本网站可能包含指向第三方服务（如 Stripe）的链接。我们不对这些服务的内容或条款负责。"}, "feature7": {"title": "7. 服务终止", "description": "我们保留随时暂停或终止您使用本服务的权利，恕不另行通知。您也可通过联系我们申请终止使用服务。"}, "feature8": {"title": "8. 责任限制", "description": "在法律允许的最大范围内，我们不对因使用服务产生的任何间接、附带、特别、后果性或惩罚性损失负责。"}, "feature9": {"title": "9. 赔偿条款", "description": "您同意就因您使用服务、违反条款、超出授权的行为、疏忽或故意不当行为所产生的任何索赔、责任和费用，为我们及其附属公司、管理人员和员工提供赔偿并使其免责。"}, "feature10": {"title": "10. 条款变更", "description": "我们可能会不定期更新这些条款。您继续使用本网站即表示接受修订后的条款。如有重大变更，我们将向用户发出通知。"}, "feature11": {"title": "11. 适用法律", "description": "本条款受开曼群岛法律管辖，不考虑其法律冲突原则。"}, "feature12": {"title": "12. 联系我们", "description": "如您对本条款有任何疑问，请联系我们："}}}, "PrivacyPolicy": {"title": "隐私政策", "Date": "生效日期：2025年5月", "description": {"line1": "本隐私政策解释了 ai-wk.com（“网站”）如何在您访问网站和使用服务时收集、使用、披露和保护您的个人信息。", "line2": "访问或使用我们的服务即表示您同意本隐私政策的条款。"}, "features": {"feature1": {"title": "1. 我们收集的信息", "listA": {"title": "a. 您提供的信息：", "item1": "姓名、电子邮件、电话号码、公司名称（如适用）", "item2": "账单与支付信息", "item3": "个人及使用信息（通过第三方支付提供商处理）", "item4": "支持请求及其他沟通内容"}, "listB": {"title": "b. 我们自动收集的信息：", "item1": "IP地址、浏览器类型与设备信息", "item2": "使用数据，包括访问页面、停留时间等", "item3": "Cookies 和类似的跟踪技术"}}, "feature2": {"title": "2. 我们如何使用您的信息", "list": {"item1": "提供、运营与维护我们的服务", "item2": "改善用户体验", "item3": "与您沟通，包括通知与支持", "item4": "处理交易并管理账户", "item5": "优化平台与功能", "item6": "遵守法律义务并执行使用条款"}}, "feature3": {"title": "3. 信息共享与披露", "description": "我们不会出售您的个人数据。我们可能在以下情况下共享信息：", "list": {"item1": "为平台运营提供支持的服务商（如云托管、支付、分析）", "item2": "依法或为保护权益向法律或监管机构披露", "item3": "在合并、收购或资产转让情况下的继承者"}}, "feature4": {"title": "4. 数据安全", "description": "我们采取合理的管理、技术和物理措施保护您的信息。但请注意，互联网传输或电子存储方式并非绝对安全。"}, "feature5": {"title": "5. 数据保留", "description": "我们仅在业务合法目的或法律要求的期限内保留您的个人信息。"}, "feature6": {"title": "6. 您的权利与选择", "description": "根据您的所在地区，您可能拥有如下权利：", "list": {"item1": "访问、更正或删除您的个人信息", "item2": "选择退出营销信息接收", "item3": "撤回数据处理同意", "item4": "向数据保护监管机构提出投诉"}, "info": "您可通过 <EMAIL> 联系我们行使上述权利。"}, "feature7": {"title": "7. <PERSON><PERSON> 使用", "description": "我们使用Cookies和类似技术提升浏览体验、分析流量并个性化内容。您可通过浏览器设置管理Cookies偏好。"}, "feature8": {"title": "8. 国际用户", "description": "我们的服务可能在全球范围托管。如您所在地区适用数据保护法规（如GDPR），我们将努力遵守相关规定。尽管我们不是欧盟公司，但我们会合理履行本地法律下的隐私与数据保护责任。"}, "feature9": {"title": "9. 政策更新", "description": "我们可能会不定期更新本隐私政策。更新后的版本将发布在本页，并注明生效日期。继续使用服务即表示您接受更新后的政策。"}, "feature10": {"title": "10. 联系我们", "description": "如您对本政策有任何疑问或意见，请通过以下方式与我们联系："}}}, "AIDisclaimer": {"title": "AI 模型免责声明", "Date": "生效日期：2025年5月1日", "description": {"text1": "本免责声明适用于所有通过 ai-wk.com 使用的 AI 工作模型，包括但不限于", "text2": "<PERSON><PERSON>", "text3": "与", "text4": "Orion"}, "features": {"feature1": {"title": "1. 非投资建议", "description": "本平台 AI 模型生成的内容和输出仅供一般信息参考，并不构成任何投资、财务、法律或其他专业建议。请勿仅依赖 AI 内容进行投资或商业决策。"}, "feature2": {"title": "2. AI 内容的局限性", "description": "我们的模型基于输入数据和算法生成结果，可能存在不完整、不准确或过时的情况，因此：", "list": {"item1": "分析与建议可能存在错误或偏差", "item2": "所用数据可能不全面或缺乏上下文", "item3": "内容可能不反映最新市场情况或用户具体情境"}, "info": "我们不保证任何 AI 内容的准确性、完整性或可靠性。"}, "feature3": {"title": "3. 用户责任", "description": "用户需自行评估使用 AI 输出内容的风险与适用性。我们强烈建议您在基于 AI 内容做出决策前咨询有执照的专业人士。"}, "feature4": {"title": "4. 责任限制", "description": {"paragraph1": "ai-wk.com 及其关联方不对因使用或依赖 AI 输出内容造成的任何直接、间接、附带或后果性损失承担责任，包括财务损失。", "paragraph2": "使用本服务即表示您接受本免责声明。"}}, "feature5": {"title": "5. 联系我们", "description": "如您对本免责声明有任何疑问，请联系我们："}}}, "RefundPolicy": {"title": "退款政策", "description": "在 ai-wk.com，我们致力于为用户提供高质量的 AI 工作模型与服务。请阅读以下退款政策：", "features": {"feature1": {"title": "1. 数字产品与代币", "description": {"paragraph1": "所有已使用代币或已访问的付费服务均不可退款。", "paragraph2": "在以下情况下我们可提供退款："}, "list": {"item1": "因系统错误导致用户被错误收费", "item2": "用户在短时间内重复购买多个服务包", "item3": "因经验证的技术问题导致用户无法访问服务"}}, "feature2": {"title": "2. 退款资格", "description": "如您申请退款，需满足以下条件：", "list": {"item1": "在交易日期起7日内提交退款请求", "item2": "提供交易ID与问题简要说明", "item3": "确保所请求退款的代币或服务尚未被使用"}, "info": "不满足上述条件的退款请求可能被拒绝。"}, "feature3": {"title": "3. 如何申请退款", "description": {"text1": "请通过 ai-wk.com 提交支持表单，或联系客户支持团队（邮箱：", "text2": "），并提供以下信息："}, "list": {"item1": "您的全名", "item2": "注册邮箱地址", "item3": "购买日期与时间", "item4": "交易 ID", "item5": "退款原因"}, "info": "我们将在 3–5 个工作日内审核并回复所有退款请求。"}, "feature4": {"title": "4. 最终说明", "description": "所有退款决定由 Alpharithm Investments Limited 全权决定。经批准的退款将退回原支付方式。"}}}, "DashboardHermesC": {"title": {"line1": "Hermes C：抢先掌握", "line2": "实时股票事件"}, "TrialCountdownBanner": {"countdown": "您的免费试用还剩 {days}天 {hours}小时 {minutes}分钟 {seconds}秒。立即订阅以继续享受服务"}, "description": "完整分析、触手可及。", "AlertsTable": {"title": "历史信号", "Latest": "最新", "delayed": "延迟 24 小时", "UTC": "协调世界时间 (UTC)", "NYT": "纽约时间 (NYT)", "UserTime": "用户本地时间", "TimeZone": "时区", "ViewPerformance": "查看表现", "Filter": "筛选", "Button": {"Loading": "加载中...", "EnquireNow": "立即咨询", "SubscribeNow": "立即订阅", "StartFreeTrial": "开始免费试用"}, "toast": {"notLoadAlerts": "无法加载信号", "notLoadSavedFilters": "无法加载保存的筛选条件", "notLoadSubscriptionStatus": "无法加载订阅状态"}, "Table": {"ViewChart": "查看图表", "RawStrategyReturn": {"text": "原始策略回报", "description": "该数据反映了 AI 设定的原始策略所产生的回报，不包含人为干预。以 AI 推荐入场价买入，在止盈或下一个常规交易日收盘时退出，以先到者为准。"}, "PassiveReturnClose": {"text": "持仓至收盘回报", "description": "从信号发布到下一个常规收盘的回报，假设在信号时间段的平均价格建立被动仓位并持有至收盘。"}, "ActiveReturnToMax": {"text": "最大主动回报", "description": "从信号时间段的平均价到下一个收盘前可能的最大回报，假设交易者积极管理并成功抓住最大利润。"}, "NoResultsFound": "未找到结果", "Header": {"Time": "时间", "Ticker": "股票代码", "Event": "事件", "Direction": "方向", "Confidence": "信心度", "Condition": "建仓条件"}}}, "EnquireFlow": {"Loading": "加载中...", "ImportantDisclaimers": {"title": "重要免责声明", "description": "Hermes C 并非金融顾问，仅为自动化投资工作流程而设计的信息处理系统。请用户注意：", "contents": {"line1": {"text1": "本产品不提供投资建议。", "text2": "用户在做出投资决策时仍需结合人为判断，不应完全依赖 Hermes。"}, "line2": {"text1": "Hermes 可能受误导新闻影响。", "text2": "新闻信号的质量取决于来源，用户需自行验证其有效性。"}, "line3": {"text1": "用户需自备数据源。", "text2": "因许可限制，我们不提供第三方新闻或市场数据。"}, "line4": {"text1": "不适用于散户投资者。", "text2": "仅限具备专业判断能力的专业人士使用本工具。"}}}, "ScheduleMeeting": {"title": "下一步：安排会议", "description": "为了确保系统契合您的需求与投资架构，我们不提供即时订阅。请点击下方按钮安排会议讨论之后方可订阅。"}, "Organization": {"text": "机构名称", "description": "基金、公司或家族办公室名称"}, "TypeOfInvestor": {"text": "投资者类型", "description": "例如对冲基金、家族办公室、自营交易"}, "Country": {"text": "国家", "description": "您的业务所属司法辖区"}, "toast": {"DisclaimerAccepted": "免责声明接受成功！", "FailedToAccept": "免责声明接受失败", "MeetingSuccessfully": "会议请求提交成功！", "FailedToSave": "保存会议请求失败"}}, "FilterAlerts": {"Reset": "重置为默认", "LoadingFilters": "正在加载筛选条件...", "FilterTable": "筛选表格", "Direction": {"text": "方向", "description": "可选择多个方向", "long": "多头", "short": "空头"}, "Condition": {"text": "条件", "description": "可选择多个条件", "normal": "正常", "breakthrough": "突破"}, "Confidence": {"text": "信心度", "description": "可选择多个信心等级"}, "toast": {"notLoadSavedFilters": "无法加载保存的筛选条件", "FailedToSaveFilters": "保存筛选条件失败"}}, "Subscribe": {"title": "订阅以接收最新信号", "description": "我们将信号直接发送到您绑定的 Telegram ID，确保您不错过任何重要信息。选择订阅类型并点击“继续”以激活信号服务。", "toast": {"Successful": "订阅成功！", "SubscriptionFailed": "订阅失败"}, "Monthly": {"text": "月度", "description": "1,000代币"}, "Annually": {"text": "年度", "description": "12,000代币"}}, "FreeTrial": {"title": "开启 5 天免费试用", "description": "享受为期 5 天的免费试用，期满后需订阅以继续接收 Hermes C 信号。", "StartFreeTrial": "开始免费试用", "toast": {"TrialStartedSuccessfully": "试用已成功开启！", "notStartTrial": "无法开启试用"}}, "Performance": {"Title": "表现分析", "Header": {"line1": "Hermes C：抢先掌握", "line2": "实时股票事件", "description": "完整分析、触手可及。"}, "ViewGraph": {"loading": "加载中...", "strategyReturn": "原始策略回报", "passiveReturnToClose": "持仓至收盘回报", "passiveReturnToMax": "主动最大回报", "averageReturns": "平均回报"}, "ViewChart": {"loading": "加载中..."}, "Table": {"Time": "纽约时间 (NYT)", "NumberOfAlerts": "信号数量", "LongPositions": "多头仓位 %", "NormalConditions": "正常建仓条件 %", "RawStrategyReturn": "原始策略回报", "PassiveReturnToClose": "持仓至收盘回报", "ActiveReturnToMax": "主动最大回报", "NoResultsFound": "未找到结果", "UserTime": "用户本地时间", "NYT": "纽约时间", "Previous": "上一页", "Next": "下一页", "Page": "页码", "of": "共", "Filter": "筛选", "Period": {"Daily": "日度", "Weekly": "周度", "Monthly": "月度"}}, "FilterAlertsModalHermesC": {"filterTable": "筛选表格", "reset": "重置为默认", "loading": "加载筛选条件中...", "direction": {"text": "方向", "description": "可选择多个方向", "Long": "多头", "Short": "空头"}, "condition": {"text": "建仓条件", "description": "可选择多个条件", "normal": "正常", "breakthrough": "突破"}, "confidence": {"text": "信心度", "description": "可选择多个信心等级"}, "cancel": "取消", "apply": "筛选"}, "PerformanceChart": {"constantCapital": "固定资本投资", "compounding": "复利投资", "legend": {"rawStrategyReturn": "原始策略回报", "passiveReturnToClose": "持仓至收盘回报", "activeReturnToMax": "主动最大回报", "activeAlphaZone": "主动阿尔法区间", "activeAlphaZoneTooltip": "该区域代表被动持仓与最大回报之间的利润空间。体现了熟练交易者通过时机把握、技巧与判断力可以实现的交易利润。"}, "customTooltip": {"date": "日期：{label}"}, "accumulativePerformance": "每日平均回报假设下的累积资本（基于100本金）"}, "SubscriptionFlowModal": {"disclaimer": {"title": "重要免责声明", "desc1": "Hermes C 并非金融顾问，而是用于自动化投资流程的一般信息处理系统。请用户注意：", "noAdvice": "不提供投资建议。", "noAdviceDesc": "用户应结合自身判断，不应完全依赖 Hermes。", "misled": "可能受误导性新闻影响。", "misledDesc": "信号质量取决于来源，用户需自行验证。", "ownData": "需自备数据源。", "ownDataDesc": "由于许可限制，我们不捆绑第三方数据。", "notRetail": "不适用于散户投资者。", "notRetailDesc": "仅限具备解释其输出能力的专业人士使用。", "cancel": "取消", "agree": "同意并继续", "processing": "处理中..."}, "meeting": {"title": "下一步：安排会议", "description": "为了确保本系统适合您的需求与设置，我们不提供即时订阅。请点击下方申请会议！", "organization": "机构名称", "organizationPlaceholder": "请输入您的基金、公司或家族办公室名称", "typeOfInvestor": "投资者类型", "typeOfInvestorPlaceholder": "例如：对冲基金、家族办公室、自营交易", "country": "国家", "countryPlaceholder": "运营所在司法辖区", "message": "留言", "messagePlaceholder": "请简要说明您的需求或问题", "cancel": "取消", "submit": "提交", "submitting": "提交中..."}}, "SubscribeModalHermesC": {"title": "订阅以接收最新信号", "description": "我们将信号直接发送到您绑定的 Telegram ID，确保您不错过任何重要信息。选择订阅类型并点击“继续”以激活信号服务。", "monthly": "月度", "monthlyCredits": "1,000 代币", "annually": "年度", "annualCredits": "12,000 代币", "cancel": "取消", "proceed": "继续", "success": "订阅成功！", "error": "订阅失败"}}}, "DashboardHermesX": {"title": {"line1": "Hermes X：抢先掌握", "line2": "实时市场动态"}, "Social": {"Follow1": "Hermes X 免费版", "Follow2": "关注我们的 X 账号，免费获取顶级信号"}, "description": "消息将实时发送至您的 Telegram，全天候 24/7 服务。下方为延迟示例。", "AlertsTable": {"title": "历史信号", "Latest": "最新", "delayed": "延迟 24 小时", "UTC": "协调世界时间 (UTC)", "NYT": "纽约时间 (NYT)", "UserTime": "用户本地时间", "ViewPerformance": "查看表现", "Filter": "筛选", "TelegramPreferences": "Telegram 偏好设置", "Button": {"Loading": "加载中...", "SubscribeNow": "立即订阅", "StartFreeTrial": "开始免费试用", "FilterTable": "筛选表格"}, "toast": {"notLoadAlerts": "无法加载信号", "notLoadSavedFilters": "无法加载保存的筛选条件", "notLoadSubscriptionStatus": "无法加载订阅状态"}, "Table": {"ViewChart": "查看图表", "PreviewWarning": {"line1": "订阅后，您将实时收到预警，无延迟，并可在本表中查看。", "line2": "此预览展示带有延迟的历史信号，帮助您感受信号质量。"}, "Next": "下一页", "Previous": "上一页", "Page": "页码", "of": "共", "NoResultsFound": "未找到结果", "Header": {"Time": "时间", "TweetText": "新闻/推文", "Alert": "信号", "Category": "类别", "Assets": "资产", "Region": "地区", "Direction": "方向", "Significance": "重要性", "AffectedAssets": "受影响资产"}}}, "SubscribeFlow": {"Loading": "加载中...", "toast": {"DisclaimerAccepted": "免责声明接受成功！", "FailedToAccept": "接受免责声明失败", "MeetingSuccessfully": "会议请求提交成功！", "FailedToSave": "保存会议请求失败"}}, "FilterAlerts": {"FilterTable": "筛选表格", "Reset": "重置为默认值", "LoadingFilters": "正在加载您的偏好设置...", "Category": {"text": "类别", "description": "可选择多个类别", "Market": "市场", "Sector": "行业", "Company": "公司"}, "Significance": {"text": "重要性", "description": "可选择多个重要性等级", "High": "高", "Medium": "中", "Low": "低"}, "Assets": {"text": "资产", "description": "可选择一个或多个资产类型", "Equities": "股票", "Bonds": "债券", "FX": "外汇", "Commodities": "商品", "Crypto": "加密货币", "Private": "私募"}, "Region": {"text": "地区", "description": "可选择多个地区", "US": "美国", "China": "中国", "Japan": "日本", "India": "印度", "UK": "英国", "Germany": "德国", "Saudi Arabia": "沙特", "Canada": "加拿大", "Australia": "澳大利亚", "Southeast Asia": "东南亚", "Latin America": "拉丁美洲", "EU": "欧盟", "Global": "全球"}, "CheckBox": "自定义 Telegram 信号筛选条件", "Button": {"Cancel": "取消", "Filter": "筛选", "Saving": "保存中..."}, "toast": {"notLoadSavedFilters": "无法加载保存的偏好设置", "FailedToSaveFilters": "保存偏好设置失败"}}, "Modals": {"WelcomeModal": {"title": "欢迎使用 HermesX", "description": "很高兴您加入我们！在这里，您将收到实时预警、有价值的洞察。立即连接您的 Telegram 账户，升级体验。", "continue": "继续前往控制台"}, "TelegramSetupModal": {"title": "绑定 Telegram", "description": "请绑定 Telegram，以连接ai-wk机器人并接收优质信号。", "button": "设置个人资料", "success": "设置成功！", "error": "更新失败"}, "TelegramConfirmationModal": {"title": "您的 Telegram ID 将连接至ai-wk机器人", "description": "我们将使用您在会员控制台中注册的 Telegram ID 来连接ai-wk机器人。\n如需更改 Telegram ID，您可随时在会员控制台设置中修改。", "continue": "继续"}, "SubscribeModal": {"title": "订阅以接收最新信号", "description": "为确保您时刻掌握重要动态，我们将信号直接发送至您绑定的 Telegram ID。选择订阅类型并点击“继续”按钮，即可激活 Telegram 信号服务。", "monthly": "月度", "monthlyCredits": "20 点数", "annually": "年度", "annualCredits": "240 点数", "cancel": "取消", "proceed": "继续", "success": "订阅成功！", "error": "订阅失败"}, "SubscriptionFlowModal": {"connectTelegram": {"title": "连接您的 Telegram", "description": "请绑定 Telegram，以连接ai-wk机器人并接收优质信号。", "linkTelegram": "绑定 Telegram", "checkingStatus": "正在检查状态..."}, "subscribe": {"title": "订阅以接收最新信号", "description": "为确保您时刻掌握重要动态，我们将信号直接发送至您绑定的 Telegram ID。选择订阅类型并点击“继续”按钮，即可激活 Telegram 信号服务。", "monthly": "月度", "monthlyCredits": "20 点数", "annually": "年度", "annualCredits": "240 点数", "cancel": "取消", "proceed": "继续"}, "preferences": {"title": "Telegram 偏好设置", "reset": "重置为默认", "loading": "加载偏好设置中...", "category": {"text": "类别", "description": "可选择多个类别"}, "significance": {"text": "重要性", "description": "可选择多个选项"}, "assets": {"text": "资产", "description": "可选择一个或多个资产"}, "region": {"text": "地区", "description": "可选择多个选项"}, "cancel": "取消", "apply": "应用偏好", "saving": "保存中..."}}, "TelegramPreferencesModal": {"title": "Telegram 偏好设置", "reset": "重置为默认", "loading": "加载偏好设置中...", "category": {"text": "类别", "description": "可选择多个类别"}, "significance": {"text": "重要性", "description": "可选择多个选项"}, "assets": {"text": "资产", "description": "可选择一个或多个资产"}, "region": {"text": "地区", "description": "可选择多个选项"}, "cancel": "取消", "apply": "应用偏好", "saving": "保存中..."}, "DisclaimerModal": {"title": "AI 模型免责声明", "sections": {"noAdvice": {"title": "1. 非投资建议", "text": "我们 AI 模型生成的内容和结果仅供一般信息参考，不构成任何形式的投资、法律或专业建议。请勿仅依赖 AI 内容进行投资或商业决策。"}, "limitations": {"title": "2. AI 生成内容的局限性", "text": "我们的模型基于输入数据和算法生成内容，可能存在不完整、不准确或过时的情况。因此：", "list": {"line1": "分析与建议可能存在错误或偏差", "line2": "使用的数据可能不全面或缺乏上下文", "line3": "内容可能无法反映当前市场或个体情况"}, "footer": "我们不保证任何 AI 生成信息的准确性、完整性或可靠性。"}, "userResponsibility": {"title": "3. 用户责任", "text": "用户需自行评估使用本服务的风险与适用性 [...]"}}, "cancel": "取消", "agree": "同意并继续"}, "ExpandTweetModal": {"close": "关闭"}, "ExpandTruthsocialModal": {"title": "Truth Social 帖文"}}}, "DashboardOrion": {"Form": {"generateReportTitle": "生成报告", "generateReportDescription": "请在下方提供股票代码，我们将尽快为您生成报告。", "tickersLabel": "股票代码", "tickersSubLabel": "（仅限美国市场）", "uploadTickers": "上传股票代码", "selectTickersPlaceholder": "选择股票代码", "removeTicker": "移除代码", "uploadResearchReportsLabel": "上传研究报告", "optional": "（可选）", "researchReportsTooltip": "我们不提供第三方研究报告。用户需自行获取并上传报告。请确保所有报告符合相关法规和规范。", "viewUploads": "查看上传内容", "uploadDocumentButton": "点击上传文件", "uploadDocumentDescription": "支持 PDF、DOCX、DOC、TXT，最大10MB", "tickersNotFound": "未找到 {tickers}", "uploadEarningsLabel": "上传最新财报", "earningsTooltip": "我们将为您获取来自 SEC 的最新财报。如部分公司在官网先发布财报，用户可自行获取并上传。", "uploadOtherDocumentsLabel": "上传其他分析文件", "otherDocumentsTooltip": "除默认提供的资料外，用户可在此上传其他文件以供分析。", "otherDocumentsDescription": "支持 PDF、DOCX、DOC、TXT，最大10MB", "deliveryOptionsLabel": "报告发送方式", "selectDeliveryPlaceholder": "请选择报告接收方式", "emailOption": "电子邮箱", "telegramOption": "Telegram", "emailLabel": "邮箱地址", "emailPlaceholder": "请输入您的邮箱地址", "generatedReportsNote": "所有生成的报告将保存在您的账户中。", "secFilingCheckbox": "请发送最新的 SEC 文件和管理层展示（如有）。", "generateReportButton": "生成报告", "generatingReportButton": "报告生成中...", "reportDelivered": "报告已发送至您的 {method}！", "tickersFound": "已识别股票代码", "tickersNotRecognized": "未识别的代码", "cancel": "取消", "finish": "完成", "searchPlaceholder": "搜索代码，如 AAPL、META、NVDA", "searchButton": "搜索", "searchError": "搜索出错：{error}", "tickerLimitExceeded": "超出代码数量限制，请减少至10个或以下。", "enterTickersInstruction": "请输入所有股票代码，使用逗号或空格分隔", "pleaseSelectTickersFirst": "请先选择股票代码", "pleaseSelectAtLeastOneTicker": "请至少选择一个股票代码", "pleaseSelectDownloadOption": "请选择下载方式", "pleaseEnterEmail": "请输入邮箱地址", "pleaseEnterValidEmail": "请输入有效的邮箱地址", "pleaseEnterTelegramId": "请输入 Telegram ID", "checkingStatus": "状态检查中...", "pleaseLinkTelegram": "请先绑定 Telegram", "generateReportConfirmTitle": "生成报告", "generateReportConfirmDescription": "确定要生成报告吗？该操作将消耗积分。", "dontAskAgain": "不再提示"}, "UploadTickers": {"uploadTickersTitle": "上传股票代码", "clickToUploadDocument": "点击上传文件（.csv）", "tickerLimit": "代码限制：", "selectedFile": "已选择文件：", "validating": "验证中...", "limitExceeded": "超出限制，请减少至10个或以下。", "failedToReadCSV": "读取 CSV 文件失败", "noFileSelected": "未选择文件", "notCSVFile": "不是 CSV 文件", "pleaseFixValidationErrors": "请修复验证错误后再上传", "uploadSuccessful": "上传成功", "uploadFailed": "上传失败", "uploadButton": "上传", "uploadingButton": "上传中...", "useCSVTemplate": "使用 CSV 模板"}, "UploadSheet": {"uploadResearchReportsTitle": "上传研究报告", "uploading": "上传中", "clickToUploadDocument": "点击上传文件", "uploadDocumentDescription": "支持 PDF、DOCX、DOC、TXT, 最大10MB", "uploadsSectionTitle": "上传内容", "selectedFile": "已选择文件：", "cancel": "取消", "finish": "完成", "searchForTickersPlaceholder": "搜索股票代码", "noMatchingTickersFound": "未找到匹配的代码"}, "Tutorials": {"breadcrumbOrion": "Orion", "breadcrumbTutorials": "教程", "getTheMostOutOfModels": "充分发挥模型的作用", "watchTutorials": "观看教程", "watchHowToUse": "观看使用方法和最佳使用场景视频", "noVideosAvailable": "本节暂无视频"}, "TelegramModal": {"saveTelegramIdTitle": "保存 Telegram ID", "saveTelegramIdDescription": "检测到您添加了新的 Telegram ID, 是否保存以便后续使用？", "replaceExistingIdWarning": "这将替换现有的 ID。", "noDontSave": "否，不保存", "yesSaveId": "是，保存", "saving": "保存中..."}, "ReportHistory": {"reportsHistoryTitle": "报告历史", "reportsHistoryDescription": "您可离开此页面，报告准备好后将自动发送", "loadingReports": "加载报告中...", "noReportsFound": "未找到报告", "wantToKnow": "Want to know what the reports look like?", "reportSample": "报告示例", "noTickers": "无股票代码", "failed": "失败", "showingRows": "显示行数", "previous": "上一页", "next": "下一页", "share": "分享", "email": "邮箱", "telegramId": "Telegram ID", "download": "下载", "progressFailed": "失败", "progressPercent": "{percent}%", "enterReceivingEmailTitle": "输入接收邮箱地址", "enterEmailPlaceholder": "请输入您的邮箱地址", "useRegisteredAddress": "使用注册邮箱", "cancel": "取消", "send": "发送", "enterReceivingTelegramTitle": "输入接收 Telegram ID", "enterTelegramPlaceholder": "请输入您的 Telegram ID", "pleaseEnterEmail": "请输入邮箱地址", "pleaseEnterValidEmail": "请输入有效的邮箱地址", "pleaseEnterTelegramId": "请输入 Telegram ID", "noJobIdForEmail": "无可用任务 ID (邮箱)", "noJobIdForDownload": "无可用任务 ID (下载)", "reportSentToEmail": "报告成功发送至邮箱", "reportSentToTelegram": "报告成功发送至 Telegram", "reportDownloaded": "报告成功下载", "reportGenerationComplete": "报告生成完成！", "reportGenerationFailed": "报告生成失败"}, "Onboard": {"orionTutorialTitle": "Orion 教程", "welcomeToOrion": "欢迎使用 Orion", "welcomeDescription": "欢迎加入！输入股票代码，生成分析报告，获取所需洞察，做出明智决策。", "watchHowToVideo": "观看使用视频", "continueToDashboard": "进入控制台", "previousVideo": "上一个视频", "watchLater": "稍后观看", "continueDashboard": "继续使用控制台", "nextVideo": "下一个视频：{title}"}, "LoadingModal": {"loadingDialogTitle": "加载中", "generating": "生成中", "downloadingReport": "报告下载中"}, "Header": {"orion": "Orion", "home": "首页", "reports": "报告", "returnToAiWk": "返回 ai-wk", "aiWk": "ai-wk", "needHelp": "需要帮助？", "tutorials": "使用教程"}}, "DashboardYumiSandbox": {"InternalLogPanel": {"noLogs": "未找到内部日志。"}, "SandboxHomePage": {"needHelp": "需要帮助？请联系", "getStartedPrompt": "请问您的问题", "loading": "加载中...", "ticketCreated": "工单创建成功"}, "SandboxCallCenterPage": {"callCenterHeader": "呼叫中心", "callCenterDescription": "能够独立运行完整的 AI 驱动呼叫中心。您可以通过下方选项，模拟与 AI 业务专员的通话体验。", "presetUsersBelow": "使用以下预设用户进行呼叫：", "callUsing": "使用以下身份拨打：", "InitiateCall": "发起呼叫", "callingAs": "以 {firstname} {lastname} 身份拨打中", "retry": "重试", "callEnded": "通话已结束", "callDroppedByServer": "通话被服务器中断", "callEndedWithError": "通话因错误结束", "connecting": "正在连接...", "callPickedUp": "对方已接听", "wsError": "WebSocket 错误", "error": "错误"}, "SandboxHeader": {"returnToAiWk": "返回 ai-wk", "aiWk": "ai-wk", "needHelp": "需要帮助？", "testUser": "测试用户："}, "SandboxSubscribeModal": {"title": "<PERSON><PERSON> 沙盒", "description": "开始使用 Yumi 为客户服务，仅需 30 点数即可获得 3 天完整访问权限，亲自体验 Yumi 的实际效果。", "thirtyCredits": "30 点数", "forThreeDays": "为期 3 天", "termsOfUse": "使用条款", "disclaimer": "免责声明", "returnToAiWk": "返回 ai-wk", "agreeAndContinue": "同意并继续", "agreementPrefix": "我已阅读并同意", "and": "和"}, "SandboxSubscribeModalWrapper": {"success": "成功！", "subscriptionFailed": "订阅失败"}, "SupportPage": {"ticketDetailsPanel": {"fullName": "全名", "email": "邮箱", "issue": "问题", "status": {"label": "状态", "resolved": "已解决", "pending": "待处理"}, "priority": "优先级", "ticketId": "工单编号", "subject": "主题", "description": "问题描述", "additionalInfo": "附加信息", "attachments": "附件:", "emailAttachments": "邮件附件:", "attachmentLabel": "附件 {number}", "download": "下载", "downloadError": "下载附件失败。", "imageAlt": "图片"}, "table": {"sn": "序号", "date": "日期", "customer": "客户", "email": "电子邮箱", "issueType": "问题类型", "status": "状态", "priority": "优先级"}, "type": {"GI": "一般咨询", "PSQ": "产品或服务问题", "OTI": "订单或交易问题", "BPQ": "账单或付款问题", "AAH": "账户或访问协助", "TSE": "技术支持或错误", "FS": "反馈或建议", "PSC": "隐私或敏感问题"}, "error": "加载工单失败。", "status": {"resolved": "已解决", "pending": "待处理", "unknown": "未知"}, "priority": {"unknown": "未知"}, "header": "内部支持", "tabs": {"all": "所有工单", "pending": "待处理", "resolved": "已解决"}, "summary": {"all": "所有工单", "pending": "待处理工单", "resolved": "已解决工单", "total": "提交的工单总数", "pendingTotal": "待处理工单总数", "resolvedTotal": "已解决工单总数"}, "dateRange": {"select": "选择日期范围", "from": "从", "to": "到", "cancel": "取消", "apply": "应用"}, "pagination": {"pageInfo": "第 {page} 页，共 {totalPages} 页", "rowsPerPage": "每页行数：", "previous": "上一页", "next": "下一页"}, "filterBar": {"searchPlaceholder": "搜索姓名、邮箱…", "period": "时间范围", "from": "从", "to": "到", "status": "状态", "priority": "优先级", "periodOptions": {"All Time": "全部时间", "Today": "今天", "Last 7 Days": "最近7天", "This Month": "本月", "Custom…": "自定义…"}, "statusOptions": {"All": "全部", "Pending": "待处理", "Resolved": "已解决"}, "priorityOptions": {"All": "全部"}, "dateRangePicker": {"chooseRange": "选择日期范围", "rangeLabel": "{from} – {to}"}, "allTicketsTable": {"header": "所有工单"}}, "transactionTable": {"table": {"dateTime": "日期与时间", "amount": "金额", "type": "类型", "narration": "说明"}}, "transactionHistoryTab": {"custom": {"from": "从", "to": "到"}, "error": "无法加载交易记录。", "showingRows": "显示 {count} 条，共 {total} 条记录"}, "transactionFilterBar": {"period": "时间范围", "model": "模型", "periodOptions": {"All Time": "全部时间", "Last 7 Days": "最近 7 天", "This Month": "本月", "Custom…": "自定义…"}, "modelOptions": {"All": "全部", "Orion": "Orion", "Hermes": "<PERSON><PERSON>", "Olympus": "Olympus"}}, "ticketsContent": {"selectTicketPrompt": "请选择一个工单查看详情。", "error": {"couldNotLoadTicket": "无法加载工单详情。"}, "tabs": {"details": "工单详情", "emailLog": "邮件日志记录", "internalLogs": "内部日志"}, "referIssue": "转交问题", "referDialog": {"title": "转交问题", "description": "请选择要转交给谁，并说明理由。", "referTo": "转交给", "selectWho": "选择转交对象", "failedToLoad": "加载可转交人员失败。", "reason": "原因", "reasonPlaceholder": "请输入转交理由", "referIssue": "确认转交", "confirmTitle": "确认转交", "confirmDescription": "您确定要将此问题转交给：", "to": "转交给：", "referring": "正在转交...", "confirm": "确认"}, "roles": {"CustomerSupportLead": "客户支持主管", "OperationsTeam": "运营团队", "TechnicalTeam": "技术团队", "ProductTeam": "产品团队", "MarketingOrCommunications": "市场或传播部门", "Leadership": "领导层", "PeopleAndCulture": "人力与文化（人力资源）", "FinanceAndBilling": "财务与账务", "ComplianceOrLegal": "合规或法律部门", "CEO": "首席执行官"}, "toast": {"ticketReferred": "工单转交成功！", "failedToRefer": "工单转交失败。", "ticketResolved": "工单已成功标记为已解决！"}, "resolveDialog": {"resolved": "已解决", "resolving": "正在解决...", "resolveTicket": "标记为已解决", "title": "确认解决", "description": "您确定此工单已为客户解决？该操作不可撤销。", "confirm": "是的，标记为已解决"}}}, "SupportTicketDetailPage": {"notFound": "未找到客户信息", "breadcrumb": {"internalSupport": "内部支持"}}, "SetupPage": {"finishSetupNotice": "请完成客服设置以访问沙盒环境。", "proactiveText": "你是一位积极主动、热情且乐于助人的礼宾人员。你讲话清晰简洁，始终以自信与友善的态度引导用户。你保持专业的同时也易于接近，避免使用术语，并根据用户的理解程度进行调整。你的语气平和、尊重且专注，让用户感受到支持与理解。", "conciergeSetup": "客服设置", "knowledgeBaseTab": "知识库", "customizationTab": "个性化设置", "editCustomization": "编辑个性化设置", "customizationTabSetup": "设置您的客服详细信息。", "customizationTabDescription": "您可以在下方更新客服设定，所有字段为可选项。", "customizationUpdated": "个性化设置已更新！", "uploadedFiles": "已上传文件", "uploadedDocuments": "已上传文档", "updateCustomization": "更新个性化设置", "updating": "正在更新...", "cancelEdit": "取消编辑", "saveAndSetup": "保存并设置客服", "saving": "保存中...", "uploadInfo": "请上传您的公司信息，我们将基于此信息配置您的客服助手", "organizationName": "组织名称", "organizationNamePlaceholder": "示例：Organization Q", "conciergeName": "客服名称", "conciergeNamePlaceholder": "示例：Lato", "language": "语言", "languagePlaceholder": "选择语言", "personalityPrompt": "个性化提示语", "personalityPromptPlaceholder": "示例：Lato 很有礼貌", "cancel": "取消", "continue": "继续", "successToast": "客服设置成功！", "customizationUpdatedToast": "个性化设置已更新！", "failedToCreate": "创建客服失败", "failedToUpdate": "更新个性化设置失败", "alreadyCreated": "您已创建沙盒环境，请使用现有沙盒。如需重置，请联系支持团队。", "somethingWentWrong": "发生错误，请稍后再试", "upload": "拖放文件至此或从设备上传", "uploadSub": "支持文件类型：DOC、DOCX、PDF。最大文件大小：5MB。", "noFileSelected": "未选择文件", "changeCon": "点击更换客服头像", "table": {"documentName": "文档名称", "sn": "序号", "fileName": "文件名", "size": "大小"}}, "TestChecklistPage": {"title": "沙盒测试清单", "checklist": {"chatFunctionality": {"title": "1. 聊天功能", "goal": "目标：确保 {concierge} 能理解用户输入并根据知识库与优先级做出回应。", "askGeneral": "提出通用问题，测试 {concierge} 是否能使用知识库进行回答", "testSimilarity": "测试词语/短语相似性，例如：\"版本\"、\"帮助\"等变体"}, "ticketCreation": {"title": "2. 通过聊天创建工单", "goal": "目标：测试 {concierge} 是否能根据聊天自动创建工单。", "confirmTicket": "确认 {concierge} 是否记录了包含问题类型、优先级、摘要等信息的工单", "checkConfirmation": "检查 {concierge} 是否回应（如“您的工单已提交”）"}, "ticketCreationEmail": {"title": "3. 通过邮件创建工单", "goal": "目标：验证 {concierge} 是否能处理支持邮箱中的请求并生成工单。", "sendSupportRequest": "请使用您的 ai-wk 登录邮箱发送请求至 <EMAIL>", "observeReply": "观察 {concierge} 是否自动回复确认收到并创建了工单", "checkConfirmationEmail": "检查是否收到工单确认邮件"}, "ticketCreationCall": {"title": "4. 通过呼叫中心创建工单", "goal": "目标：验证 {concierge} 是否能根据语音通话生成支持工单。", "sendSupportRequest": "点击“发起呼叫”以启动浏览器通话，并在通话过程中描述问题", "observeReply": "{concierge} 应能将请求转录并提取关键信息创建工单", "checkConfirmationEmail": "在内部支持界面中确认收到工单"}, "internalSupportView": {"title": "5. 内部支持系统视图", "goal": "目标：查看内部团队如何看到已记录的问题。", "checkDashboard": "检查内部支持仪表板", "locateTickets": "找到您提交的工单（邮件/聊天）", "verifyMetadata": "核查元数据：用户信息、联系方式、问题类型、来源（邮件/聊天）、时间戳、消息记录", "checkAttachments": "确认是否正确保存了附件（如有）或日志"}, "escalationReferral": {"title": "6. 升级/转交流程", "goal": "目标：测试内部问题升级与转交流程。", "selectIssue": "选择一个已记录的问题", "referInterface": "使用界面将其转交给后台或管理人员", "confirmAssignee": "确认系统中已更新受理人及状态"}, "markResolved": {"title": "7. 标记问题为已解决", "goal": "目标：确保解决流程顺畅并完整记录。", "markResolved": "将测试问题标记为已解决", "confirmTimestamp": "确认系统记录了解决时间与备注"}}}, "ProfileHeader": {"creditActions": "代币操作", "creditDialog": {"title": "添加/扣除代币", "description": "请输入代币数，或使用按钮调整客户余额。", "currentBalance": "当前余额", "amountLabel": "要添加或扣除的代币数量", "increment": "+", "decrement": "−", "narration": "备注说明", "saveChange": "保存更改"}, "confirmDialog": {"title": "确认更改", "description": "您确定要{action} {amount} 代币？", "add": "添加", "subtract": "扣除", "confirm": "是的，确认"}, "actions": {"cancel": "取消", "updating": "更新中..."}, "toast": {"suspendSuccess": "客户账号已成功暂停！", "suspendError": "暂停客户账号失败。", "reactivateSuccess": "账号已成功重新激活！", "reactivateError": "账号激活失败。", "resetSuccess": "重设链接发送成功", "resetError": "重设链接发送失败", "creditsUpdated": "代币余额已更新！", "updateCreditsError": "更新代币余额失败"}}, "CustomersTab": {"details": "用户详情", "transactions": "交易记录", "chat": "聊天记录", "tickets": "工单"}, "CustomerInfoCard": {"loading": "正在加载客户信息...", "notFound": "未找到客户信息。", "fullName": "全名", "email": "邮箱", "availableCredits": "可用积分", "ticketsRaised": "已提交工单", "accountStatus": "账号状态", "active": "活跃"}, "ChatWindow": {"agentJoined": "人工客服已于 {time} 加入对话", "agentResolved": "人工客服已于 {time} 解决对话"}, "ChatMessage": {"user": "用户", "yumi": "<PERSON><PERSON>"}, "ChatLogTab": {"noSessions": "未找到聊天会话", "noHistory": "该用户暂无聊天记录。", "selectSession": "请选择一个聊天会话", "chatTab": "聊天页", "callTab": "通话页"}, "Sidebar": {"title": "<PERSON><PERSON> 沙盒", "setup": "设置", "chatWithYumi": "聊天对象", "internalSupport": "内部支持", "testChecklist": "测试清单", "callCenter": "呼叫中心"}, "Layout": {"setup": "设置", "chatWithYumi": "聊天对象", "internalSupport": "内部支持", "testChecklist": "测试清单", "callCenter": "呼叫中心", "sandbox": "沙盒"}, "TicketForm": {"title": "工单表单", "fullName": "全名", "yourName": "您的姓名", "emailAddress": "电子邮箱", "emailPlaceholder": "<EMAIL>", "issueType": "问题类型", "selectIssueType": "选择问题类型", "priorityLevel": "优先级", "selectPriorityLevel": "选择优先级", "subject": "主题", "subjectPlaceholder": "工单主题", "attachments": "附件", "addAttachment": "添加附件", "removeFile": "移除文件", "message": "消息内容", "describeYourIssue": "请描述您的问题", "reset": "重置", "confirmAndSend": "确认并提交工单", "submit": "提交", "issueTypeList": {"GI": "一般咨询", "PSQ": "产品或服务问题", "OTI": "订单或交易问题", "BPQ": "账单或付款问题", "AAH": "账户或访问协助", "TSE": "技术支持或错误", "FS": "反馈或建议", "PSC": "隐私或敏感问题"}}, "ChatBox": {"placeholder": "请输入您的消息...", "addAttachment": "添加附件", "removeFile": "移除文件", "send": "发送"}}}