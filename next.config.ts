import type { NextConfig } from "next";
import createNextIntlPlugin from 'next-intl/plugin';

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "avatars.githubusercontent.com",
      },
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
      },
      {
        protocol: "https",
        hostname: "randomuser.me",
      },
      {
        protocol: "https",
        hostname: "inlnpazfunssixvorztn.supabase.co",
      },
      {
        protocol: "https",
        hostname: "tycccztwtuaemnjxsprh.supabase.co",
      },
      {
        protocol: "https",
        hostname: "img.youtube.com",
      },
      {
        protocol: "https",
        hostname: "yumisandbox-production.up.railway.app",
        pathname: "/api/ticket/get_attachment",
      },
      {
        protocol: "https",
        hostname: "yumisandboxstag-production.up.railway.app",
        pathname: "/api/ticket/get_attachment",
      },
      {
        protocol: "https",
        hostname: "yumisandboxprod-production.up.railway.app",
        pathname: "/api/ticket/get_attachment",
      },
    ],
  },
  /* config options here */
};

const withNextIntl = createNextIntlPlugin();
export default withNextIntl(nextConfig);
