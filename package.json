{"name": "ai-wk-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.72.2", "@tanstack/react-query-devtools": "^5.84.1", "@tanstack/react-virtual": "^3.13.6", "@telegram-auth/react": "^1.0.4", "@untitled-ui/icons-react": "^0.1.4", "@uploadthing/react": "^7.3.0", "@vitalets/google-translate-api": "^9.2.1", "aos": "^2.3.4", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "countries-list": "^3.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "flag-icon-css": "^4.1.7", "framer-motion": "^12.5.0", "input-otp": "^1.4.2", "js-cookie": "^3.0.5", "lightweight-charts": "^4.2.3", "lucide-react": "^0.479.0", "luxon": "^3.6.1", "moment": "^2.30.1", "ms": "^2.1.3", "next": "15.2.2", "next-intl": "^4.3.1", "next-themes": "^0.4.6", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-country-flag": "^3.1.0", "react-day-picker": "^9.6.2", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.54.2", "react-markdown": "^10.1.0", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.12", "react-qr-code": "^2.0.15", "react-select-country-list": "^2.2.3", "react-slick": "^0.30.3", "react-spinners": "^0.15.0", "react-window": "^1.8.11", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.5", "swiper": "^11.2.6", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.5.2", "uuid": "^11.1.0", "vaul": "^1.1.2", "victory": "^37.3.6", "yup": "^1.6.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/js-cookie": "^3.0.6", "@types/ms": "^2.1.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-slick": "^0.23.13", "eslint": "^9", "eslint-config-next": "15.2.2", "tailwindcss": "^4", "typescript": "^5"}}