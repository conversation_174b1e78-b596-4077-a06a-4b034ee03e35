from fastapi import APIRouter
from typing import List
from models.schemas import AgentConfigSchema, RiskTolerance, DataAccess

router = APIRouter()

example_agents: List[AgentConfigSchema] = [
    # 1. Fundamentalist Long-Only Fund
    AgentConfigSchema(
        name="Fundamentalist Long-Only Fund",
        agent_type="Fundamentalist",
        description="A long-only fund that makes investment decisions based on fundamental analysis of companies. They have a long-term investment horizon.",
        risk_tolerance=RiskTolerance.LOW,
        reaction_speed_percentile=20,
        liquidity_constraint=0.1,
        behavioral_biases="Resistant to market noise, but can be slow to react to new information. Confirmation bias towards their own research.",
        objective="Achieve long-term capital appreciation by investing in undervalued assets.",
        initial_capital=500_000_000,
        initial_positions=0,
        long_margin_call_rate=0.02,
        short_margin_call_rate=0.02,
        allow_limit_orders=True,
        data_access=[DataAccess.FUNDAMENTAL, DataAccess.NEWS]
    ),
    # 2. Quantitative Hedge Fund
    AgentConfigSchema(
        name="Quantitative Hedge Fund",
        agent_type="Arbitrageur",
        description="A sophisticated hedge fund using complex mathematical models and high-speed execution to exploit market inefficiencies.",
        risk_tolerance=RiskTolerance.HIGH,
        reaction_speed_percentile=99,
        liquidity_constraint=0.05,
        behavioral_biases="Model-driven, so less prone to emotional biases, but susceptible to model risk and overfitting.",
        objective="Generate absolute returns regardless of market direction, often through statistical arbitrage or other quantitative strategies.",
        initial_capital=200_000_000,
        initial_positions=0,
        long_margin_call_rate=0.02,
        short_margin_call_rate=0.02,
        allow_limit_orders=True,
        data_access=[DataAccess.TECHNICAL_ANALYSIS, DataAccess.NEWS]
    ),
    # 3. Trend-Following CTA
    AgentConfigSchema(
        name="Trend-Following CTA",
        agent_type="Trend Follower",
        description="A Commodity Trading Advisor that systematically follows market trends across various asset classes.",
        risk_tolerance=RiskTolerance.MEDIUM,
        reaction_speed_percentile=80,
        liquidity_constraint=0.2,
        behavioral_biases="Herding behavior is inherent to the strategy. Prone to whipsaws in ranging markets.",
        objective="Capture profits from sustained market trends (up or down).",
        initial_capital=100_000_000,
        initial_positions=0,
        long_margin_call_rate=0.02,
        short_margin_call_rate=0.02,
        allow_limit_orders=True,
        data_access=[DataAccess.TECHNICAL_ANALYSIS]
    ),
    # 4. High-Frequency Arbitrageur
    AgentConfigSchema(
        name="High-Frequency Arbitrageur",
        agent_type="Arbitrageur",
        description="Exploits tiny, fleeting price discrepancies using high-speed trading technology.",
        risk_tolerance=RiskTolerance.HIGH,
        reaction_speed_percentile=100,
        liquidity_constraint=0.01,
        behavioral_biases="Purely algorithmic. Main risk is technology failure or 'flash crashes'.",
        objective="Profit from risk-free arbitrage opportunities that exist for milliseconds.",
        initial_capital=50_000_000,
        initial_positions=0,
        long_margin_call_rate=0.02,
        short_margin_call_rate=0.02,
        allow_limit_orders=True,
        data_access=[DataAccess.TECHNICAL_ANALYSIS]
    ),
    # 5. Designated Market Maker
    AgentConfigSchema(
        name="Designated Market Maker",
        agent_type="Market Maker",
        description="Provides liquidity to the market by continuously quoting bid and ask prices, profiting from the spread.",
        risk_tolerance=RiskTolerance.LOW,
        reaction_speed_percentile=95,
        liquidity_constraint=0.0,
        behavioral_biases="Driven by order flow and inventory management. Risk of holding inventory when market gaps.",
        objective="Profit from the bid-ask spread while maintaining a neutral position.",
        initial_capital=75_000_000,
        initial_positions=0,
        long_margin_call_rate=0.02,
        short_margin_call_rate=0.02,
        allow_limit_orders=True,
        data_access=[DataAccess.TECHNICAL_ANALYSIS]
    ),
    # 6. Retail Momentum Trader
    AgentConfigSchema(
        name="Retail Momentum Trader",
        agent_type="Noise Trader",
        description="An individual investor who chases recent performance and follows hot trends, often driven by social media.",
        risk_tolerance=RiskTolerance.HIGH,
        reaction_speed_percentile=60,
        liquidity_constraint=0.5,
        behavioral_biases="FOMO (Fear Of Missing Out), herding, disposition effect (selling winners too early, holding losers too long).",
        objective="Ride the wave of popular stocks for short-term gains.",
        initial_capital=50_000,
        initial_positions=0,
        long_margin_call_rate=0.02,
        short_margin_call_rate=0.02,
        allow_limit_orders=False,
        data_access=[DataAccess.NEWS, DataAccess.TECHNICAL_ANALYSIS]
    ),
    # 7. Cautious Retail Investor
    AgentConfigSchema(
        name="Cautious Retail Investor",
        agent_type="Noise Trader",
        description="A typical individual investor saving for retirement. Tries to invest wisely but can be influenced by news headlines and fear.",
        risk_tolerance=RiskTolerance.LOW,
        reaction_speed_percentile=30,
        liquidity_constraint=0.6,
        behavioral_biases="Loss aversion, confirmation bias, overreacts to negative news.",
        objective="Grow savings over the long term, with a focus on capital preservation.",
        initial_capital=150_000,
        initial_positions=0,
        long_margin_call_rate=0.02,
        short_margin_call_rate=0.02,
        allow_limit_orders=True,
        data_access=[DataAccess.NEWS]
    ),
    # 8. Activist Hedge Fund
    AgentConfigSchema(
        name="Activist Hedge Fund",
        agent_type="Fundamentalist",
        description="A hedge fund that takes large stakes in companies to influence management and unlock shareholder value.",
        risk_tolerance=RiskTolerance.MEDIUM,
        reaction_speed_percentile=25,
        liquidity_constraint=0.1,
        behavioral_biases="Overconfidence in their ability to effect change. Confirmation bias regarding the target company's flaws.",
        objective="Force changes at a target company to increase its stock price.",
        initial_capital=750_000_000,
        initial_positions=0,
        long_margin_call_rate=0.02,
        short_margin_call_rate=0.02,
        allow_limit_orders=True,
        data_access=[DataAccess.FUNDAMENTAL, DataAccess.NEWS]
    ),
    # 9. Large Pension Fund
    AgentConfigSchema(
        name="Large Pension Fund",
        agent_type="Fundamentalist",
        description="A large institutional investor with a very long-term horizon, managing retirement funds for a large number of beneficiaries. Highly regulated.",
        risk_tolerance=RiskTolerance.LOW,
        reaction_speed_percentile=10,
        liquidity_constraint=0.05,
        behavioral_biases="Herding (to avoid deviating from peers), conservatism, and aversion to volatility.",
        objective="Generate stable, long-term returns to meet pension obligations, with a strong focus on risk management.",
        initial_capital=1_000_000_000,
        initial_positions=0,
        long_margin_call_rate=0.02,
        short_margin_call_rate=0.02,
        allow_limit_orders=True,
        data_access=[DataAccess.FUNDAMENTAL]
    ),
    # 10. Contrarian Hedge Fund
    AgentConfigSchema(
        name="Contrarian Hedge Fund",
        agent_type="Fundamentalist",
        description="A fund that purposefully goes against prevailing market trends, buying assets that are out of favor.",
        risk_tolerance=RiskTolerance.HIGH,
        reaction_speed_percentile=40,
        liquidity_constraint=0.2,
        behavioral_biases="Prone to 'value traps'. Requires strong conviction and can underperform for long periods.",
        objective="Identify and invest in mispriced assets by betting against popular sentiment.",
        initial_capital=150_000_000,
        initial_positions=0,
        long_margin_call_rate=0.02,
        short_margin_call_rate=0.02,
        allow_limit_orders=True,
        data_access=[DataAccess.FUNDAMENTAL, DataAccess.NEWS, DataAccess.TECHNICAL_ANALYSIS]
    ),
]


@router.get("/examples", response_model=List[AgentConfigSchema])
async def get_example_agents():
    """
    Returns a list of 10 example agent configurations representing various market players.
    """
    return example_agents
