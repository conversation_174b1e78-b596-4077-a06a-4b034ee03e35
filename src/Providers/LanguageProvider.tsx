// src/contexts/LanguageContext.tsx
// @ts-nocheck

"use client";

import { Locale } from "@/i18n/config";
import { getUserLocale, setUserLocale } from "@/services/locale";
import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  startTransition,
  useTransition,
  useEffect,
} from "react";

export type LanguageCode = "en" | "zh" | "ja" | "fr" | "es";

interface LanguageContextType {
  currentLanguage: LanguageCode;
  setLanguage: (lang: LanguageCode) => void;
  languages: { code: LanguageCode; name: string; nativeName: string }[];
  isPending: boolean;
  onChange: (lang: string) => void;
  currentLocale: string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined
);

const languages = [
  { code: "en" as const, name: "English", nativeName: "English" },
  { code: "zh" as const, name: "Chinese (Simplified)", nativeName: "简体中文" },
  { code: "ja" as const, name: "Japanese", nativeName: "日本語" },
  { code: "fr" as const, name: "French", nativeName: "Français" },
  { code: "es" as const, name: "Spanish", nativeName: "Español" },
];

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>("en");

  const setLanguage = (lang: LanguageCode) => {
    console.log("Setting language to:", lang); // Debug log
    setCurrentLanguage(lang);
    if (typeof window !== "undefined") {
      localStorage.setItem("preferred-language", lang);
    }
  };

  // Load saved language on mount
  React.useEffect(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("preferred-language") as LanguageCode;
      if (saved && languages.some((l) => l.code === saved)) {
        console.log("Loading saved language:", saved); // Debug log
        setCurrentLanguage(saved);
      }
    }
  }, []);

  // Debug log for current language changes
  React.useEffect(() => {
    console.log("Current language changed to:", currentLanguage);
  }, [currentLanguage]);

  const [isPending, startTransition] = useTransition();
  const [currentLocale, setCurrentLocale] = useState("");

  const onChange = (value: string): void => {
    const locale = value as Locale;
    startTransition(() => {
      setUserLocale(locale);
      setCurrentLocale(locale);
    });
  };

  useEffect(() => {
    getUserLocale().then((res) => {
      setCurrentLocale(res as string);
    });
  }, []);

  //const userLang = navigator.language || navigator?.userLanguage;
  function isChineseLanguage() {
    const langs = navigator.languages || [
      navigator.language || navigator.userLanguage,
    ];

    return langs.some((lang) => {
      const lower = lang.toLowerCase();
      return (
        lower === "zh" || // Generic Chinese
        lower.startsWith("zh-") // zh-CN, zh-TW, zh-HK, etc.
      );
    });
  }

  const [autoDetectEnabled, setAutoDetectEnabled] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const enabled = localStorage.getItem("autoDetect") ? true : false;
      setAutoDetectEnabled(enabled);
      if (enabled && isChineseLanguage()) {
        onChange("zh-CN");
      }
    }
  }, []);

  return (
    <LanguageContext.Provider
      value={{
        currentLanguage,
        setLanguage,
        languages,
        isPending,
        onChange,
        currentLocale,
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
}
