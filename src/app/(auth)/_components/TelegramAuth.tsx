// components/TelegramLogin.tsx
// @ts-nocheck
"use client";

import { useEffect, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useLogin, useSubmitQuery } from "@/services/api_hooks";
import { Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import Moon<PERSON>oader from "react-spinners/MoonLoader";
import { botName } from "@/config/baseUrl";

declare global {
  interface Window {
    onTelegramAuth?: (user: TelegramUser) => void;
    Telegram?: {
      Login?: {
        auth: (
          params: {
            bot_id: number;
            request_access?: boolean;
            lang?: string;
          },
          callback: (data: TelegramUser | false) => void
        ) => void;
      };
    };
  }
}

interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  photo_url?: string;
  auth_date: number;
  hash: string;
}

interface TelegramLoginProps {
  /** numeric bot ID */
  botId?: string;
  /** bot username */
  botUsername?: string;
  /** ask for write access */
  requestAccess?: boolean;
  /** widget language code */
  lang?: string;
  /** additional container classes */
  className?: string;
  /** button corner radius in px */
  cornerRadius?: number;
}

export default function TelegramLogin({
  botId = 8047120185,
  botUsername = botName,
  requestAccess = true,
  lang = "en",
  className = "",
  cornerRadius = 10,
  actionText = "Sign up with Telegram",
}: TelegramLoginProps) {
  const [ready, setReady] = useState(false);
  const router = useRouter();
  const fallbackContainerRef = useRef<HTMLDivElement>(null);

  const { mutateAsync: loginWithTelegram, isPending } = useLogin(
    "/auth/telegram",
    {
      onSuccess() {
        toast.success("Logged in via Telegram!", {
          position: "top-right",
          className: "p-4",
        });
        router.push("/dashboard/home");
      },
      onError(err: any) {
        toast.error(err.response?.data?.message || "Telegram login failed", {
          position: "top-right",
          className: "p-4",
        });
      },
    }
  );

  // Load Telegram widget script
  useEffect(() => {
    // Define the global callback for the default widget (fallback)
    window.onTelegramAuth = (user: TelegramUser) => {
      loginWithTelegram(user);
    };

    if (window.Telegram?.Login?.auth) {
      setReady(true);
      return;
    }

    const script = document.createElement("script");
    script.src = "https://telegram.org/js/telegram-widget.js?22";
    script.async = true;
    script.onload = () => setReady(true);
    script.onerror = () => {
      console.error("Telegram script failed to load");
      // If script fails to load properly, inject the fallback button
      if (fallbackContainerRef.current) {
        fallbackContainerRef.current.innerHTML = "";
        const fallbackScript = document.createElement("script");
        fallbackScript.src = "https://telegram.org/js/telegram-widget.js?22";
        fallbackScript.setAttribute("data-telegram-login", botUsername);
        fallbackScript.setAttribute("data-size", "large");
        fallbackScript.setAttribute("data-userpic", "false");
        fallbackScript.setAttribute(
          "data-request-access",
          requestAccess ? "write" : "read"
        );
        fallbackScript.setAttribute("data-onauth", "onTelegramAuth(user)");
        fallbackContainerRef.current.appendChild(fallbackScript);
      }
    };
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
      delete window.onTelegramAuth;
    };
  }, [loginWithTelegram, botUsername, requestAccess]);

  // When clicked, open Telegram OAuth
  const handleClick = () => {
    if (!window.Telegram?.Login?.auth) {
      toast.error("Telegram login not ready");
      return;
    }

    window.Telegram.Login.auth(
      {
        bot_id: Number(botId),
        request_access: requestAccess,
        lang,
      },
      (dataOrFalse: TelegramUser | false) => {
        if (!dataOrFalse) {
          // User canceled the login
          return;
        }
        const jsonStrData = JSON.stringify(dataOrFalse);
        // Send the raw payload to your API
        loginWithTelegram(jsonStrData);
      }
    );
  };

  return (
    <>
      {/* Custom button */}
      <Button
        variant="outline"
        type="button"
        onClick={handleClick}
        disabled={!ready || isPending}
        className={`flex mt-2 md:max-w-[220px] w-full items-center rounded-md justify-center gap-2 cursor-pointer h-[40px] ${
          !ready ? "opacity-50 cursor-not-allowed" : "hover:bg-gray-50"
        }`}
      >
        <span>{actionText}</span>
        <Send className="w-5 h-5" color="#0088CC" />
        <div style={{ display: "flex" }}>
          <MoonLoader size={17} color={"black"} loading={isPending} />
        </div>
      </Button>

      {/* Fallback container if the custom button approach fails */}
      <div
        ref={fallbackContainerRef}
        className={`hidden ${!ready ? "block" : ""}`}
      />

      {isPending && <p className="mt-2 text-sm text-gray-500">Logging in…</p>}
    </>
  );
}
