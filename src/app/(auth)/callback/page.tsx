// app/auth/callback/page.tsx
"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
// Update path if needed
import { Card } from "@/components/ui/card";
import { handleGoogleCallback } from "@/services/api_hooks";

export default function GoogleCallbackPage() {
  const [status, setStatus] = useState("Processing your sign in...");
  const [error, setError] = useState("");
  const router = useRouter();
  //   cont searchParams = useSearchParams();

  useEffect(() => {
    const processOAuthCallback = async () => {
      try {
       
        // Get tokens from either search params or hash params
        const searchParams = new URLSearchParams(window.location.search);
        const hashParams = new URLSearchParams(
          window.location.hash.substring(1)
        );

        const accessToken =
          searchParams.get("access_token") || hashParams.get("access_token");
        const refreshToken =
          searchParams.get("refresh_token") || hashParams.get("refresh_token");

        if (!accessToken || !refreshToken) {
          throw new Error("Missing authentication tokens in callback");
        }

        // Send these tokens to your backend to complete authentication
        await handleGoogleCallback({
          access_token: accessToken,
          refresh_token: refreshToken,
        });

        setStatus("Sign in successful! Redirecting...");

        // Redirect to dashboard after successful authentication
        setTimeout(() => {
          router.push("/dashboard/home");
        }, 1500);
      } catch (error: any) {
        console.error("Error during OAuth callback:", error);
        setError("Authentication failed. Please try again.");

        // Redirect to login page after error
        setTimeout(() => {
          router.push("/login");
        }, 3000);
      }
    };

    processOAuthCallback();
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-[#FAFAFA]">
      <Card className="w-full max-w-md p-8 shadow-lg">
        <div className="text-center">
          {error ? (
            <div className="text-red-600 font-medium text-lg">{error}</div>
          ) : (
            <div>
              <h1 className="text-2xl font-bold mb-4">Sign In</h1>
              <div className="animate-pulse text-gray-700">{status}</div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
