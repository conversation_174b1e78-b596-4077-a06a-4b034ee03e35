// components/EmailVerification.tsx
"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { useSubmitQuery } from "@/services/api_hooks";
import { toast } from "sonner";

const EmailVerification: React.FC = () => {
  const getEmail = () => {
    if (typeof window !== "undefined") {
      const searchParams = new URLSearchParams(window.location.search);
      return searchParams.get("email");
    }
    return null;
  };

  const email = getEmail();

  

  const { mutateAsync: resend, isPending: submitting } = useSubmitQuery(
    "/auth/resend",
    "POST",
    {
      onSuccess() {
        toast.success("Submitted successfully!", {
          position: "top-right",
          className: "p-4",
        });
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || "Submission failed", {
          position: "top-right",
          className: "p-4",
        });
      },
    }
  );

  const handleResend = async (data: any) => {
    try {
      await resend({
        email: email,
      });
    } catch (error) {}
  };
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="w-full max-w-md  bg-white rounded-lg shadow-md">
        <div className="flex flex-col items-center justify-center">
          {/* Envelope Illustration */}
          <div className="w-full h-[230px] rounded-t-lg bg-[#F7FAFF] flex items-center justify-center">
            <Image
              src="/email-sent.svg"
              alt="HermesX Logo"
              width={200}
              height={200}
              priority
              className="object-contain"
            />
          </div>

          {/* Text Content */}
          <div className="p-8 text-[13px] text-[#0F172A]">
            <h2 className="text-[16px] font-[500] text-[#0F172A] mb-2">
              Verify Your Email Address
            </h2>
            <p className="text-gray-600  mb-4">
              A verification email has been sent to Click on the confirmation
              link to continue to the dashboard.
            </p>

            <div className="text-gray-600 text-sm">
              Did not receive the link?{" "}
              <button
                onClick={handleResend}
                className="text-blue-600 cursor-pointer underline hover:text-blue-800 hover:underline focus:outline-none"
              >
                {submitting ? "Resending..." : "Resend Link"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailVerification;
