"use client";

// import { Metadata } from "next";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";

import Image from "next/image";

import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";

import * as yup from "yup";
import Logo from "@/components/Logo";
import { logo2, newLogo } from "@/assets";
import Moon<PERSON>oader from "react-spinners/MoonLoader";
import { useState } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { api } from "@/services/api_hooks";

// export const metadata: Metadata = {
//   title: "Login",
//   description: "Login to your account",
// };



const schema = yup
  .object({
    email: yup.string().required("Email is required"),
  })
  .required();

export default function ForgotPassword() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    control,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (formData: any) => {
    try {
      setLoading(true);
  
      // Make API request using axios
      const response = await api.post("/auth/forgot-password", {
        email: formData.email
      });
  
      setLoading(false);
      toast.success("Link sent to your email", {
        position: "top-right",
        className: "p-4",
      });
      
    } catch (err: any) {
      setLoading(false);
      console.error("Password reset request error:", err);
      toast.error(
        err?.response?.data?.message || 
        "Failed to send reset link. Please try again."
      ),{
        position: "top-right",
        className: "p-4",
      }
    }
  };

  return (
    <div className="lg:h-screen overflow-auto flex flex-col bg-[#FAFAFA] px-[5%] pb-6">
      <div className="py-3">
        <Logo textColor="black" textSize="15px" height={50}width={50} />
      </div>
      <Card className="w-full max-w-[484px] m-auto bg-white border-none shadow-md rounded-md py-7 px-7 ">
        {/* <Link href="/login">
          <MoveLeft />
        </Link> */}

        <div className="flex flex-col items-center space-y-1 ">
          <Image src="/logo-black.svg" alt="logo" width={50} height={50} />

          <p className="text-[24px] font-semibold text-center">
            Forgot your password
          </p>
          <p className="text-[14px] text-gray-500 text-center">
            Enter your email address below, you will receive a link in your
            inbox to continue.
          </p>
        </div>

        <CardContent className="p-0">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div className="">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Email
              </label>
              <Input
                id="email"
                type="email"
                className={`w-full h-[34px] ${
                  errors.email ? "border-red-500" : ""
                }`}
                {...register("email")}
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>

            <Button
              className="w-full disabled:opacity-[0.7] flex flex-row bg-[#121212] hover:bg-[] text-white h-[40px] cursor-pointer"
              type="submit"
              disabled={loading}
            >
              Send
              <div style={{ display: "flex" }}>
                <MoonLoader size={17} color={"white"} loading={loading} />
              </div>
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
