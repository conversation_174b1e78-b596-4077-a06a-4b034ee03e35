//@ts-nocheck

"use client";

import { useState, useRef } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Eye, EyeOff } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import Logo from "@/components/Logo";
import { logo2 } from "@/assets";
import ReCAPTCHA from "react-google-recaptcha";
import { schema } from "./schema";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useRegister, useSignInWithGoogle } from "@/services/api_hooks";
import dynamic from "next/dynamic";
import MoonLoader from "react-spinners/MoonLoader";
import TelegramLogin from "../_components/TelegramAuth";

export default function InvitePage() {
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [captchaValue, setCaptchaValue] = useState<string | null>(null);
  const recaptchaRef = useRef<ReCAPTCHA>(null);
  const router = useRouter();
  const [showTelegram, setShowTelegram] = useState(false);

  const { mutate: signInWithGoogle, isPending: loadingGoogle } =
    useSignInWithGoogle({
      onError: (error) => {
        // Handle error (e.g., show a toast notification)
      },
    });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      firstName: "",
      lastName: "",
      username: "",
      email: "",
      password: "",
    },
  });

  // Use the register mutation hook
  const registerMutation = useRegister({
    onSuccess: (response) => {
      toast.success("Sign up Successful", {
        position: "top-right",
        className: "p-4",
      });

      setSuccess(
        "Registration successful! Please check your email to confirm your account."
      );

      // Reset reCAPTCHA
      if (recaptchaRef.current) {
        recaptchaRef.current.reset();
      }
      setCaptchaValue(null);
      reset();

      // Redirect if needed
      // router.push("/dashboard");
    },
    onError: (error: any) => {
      console.error(error.response.data.message);
      setError(error.response.data.message || error.response.data.error);
    },
  });

  const handleRecaptchaChange = (value: string | null) => {
    setCaptchaValue(value);
  };

  const onSubmit = async (formData: any) => {
    setError("");

    if (!captchaValue) {
      setError("Please complete the CAPTCHA verification");
      return;
    }

    // Prepare the data for the API
    const registerData = {
      email: formData.email,
      password: formData.password,
      username: formData.username,
      firstname: formData.firstName,
      lastname: formData.lastName,
      recaptchaToken: captchaValue,
    };

    // Call the mutation
    registerMutation.mutate(registerData);
  };

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  return (
    <div className="min-h-screen overflow-auto bg-[#FAFAFA] px-[5%] pb-6">
      <div className="py-3">
      <Logo textColor="black" textSize="15px" height={38}width={34} />
      </div>
      <Card className="w-full max-w-[485px] mx-auto bg-white border-none shadow-md rounded-md py-3 px-4">
        <div className="flex flex-col items-center space-y-1">
          <Image src={logo2} alt="logo" width={34} height={38} />

          <p className="text-[24px] font-semibold text-center">
            Alpharithinmv has invited you to join their organization
          </p>
        </div>

        <div className="flex flex-wrap items-center justify-between gap-2">
          <Button
            variant="outline"
            className="flex mt-2 md:max-w-[220px] w-full items-center rounded-md justify-center gap-2 cursor-pointer h-[40px]"
            type="button"
            onClick={() => signInWithGoogle()}
          >
            Sign up in with Google
            <Image src="/google-icon.svg" alt="Google" width={20} height={20} />
            <div style={{ display: "flex" }}>
              <MoonLoader size={17} color={"black"} loading={loadingGoogle} />
            </div>
          </Button>

          <TelegramLogin
            // botName={
            //   process.env.NEXT_PUBLIC_TELEGRAM_BOT_USERNAME ||
            //   "AIWk_telegram_bot"
            // }
            buttonSize="large"
            showUserPic={false}
            cornerRadius={10}
            className="w-full flex justify-center h-[46px]"
          />
        </div>

        <hr className="my-6" />

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded mb-4 text-sm">
            {error}
          </div>
        )}

        {success && (
          <p className="bg-green-50 border border-green-200 text-green-700 text-center px-4 py-2 rounded mb-4 text-sm">
            {success}
          </p>
        )}

        <CardContent className="p-0">
          <form className="space-y-3" onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="firstName"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  First Name
                </label>
                <Input
                  id="firstName"
                  type="text"
                  className={`w-full h-[34px] ${
                    errors.firstName ? "border-red-500" : ""
                  }`}
                  {...register("firstName")}
                />
                {errors.firstName && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.firstName.message}
                  </p>
                )}
              </div>
              <div>
                <label
                  htmlFor="lastName"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Last Name
                </label>
                <Input
                  id="lastName"
                  type="text"
                  className={`w-full h-[34px] ${
                    errors.lastName ? "border-red-500" : ""
                  }`}
                  {...register("lastName")}
                />
                {errors.lastName && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.lastName.message}
                  </p>
                )}
              </div>
            </div>

            <div className="">
              <label
                htmlFor="username"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Username
              </label>
              <Input
                id="username"
                type="text"
                className={`w-full h-[34px] ${
                  errors.username ? "border-red-500" : ""
                }`}
                {...register("username")}
              />
              {errors.username && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.username.message}
                </p>
              )}
            </div>

            <div className="">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Email
              </label>
              <Input
                id="email"
                type="email"
                className={`w-full h-[34px] ${
                  errors.email ? "border-red-500" : ""
                }`}
                {...register("email")}
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>

            <div className="">
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Password
              </label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  className={`w-full h-[34px] ${
                    errors.password ? "border-red-500" : ""
                  }`}
                  {...register("password")}
                />
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-500" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-500" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.password.message}
                </p>
              )}
            </div>

            <div className="flex items-center justify-center">
              <ReCAPTCHA
                ref={recaptchaRef}
                sitekey={"6LfrIxIrAAAAALqqsoVEQwnLBoFrfEeoQCLnDH3c" || ""}
                onChange={handleRecaptchaChange}
              />
            </div>

            <Button
              className="w-full bg-[#121212] hover:bg-primary text-white py-2 h-[40px] cursor-pointer"
              type="submit"
              disabled={registerMutation.isPending || !captchaValue}
            >
              {registerMutation.isPending ? "Signing Up..." : "Sign Up"}
            </Button>

            <div className="text-sm text-center my-4">
              Already have an account?{" "}
              <Link
                href="/login"
                className="text-black hover:underline font-[700]"
              >
                Login Here
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
