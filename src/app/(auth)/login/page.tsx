// @ts-nocheck

"use client";

import { useState, useRef } from "react";
import { useTranslations } from 'next-intl';
import Link from "next/link";
import Image from "next/image";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import ReCAPTCHA from "react-google-recaptcha";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Logo from "@/components/Logo";
import { logo2 } from "@/assets";

import { useLogin, useSignInWithGoogle } from "@/services/api_hooks";
import TelegramLogin from "../_components/TelegramAuth";
import MoonLoader from "react-spinners/MoonLoader";

export default function SignInPage() {

  // use next-intl for i18n
  const t = useTranslations("UserLogin");
  const tForm = useTranslations("Form");
  const tFormLogin = useTranslations("Form.Login");
  const tGlobal = useTranslations("global");

  const schema = yup
  .object({
    email: yup.string().email(tForm("email.dataFormatError")).required(tForm("email.required")),
    password: yup
      .string()
      .required(tForm("password.required"))
      .min(8, tForm("password.minError"))
      .matches(/[0-9]/, tForm("password.containNumberError"))
      .matches(/[a-z]/, tForm("password.containLowercaseLetter"))
      .matches(/[A-Z]/, tForm("password.containUppercaseLetter"))
      .matches(/[!@#$%^&*(),.?":{}|<>]/, tForm("password.containSpecialCharacter")),
  })
  .required();

  const router = useRouter();
  // const recaptchaRef = useRef<ReCAPTCHA>(null);
  // const [captchaValue, setCaptchaValue] = useState<string | null>(null);
  const [error, setError] = useState<string>("");

  const { mutate: signInWithGoogle, isPending:loadingGoogle } = useSignInWithGoogle({
    onError: (error) => {
      // Handle error (e.g., show a toast notification)
    },
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({ resolver: yupResolver(schema) });

  const { mutateAsync: login, isPending } = useLogin("/auth/login", {
    onSuccess: () => {
      router.push("/dashboard/home");
      toast.success(tGlobal("toast.LoginSuccessful"), {
        position: "top-right",
        className: "p-4",
      });

      // reset captcha
      // recaptchaRef.current?.reset();
      // setCaptchaValue(null);
    },
    onError: (err: any) => {
      console.log(err, "dd");
      toast.error(err.response?.data?.error, {
        position: "top-right",
        className: "p-4",
      });
      // recaptchaRef.current?.reset();
      // setCaptchaValue(null);
    },
  });

  const onSubmit = async (data: any) => {
    setError("");
    // if (!captchaValue) {
    //   setError("Please complete the CAPTCHA");
    //   return;
    // }

    await login({ ...data});
  };

  return (
    <div className="min-h-screen overflow-auto flex flex-col bg-[#FAFAFA] px-[5%] pb-6">
      <div className="py-3">
      <Logo textColor="black" textSize="15px" height={50}width={50} />
      </div>
      <Card className="w-full max-w-[484px] mx-auto bg-white shadow-md rounded-md py-3 px-4">
        <div className="flex flex-col items-center space-y-1">
          <Image src="/logo-black.svg" alt="logo" width={50} height={50} />
          <p className="text-[24px] font-semibold">{t("title")}</p>
          <p className="text-[14px] text-gray-500 text-center">
            {t("description")}
          </p>
        </div>
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded my-3 text-sm">
            {error}
          </div>
        )}
        <CardContent className="p-0">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-1">
                {tFormLogin("email.text")}
              </label>
              <Input
                id="email"
                type="email"
                {...register("email")}
                className={`w-full h-[34px] ${
                  errors.email ? "border-red-500" : ""
                }`}
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>

            {/* Password */}
            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium mb-1"
              >
                {tForm("password.text")}
              </label>
              <Input
                id="password"
                type="password"
                {...register("password")}
                className={`w-full h-[34px] ${
                  errors.password ? "border-red-500" : ""
                }`}
              />
              {errors.password && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.password.message}
                </p>
              )}
              <div className="flex justify-end mt-2">
                <Link
                  href="/forgot-password"
                  className="text-[12px] hover:underline"
                >
                  {t("ForgotPassword")}
                </Link>
              </div>
            </div>

            {/* reCAPTCHA */}
            {/* <div className="flex justify-center">
              <ReCAPTCHA
                ref={recaptchaRef}
                sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY!}
                onChange={(val) => {
                  setCaptchaValue(val);
                  setError("");
                }}
              />
            </div> */}

            {/* Submit */}
            <Button
              type="submit"
              disabled={isPending}
              className="w-full bg-[#121212] text-white h-[40px] flex items-center justify-center"
            >
              {isPending ? tForm("Button.Logging") : tForm("Button.Login")}
            </Button>


      

            <div className="flex items-center">
              <hr className="w-full"/>
              <span className="text-[12px] text-[#828282] mx-2">{t("OR")}</span>
              <hr className="w-full"/>
            </div>

            <div className="flex flex-wrap items-center justify-between gap-2">
              <Button
                variant="outline"
                className="flex mt-2 w-full items-center rounded-md justify-center gap-2 cursor-pointer h-[40px]"
                type="button"
                onClick={() => signInWithGoogle()}
              >
                {t("LogWithGoogle")}
                <Image
                  src="/google-icon.svg"
                  alt="Google"
                  width={20}
                  height={20}
                />
                <div style={{ display: "flex" }}>
                  <MoonLoader size={17} color={"black"} loading={loadingGoogle} />
                </div>
              </Button>

              {/* <TelegramLogin
                // botName={
                //   process.env.NEXT_PUBLIC_TELEGRAM_BOT_USERNAME ||
                //   "AIWk_telegram_bot"
                // }
                actionText="Log in with Telegram"
                buttonSize="large"
                showUserPic={false}
                cornerRadius={10}
                className="w-full flex justify-center h-[46px]"
              /> */}
            </div>

            <div className="text-sm text-center text-[#828282]">
              {t("DoHaveAccount")}{" "}
              <Link
                href="/sign-up"
                className="text-black font-[700] hover:underline"
              >
                {t("SignUpHere")}
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
