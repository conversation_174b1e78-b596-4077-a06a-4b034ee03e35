// import { <PERSON>ada<PERSON> } from "next";
"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";

import Image from "next/image";

import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";

import * as yup from "yup";

import Logo from "@/components/Logo";
import { logo2, newLogo } from "@/assets";
import Spinner from "@/components/Loaders/MoonLoader";
import Moon<PERSON>oader from "react-spinners/MoonLoader";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { api } from "@/services/api_hooks";

// export const metadata: Metadata = {
//   title: "Login",
//   description: "Login to your account",
// };


const schema = yup
  .object({
    password: yup
      .string()
      .required("This field is required")
      .min(8, "Password must be at least 8 characters")
      .matches(/[0-9]/, "Password must contain a number")
      .matches(/[a-z]/, "Password must contain a lowercase letter")
      .matches(/[A-Z]/, "Password must contain an uppercase letter")
      .matches(
        /[!@#$%^&*(),.?":{}|<>]/,
        "Password must contain a special character"
      ),
    confirm_password: yup
      .string()
      .required("This field is required")
      .oneOf([yup.ref("password")], "Passwords must match"),
  })
  .required();

export default function ResetPassword() {
  const [loading, setLoading] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const router = useRouter();

  // Get tokens from either search params or hash params

  const getAccessToken = () => {
    if (typeof window !== "undefined") {
      const searchParams = new URLSearchParams(window.location.search);
      const hashParams = new URLSearchParams(window.location.hash.substring(1));
      const accessToken =
        searchParams.get("access_token") || hashParams.get("access_token");
      return accessToken;
    }
    return null;
  };

  const getRefreshToken = () => {
    if (typeof window !== "undefined") {
      const searchParams = new URLSearchParams(window.location.search);
      const hashParams = new URLSearchParams(window.location.hash.substring(1));
      const refreshToken =
        searchParams.get("refresh_token") || hashParams.get("refresh_token");
      return refreshToken;
    }
    return null;
  };

  const getLinkExpired = () => {
    if (typeof window !== "undefined") {
      const searchParams = new URLSearchParams(window.location.search);
      const hashParams = new URLSearchParams(window.location.hash.substring(1));
      const linkExpired =
        searchParams.get("error_code") || hashParams.get("error_code");
      return linkExpired;
    }
    return null;
  };

  const accessToken = getAccessToken();

  const refreshToken = getRefreshToken();

   const linkExpired = getLinkExpired();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      password: "",
      confirm_password: "",
    },
  });

  const onSubmit = async (formData: any) => {
    try {
   

     

      // Verify that passwords match before submitting
      if (formData.password !== formData.confirm_password) {
        toast.error("Passwords do not match", {
          position: "top-right",
          className: "p-4",
        });
        setLoading(false);
        return;
      }

       if (linkExpired) {
        toast.error("Link Expired", {
          position: "top-right",
          className: "p-4",
        });
        return;
      }

         setLoading(true);

      // // Get tokens from wherever they are stored (localStorage, cookies, context, etc.)
      // const accessToken = localStorage.getItem("access_token");
      // const refreshToken = localStorage.getItem("refresh_token");

      // Make API request using axios
      const response = await api.post("/auth/reset-password", {
        access_token: accessToken,
        refresh_token: refreshToken,
        new_password: formData.password,
      });

      setLoading(false);
      toast.success("Password reset successfully", {
        position: "top-right",
        className: "p-4",
      });

      // Redirect to login page after successful password reset
      setTimeout(() => {
        router.push("/login");
      }, 2000);
    } catch (err: any) {
      setLoading(false);
      console.error("Password reset error:", err);
      toast.error(err?.response?.data?.error || "Password reset failed", {
        position: "top-right",
        className: "p-4",
      });
    }
  };

  const togglePasswordVisibility = () => {
    setPasswordVisible(!passwordVisible);
  };

  const toggleConfirmPasswordVisibility = () => {
    setConfirmPasswordVisible(!confirmPasswordVisible);
  };

  return (
    <div className="lg:h-screen overflow-auto flex flex-col bg-[#FAFAFA] px-[5%] pb-6">
      <div className="py-3">
        <Logo textColor="black" textSize="15px" height={50} width={50} />
      </div>
      <Card className="w-full max-w-[484px] m-auto bg-white border-none shadow-md rounded-md py-7 px-7 ">
        <div className="flex flex-col items-center space-y-1 ">
          <Image src="/logo-black.svg" alt="logo" width={50} height={50} />

          <p className="text-[24px] font-semibold text-center">
            Reset Password
          </p>
          <p className="text-[14px] text-gray-500 text-center">
            Enter a new password for your account
          </p>
        </div>

        <CardContent className="p-0 mt-6">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div className="">
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Enter New Password
              </label>
              <div className="relative">
                <Input
                  id="password"
                  type={passwordVisible ? "text" : "password"}
                  className={`w-full h-[34px] pr-10 ${
                    errors.password ? "border-red-500" : ""
                  }`}
                  {...register("password")}
                />
              </div>
              {errors.password && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.password.message}
                </p>
              )}
            </div>

            <div className="">
              <label
                htmlFor="confirm_password"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Confirm Password
              </label>
              <div className="relative">
                <Input
                  id="confirm_password"
                  type={confirmPasswordVisible ? "text" : "password"}
                  className={`w-full h-[34px] pr-10 ${
                    errors.confirm_password ? "border-red-500" : ""
                  }`}
                  {...register("confirm_password")}
                />
              </div>
              {errors.confirm_password && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.confirm_password.message}
                </p>
              )}
            </div>

            <Button
              className="w-full flex items-center justify-center bg-[#121212] hover:bg-primary text-white h-[40px] cursor-pointer"
              type="submit"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="mr-2">Resetting Password</span>
                  <MoonLoader size={17} color={"white"} loading={true} />
                </>
              ) : (
                "Reset Password"
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
