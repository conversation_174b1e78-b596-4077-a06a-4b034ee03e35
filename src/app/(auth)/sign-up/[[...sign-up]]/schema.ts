import * as yup from "yup";

export const schema = yup
  .object({
    firstName: yup.string().required("First Name is required"),
    lastName: yup.string().required("Last Name is required"),
    email: yup
      .string()
      .email("Invalid email format")
      .required("Email is required")
      .matches(
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,

        "Invalid email format"
      ),
    // username: yup.string().required("Username is required"),
    // countryCode: yup.string().required("Country code is required"),
    // country: yup.object().required("Country is required"),
    password: yup
      .string()
      .required("This field is required")
      .min(8, "Password must be at least 8 characters")
      .matches(/[0-9]/, "Password must contain a number")
      .matches(/[a-z]/, "Password must contain a lowercase letter")
      .matches(/[A-Z]/, "Password must contain an uppercase letter")
      .matches(
        /[!@#$%^&*(),.?":{}|<>]/,
        "Password must contain a special character"
      ),
  })
  .required();