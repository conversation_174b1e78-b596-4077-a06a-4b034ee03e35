// app/auth/callback/page.tsx
"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { handleGoogleCallback } from "@/services/api_hooks";

export default function GoogleCallbackPage() {
  const router = useRouter();
  //   cont searchParams = useSearchParams();

  useEffect(() => {
    const processOAuthCallback = async () => {
      try {
       
        // Get tokens from either search params or hash params
        const searchParams = new URLSearchParams(window.location.search);
        const hashParams = new URLSearchParams(
          window.location.hash.substring(1)
        );

        const accessToken =
          searchParams.get("access_token") || hashParams.get("access_token");
        const refreshToken =
          searchParams.get("refresh_token") || hashParams.get("refresh_token");

        if (!accessToken || !refreshToken) {
          throw new Error("Missing authentication tokens in callback");
        }

        // Send these tokens to your backend to complete authentication
        await handleGoogleCallback({
          access_token: accessToken,
          refresh_token: refreshToken,
        });
        // Redirect to dashboard after successful authentication
          router.replace("/dashboard/home");
      } catch (error: any) {
        console.error("Error during OAuth callback:", error);
        // Redirect to login page after error
          router.replace("/login");
      }
    };

    processOAuthCallback();
  }, [router]);

  return null
}
