// InvestmentBankerSection.tsx
import Image from "next/image";
import React from "react";
import { useTranslations } from "next-intl";
import Link from "next/link";
const InvestmentBankerSection: React.FC = () => {
  // use next-intl for i18n
  const t = useTranslations("AboutUs.InvestmentBankerSection");

  const listItems = ["item1", "item2", "item3", "item4"].map((key) => {
    return {
      title: t(`list.${key}.title`),
      description: t(`list.${key}.description`),
    };
  });

  return (
    <div className="px-[5%] py-16 bg-[#F6FAF3]">
      <div className="flex flex-col lg:flex-row justify-between mx-auto max-w-[1300px] gap-10">
        <div className="w-full lg:max-w-[571px]  flex flex-col justify-center">
          <h2 className="text-3xl md:text-[30px] lg:text-[40px] font-bold mb-4 text-gray-900">
            {t("title")}
          </h2>
          <div className="text-[#1E1E1E] mb-6 leading-[35px] text-[16px] block">
            {t("description1")}{" "}
            <Link href="/ericbai" className="underline text-[#001ae0]">
              {t("name")}
            </Link>
            {t("description2")}
          </div>

          <ul className="space-y-4 text-[#1E1E1E] text-[16px]">
            {listItems.map((item, index) => (
              <li key={index} className="flex items-start">
                <div className="mr-2">•</div>
                <div>
                  <span className="font-medium">{item.title}</span>{" "}
                  {item.description}
                </div>
              </li>
            ))}
          </ul>
        </div>

        <div className="w-full max-w-[571px] bg-gray-200 rounded-[10px]">
          <Image
            src="/investmentbanker.svg"
            alt="Modern office with AI visualization overlays"
            width={1200}
            height={600}
            className="object-cover h-full w-full rounded-[10px]"
            unoptimized
          />
        </div>
      </div>
    </div>
  );
};

export default InvestmentBankerSection;
