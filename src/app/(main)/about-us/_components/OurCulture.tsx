// components/OurCulture.tsx
import { cn } from "@/lib/utils";
import { useTranslations } from 'next-intl';
import Image from "next/image";
import React from "react";

const CultureCard: React.FC<{
  icon: React.ReactNode;
  title: string;
  boldText: string;
  normalText: string;
  color: string;
}> = ({ icon, title, boldText, normalText, color }) => {
  const bg = color ? color : "white";
  return (
    <div
      className={cn(
        `flex flex-col justify-center p-5 border border-[#eeeeee] rounded-[8px] min-h-[240px] ${color}`
      )}
      style={{ backgroundColor: color }}
    >
      <div className="text-gray-700 mb-4">{icon}</div>
      {/* <h3 className="text-lg font-bold text-gray-900 mb-2">{title}</h3> */}
      <p className="text-[#797979] font-[500] text-[20px]">
        <span className=" text-black">{boldText}</span> {normalText}
      </p>
    </div>
  );
};

const OurCulture: React.FC = () => {

  // use next-intl for i18n
  const t = useTranslations("AboutUs.OurCulture");
  const tCultureCards = useTranslations("AboutUs.OurCulture.cultureCards");

  const cultureCards = [
    {
      icon: (
        <Image
          src="/chip-connect.svg"
          alt={tCultureCards("alt")}
          width={30}
          height={30}
          className=""
        />
      ),
      title: tCultureCards("TechForward.title"),
      boldText: tCultureCards("TechForward.boldText"),
      normalText: tCultureCards("TechForward.normalText"),
      color: "#F6FAF3",
    },
    {
      icon: (
        <Image
          src="/user-doubleline.svg"
          alt={tCultureCards("alt")}
          width={30}
          height={30}
          className=""
        />
      ),
      title: tCultureCards("ClientCentric.title"),
      boldText: tCultureCards("ClientCentric.boldText"),
      normalText: tCultureCards("ClientCentric.normalText"),
      color: "#F7F8FF",
    },
    {
      icon: (
        <Image
          src="/user-lock.svg"
          alt={tCultureCards("alt")}
          width={30}
          height={30}
          className=""
        />
      ),
      title: tCultureCards("PrivacyFirst.title"),
      boldText: tCultureCards("PrivacyFirst.boldText"),
      normalText: tCultureCards("PrivacyFirst.normalText"),
      color: "#FFFCF3",
    },
    {
      icon: (
        <Image
          src="/world-revolve.svg"
          alt={tCultureCards("alt")}
          width={30}
          height={30}
          className=""
        />
      ),
      title: tCultureCards("Multicultural.title"),
      boldText: tCultureCards("Multicultural.boldText"),
      normalText: tCultureCards("Multicultural.normalText"),
      color: "#F7F8FF",
    },
    {
      icon: (
        <Image
          src="/cloud-upload.svg"
          alt={tCultureCards("alt")}
          width={30}
          height={30}
          className=""
        />
      ),
      title: tCultureCards("CloudNative.title"),
      boldText: tCultureCards("CloudNative.boldText"),
      normalText: tCultureCards("CloudNative.normalText"),
      color: "#F6FAF3",
    },
  ];

  return (
    <section className="px-[5%]">
      <div className="container mx-auto max-w-[1300px] space-y-3">
        {/* Hardcoded first card */}

        <div className="">
          <h2 className="text-3xl md:text-[30px] lg:text-[40px] font-bold text-gray-900 mb-4">
            {t("title")}
          </h2>
          <p className="text-[16px] font-[400] text-gray-700">
            {t("description")}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ">
          {/* Mapped culture cards */}
          {cultureCards.map((card, index) => (
            <CultureCard
              key={index}
              icon={card.icon}
              title={card.title}
              boldText={card.boldText}
              normalText={card.normalText}
              color={card.color}
            />
          ))}
        </div>

        {/* <div className="mt-7 ">
          <p className="text-lg text-[#22263F]">
            We&apos;re not just building tools,{" "}
            <span className="font-bold">
              we&apos;re shaping the next generation of work.
            </span>
          </p>
        </div> */}
      </div>
    </section>
  );
};

export default OurCulture;
