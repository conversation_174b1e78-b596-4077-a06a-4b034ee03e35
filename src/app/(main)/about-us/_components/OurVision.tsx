// components/OurVision.tsx
import React from "react";
import { useTranslations } from 'next-intl';
import Image from "next/image";

const OurVision: React.FC = () => {

  // use next-intl for i18n
  const t = useTranslations("AboutUs.OurVision");

  return (
    <section className="px-[5%]">
      <div className="container mx-auto max-w-[1300px]">
        <div className="flex flex-col lg:flex-row gap-12">
          {/* Left column - Title */}
          <div className="w-full lg:w-2/5">
            <div className="mb-2">
              <span className="inline-block bg-[#D9DEFF] text-indigo-800 px-3 py-1 text-sm font-medium rounded-full">
                {t("subTitle")}
              </span>
            </div>
            <h2 className="text-3xl md:text-[30px] lg:text-[40px] font-bold text-gray-900 leading-tight">
              {t("title")}
            </h2>
          </div>

          {/* Right column - Vision text */}
          <div className="w-full lg:w-3/5">
            <p className="text-[#1E1E1E] text-[16px] font-[400] leading-[35px] mb-4">
              {t("content.paragraph1")}<br/>
              {t("content.paragraph2")}
            </p>
            {/* <p className="text-gray-700 text-lg leading-relaxed mb-4">
              
            </p> */}
          </div>
        </div>

        {/* Image container */}
        <div className="mt-12 rounded-xl overflow-hidden shadow-lg lg:h-[550px]">
          <Image
            src="/officebackground.jpg"
            alt="Modern office with AI visualization overlays"
            width={1200}
            height={600}
            className="object-cover h-full w-full"
            priority
          />
        </div>
      </div>
    </section>
  );
};

export default OurVision;
