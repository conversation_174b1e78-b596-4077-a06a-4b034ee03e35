// components/OurVision.tsx
import React from "react";
import { useTranslations } from 'next-intl';
import Image from "next/image";
import Link from "next/link";

const PurposeBuilt: React.FC = () => {
  // use next-intl for i18n
  const t = useTranslations("AboutUs.PurposeBuilt");

  return (
    <section className="px-[5%]">
      <div className="container mx-auto max-w-[1300px]">
        <div className="flex flex-col lg:flex-row items-center gap-12">
          {/* Left column - Title */}
          <div className="w-full lg:max-w-[600px] space-y-4">
            <div className="mb-2">
              <span className="inline-block bg-[#D9DEFF] text-indigo-800 px-3 py-1 text-sm font-medium rounded-full">
                {t("subTitle")}
              </span>
            </div>
            <h2 className="text-3xl md:text-[30px] lg:text-[40px] font-bold text-gray-900 leading-tight">
              {t("title")}
            </h2>

            <p className="text-[16px] font-[400] text-[#1E1E1E] leading-7">
              {t("description.text1")}{" "}
              <Link target="_blank" href="https://alpharithminv.com" className="underline text-blue-600">
                {t("description.name")}
              </Link>{" "}
              {t("description.text2")}
            </p>
          </div>

          {/* Right column - Vision text */}
          <div className="w-full md:w-3/ ">
            <video
              src="/bornFromAlpha.mp4"
              autoPlay
              muted
              loop
              playsInline
              preload="none"
              className="rounded-xl object-cover w-auto lg:h-[450px]"
              
            ></video>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PurposeBuilt;
