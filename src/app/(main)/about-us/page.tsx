export const metadata = {
  title: "About AIWK | Purpose-Built AI Work Assistants for Business",
  description:
    "AIWK builds purpose-built AI assistants that help professionals automate decisions, reports, and workflows—without code or engineering.",
  openGraph: {
    title: "About AIWK | Purpose-Built AI Work Assistants for Business",
     description:
    "AIWK builds purpose-built AI assistants that help professionals automate decisions, reports, and workflows—without code or engineering.",
    url: "https://ai-wk.com/about-us",
    siteName: "AIWK",
    images: [
      {
        url: "/og-logo.png",
        width: 1200,
        height: 630,
        alt: "AIWK | AI Work Models for Professionals",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "About AIWK | Purpose-Built AI Work Assistants for Business",
    description:
    "AIWK builds purpose-built AI assistants that help professionals automate decisions, reports, and workflows—without code or engineering.",
    images: ["/og-logo.png"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "https://ai-wk.com/about-us",
  },
};
import React from "react";
import OurVision from "./_components/OurVision";
import OurCulture from "./_components/OurCulture";
import InvestmentBankerSection from "./_components/InvestmentBanker";
import HeaderBlack from "@/components/Header/HeaderBlack";
import PurposeBuilt from "./_components/PurposeBuilt";

function AboutPage() {
  return (
    <div className="overflow-hidden ">
      <HeaderBlack bgColor="white" />
      <div className="space-y-[100px] md:space-y-[150px]">
        <div className="mt-[30px] md:mt-[60px]">
          <OurVision />
        </div>

        <InvestmentBankerSection />
        <OurCulture />

        <div className="mb-[40px] md:mb-[100px]">
          <PurposeBuilt />
        </div>
      </div>
    </div>
  );
}
export default AboutPage;
