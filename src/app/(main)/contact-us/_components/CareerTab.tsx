import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Select } from "@radix-ui/react-select";
import React from "react";
import { useTranslations } from 'next-intl';
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { toast } from "sonner";
import { useSubmitQuery } from "@/services/api_hooks";
import ResumeDropzone from "./ResumeDropzone";

const CareerTab = () => {

  // use next-intl for i18n
  const t = useTranslations("ContactUs.Tabs.Career.pane");
  const tForm = useTranslations("Form");
  const tGlobal = useTranslations("global");
  const tRoleOptions = useTranslations("Form.role.options");

  const mediaSchema = yup.object({
    fullname: yup.string().required(tForm("fullname.required")),
    email: yup.string().email(tForm("email.error")).required(tForm("email.required")),
    role: yup.string().required(tForm("role.required")),
    cover_letter: yup.string().required(tForm("coverLetter.required")),
    resume: yup
      .mixed()
      .test(
        "fileRequired",
        tForm("resume.required"),
        (value) =>
          !!value &&
          ((value instanceof FileList && value.length > 0) ||
            (Array.isArray(value) && value.length > 0))
      )
      .test("fileType", tForm("resume.fileTypeError"), (value) => {
        if (
          !value ||
          !(
            (value instanceof FileList && value.length > 0) ||
            (Array.isArray(value) && value.length > 0)
          )
        ) {
          return false;
        }
        const file =
          value instanceof FileList
            ? value[0]
            : Array.isArray(value)
            ? value[0]
            : null;
        if (!file) return false;
        return (
          file.type === "application/pdf" ||
          file.type === "application/msword" ||
          file.type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        );
      }),
  });


  const mediaForm = useForm({
    resolver: yupResolver(mediaSchema),
    mode: "onChange",
  });

  const { mutateAsync: submitInquiry, isPending: submitting } = useSubmitQuery(
    "/contact/career",
    "POST",
    {
      onSuccess() {
        toast.success(tGlobal("toast.SubmittedSuccessfully"), {
          position: "top-right",
          className: "p-4",
        });
        mediaForm.reset({
          fullname: "",
          email: "",
          role: "",
          cover_letter: "",
          resume: {}, // explicitly reset the file field
        });
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || tGlobal("toast.SubmissionFailed"), {
          position: "top-right",
          className: "p-4",
        });
      },
    }
  );

  const handleMediaSubmit = async (data: any) => {
    try {
      const formData = new FormData();
      formData.append("fullname", data.fullname);
      formData.append("email", data.email);
      formData.append("role", data.role);
      formData.append("cover_letter", data.cover_letter);

      // Robustly extract the file
      let file: File | null = null;
      if (data.resume instanceof FileList && data.resume.length > 0) {
        file = data.resume[0];
      } else if (Array.isArray(data.resume) && data.resume.length > 0 && data.resume[0] instanceof File) {
        file = data.resume[0];
      }
      if (file) {
        console.log("Uploading file:", file);
        formData.append("file", file);
      }

      await submitInquiry(formData);
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div className="space-y-8">
        <p className="text-[25px] md:text-[27px] font-[600] leading-[45px] max-w-[500px]">
          {t("title")}{" "}
        </p>

        <p className="text-[16px] font-[400] mb-5 text-[#1E1E1E]">
          {t("description")}
        </p>

        <div className="space-y-6">
          <div className="border-b border-black pb-4">
            <h3 className="font-[600] text-[20px] mb-1">
              {t("areas.Engineers.title")}
            </h3>
            <p className="text-[16px] font-[300] text-[#1E1E1E]">
              {t("areas.Engineers.description")}
            </p>
          </div>

          <div className="border-b border-black pb-4">
            <h3 className="font-[600] text-[20px] mb-1">
              {t("areas.Sales.title")}
            </h3>
            <p className="text-[16px] font-[300] text-[#1E1E1E]">
              {t("areas.Sales.description")}
            </p>
          </div>

          {/* <div className="border-b border-black pb-4">
            <h3 className="font-[600] text-[20px] mb-1">Support Staff</h3>
            <p className="text-[16px] font-[300] text-[#1E1E1E]">
              (Operations, client support, coordination roles)
            </p>
          </div> */}

          {/* <div className="border-b border-black pb-4">
            <h3 className="font-[600] text-[20px] mb-1">
              Browse open roles or submit an application
              We will be open for application shortly
            </h3>
            <a href="#" className="text-blue-600 hover:underline text-sm underline">
              Click here
            </a>
          </div> */}
        </div>
      </div>
      <form
        onSubmit={mediaForm.handleSubmit(handleMediaSubmit)}
        className="space-y-4"
        encType="multipart/form-data"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="fullname" className="text-sm">
              {tForm("fullname.text")}{" "}<span className="text-red-600">*</span>
            </label>
            <Controller
              name="fullname"
              control={mediaForm.control}
              render={({ field, fieldState }) => (
                <>
                  <Input
                    id="fullname"
                    {...field}
                    placeholder=""
                    className={fieldState.error ? "border-red-500" : ""}
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">
                      {fieldState.error.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>
          <div>
            <label htmlFor="email" className="text-sm">
              {tForm("email.text")}{" "}<span className="text-red-600">*</span>
            </label>
            <Controller
              name="email"
              control={mediaForm.control}
              render={({ field, fieldState }) => (
                <>
                  <Input
                    id="email"
                    type="email"
                    {...field}
                    placeholder=""
                    className={fieldState.error ? "border-red-500" : ""}
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">
                      {fieldState.error.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>
        </div>
  
        <div className="grid grid-cols-1 gap-4">
          <div>
            <label htmlFor="inquiryType" className="text-sm">
              {tForm("role.text")}{" "}<span className="text-red-600">*</span>
            </label>
            <Controller
              name="role"
              control={mediaForm.control}
              render={({ field, fieldState }) => (
                <>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger
                      className={fieldState.error ? "border-red-500" : ""}
                    >
                      <SelectValue placeholder={tRoleOptions("placeholder")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AI Engineer">{tRoleOptions("AIEngineer")}</SelectItem>
                      <SelectItem value="Sales Consultant">{tRoleOptions("SalesConsultant")}</SelectItem>
                      <SelectItem value="Other">{tRoleOptions("Other")}</SelectItem>
                    </SelectContent>
                  </Select>
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">
                      {fieldState.error.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>
        </div>
  
        <div>
          <label htmlFor="message" className="text-sm">
            {tForm("coverLetter.text")}{" "}<span className="text-red-600">*</span>
          </label>
          <Controller
            name="cover_letter"
            control={mediaForm.control}
            render={({ field, fieldState }) => (
              <>
                <Textarea
                  id="message"
                  {...field}
                  placeholder=""
                  className={`h-32 ${fieldState.error ? "border-red-500" : ""}`}
                />
                {fieldState.error && (
                  <p className="text-red-500 text-xs mt-1">
                    {fieldState.error.message}
                  </p>
                )}
              </>
            )}
          />
        </div>
  
        {/* Resume Upload Field */}
        <div>
          <label htmlFor="resume" className="text-sm">
            {tForm("resume.text")}{" "}<span className="text-red-600">*</span>
          </label>
          <Controller
            name="resume"
            control={mediaForm.control}
            render={({ field, fieldState }) => {
              let resumeValue = null;
              if (typeof window !== "undefined") {
                if (field.value instanceof window.FileList) {
                  resumeValue = field.value;
                } else if (
                  Array.isArray(field.value) &&
                  field.value.length > 0 &&
                  field.value[0] instanceof window.File
                ) {
                  // Convert array to FileList using DataTransfer
                  const dt = new DataTransfer();
                  field.value.forEach((file: File) => dt.items.add(file));
                  resumeValue = dt.files;
                }
              }
              return (
                <>
                  <ResumeDropzone
                    onChange={field.onChange}
                    value={resumeValue}
                    error={fieldState.error?.message}
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">
                      {fieldState.error.message}
                    </p>
                  )}
                </>
              );
            }}
          />
        </div>
  
        <div className="text-right">
          <Button
            type="submit"
            className="bg-[#131313] rounded-[8px] cursor-pointer text-white px-6"
            disabled={!mediaForm.formState.isValid || submitting}
          >
            {submitting ? tForm("Button.Sending") : tForm("Button.SendMessage")}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default CareerTab;
