import React from "react";
import { useTranslations } from 'next-intl';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";// Assuming this hook exists
import { useQueryClient } from "@tanstack/react-query"; // Make sure this is imported
import { useSubmitQuery } from "@/services/api_hooks";
import { toast } from "sonner";

const CustomModelsTab = () => {

  // use next-intl for i18n
  const t = useTranslations("ContactUs.Tabs.CustomModels.pane");
  const tForm = useTranslations("Form");
  const tGlobal = useTranslations("global");

  const customModelsSchema = yup.object({
    fullname: yup.string().required(tForm("fullname.required")),
    email: yup.string().email(tForm("email.error")).required(tForm("email.required")),
    company: yup.string().required(tForm("company.required")),
    industry: yup.string().required(tForm("industry.required")),
    teamSize: yup.string(),
    website: yup.string(),
    workflow: yup.string().required(tForm("workflow.required")),
    problem: yup.string(),
    tools: yup.string(),
    message: yup.string(),
  });

  const queryClient = useQueryClient();
  const customModelsForm = useForm({
    resolver: yupResolver(customModelsSchema),
  });

  const { mutateAsync: submitCustomModel, isPending: submitting } = useSubmitQuery(
    "/contact/custom-model", 
    "POST", 
    {
      onSuccess() {
        toast.success(tGlobal("toast.SubmittedSuccessfully"), {
          position: "top-right",
          className: "p-4",
        });
        customModelsForm.reset({
          fullname: "",
          email: "",
          company: "",
          industry: "",
          teamSize: "",
          website: "",
          workflow: "",
          problem: "",
          tools: "",
          message: "",
        });
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || tGlobal("toast.SubmissionFailed"), {
          position: "top-right",
          className: "p-4",
        });
      },
    }
  );

  const handleCustomModelsSubmit = async (data: any) => {
    try {
      // Map form field names to API expected field names
      const formData = {
        fullname: data.fullname,
        email: data.email,
        companyname: data.company,
        industry: data.industry,
        teamsize: data.teamSize || "",
        website: data.website || "",
        workflow: data.workflow,
        problem: data.problem || "",
        currenttools: data.tools || "",
        message: data.message || "",
      };

      await submitCustomModel(formData);
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
      <div className="space-y-10 max-w-[500px] ">
        <h2 className="text-[25px] md:text-[27px] font-[600] leading-[45px]">
          {t("title")}
        </h2>
        <p className="mt-6 text-[27px] font-[600] leading-[45px]">
          {t("description")}
        </p>
      </div>

      <div className="space-y-4">
        <p className="text-[16px] text-[#1E1E1E] mb-6 leading-[29px]  ">
          {t("form.description")}
        </p>
        <form
          onSubmit={customModelsForm.handleSubmit(handleCustomModelsSubmit)}
          className="space-y-4"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="fullname" className="text-sm">
                {tForm("fullname.text")}{" "}<span className="text-red-600">*</span>
              </label>
              <Controller
                name="fullname"
                control={customModelsForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      id="fullname"
                      {...field}
                      placeholder=""
                      className={fieldState.error ? "border-red-500" : ""}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
            <div>
              <label htmlFor="email" className="text-sm">
                {tForm("email.text")}{" "}<span className="text-red-600">*</span>
              </label>
              <Controller
                name="email"
                control={customModelsForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      id="email"
                      type="email"
                      {...field}
                      placeholder=""
                      className={fieldState.error ? "border-red-500" : ""}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="company" className="text-sm">
                {tForm("company.text")}{" "}<span className="text-red-600">*</span>
              </label>
              <Controller
                name="company"
                control={customModelsForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      id="company"
                      {...field}
                      placeholder=""
                      className={fieldState.error ? "border-red-500" : ""}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
            <div>
              <label htmlFor="industry" className="text-sm">
                {tForm("industry.text")}{" "}<span className="text-red-600">*</span>
              </label>
              <Controller
                name="industry"
                control={customModelsForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      id="industry"
                      {...field}
                      placeholder=""
                      className={fieldState.error ? "border-red-500" : ""}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="teamSize" className="text-sm">
                {tForm("teamSize.text")}
              </label>
              <Controller
                name="teamSize"
                control={customModelsForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      id="teamSize"
                      {...field}
                      placeholder=""
                      className={fieldState.error ? "border-red-500" : ""}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
            <div>
              <label htmlFor="website" className="text-sm">
                {tForm("website.text")}
              </label>
              <Controller
                name="website"
                control={customModelsForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      id="website"
                      {...field}
                      placeholder=""
                      className={fieldState.error ? "border-red-500" : ""}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
          </div>

          <div>
            <label htmlFor="workflow" className="text-sm">
              {tForm("workflow.text")}{" "}<span className="text-red-600">*</span>
            </label>
            <Controller
              name="workflow"
              control={customModelsForm.control}
              render={({ field, fieldState }) => (
                <>
                  <Textarea
                    id="workflow"
                    {...field}
                    placeholder=""
                    className={`h-24 ${
                      fieldState.error ? "border-red-500" : ""
                    }`}
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">
                      {fieldState.error.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>

          <div>
            <label htmlFor="problem" className="text-sm">
              {t("form.problemText")}
            </label>
            <Controller
              name="problem"
              control={customModelsForm.control}
              render={({ field, fieldState }) => (
                <>
                  <Textarea
                    id="problem"
                    {...field}
                    placeholder=""
                    className={`h-24 ${
                      fieldState.error ? "border-red-500" : ""
                    }`}
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">
                      {fieldState.error.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>

          <div>
            <label htmlFor="tools" className="text-sm">
              {t("form.toolsText")}
            </label>
            <Controller
              name="tools"
              control={customModelsForm.control}
              render={({ field, fieldState }) => (
                <>
                  <Input
                    id="tools"
                    {...field}
                    placeholder=""
                    className={fieldState.error ? "border-red-500" : ""}
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">
                      {fieldState.error.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>

          <div>
            <label htmlFor="message" className="text-sm">
              {tForm("message.text")}
            </label>
            <Controller
              name="message"
              control={customModelsForm.control}
              render={({ field, fieldState }) => (
                <>
                  <Textarea
                    id="message"
                    {...field}
                    placeholder=""
                    className={`h-24 ${
                      fieldState.error ? "border-red-500" : ""
                    }`}
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">
                      {fieldState.error.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>

          <div className="text-right">
            <Button
              type="submit"
              className="bg-[#131313] rounded-[8px] cursor-pointer text-white px-6"
              disabled={submitting}
            >
              {submitting ? tForm("Button.Sending") : tForm("Button.SendMessage")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CustomModelsTab;