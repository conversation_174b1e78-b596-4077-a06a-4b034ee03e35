import React from "react";
import { useTranslations } from 'next-intl';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useQueryClient } from "@tanstack/react-query"; // Make sure this is imported
import { useSubmitQuery } from "@/services/api_hooks";
import { toast } from "sonner";

const InvestmentTab = () => {

  // use next-intl for i18n
  const t = useTranslations("ContactUs.Tabs.Investments.pane");
  const tForm = useTranslations("Form");
  const tGlobal = useTranslations("global");
  const tQualifiedOptions = useTranslations("Form.qualified.options");
  const tInvestorTypeOptions = useTranslations("Form.investorType.options");

  const investmentSchema = yup.object({
    fullname: yup.string().required(tForm("fullname.required")),
    email: yup.string().email(tForm("email.error")).required(tForm("email.required")),
    company: yup.string().required(tForm("company.required")),
    website: yup.string().required(tForm("website.required")),
    qualified: yup.string().required(tForm("qualified.required")),
    investorType: yup.string().required(tForm("investorType.required")),
    synergies: yup.string().required(tForm("synergies.required")),
    message: yup.string(),
  });

  const queryClient = useQueryClient();
  const investmentForm = useForm({
    resolver: yupResolver(investmentSchema),
  });

  const { mutateAsync: submitInvestment, isPending: submitting } = useSubmitQuery(
    "/contact/investment", 
    "POST", 
    {
      onSuccess() {
        toast.success(tGlobal("toast.SubmittedSuccessfully"), {
          position: "top-right",
          className: "p-4",
        });
        investmentForm.reset({
          fullname: "",
          email: "",
          company: "",
          website: "",
          qualified: "",
          investorType: "",
          synergies: "",
          message: "",
        });
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || tGlobal("toast.SubmissionFailed"), {
          position: "top-right",
          className: "p-4",
        });
      },
    }
  );

  const handleInvestmentSubmit = async (data: any) => {
    try {
      // Map form field names to API expected field names
      const formData = {
        fullname: data.fullname,
        email: data.email,
        companyname: data.company,
        website: data.website,
        qualified: data.qualified,
        investortype: data.investorType,
        synergy: data.synergies,
        message: data.message || "",
      };

      await submitInvestment(formData);
    } catch (error) {
      console.error("Error submitting investment form:", error);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
      <p className="text-[25px] md:text-[27px] font-[600] leading-[45px] max-w-[550px]">
        {t("title.paragraph1")}
        <br />{" "}{t("title.paragraph2")}
      </p>

      <div className="space-y-4">
        <p className="text-[16px] text-[#1E1E1E] mb-6 leading-[29px]">
          {t("form.description")}
        </p>
        <form
          onSubmit={investmentForm.handleSubmit(handleInvestmentSubmit)}
          className="space-y-4"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="fullname" className="text-sm">
              {tForm("fullname.text")}{" "}<span className="text-red-600">*</span>
              </label>
              <Controller
                name="fullname"
                control={investmentForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      id="fullname"
                      {...field}
                      placeholder=""
                      className={fieldState.error ? "border-red-500" : ""}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
            <div>
              <label htmlFor="email" className="text-sm">
                {tForm("email.text")}{" "}<span className="text-red-600">*</span>
              </label>
              <Controller
                name="email"
                control={investmentForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      id="email"
                      type="email"
                      {...field}
                      placeholder=""
                      className={fieldState.error ? "border-red-500" : ""}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="company" className="text-sm">
                {tForm("company.text")}{" "}<span className="text-red-600">*</span>
              </label>
              <Controller
                name="company"
                control={investmentForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      id="company"
                      {...field}
                      placeholder=""
                      className={fieldState.error ? "border-red-500" : ""}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
            <div>
              <label htmlFor="website" className="text-sm">
                {tForm("website.text")}{" "}<span className="text-red-600">*</span>
              </label>
              <Controller
                name="website"
                control={investmentForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      id="website"
                      {...field}
                      placeholder="https://mywebsite.com"
                      className={fieldState.error ? "border-red-500" : ""}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="qualified" className="text-sm">
                {tForm("qualified.text")}{" "}<span className="text-red-600">*</span>
              </label>
              <Controller
                name="qualified"
                control={investmentForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <SelectTrigger
                        className={fieldState.error ? "border-red-500" : ""}
                      >
                        <SelectValue placeholder={tQualifiedOptions("placeholder")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Yes">{tQualifiedOptions("Yes")}</SelectItem>
                        <SelectItem value="No">{tQualifiedOptions("No")}</SelectItem>
                      </SelectContent>
                    </Select>
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
            <div>
              <label htmlFor="investorType" className="text-sm">
                {tForm("investorType.text")}{" "}<span className="text-red-600">*</span>
              </label>
              <Controller
                name="investorType"
                control={investmentForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <SelectTrigger
                        className={fieldState.error ? "border-red-500" : ""}
                      >
                        <SelectValue placeholder={tInvestorTypeOptions("placeholder")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Angel">{tInvestorTypeOptions("Angel")}</SelectItem>
                        <SelectItem value="Venture Capital">{tInvestorTypeOptions("VentureCapital")}</SelectItem>
                        <SelectItem value="Corporate">{tInvestorTypeOptions("Corporate")}</SelectItem>
                        <SelectItem value="Family Office">{tInvestorTypeOptions("FamilyOffice")}</SelectItem>
                        <SelectItem value="Other">{tInvestorTypeOptions("Other")}</SelectItem>
                      </SelectContent>
                    </Select>
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
          </div>

          <div>
            <label htmlFor="synergies" className="text-sm">
              {tForm("synergies.text")}{" "}<span className="text-red-600">*</span>
            </label>
            <Controller
              name="synergies"
              control={investmentForm.control}
              render={({ field, fieldState }) => (
                <>
                  <Textarea
                    id="synergies"
                    {...field}
                    placeholder=""
                    className={`h-24 ${
                      fieldState.error ? "border-red-500" : ""
                    }`}
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">
                      {fieldState.error.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>

          <div>
            <label htmlFor="message" className="text-sm">
              {tForm("message.text")}
            </label>
            <Controller
              name="message"
              control={investmentForm.control}
              render={({ field, fieldState }) => (
                <>
                  <Textarea
                    id="message"
                    {...field}
                    placeholder=""
                    className={`h-24 ${
                      fieldState.error ? "border-red-500" : ""
                    }`}
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">
                      {fieldState.error.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>

          <div className="text-right">
            <Button
              type="submit"
              className="bg-[#131313] rounded-[8px] cursor-pointer text-white px-6"
              disabled={submitting}
            >
              {submitting ? tForm("Button.Sending") : tForm("Button.SendMessage")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default InvestmentTab;