import React from "react";
import { useTranslations } from 'next-intl';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup"; // Assuming this hook exists
import { useQueryClient } from "@tanstack/react-query"; // Make sure this is imported
import { useSubmitQuery } from "@/services/api_hooks";
import { toast } from "sonner";

const MediaTab = () => {

  // use next-intl for i18n
  const t = useTranslations("ContactUs.Tabs.Media.pane");
  const tForm = useTranslations("Form");
  const tGlobal = useTranslations("global");
  const tTnquiryTypeOptions = useTranslations("Form.inquiryType.options");

  const mediaSchema = yup.object({
    fullname: yup.string().required(tForm("fullname.required")),
    email: yup.string().email(tForm("email.error")).required(tForm("email.required")),
    organization: yup.string().required(tForm("organization.required")),
    inquiryType: yup.string().required(tForm("inquiryType.required")),
    message: yup.string().required(tForm("message.required")),
  });

  const mediaForm = useForm({
    resolver: yupResolver(mediaSchema),
  });

  const { mutateAsync: submitInquiry, isPending: submitting } = useSubmitQuery(
    "/contact/inquiry", 
    "POST", 
    {
      onSuccess() {
        toast.success(tGlobal("toast.SubmittedSuccessfully"), {
          position: "top-right",
          className: "p-4",
        });
        mediaForm.reset({
          fullname: "",
          email: "",
          organization: "",
          inquiryType: "",
          message: "",
        });
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || tGlobal("toast.SubmissionFailed"), {
          position: "top-right",
          className: "p-4",
        });
      },
    }
  );

  const handleMediaSubmit = async (data: any) => {
    try {
      // Map form field names to API expected field names
      const formData = {
        fullname: data.fullname,
        email: data.email,
        organization: data.organization,
        inquirytype: data.inquiryType,
        message: data.message,
      };

      await submitInquiry(formData);
    } catch (error) {
      console.error("Error submitting inquiry form:", error);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div className="space-y-4">
        <p className="text-[25px] md:text-[27px] font-[600] leading-[45px] max-w-[550px]">
          {t("title")}
        </p>
      </div>

      <div className="space-y-4">
        <p className="text-sm text-[400] font-[400]">
          {t("form.description")}
        </p>
        <form
          onSubmit={mediaForm.handleSubmit(handleMediaSubmit)}
          className="space-y-4"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="fullname" className="text-sm">
                {tForm("fullname.text")}{" "}<span className="text-red-600">*</span>
              </label>
              <Controller
                name="fullname"
                control={mediaForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      id="fullname"
                      {...field}
                      placeholder=""
                      className={fieldState.error ? "border-red-500" : ""}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
            <div>
              <label htmlFor="email" className="text-sm">
                {tForm("email.text")}{" "}<span className="text-red-600">*</span>
              </label>
              <Controller
                name="email"
                control={mediaForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      id="email"
                      type="email"
                      {...field}
                      placeholder=""
                      className={fieldState.error ? "border-red-500" : ""}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="organization" className="text-sm">
                {tForm("organization.text")}{" "}<span className="text-red-600">*</span>
              </label>
              <Controller
                name="organization"
                control={mediaForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input
                      id="organization"
                      {...field}
                      placeholder=""
                      className={fieldState.error ? "border-red-500" : ""}
                    />
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
            <div>
              <label htmlFor="inquiryType" className="text-sm">
                {tForm("inquiryType.text")}{" "}<span className="text-red-600">*</span>
              </label>
              <Controller
                name="inquiryType"
                control={mediaForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <SelectTrigger
                        className={fieldState.error ? "border-red-500" : ""}
                      >
                        <SelectValue placeholder={tTnquiryTypeOptions("placeholder")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Media Interview">{tTnquiryTypeOptions("MediaInterview")}</SelectItem>
                        <SelectItem value="Speaking Engagement">{tTnquiryTypeOptions("SpeakingEngagement")}</SelectItem>
                        <SelectItem value="General Inquiry">{tTnquiryTypeOptions("GeneralInquiry")}</SelectItem>
                      </SelectContent>
                    </Select>
                    {fieldState.error && (
                      <p className="text-red-500 text-xs mt-1">
                        {fieldState.error.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>
          </div>

          <div>
            <label htmlFor="message" className="text-sm">
              {tForm("message.text")}{" "}<span className="text-red-600">*</span>
            </label>
            <Controller
              name="message"
              control={mediaForm.control}
              render={({ field, fieldState }) => (
                <>
                  <Textarea
                    id="message"
                    {...field}
                    placeholder=""
                    className={`h-32 ${
                      fieldState.error ? "border-red-500" : ""
                    }`}
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">
                      {fieldState.error.message}
                      </p>
                    )}
                </>
              )}
            />
          </div>

          <div className="text-right">
            <Button
              type="submit"
              className="bg-[#131313] rounded-[8px] cursor-pointer text-white px-6"
              disabled={submitting}
            >
              {submitting ? tForm("Button.Sending") : tForm("Button.SendMessage")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MediaTab;