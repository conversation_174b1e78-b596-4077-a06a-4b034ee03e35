import React from "react";
import { useTranslations } from 'next-intl';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup'; // Assuming this hook exists
import { useQueryClient } from "@tanstack/react-query"; // Make sure this is imported
import { useSubmitQuery } from "@/services/api_hooks";
import { toast } from "sonner";

const PartnershipTab = () => {
  // use next-intl for i18n
  const t = useTranslations("ContactUs.Tabs.Partnership.pane");
  const tForm = useTranslations("Form");
  const tGlobal = useTranslations("global");
  const tBusinessOptions = useTranslations("Form.business.options");
  const tCollaborationOptions = useTranslations("Form.collaboration.options");

  const partnershipSchema = yup.object({
    fullname: yup.string().required(tForm("fullname.required")),
    email: yup.string().email(tForm("email.error")).required(tForm("email.required")),
    company: yup.string().required(tForm("company.required")),
    website: yup.string().required(tForm("website.required")),
    businessNature: yup.string().required(tForm("business.required")),
    collaborationArea: yup.string().required(tForm("collaboration.required")),
    value: yup.string(),
    message: yup.string()
  });

  const queryClient = useQueryClient();
  const partnershipForm = useForm({
    resolver: yupResolver(partnershipSchema)
  });
  
  const { mutateAsync: submitPartnership, isPending: submitting } = useSubmitQuery(
    "/contact/partnership", 
    "POST", 
    {
      onSuccess() {
        toast.success(tGlobal("toast.SubmittedSuccessfully"), {
          position: "top-right",
          className: "p-4",
        });
        partnershipForm.reset({
          fullname: "",
          email: "",
          company: "",
          website: "",
          businessNature: "",
          collaborationArea: "",
          value: "",
          message: "",
        });
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || tGlobal("toast.SubmissionFailed"), {
          position: "top-right",
          className: "p-4",
        });
      },
    }
  );

  const handlePartnershipSubmit = async (data: any) => {
    try {
      // Map form field names to API expected field names
      const formData = {
        fullname: data.fullname,
        email: data.email,
        companyname: data.company,
        website: data.website,
        nature: data.businessNature,
        area: data.collaborationArea,
        value: data.value || "",
        message: data.message || "",
      };

      await submitPartnership(formData);
    } catch (error) {
      console.error("Error submitting partnership form:", error);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div className="space-y-4">
        <h2 className="text-[25px] md:text-[27px] font-[600] leading-[45px]">
          {t("title")}
        </h2>
        <ul className="list-disc pl-6 space-y-2 text-[25px] md:text-[27px] font-[600]">
          {["item1", "item2", "item3"].map((key) => (<li key={key}>{t(`list.${key}`)}</li>))}
        </ul>
        <p className="mt-6 text-[25px] md:text-[27px] font-[600] leading-[45px]">
          {t("description")}
        </p>
      </div>
      
      <div className="space-y-4">
        <p className="text-[400] font-[400] mb-6">{t("form.description")}</p>
        <form onSubmit={partnershipForm.handleSubmit(handlePartnershipSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="fullname" className="text-sm">{tForm("fullname.text")}{" "}<span className="text-red-600">*</span></label>
              <Controller
                name="fullname"
                control={partnershipForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input id="fullname" {...field} placeholder="" className={fieldState.error ? "border-red-500" : ""} />
                    {fieldState.error && <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>}
                  </>
                )}
              />
            </div>
            <div>
              <label htmlFor="email" className="text-sm">{tForm("email.text")}{" "}<span className="text-red-600">*</span></label>
              <Controller
                name="email"
                control={partnershipForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input id="email" type="email" {...field} placeholder="" className={fieldState.error ? "border-red-500" : ""} />
                    {fieldState.error && <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>}
                  </>
                )}
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="company" className="text-sm">{tForm("company.text")}{" "}<span className="text-red-600">*</span></label>
              <Controller
                name="company"
                control={partnershipForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input id="company" {...field} placeholder="" className={fieldState.error ? "border-red-500" : ""} />
                    {fieldState.error && <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>}
                  </>
                )}
              />
            </div>
            <div>
              <label htmlFor="website" className="text-sm">{tForm("website.text")}{" "}<span className="text-red-600">*</span></label>
              <Controller
                name="website"
                control={partnershipForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Input id="website" {...field} placeholder="" className={fieldState.error ? "border-red-500" : ""} />
                    {fieldState.error && <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>}
                  </>
                )}
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="businessNature" className="text-sm">{tForm("business.text")}{" "}<span className="text-red-600">*</span></label>
              <Controller
                name="businessNature"
                control={partnershipForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <SelectTrigger className={fieldState.error ? "border-red-500" : ""}>
                        <SelectValue placeholder={tBusinessOptions("placeholder")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Financial Services">{tBusinessOptions("FinancialServices")}</SelectItem>
                        <SelectItem value="Technology">{tBusinessOptions("Technology")}</SelectItem>
                        <SelectItem value="Healthcare">{tBusinessOptions("Healthcare")}</SelectItem>
                        <SelectItem value="Education">{tBusinessOptions("Education")}</SelectItem>
                        <SelectItem value="Other">{tBusinessOptions("Other")}</SelectItem>
                      </SelectContent>
                    </Select>
                    {fieldState.error && <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>}
                  </>
                )}
              />
            </div>
            <div>
              <label htmlFor="collaborationArea" className="text-sm">{tForm("collaboration.text")}{" "}<span className="text-red-600">*</span></label>
              <Controller
                name="collaborationArea"
                control={partnershipForm.control}
                render={({ field, fieldState }) => (
                  <>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <SelectTrigger className={fieldState.error ? "border-red-500" : ""}>
                        <SelectValue placeholder={tCollaborationOptions("placeholder")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Product Integration">{tCollaborationOptions("ProductIntegration")}</SelectItem>
                        <SelectItem value="Distribution">{tCollaborationOptions("Distribution")}</SelectItem>
                        <SelectItem value="Co-Marketing">{tCollaborationOptions("CoMarketing")}</SelectItem>
                        <SelectItem value="Data Partnership">{tCollaborationOptions("DataPartnership")}</SelectItem>
                        <SelectItem value="Other">{tCollaborationOptions("Other")}</SelectItem>
                      </SelectContent>
                    </Select>
                    {fieldState.error && <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>}
                  </>
                )}
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="value" className="text-sm">{t("form.valueText")}</label>
            <Controller
              name="value"
              control={partnershipForm.control}
              render={({ field, fieldState }) => (
                <>
                  <Textarea id="value" {...field} placeholder="" className={`h-24 ${fieldState.error ? "border-red-500" : ""}`} />
                  {fieldState.error && <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>}
                </>
              )}
            />
          </div>
          
          <div>
            <label htmlFor="message" className="text-sm">{tForm("message.text")}</label>
            <Controller
              name="message"
              control={partnershipForm.control}
              render={({ field, fieldState }) => (
                <>
                  <Textarea id="message" {...field} placeholder="" className={`h-32 ${fieldState.error ? "border-red-500" : ""}`} />
                  {fieldState.error && <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>}
                </>
              )}
            />
          </div>
          
          <div className="text-right">
            <Button 
              type="submit" 
              className="bg-[#131313] rounded-[8px] cursor-pointer text-white px-6"
              disabled={submitting}
            >
              {submitting ? tForm("Button.Sending") : tForm("Button.SendMessage")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PartnershipTab;