import React, { useRef } from "react";
import { UploadCloud, FileText, X } from "lucide-react";

interface ResumeDropzoneProps {
  onChange: (files: FileList | null) => void;
  error?: string;
  value?: FileList | null;
}

const formatBytes = (bytes: number) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const ResumeDropzone: React.FC<ResumeDropzoneProps> = ({ onChange, error, value }) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      onChange(e.dataTransfer.files);
    }
  };

  const handleClick = () => {
    inputRef.current?.click();
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Clear the file input
    if (inputRef.current) inputRef.current.value = "";
    onChange(null);
  };

  return (
    <div
      className={`border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer transition-colors ${
        error ? "border-red-500 bg-red-50" : "border-[#E5EAF1] bg-white hover:bg-[#F8FAFC]"
      }`}
      onClick={handleClick}
      onDrop={handleDrop}
      onDragOver={(e) => e.preventDefault()}
    >
      <input
        ref={inputRef}
        type="file"
        accept=".pdf,.doc,.docx"
        className="hidden"
        onChange={(e) => onChange(e.target.files)}
      />
      {!value || value.length === 0 ? (
        <>
          <UploadCloud className="w-8 h-8 text-[#94A3B8] mb-2" />
          <div className="font-medium text-[#1E293B] mb-1">Upload your resume</div>
          <div className="text-xs text-[#64748B] mb-1">
            Click to browse or drag and drop your resume here
          </div>
          <div className="text-xs text-[#64748B]">Maximum of 10mb</div>
        </>
      ) : (
        <div className="flex flex-col items-center w-full">
          <div className="flex items-center gap-2 bg-[#F1F5F9] rounded px-3 py-2 w-full justify-between">
            <div className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-[#64748B]" />
              <span className="text-sm text-[#0F172A] font-medium">{value[0].name}</span>
              <span className="text-xs text-[#64748B] ml-2">{formatBytes(value[0].size)}</span>
            </div>
            <button
              type="button"
              onClick={handleRemove}
              className="ml-2 text-[#64748B] hover:text-red-500"
              aria-label="Remove file"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResumeDropzone;