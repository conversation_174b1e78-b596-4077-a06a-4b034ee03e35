import React from "react";
import { useTranslations } from 'next-intl';

const SupportTab = () => {

  // use next-intl for i18n
  const t = useTranslations("ContactUs.Tabs.Support.pane");

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <p className="text-[25px] md:text-[27px] font-[600] leading-[45px] max-w-[500px]">
        {t("title")}{" "}
      </p>

      <p className="text-[16px] font-[400] max-w-[500px]">
        {t("description")}
      </p>
    </div>
  );
};

export default SupportTab;
