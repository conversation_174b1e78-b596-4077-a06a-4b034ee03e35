"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { useEffect, useState } from "react";
import { useTranslations } from 'next-intl';
import PartnershipTab from "./PartnershipTab";
import CustomModelsTab from "./CustomModelsTab";
import InvestmentTab from "./InvestmentTab";
import SupportTab from "./SupportTab";
import CareerTab from "./CareerTab";
import MediaTab from "./MediaTab";
import { useRouter, useSearchParams } from "next/navigation";

type ContactTabProps = {
  className?: string;
};

const Tab: React.FC<ContactTabProps> = ({ className }) => {

  // use next-intl for i18n
  const t = useTranslations("ContactUs.Tabs");

  const tabs = ["CustomModels", "Support", "Career", "Investments", "Partnership", "Media"].map((key) => {
    return {
      value: t(`${key}.value`),
      label: t(`${key}.label`)
    }
  });

  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const router = useRouter();
  
  // Default to "custom-models" if no tab query parameter exists
  const initialTab = tabParam || "custom-models";
  const [activeTab, setActiveTab] = useState(initialTab);
  
  // Set URL query parameter to "custom-models" if none exists
  useEffect(() => {
    if (!tabParam) {
      router.push("?tab=custom-models", { scroll: false });
    }
  }, [tabParam, router]);

  // Update activeTab when URL parameter changes
  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    router.push(`?tab=${value}`, { scroll: false });
  };

  return (
    <div className={`px-[5%] ${className}`}>
      <Tabs
        defaultValue="custom-models"
        value={activeTab}
        onValueChange={handleTabChange}
        className="container mx-auto max-w-[1300px]"
      >
        <TabsList className="grid grid-cols-2 md:grid-cols-3 lg:flex lg:border-b rounded-none bg-white justify-start mb-20 lg:mb-10">
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.value}
              value={tab.value}
              className="lg:h-10 cursor-pointer font-[500] rounded-none data-[state=active]:shadow-none px-5 text-[#A0A7B4] data-[state=active]:text-black data-[state=active]:border-b-2 data-[state=active]:border-b-black"
            >
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>

        {/* Partnership Tab Content */}
        <TabsContent value="partnership" className="mt-6">
          <PartnershipTab />
        </TabsContent>

        {/* Custom Models Tab Content */}
        <TabsContent value="custom-models" className="mt-6">
          <CustomModelsTab />
        </TabsContent>

        {/* Investment Tab Content */}
        <TabsContent value="investment" className="mt-6">
          <InvestmentTab />
        </TabsContent>

        {/* Support Tab Content */}
        <TabsContent value="support" className="mt-6">
          <SupportTab />
        </TabsContent>

        {/* Career Tab Content */}
        <TabsContent value="career" className="mt-6">
          <CareerTab />
        </TabsContent>

        {/* Media & General Inquiries Tab Content */}
        <TabsContent value="media" className="mt-6">
          <MediaTab />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Tab;
