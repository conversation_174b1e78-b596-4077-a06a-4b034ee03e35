export const metadata = {
  title: "Contact AIWK | Build Your Own Custom AI Assistant",
  description:
    "Get in touch with AIWK to build a custom AI assistant tailored to your business. No code or engineering needed—just your workflows and goals.",
  openGraph: {
    title: "Contact AIWK | Build Your Own Custom AI Assistant",
     description:
    "Get in touch with AIWK to build a custom AI assistant tailored to your business. No code or engineering needed—just your workflows and goals.",
    url: "https://ai-wk.com/contact-us",
    siteName: "AIWK",
    images: [
      {
        url: "/og-logo.png",
        width: 1200,
        height: 630,
        alt: "AIWK | AI Work Models for Professionals",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Contact AIWK | Build Your Own Custom AI Assistant",
    description:
    "Get in touch with AIWK to build a custom AI assistant tailored to your business. No code or engineering needed—just your workflows and goals.",
    images: ["/og-logo.png"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "https://ai-wk.com/contact-us",
  },
};
import React from "react";
import HeaderBlack from "@/components/Header/HeaderBlack";
import ContactUsSection from "./_components/ContactUsSection";
import Tab from "./_components/Tab";

function ContactUsPage() {
  return (
    <div className="overflow-hidden min-h-screen">
      <HeaderBlack bgColor="white" />
      <div className="space-y-[50px] md:space-y-[100px] mb-[50px]">
        <div className="mt-[30px] md:mt-[60px]">
          <ContactUsSection />
        </div>

        <Tab/>

      </div>
    </div>
  );
}
export default ContactUsPage;
