export const metadata = {
  title: "Disclaimer | AI Model Use & Liability Notice | AIWK",
  description:
    "Read AIWK's disclaimer to understand the limitations, risks, and legal boundaries of using our AI models, outputs, and services.",
  openGraph: {
    title: "Disclaimer | AI Model Use & Liability Notice | AIWK",
     description:
    "Read AIWK's disclaimer to understand the limitations, risks, and legal boundaries of using our AI models, outputs, and services.",
    url: "https://ai-wk.com/disclaimer",
    siteName: "AIWK",
    images: [
      {
        url: "/og-logo.png",
        width: 1200,
        height: 630,
        alt: "AIWK | AI Work Models for Professionals",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Disclaimer | AI Model Use & Liability Notice | AIWK",
    description:
    "Read AIWK's disclaimer to understand the limitations, risks, and legal boundaries of using our AI models, outputs, and services.",
    images: ["/og-logo.png"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "https://ai-wk.com/disclaimer",
  },
};
import HeaderBlack from "@/components/Header/HeaderBlack";
import { ExternalLink } from "lucide-react";
import Link from "next/link";
import React from "react";
import { useTranslations } from 'next-intl';

const AIDisclaimer = () => {

  // use next-intl for i18n
  const t = useTranslations("AIDisclaimer");
  const tFeatures = useTranslations("AIDisclaimer.features");
  const tGlobal = useTranslations("global");

  return (
    <div>
      <HeaderBlack bgColor="white" />

      <div className="px-[5%]">
        <div className="max-w-[1300px] mx-auto">
          <div className="space-y-10 text-sm pb-10">
            <h1 className="text-[55px] font-[600] mt-5">{t("title")}</h1>
            <p className="text-black text-sm">{t("Date")}</p>
            <p className="leading-[26px] text-sm text-[#242424]">
              {t("description.text1")} <span className="font-[600]">{t("description.text2")}</span> {t("description.text3")} <span className="font-[600]">{t("description.text4")}</span>.
            </p>

            <div className="space-y-4 font-[400]">
              <section>
                <h2 className="text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature1.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature1.description")}
                </p>
              </section>

              <section>
                <h2 className="text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature2.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature2.description")}
                </p>
                <ul className="list-disc pl-5 text-sm text-[#242424] space-y-1 mt-2">
                  {["item1", "item2", "item3"].map((key) => (
                    <li key={key}>{tFeatures(`feature2.list.${key}`)}</li>
                  ))}
                </ul>
                <p className="leading-[26px] text-sm text-[#242424] mt-2">
                  {tFeatures("feature2.info")}
                </p>
              </section>

              <section>
                <h2 className="text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature3.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature3.description")}
                </p>
              </section>

              <section>
                <h2 className="text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature4.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature4.description.paragraph1")}
                </p>
                <p className="leading-[26px] text-sm text-[#242424] mt-2">
                  {tFeatures("feature4.description.paragraph2")}
                </p>
              </section>

              <section>
                <h2 className="text-base mb-2 text-[20px] font-[600]">
                  5{tFeatures("feature1.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature1.description")}
                </p>
                <Link
                  href="/contact-us?tab=media"
                 
                  className="flex items-center gap-1 mt-2 underline text-blue-600"
                >
                  {tGlobal("ContactUs")}
                  <ExternalLink size={14} />
                </Link>
              </section>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIDisclaimer;