import HeaderBlack from "@/components/Header/HeaderBlack";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("ERICBAI");

  return {
    title: t("meta.title"),
    description: t("meta.description"),
    openGraph: {
      title: t("meta.title"),
      description: t("meta.description"),
      url: "https://ai-wk.com/ericbai",
      siteName: "AIWK",
      images: [
        {
          url: "/eric.png",
          width: 600,
          height: 750,
          alt: t("meta.title"),
        },
      ],
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: t("meta.title"),
      description: t("meta.description"),
      images: ["/eric.png"],
      creator: "@ai_wk_com",
    },
    alternates: {
      canonical: "https://ai-wk.com/ericbai",
    }
  };
}

const EricBai = () => {
  // use next-intl for i18n
  const t = useTranslations("ERICBAI");

  return (
    <div>
      <HeaderBlack bgColor="white" />

      <main className="container mx-auto max-w-[1300px] py-12 grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
        <div className="flex flex-col gap-6">
          <span className="inline-block bg-[#d9deff] text-[#22263f] px-4 py-1 rounded-full text-sm w-fit font-medium">
            {t("foundersCorner")}
          </span>
          <h1 className="text-5xl font-bold leading-tight cursor-pointer">
            {t("name")}
          </h1>

          <h2 className="text-xl font-semibold text-[#03061d]">{t("title")}</h2>
          <p className="text-base leading-relaxed text-[#131313]">
            {t("bio.paragraph1")}
          </p>
          <p className="text-base leading-relaxed text-[#131313]">
            {t("bio.goldParagraph1")}{" "}
            <Link
              href="https://www.goldmansachs.com/pressroom/press-releases/2013/managing-directors-announced-november-2013"
              target="_blank"
              className="text-blue-600 underline hover:no-underline"
            >
              {t("links.goldmanSachs")}
            </Link>{" "}
            {t("bio.goldParagraph2")}{" "}
            <TransactionDialog>
              <span className="text-blue-600 underline hover:no-underline cursor-pointer">
                {t("links.capitalMarkets")}
              </span>
            </TransactionDialog>
            {t("bio.goldParagraph3")}
          </p>
          <p className="text-base leading-relaxed text-[#131313]">
            {t("bio.paragraph3")}
          </p>
          <p className="text-base leading-relaxed text-[#131313]">
            {t("bio.paragraph4")}
          </p>
          <p className="text-base leading-relaxed text-[#131313]">
            {t("bio.paragraph5")}
          </p>
        </div>

        <div className="flex justify-center lg:justify-end">
          <Image
            src="/eric.png"
            alt="Eric Bai"
            width={600}
            height={600}
            className="rounded-lg shadow-lg object-cover"
          />
        </div>
      </main>
    </div>
  );
};

export default EricBai;

const TransactionDialog = ({ children }: { children: React.ReactNode }) => {
  const t = useTranslations("ERICBAI");

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto p-8 rounded-xl shadow-lg">
        <DialogHeader>
          <DialogTitle className="text-3xl font-bold text-[#03061d] mb-4">
            {t("dialog.title")}
          </DialogTitle>
          <DialogDescription className="text-[#131313] mb-6">
            {t("dialog.description")}
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6 text-[#131313]">
          <div>
            <h4 className="font-bold text-lg mb-2">
              {t("dialog.sections.ipos.title")}
            </h4>
            <ul className="list-disc pl-5 space-y-1">
              {t
                .raw("dialog.sections.ipos.items")
                .map((item: string, index: number) => (
                  <li key={index}>{item}</li>
                ))}
            </ul>
          </div>

          <div>
            <h4 className="font-bold text-lg mb-2">
              {t("dialog.sections.placements.title")}
            </h4>
            <ul className="list-disc pl-5 space-y-1">
              {t
                .raw("dialog.sections.placements.items")
                .map((item: string, index: number) => (
                  <li key={index}>{item}</li>
                ))}
            </ul>
          </div>

          <div>
            <h4 className="font-bold text-lg mb-2">
              {t("dialog.sections.debt.title")}
            </h4>
            <ul className="list-disc pl-5 space-y-1">
              {t
                .raw("dialog.sections.debt.items")
                .map((item: string, index: number) => (
                  <li key={index}>{item}</li>
                ))}
            </ul>
          </div>

          <div>
            <h4 className="font-bold text-lg mb-2">
              {t("dialog.sections.acquisitions.title")}
            </h4>
            <ul className="list-disc pl-5 space-y-1">
              {t
                .raw("dialog.sections.acquisitions.items")
                .map((item: string, index: number) => (
                  <li key={index}>{item}</li>
                ))}
            </ul>
          </div>

          <div>
            <h4 className="font-bold text-lg mb-2">
              {t("dialog.sections.strategicInvestments.title")}
            </h4>
            <ul className="list-disc pl-5 space-y-1">
              {t
                .raw("dialog.sections.strategicInvestments.items")
                .map((item: string, index: number) => (
                  <li key={index}>{item}</li>
                ))}
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
