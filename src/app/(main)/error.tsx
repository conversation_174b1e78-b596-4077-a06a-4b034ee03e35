'use client';

import { Button } from '@/components/ui/button';
import { useTranslations } from 'next-intl';

export default function Error({
  reset,
}: {
  reset: () => void;
}) {
  // use next-intl for i18n
  const t = useTranslations("global.errorMessage");

  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <h2 className="mb-5 text-center">{t("title")}</h2>
      <Button
        type="submit"
        variant="default"
        onClick={() => reset()}
      >
        {t("Tryagain")}
      </Button>
    </div>
  );
}