import { MainNavigation } from "@/components/layout/MainNavigation";
import { SiteFooter } from "@/components/layout/site-footer";


interface MarketingLayoutProps {
  children: React.ReactNode;
}

export default function MarketingLayout({ children }: MarketingLayoutProps) {
  return (
    <div className=" flex flex-col ">
      {/* <MainNavigation scroll={true} /> */}
      <div className="">{children}</div> 
      <SiteFooter />
    </div>
  );
}
