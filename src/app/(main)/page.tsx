export const metadata = {
  title: "AIWK | AI Work Models for Professionals",
  description:
    "AIWK (ai-wk) offers purpose-built AI work models—OrionAI for equity research, HermesAI for market monitoring, FreddieAI for recruiting, YumiAI for support, LucaAI for accounting, and OlympusAI for simulation. Boost productivity and automate workflows without hiring engineers.",
  openGraph: {
    title: "AIWK | AI Work Models for Professionals",
    description:
      "AIWK (ai-wk) offers purpose-built AI work models—OrionAI for equity research, HermesAI for market monitoring, FreddieAI for recruiting, YumiAI for support, LucaAI for accounting, and OlympusAI for simulation. Boost productivity and automate workflows without hiring engineers.",
    url: "https://ai-wk.com/",
    siteName: "AIWK",
    images: [
      {
        url: "/og-logo.png",
        width: 1200,
        height: 630,
        alt: "AIWK | AI Work Models for Professionals",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AIWK | AI Work Models for Professionals",
    description:
      "AIWK (ai-wk) offers purpose-built AI work models—OrionAI for equity research, HermesAI for market monitoring, FreddieAI for recruiting, YumiAI for support, LucaAI for accounting, and OlympusAI for simulation. Boost productivity and automate workflows without hiring engineers.",
    images: ["/og-logo.png"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "https://ai-wk.com/",
  },
};
import { FC } from "react";
import { Figtree } from "next/font/google";
import { useTranslations } from "next-intl";
import Header from "@/components/Header";
import HeroSection from "@/components/Hero";
import Image from "next/image";
import AIBusiness from "@/components/sections/AIBusiness";
import StatisticsSection from "@/components/sections/Statistics";
import CarouselDynamic from "@/components/TabCarousel/caroselDynamic";
import AIWorkModelsClosingHero from "@/components/sections/ClosingHero";

const figtree = Figtree({
  subsets: ["latin"],
  weight: ["400", "700"],
  variable: "--font-figtree",
});

const Home: FC = () => {
  // use next-intl for i18n
  const t = useTranslations("Home");
  const tOurAIModels = useTranslations("Home.OurAIModels");
  return (
    <div className=" bg-white overflow-hidden">
      <div className="relative flex flex-col w-full min-h-[53vh] md:min-h-[50vh] [@media(min-width:2000px)]:min-h-[100vh] lg:h-screen pb-[40px]">
        {/* Original image */}
        <div className="absolute inset-0 z-0 bg-primary">
          <Image
            src={"/hero2.webp"}
            alt="landing page image"
            fill
            style={{ objectFit: "cover" }}
            priority
          />
        </div>

        {/* Smoothing overlay */}
        <div className="absolute inset-0 z-[1] bg-black opacity-[0.3]"></div>

        <div className="relative z-50  ">
          <Header />
        </div>

        {/* Centering container - grows to fill space and centers content */}
        <div className="h-full flex flex-col justify-end relative z-10 ">
          <HeroSection font={figtree} />
        </div>
      </div>

      <div className="min-h-screen py-16 place-content-center ">
        <section className="  space-y-4">
          <div className="  px-[5%]">
            <div className="max-w-4xl mx-auto text-center">
              <h1
                className={`${figtree.className} text-3xl  md:text-[45px] font-[600] text-[#22263F] mb-3 md:mb-1 leading-tight`}
              >
                {tOurAIModels("title")}
              </h1>

              <p className="text-base sm:text-lg md:text-[17px] text-[#828282] font-[500] mx-auto leading-relaxed">
                {tOurAIModels("description")}
              </p>
            </div>
          </div>
          {/* <TabCardCarousel /> */}
          <CarouselDynamic />
        </section>
      </div>

      <div className="min-h-screen py-16 place-content-center bg-[#F7F7F7]">
        <AIBusiness />
      </div>
      <div className="min-h-screen py-10 place-content-center ">
        <StatisticsSection />
      </div>

      <div className="mb-[50px]">
        <AIWorkModelsClosingHero />
      </div>
    </div>
  );
};

export default Home;
