"use client";

import React, { useState } from "react";
import { CheckCircle, LogOut } from "lucide-react";
import { useGetQuery } from "@/services/api_hooks";
import useLocalStorage from "@/hooks/use-local-storage";
import { useSearchParams } from "next/navigation";
import Logo from "@/components/Logo";
import Image from "next/image";
import Link from "next/link";

interface PaymentSuccessProps {
  amount?: string;
  stripeCredits?: string;
  customerName?: string;
  customerEmail?: string;
}

const PaymentSuccessPage = () => {
  const [status, setStatus] = useState("Processing your sign in...");
  const [error, setError] = useState("");
  const searchParams = useSearchParams();
  const session_id = searchParams.get("session_id");
  const custom_id = searchParams.get("custom_id");

  // Currency mapping
  const currencies = [
    { code: "USD", symbol: "$" },
    { code: "EUR", symbol: "€" },
    { code: "GBP", symbol: "£" },
    { code: "SGD", symbol: "S$" },
    { code: "AUD", symbol: "A$" },
    { code: "CAD", symbol: "C$" },
    { code: "JPY", symbol: "¥" },
    { code: "HKD", symbol: "HK$" },
  ];

  // Fetch Stripe data from API
  const { data: stripeData, isLoading: loadingStripeData } = useGetQuery(
    `/api/payment/stripe/success/${session_id}`,
    ["get-stripe-data"],
    {
      enabled: !!session_id,
      onError() {
        // toast.error("Could not load your profile");
      },
    }
  );

  // Fetch Yedpay data from API
  const { data: yedpayData, isLoading: loadingYedpayData } = useGetQuery(
    `/api/payment/yedpay/transaction/${custom_id}`,
    ["get-yedpay-data"],
    {
      enabled: !!custom_id,
      onError() {
        // toast.error("Could not load your profile");
      },
    }
  );

  // Helper function to get currency symbol
  const getCurrencySymbol = (currencyCode: any) => {
    const currency = currencies.find(
      (c) => c.code.toLowerCase() === currencyCode?.toLowerCase()
    );
    return currency ? currency.symbol : "$"; // Default to $ if currency not found
  };

  // Helper function to format amount with currency
  const formatAmount = (amount: any, currency: any) => {
    const symbol = getCurrencySymbol(currency);
    const formattedAmount = parseFloat(amount).toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
    return `${symbol}${formattedAmount}`;
  };

  // Determine which data source to use and extract data
  let paymentData = null;
  let paymentProvider = "";
  let isLoading = false;

  if (session_id && stripeData) {
    // Use Stripe data
    paymentProvider = "Stripe";
    paymentData = {
      amount: stripeData.payment_details?.amount,
      credits: stripeData.payment_details?.credits,
      currency: stripeData.payment_details?.currency,
      customerName: stripeData.payment_details?.user?.name,
      customerEmail: stripeData.payment_details?.user?.email,
    };
    isLoading = loadingStripeData;
  } else if (custom_id && yedpayData) {
    // Use Yedpay data
    paymentProvider = "Yedpay";
    paymentData = {
      amount: yedpayData.amount,
      credits: yedpayData.credits,
      currency: yedpayData.currency,
      customerName: yedpayData.user
        ? `${yedpayData.user.firstname} ${yedpayData.user.lastname}`.trim()
        : null,
      customerEmail: yedpayData.user?.email,
    };
    isLoading = loadingYedpayData;
  } else {
    // Check if we're still loading
    isLoading =
      (session_id && loadingStripeData) || (custom_id && loadingYedpayData);
  }

  // Format the extracted data
  const amount = paymentData
    ? formatAmount(paymentData.amount, paymentData.currency)
    : "$0.00";
  const credits = paymentData ? paymentData.credits : "$0.00";
  const customerName = paymentData?.customerName || "N/A";
  const customerEmail = paymentData?.customerEmail || "N/A";
  const currencySymbol = paymentData
    ? getCurrencySymbol(paymentData.currency)
    : "$";

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-lg p-8 max-w-md w-full mx-auto text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Loading payment details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-lg p-8 max-w-md w-full mx-auto">
        {/* Header with Icon */}
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4">
            <div className="relative">
              <Image
                src="/logo-black.svg"
                height={50}
                width={50}
                alt="logo"
                className="rounded-full "
              />
              <div className="absolute top-2 -right-5  flex items-center justify-center">
                <Image
                  src={
                    paymentProvider === "Stripe"
                      ? "/stripe-logo.svg"
                      : "/yedpay-logo.png"
                  }
                  height={35}
                  width={35}
                  alt={`${paymentProvider} logo`}
                  className=" 0 bg-white"
                />
              </div>
            </div>
          </div>

          {/* Amount */}
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{amount}</h1>

          {/* Status */}
          <p className="text-lg font-semibold text-gray-900 mb-4">
            Payment Successful
          </p>

          {/* Description */}
          <p className="text-gray-600 text-sm leading-relaxed">
            Your payment with {paymentProvider || "our payment processor"} was
            successful. <br />
            {credits} credits has been added to your account
          </p>
        </div>

        {/* Customer Details */}
        <div className="space-y-4 pt-6 border-t border-gray-100">
          <div className="flex justify-between items-center">
            <span className="text-gray-600 font-medium">Name:</span>
            <span className="text-gray-900 font-medium">{customerName}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-600 font-medium">Email Address:</span>
            <span className="text-gray-900 font-medium">{customerEmail}</span>
          </div>
        </div>
        <div className="flex justify-center">
          <Link
            href="/dashboard/home/<USER>"
            className="flex items-center gap-2 justify-center max-w-[200px] text-sm py-2 px-4 rounded-md bg-black text-white w-full mt-7"
          >
            Return to ai-wk
            <LogOut size={14} />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccessPage;
