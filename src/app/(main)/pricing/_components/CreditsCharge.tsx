import { color } from "framer-motion";
import React from "react";
import { useTranslations } from 'next-intl';

const CreditsCharge = () => {

  // use next-intl for i18n
  const t = useTranslations("Pricing.CreditsCharge");
  const tCreditModels = useTranslations("Pricing.CreditsCharge.creditModels");

  console.log()
  const creditModels = [
    {
      name: tCreditModels("Orion.name"),
      description: tCreditModels("Orion.description"),
      charges: [tCreditModels("Orion.charges.option1")],
      color: "#F6FAF3",
    },
    {
      name: tCreditModels("HermesX.name"),
      description: tCreditModels("HermesX.description"),
      charges: [tCreditModels("HermesX.charges.option1")],
      color: "#F7F8FF",
    },
    {
      name: tCreditModels("Freddie.name"),
      description: tCreditModels("Freddie.description"),
      charges: [tCreditModels("Freddie.charges.option1")],
      color: "#FFFCF3",
    },
    {
      name: tCreditModels("Luca.name"),
      description: tCreditModels("Luca.description"),
      charges: [
        tCreditModels("Luca.charges.option1"),
        tCreditModels("Luca.charges.option2"),
      ],
      color: "#F7F8FF",
    },
    {
      name: tCreditModels("Olympus.name"),
      description: tCreditModels("Olympus.description"),
      charges: [tCreditModels("Olympus.charges.option1")],
      color: "#F6FAF3",
    },
    {
      name: tCreditModels("HermesC.name"),
      description: tCreditModels("HermesC.description"),
      charges: [tCreditModels("HermesC.charges.option1")],
      color: "#FFFCF3",
    },
  ];

  return (
    <section className="px-[5%] ">
      <div className="flex flex-col mx-auto max-w-[1300px]">
        <p className="text-[30px] md:text-4xl font-bold mb-5 text-gray-900">
          {t("title")}
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 justify-between gap-4">
          {creditModels.map((model) => (
            <div
              key={model.name}
              className="bg-[#F6FAF3] p-8 rounded-sm flex flex-col justify-between min-h-60 gap-3 w-full"
              style={{ backgroundColor: model.color }}
            >
              <div>
                <h3 className="text-[#1E1E1E] text-[17px] lg:text-[32px] font-[600]">
                  {model.name}
                </h3>
                <p className="text-[#797979] text-[20px] font-[500]">
                  {model.description}
                </p>
              </div>

              <div>
                <p className="text-[#121212] text-[16px] lg:text-[20px] font-[500 space-y-2">
                  {model?.charges?.map((charge, index) => (
                    <span key={index} className="block">
                      {charge}
                    </span>
                  ))}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CreditsCharge;
