"use client";

// InvestmentBankerSection.tsx
import Image from "next/image";
import React, { useEffect, useMemo, useState } from "react";
import { useTranslations } from 'next-intl';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import ReactCountryFlag from "react-country-flag";
import { ArrowDown, ChevronDown, Equal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { useGetQuery } from "@/services/api_hooks";

const CreditsPower: React.FC = () => {
  const [selectedCurrency, setSelectedCurrency] = useState<any>(null);

  // use next-intl for i18n
  const t = useTranslations("Pricing.CreditsPower");
  const tCurrencyMetadata = useTranslations("Pricing.CreditsPower.currencyMetadata");
  const tList = useTranslations("Pricing.CreditsPower.list");
  
  const currencyMetadata = [
    { code: "USD", symbol: "$", name: tCurrencyMetadata("USD") },
    { code: "EUR", symbol: "€", name: tCurrencyMetadata("EUR") },
    { code: "GBP", symbol: "£", name: tCurrencyMetadata("GBP") },
    { code: "SGD", symbol: "S$", name: tCurrencyMetadata("SGD") },
    { code: "AUD", symbol: "A$", name: tCurrencyMetadata("AUD") },
    { code: "CAD", symbol: "C$", name: tCurrencyMetadata("CAD") },
    { code: "JPY", symbol: "¥", name: tCurrencyMetadata("JPY") },
    { code: "CNY", symbol: "¥", name: tCurrencyMetadata("CNY") },
    { code: "HKD", symbol: "HK$", name: tCurrencyMetadata("HKD") },
  ];

  // map currency → ISO country code
  const flagMap: Record<string, string> = {
    USD: "US",
    EUR: "EU",
    GBP: "GB",
    SGD: "SG",
    AUD: "AU",
    CAD: "CA",
    JPY: "JP",
    CNY: "CN",
    HKD: "HK",
  };

  const { data, isLoading } = useGetQuery(
    "/auth/exchange-rates",
    ["exchange-rates"],
    {
      onError() {
        // toast.error("Could not load exchange rates");
      },
    }
  );

  // Combine API rates with currency metadata
  const currencies = useMemo(() => {
    if (!data?.rates) return [];

    return currencyMetadata
      .filter((currency) => data.rates[currency.code] !== undefined)
      .map((currency) => ({
        ...currency,
        rate: data.rates[currency.code],
      }));
  }, [data]);

  // Set default currency when data loads
  useEffect(() => {
    if (currencies.length > 0 && !selectedCurrency) {
      setSelectedCurrency(
        currencies.find((c) => c.code === "USD") || currencies[0]
      );
    }
  }, [currencies, selectedCurrency]);

  const listItems = [
    "item1", "item2", "item3"
  ].map((key) => {
    return {
      title: tList(`${key}.title`),
      description: tList(`${key}.description`),
    }
  });

 console.log(currencies)

  return (
    <div className="px-[5%] ">
      <div className="flex flex-col md:flex-row justify-between mx-auto max-w-[1300px] gap-10">
        <div className="w-full gap-5 flex flex-col ">
          <div className="mt-4">
            <span className="inline-block bg-[#D9DEFF] text-indigo-800 px-3 py-1 mb-2 text-sm font-medium rounded-full">
              {t("subTitle")}
            </span>
            <h2 className="text-3xl md:text-[30px] lg:text-[64px] font-bold  text-gray-900">
              {t("title.line1")}
              <br /> {t("title.line2")}
            </h2>

            {isLoading || !selectedCurrency ? (
              <div className="flex items-center justify-center h-20">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : (
              <div className="flex flex-col md:flex-row md:items-center gap-4 md:gap-6 my-5">
                <div className="flex items-center justify-between w-full max-w-[300px] h-[50px] border rounded-md py-1 px-2">
                  <span>1.00</span>
                  <div className="flex items-center gap-2">
                    <Image
                      src="/logo-black.svg"
                      alt="logo"
                      width={35}
                      height={35}
                      className=""
                    />
                    {t("Credit")}
                  </div>
                </div>

                <span className="hidden md:flex">
                  <Equal />
                </span>
                <div className="flex md:hidden text-center w-full justify-center max-w-[300px]">
                  <ArrowDown />
                </div>

                <div className="max-w-[300px] w-full">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full h-12 px-3 justify-between  border-gray-300 hover:bg-gray-50 rounded-md"
                      >
                        <div className="flex items-center">
                          <span className=" text-[15px] mr-2">
                            {selectedCurrency.rate}
                          </span>
                          <ReactCountryFlag
                            countryCode={flagMap[selectedCurrency.code]}
                            svg
                            style={{ width: "1.25em", height: "1em" }}
                            className="mr-2 rounded-sm"
                            aria-label={selectedCurrency.code}
                          />
                          <span className="font-medium ">
                            {selectedCurrency.code}
                          </span>
                        </div>
                        <ChevronDown className="h-4 w-4 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>

                    <DropdownMenuContent
                      className="w-[var(--radix-dropdown-menu-trigger-width)] p-1 bg-white border border-gray-200 shadow-lg rounded-md"
                      align="start"
                    >
                      {currencies.map((currency) => (
                        <DropdownMenuItem
                          key={currency.code}
                          className="flex items-center justify-between px-3 py-2.5 cursor-pointer hover:bg-gray-50 rounded-sm focus:bg-gray-50 min-h-[44px]"
                          onClick={() => setSelectedCurrency(currency)}
                        >
                          <div className="flex items-center">
                            <ReactCountryFlag
                              countryCode={flagMap[currency.code]}
                              svg
                              style={{ width: "1.25em", height: "1em" }}
                              className="mr-2 rounded-sm"
                              aria-label={currency.code}
                            />
                            <span className="font-medium text-sm text-gray-900">
                              {currency.code}
                            </span>
                          </div>
                          <span className="text-sm text-gray-600 ml-4">
                            {currency.name}
                          </span>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            )}
          </div>

          <ul className="space-y-4 text-[#1E1E1E] text-[16px]">
            {listItems.map((item, index) => (
              <li key={index} className="flex items-start">
                <div className="mr-2">•</div>
                <div>
                  {/* <span className="font-medium">{item.title}</span>{" "} */}
                  {item.description}
                </div>
              </li>
            ))}
          </ul>
        </div>

        <div className="w-full max-w-[484px] rounded-[10px]">
          <Image
            src="/creditspower.svg"
            alt="pricing image"
            width={1200}
            height={600}
            className="object-cover h-full w-full rounded-[10px]"
            unoptimized
          />
        </div>
      </div>
    </div>
  );
};

export default CreditsPower;
