// EarnMore.tsx
import React from 'react';
import { useTranslations } from 'next-intl';

interface TierInfo {
  name: string;
  range: string;
  bonusRate: string;
}

const EarnMore: React.FC = () => {

  // use next-intl for i18n
  const t = useTranslations("Pricing.EarnMore");

  const tiers: TierInfo[] = [
    "Basic", "Pro", "VIP", "SVIP"
  ].map((key) => {
    return {
      name: t(`tiers.${key}.name`),
      range: t(`tiers.${key}.range`),
      bonusRate: t(`tiers.${key}.bonusRate`),
    }
  });

  return (
    <div className='md:px-[5%] '>
    <div className="bg-[#0a1155] text-white py-6 md:py-16 px-6 md:px-8 md:rounded-lg shadow-lg max-w-5xl mx-auto">
      <h2 className="text-[30px] lg:text-[40px] font-bold mb-2">{t("title")}</h2>
      <p className="text-[16px] font-[400] mb-6 leading-[35px]">
        {t("description.part1")}<br />
        {t("description.part2")}
      </p>

      <div className="w-full text-white text-[16px]">
        <div className="grid grid-cols-3 mb-4 font-[600] ">
          <div>{t("tiers.header.Tier")}</div>
          <div>{t("tiers.header.TopUp")}</div>
          <div className="text-right">{t("tiers.header.BonusRate")}</div>
        </div>

        <div className="space-y-4 font-[400]">
          {tiers.map((tier, index) => (
            <div 
              key={tier.name} 
              className="grid grid-cols-3 py-4 border-t border-navy-700 "
            >
              <div className="font-medium">{tier.name}</div>
              <div>{tier.range}</div>
              <div className="text-right font-medium">{tier.bonusRate}</div>
            </div>
          ))}
        </div>

        <div className="mt-4 text-sm text-gray-300">
          {t("tiers.prompt")}
        </div>
      </div>
    </div>
    </div>
  );
};

export default EarnMore;