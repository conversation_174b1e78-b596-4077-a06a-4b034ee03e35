"use client";

import Link from "next/link";
import React, { useState } from "react";
import { useTranslations } from 'next-intl';

const PricingFAQ: React.FC = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  // use next-intl for i18n
  const t = useTranslations("Pricing.FAQs");

  const faqs = [
    { id: "q1", pLength: 1 },
    { id: "q2", pLength: 1 },
    { id: "q3", pLength: 0 },
    { id: "q4", pLength: 1 },
    { id: "q5", pLength: 1 },
  ].map((item) => {
    
    // Return HTML for q3
    if (item.id === 'q3') {
      return {
        question: t(`faqs.${item.id}.question`),
        answer: [<div key="refund">{t(`faqs.${item.id}.answer.label`)}<Link href="refund-policy" className="underline text-blue-500">{t(`faqs.${item.id}.answer.RefundPolicy`)}</Link></div>]
      }
    }
    return {
      question: t(`faqs.${item.id}.question`),
      answer: Array.from({ length: item.pLength }, (v, i) => t(`faqs.${item.id}.answer.paragraph${i+1}`))
    }
  });

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className=" px-[5%]">
      {/* Left Title */}
      <div className=" flex flex-col md:flex-row justify-between items-start max-w-[1300px] mx-auto gap-10">
        <div className=" justify-start text-stone-900 text-[26px] md:text-[30px] lg:text-[40px] font-bold">
          {t("title")}
        </div>

        {/* FAQ Section */}
        <div className="w-full flex flex-col gap-6">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="self-stretch pb-6 border-b border-black flex flex-col gap-4 cursor-pointer"
              
            >
              {/* Question */}
              <div className="flex justify-between items-center" onClick={() => toggleFAQ(index)}>
                <div className="text-stone-900 text-[18px] md:text-[20px] font-semibold" >
                  {faq.question}
                </div>

                {/* SVG Toggle Icon */}
                <div>
                  {openIndex === index ? (
                    // Up Arrow SVG (Open)
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                    >
                      <path
                        d="M10 16.667V3.33366M10 3.33366L15 8.33366M10 3.33366L5 8.33366"
                        stroke="#1E1E1E"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  ) : (
                    // Down Arrow SVG (Closed)
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                    >
                      <path
                        d="M10 3.33301V16.6663M10 16.6663L15 11.6663M10 16.6663L5 11.6663"
                        stroke="#1E1E1E"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                </div>
              </div>

              {/* Answer Section (Only Show When Open) */}
              {openIndex === index && (
                <div className="text-[#1E1E1E] flex flex-col text-[16px] leading-normal space-y-2">
                  {faq.answer.map((line, i) => (
                    <div key={i}>{line}</div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PricingFAQ;
