export const metadata = {
  title: "AI Work Models Pricing Plans | Transparent & Scalable | AIWK",
  description:
    "See AIWK's transparent pricing for AI models and assistants. Start free, scale with usage, and enjoy no hidden fees or engineering costs.",
  openGraph: {
    title: "AI Work Models Pricing Plans | Transparent & Scalable | AIWK",
     description:
    "See AIWK's transparent pricing for AI models and assistants. Start free, scale with usage, and enjoy no hidden fees or engineering costs.",
    url: "https://ai-wk.com/pricing",
    siteName: "AIWK",
    images: [
      {
        url: "/og-logo.png",
        width: 1200,
        height: 630,
        alt: "AIWK | AI Work Models for Professionals",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AI Work Models Pricing Plans | Transparent & Scalable | AIWK",
    description:
    "See AIWK's transparent pricing for AI models and assistants. Start free, scale with usage, and enjoy no hidden fees or engineering costs.",
    images: ["/og-logo.png"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "https://ai-wk.com/pricing",
  },
};
import HeaderBlack from "@/components/Header/HeaderBlack";
import CreditsPower from "./_components/CreditsPower";
import EarnMore from "./_components/EarnMore";
import CreditsCharge from "./_components/CreditsCharge";
import PricingFAQ from "./_components/FAQs";

export default async function PricingPage() {
  return (
    <div className="overflow-hidden ">
      <HeaderBlack bgColor="white" />
      <div className="space-y-[100px] md:space-y-[150px]">
        <div className="mt-[30px] md:mt-[60px]">
          <CreditsPower />
        </div>

        {/* <InvestmentBankerSection /> */}
        <EarnMore />
        <CreditsCharge />

        <div className="mb-[40px] md:mb-[100px]">
          <PricingFAQ />
        </div>
      </div>
    </div>
  );
}
