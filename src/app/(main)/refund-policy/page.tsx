export const metadata = {
  title: "Refund Policy | AI Work Models & Credits | AIWK",
  description:
    "Review AIWK's refund policy for AI model and credit purchases. Learn about refund eligibility, support channels, and conditions.",
  openGraph: {
    title: "Refund Policy | AI Work Models & Credits | AIWK",
     description:
    "Review AIWK's refund policy for AI model and credit purchases. Learn about refund eligibility, support channels, and conditions.",
    url: "https://ai-wk.com/refund-policy",
    siteName: "AIWK",
    images: [
      {
        url: "/og-logo.png",
        width: 1200,
        height: 630,
        alt: "AIWK | AI Work Models for Professionals",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Refund Policy | AI Work Models & Credits | AIWK",
    description:
    "Review AIWK's refund policy for AI model and credit purchases. Learn about refund eligibility, support channels, and conditions.",
    images: ["/og-logo.png"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "https://ai-wk.com/refund-policy",
  },
};
import HeaderBlack from "@/components/Header/HeaderBlack";
import React from "react";
import { useTranslations } from 'next-intl';

const RefundPolicyPage: React.FC = () => {

  // use next-intl for i18n
  const t = useTranslations("RefundPolicy");
  const tFeatures = useTranslations("RefundPolicy.features");

  return (
    <div>
      <HeaderBlack bgColor="white" />

      <div className="px-[5%]">
        <div className="max-w-[1300px] mx-auto">
          <div className="space-y-10 text-sm pb-10">
            <div className="flex items-center gap-3 mt-5">
              <h1 className="text-[55px] font-[600]">{t("title")}</h1>
            </div>
            
            <p className="leading-[26px] text-sm text-[#242424]">
              {t("description")}
            </p>

            <div className="space-y-7 font-[400]">
              <section>
                <h2 className="text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature1.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature1.description.paragraph1")}
                </p>
                <p className="text-sm text-[#242424] mt-2">
                  {tFeatures("feature1.description.paragraph2")}
                </p>
                <ul className="list-disc pl-5 text-sm text-[#242424] space-y-1 mt-2">
                  {["item1", "item2", "item3"].map((key) => (
                    <li key={key}>{tFeatures(`feature1.list.${key}`)}</li>
                  ))}
                </ul>
              </section>

              <section>
                <h2 className="text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature2.title")}
                </h2>
                <p className="text-sm text-[#242424]">
                  {tFeatures("feature2.description")}
                </p>
                <ul className="list-disc pl-5 text-sm text-[#242424] space-y-1 mt-2">
                  {["item1", "item2", "item3"].map((key) => (
                    <li key={key}>{tFeatures(`feature2.list.${key}`)}</li>
                  ))}
                </ul>
                <p className="text-sm text-[#242424] mt-2">
                  {tFeatures("feature2.info")}
                </p>
              </section>

              <section>
                <h2 className="text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature3.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature3.description.text1")} <span className="font-semibold"><EMAIL></span> {tFeatures("feature3.description.text2")}
                </p>
                <ul className="list-disc pl-5 text-sm text-[#242424] space-y-1 mt-2">
                  {["item1", "item2", "item3", "item4", "item5"].map((key) => (
                    <li key={key}>{tFeatures(`feature3.list.${key}`)}</li>
                  ))}
                </ul>
                <p className="text-sm text-[#242424] mt-2">
                  {tFeatures("feature3.info")}
                </p>
              </section>

              <section>
                <h2 className="text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature4.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature4.description")}
                </p>
                {/* <Link
                  href="/contact-us?tab=media"
                  className="flex items-center gap-1 mt-2 underline text-blue-600"
                >
                  Contact Us
                  <ExternalLink size={14} />
                </Link> */}
              </section>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RefundPolicyPage;