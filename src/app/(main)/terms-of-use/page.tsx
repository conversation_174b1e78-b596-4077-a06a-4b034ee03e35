export const metadata = {
  title: "Terms of Use | AI Work Models Platform | AIWK",
  description:
    "Review the terms for using AIWK's AI work models and services. Understand your rights, responsibilities, and usage rules before engaging with our tools.",
  openGraph: {
    title: "Terms of Use | AI Work Models Platform | AIWK",
     description:
    "Review the terms for using AIWK's AI work models and services. Understand your rights, responsibilities, and usage rules before engaging with our tools.",
    url: "https://ai-wk.com/terms-of-use",
    siteName: "AIWK",
    images: [
      {
        url: "/og-logo.png",
        width: 1200,
        height: 630,
        alt: "AIWK | AI Work Models for Professionals",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Terms of Use | AI Work Models Platform | AIWK",
    description:
    "Review the terms for using AIWK's AI work models and services. Understand your rights, responsibilities, and usage rules before engaging with our tools.",
    images: ["/og-logo.png"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "https://ai-wk.com/terms-of-use",
  },
};
import HeaderBlack from "@/components/Header/HeaderBlack";
import { ExternalLink } from "lucide-react";
import Link from "next/link";
import React from "react";
import { useTranslations } from 'next-intl';

const TOS = () => {

  // use next-intl for i18n
  const t = useTranslations("TOS");
  const tFeatures = useTranslations("TOS.features");
  const tGlobal = useTranslations("global");

  return (
    <div>
      <HeaderBlack bgColor="white" />

      <div className="px-[5%]">
        <div className="max-w-[1300px] mx-auto">
          <div className="space-y-10 text-sm pb-10">
            <h1 className="text-[55px] font-[600] mt-5">{t("title")}</h1>
            <p className="text-black text-sm">{t("Date")}</p>
            <p className="leading-[26px] text-sm text-[#242424]">
              {t("description")}
            </p>

            <div className="space-y-4 font-[400]">
              <section>
                <h2 className=" text-base mb-2 text-[20px] font-[600]">
                 {tFeatures("feature1.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature1.description")}
                </p>
              </section>

              <section>
                <h2 className="text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature2.title")}
                </h2>
                <p className="text-sm text-[#242424]">
                  {tFeatures("feature2.description")}
                </p>
                <ul className="list-disc pl-5 text-sm text-[#242424] space-y-1 mt-2">
                  {["item1", "item2", "item3", "item4"].map((key) => (
                    <li key={key}>{tFeatures(`feature2.list.${key}`)}</li>
                  ))}
                </ul>
              </section>

              <section>
                <h2 className=" text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature3.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature3.description")}
                </p>
              </section>

              <section>
                <h2 className=" text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature4.title")}
                </h2>
                <p className="text-sm text-[#242424]">
                  {tFeatures("feature4.description.paragraph1")}
                </p>
                <p className="text-sm text-[#242424] mt-2">
                  {tFeatures("feature4.description.paragraph2")}
                </p>
              </section>

              <section>
                <h2 className=" text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature5.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature5.description")}
                </p>
                <ul className="list-disc pl-5 text-sm text-[#242424] space-y-1 mt-2">
                  {["item1", "item2", "item3", "item4"].map((key) => (
                    <li key={key}>{tFeatures(`feature5.list.${key}`)}</li>
                  ))}
                </ul>
              </section>

              <section>
                <h2 className=" text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature6.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature6.description")}
                </p>
              </section>

              <section>
                <h2 className=" text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature7.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature7.description")}
                </p>
              </section>

              <section>
                <h2 className=" text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature8.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature8.description")}
                </p>
              </section>

              <section>
                <h2 className=" text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature9.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature9.description")}
                </p>
              </section>

              <section>
                <h2 className=" text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature10.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature10.description")}
                </p>
              </section>

              <section>
                <h2 className=" text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature11.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature11.description")}
                </p>
              </section>

              <section>
                <h2 className=" text-base mb-2 text-[20px] font-[600]">
                  {tFeatures("feature12.title")}
                </h2>
                <p className="leading-[26px] text-sm text-[#242424]">
                  {tFeatures("feature12.description")}
                </p>
                <Link
                  href="/contact-us?tab=media"
                 
                  className="flex items-center gap-1 mt-2 underline text-blue-600"
                >
                  {tGlobal("ContactUs")}
                  <ExternalLink size={14} />
                </Link>
              </section>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TOS;
