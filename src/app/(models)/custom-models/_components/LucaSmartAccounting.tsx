"use client";

import React, { useState } from "react";
import { useTranslations } from 'next-intl';
import Image, { StaticImageData } from "next/image";
import {
  accountants,
  freelancers,
  smallBusiness,
  companies,
} from "@/assets/luca";

const initialImage: any = "/facelessHands.svg";

function LucaSmartAccounting() {

  // use next-intl for i18n
  const t = useTranslations("CustomModel.LucaSmartAccounting");
  const tFeatures = useTranslations("CustomModel.LucaSmartAccounting.features");

  const sections = [
    {
      title: tFeatures("Small.title"),
      description: tFeatures("Small.description"),
      image: "/facelessHands.svg",
    },
    {
      title: tFeatures("Business.title"),
      description: tFeatures("Business.description"),
      image: "/handOnChin.svg",
    },
    {
      title: tFeatures("Department.title"),
      description: tFeatures("Department.description"),
      image: "/stickerGlass.svg",
    },
    {
      title: tFeatures("Consultants.title"),
      description: tFeatures("Consultants.description"),
      image: "/facelessPointingChart.svg",
    },
  ];
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [selectedImage, setSelectedImage] =
    useState<StaticImageData>(initialImage); // Default image

  const handleToggle = (index: number, image: string) => {
    setActiveIndex(activeIndex === index ? null : index); // Toggle active section
    // @ts-ignore
    setSelectedImage(image); // Update side image
  };

  return (
    <div className=" bg-[#F7F7F7] py-14 px-[5%]  w-full min-h-[643px] flex flex-col place-content-center">
      <div className="flex flex-col md:flex-row items-center justify-between max-w-[1300px] mx-auto w-full gap-8">
        {/* Side Image */}

        {/* List Section */}
        <div className="max-w-[579px]  w-full flex flex-col gap-4">
          {/* Title */}
          <div className=" text-stone-900 text-[28px] md:text-[40px] font-bold">
            {t("title.line1")}<br />{t("title.line2")}
          </div>
          {sections.map((item, index) => (
            <div
              key={index}
              className="py-4 border-b border-stone-900 "
              // @ts-ignore
              
            >
              {/* Title Row */}
              <div className="flex justify-between items-center cursor-pointer" onClick={() => handleToggle(index, item.image)}>
                <span className="text-stone-900 text-[18px] md:text-[20px] font-semibold">
                  {item.title}
                </span>
                <div className="w-5 h-5">
                  {activeIndex === index ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                    >
                      <path
                        d="M10 16.6665V3.33317M10 3.33317L15 8.33317M10 3.33317L5 8.33317"
                        stroke="#1E1E1E"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                    >
                      <path
                        d="M10 3.3335V16.6668M10 16.6668L15 11.6668M10 16.6668L5 11.6668"
                        stroke="#1E1E1E"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                </div>
              </div>

              {/* Description (only show if active) */}
              {activeIndex === index && (
                <p className="mt-2 text-stone-900 text-lg font-light">
                  {item.description}
                </p>
              )}
            </div>
          ))}
        </div>

        <Image
          src={selectedImage}
          alt="Accounting section"
          width={521}
          height={384}
          className=" left-[832px] top-[96px] rounded-xl transition-opacity duration-300"
        />
      </div>
    </div>
  );
}

export default LucaSmartAccounting;
