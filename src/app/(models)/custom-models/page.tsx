export const metadata = {
  title: "Custom AI Models for Business Workflows | AIWK",
  description:
    "Design bespoke AI assistants with AIWK—tailored to your unique workflows in PDF, Excel, PPT, or CSV. Automate reporting, decisions, and ops without engineers.",
  openGraph: {
    title: "Custom AI Models for Business Workflows | AIWK",
     description:
    "Design bespoke AI assistants with AIWK—tailored to your unique workflows in PDF, Excel, PPT, or CSV. Automate reporting, decisions, and ops without engineers.",
    url: "https://ai-wk.com/custom-models",
    siteName: "AIWK",
    images: [
      {
        url: "/og-logo.png",
        width: 1200,
        height: 630,
        alt: "AIWK | AI Work Models for Professionals",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Custom AI Models for Business Workflows | AIWK",
    description:
    "Design bespoke AI assistants with AIWK—tailored to your unique workflows in PDF, Excel, PPT, or CSV. Automate reporting, decisions, and ops without engineers.",
    images: ["/og-logo.png"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "https://ai-wk.com/custom-models",
  },
};
import React from "react";
import LucaLandingHeroSection from "./_components/LucaLandingHeroSection";
import HowLucaWorks from "./_components/HowLucaWorks";
import LucaComparison from "./_components/LucaComparison";
import LucaSmartAccounting from "./_components/LucaSmartAccounting";
import LucaKeyFeatures from "./_components/LucaKeyFeatures";
import LucaFAQ from "./_components/LucaFaq";
import HeaderBlack from "@/components/Header/HeaderBlack";

function LucaHomePage() {
  return (
    <div className="overflow-hidden space-y-[100px]">
      <div className=" bg-[#F0FFFA]" >
        <HeaderBlack bgColor="#F0FFFA" />
        <LucaLandingHeroSection />
      </div>

      {/* <LucaGitHub /> */}

      <LucaKeyFeatures />

      <LucaSmartAccounting />

      <LucaComparison />
      <HowLucaWorks />
      <div className="mb-[30px] md:mb-[100px]">
        <LucaFAQ />
      </div>

      {/* <LucaCTA /> */}
    </div>
  );
}

export default LucaHomePage;
