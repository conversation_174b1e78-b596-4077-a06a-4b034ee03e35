import React from "react";
import { useTranslations } from 'next-intl';

function <PERSON><PERSON><PERSON>parison() {

  // use next-intl for i18n
  const t = useTranslations("Freddie.LucaComparison");

  const leftComparisons = [
    "comparison1", "comparison2", "comparison3"
  ].map((key) => {
    return {
      title: t(`leftComparisons.${key}.title`),
      description: t(`leftComparisons.${key}.description`)
    }
  });

  const rightComparisons = [
    "comparison1", "comparison2", "comparison3"
  ].map((key) => {
    return {
      title: t(`rightComparisons.${key}.title`),
      description: t(`rightComparisons.${key}.description`)
    }
  });

  // Render a comparison item
  const renderItem = (item: any, index: number) => (
    <div key={index} className="flex justify-start items-center gap-3">
      <div className="w-5 h-5 relative">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <g clipPath="url(#clip0_324_1399)">
            <path
              d="M18.3333 9.23843V10.0051C18.3323 11.8021 17.7504 13.5507 16.6745 14.99C15.5985 16.4292 14.0861 17.4822 12.3628 17.9917C10.6395 18.5012 8.79772 18.44 7.11206 17.8172C5.42641 17.1945 3.98722 16.0435 3.00914 14.536C2.03106 13.0285 1.5665 11.2451 1.68474 9.45202C1.80297 7.6589 2.49768 5.95203 3.66524 4.58599C4.8328 3.21994 6.41066 2.26791 8.1635 1.87188C9.91633 1.47585 11.7502 1.65704 13.3917 2.38843M18.3333 3.33366L10 11.6753L7.50001 9.17533"
              stroke="#1E1E1E"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </g>
          <defs>
            <clipPath id="clip0_324_1399">
              <rect width="20" height="20" fill="white" />
            </clipPath>
          </defs>
        </svg>
      </div>
      <div className="text-[18px] justify-start">
        <span className="text-stone-900 font-semibold">{item.title} </span>
        <span className="text-neutral-500 font-normal">{item.description}</span>
      </div>
    </div>
  );

  return (
    <div className="px-[5%]">
      <div className="flex md:flex-row flex-col justify-between max-w-[1058px] mx-auto gap-15 md:gap-7">
        {/* Left */}
        <div className="flex flex-col justify-start items-start w-fit">
          <div className="justify-star text-[26px] md:text-[28px] font-[600] mb-5">
            <span className="text-[#357D00]">{t("Freddie")} </span>{t("vs")}
            <br /> {t("Hiring")}
          </div>
          <div className="flex flex-col justify-start items-start gap-8">
            {leftComparisons.map(renderItem)}
          </div>
        </div>

        <div className="md:h-[175px] w-[175px] md:w-[0px] border border-[#1E1E1E] self-center md:rotate-180"></div>

        {/* Right */}
        <div className="flex-col justify-start items-start md:pl-4">
          <div className="justify-start text-[26px] md:text-[28px] font-[600] mb-5">
            <span className="text-[#357D00]">{t("Freddie")} </span>{t("vs")}
            <br /> {t("HumanHiring")}
          </div>
          <div className="flex flex-col justify-start items-start gap-8">
            {rightComparisons.map(renderItem)}
          </div>
        </div>
      </div>
    </div>
  );
}

export default LucaComparison;
