import React from "react";
import { useTranslations } from 'next-intl';
import Image from "next/image";
import { douglas } from "@/assets/models";
import Link from "next/link";

function LucaLandingHeroSection() {

  // use next-intl for i18n
  const t = useTranslations("Freddie.LucaLandingHeroSection");
  const tGlobal = useTranslations("global");

  return (
    <div className="relative w-full bg-[#F6FAF3] overflow-hidden pl-[5%] pr-[5%] lg:pl-[8%] lg:pr-0 [@media(min-width:1700px)]:pl-0 md:pr-0 py-12">
      <div className="flex flex-col lg:flex-row justify-between items-center  gap-4   md:gap-8 [@media(min-width:1700px)]:mx-auto [@media(min-width:1700px)]:max-w-[1300px]">
        {/* Text Content - Takes full width on mobile, half on desktop */}
        <div className="w-full  flex flex-col gap-y-6 mb-10 lg:mb-0">
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-[55px] [@media(min-width:1700px)]:text-7xl font-bold text-stone-900 leading-tight dark:text-white  max-w-[800px] " dangerouslySetInnerHTML={{__html: t.raw("title")}}>
            {/* {t("title")} */}
          </h1>
          <p className="text-base text-[16px] text-slate-800 dark:text-gray-300 leading-[32px] max-w-[800px] ">
            {t("description")}
          </p>
          <div className="mt-2">
            <Link
              href="/sign-up"
              className="w-fit sm:w-auto px-6 py-3 rounded-[4px] bg-neutral-900 text-white font-semibold outline outline-offset-[-1px] outline-neutral-900 hover:shadow-lg transition duration-200"
            >
              {tGlobal("GetStartedNow")}
            </Link>
          </div>
        </div>

        <div className=" max-w-[534px] w-full lg:-mr-10 rounded-[9px] [@media(min-width:1700px)]:mr-0">
          <Image
            src="/freddie_side.svg"
            height={100}
            width={534}
            alt="Freddie model"
            className="w-full h-full object-cover rounded-[9px]"
            priority
          />
        </div>
      </div>
    </div>
  );
}

export default LucaLandingHeroSection;
