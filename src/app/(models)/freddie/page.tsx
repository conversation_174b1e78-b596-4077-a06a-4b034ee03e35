export const metadata = {
  title: "FreddieAI: AI Recruiter for Hiring Automation | AIWK",
  description:
    "FreddieAI streamlines hiring with automated job postings, resume screening, testing, communication, and onboarding—speeding up decisions and saving resources",
  openGraph: {
    title: "FreddieAI: AI Recruiter for Hiring Automation | AIWK",
     description:
    "FreddieAI streamlines hiring with automated job postings, resume screening, testing, communication, and onboarding—speeding up decisions and saving resources",
    url: "https://ai-wk.com/freddie",
    siteName: "AIWK",
    images: [
      {
        url: "/og-logo.png",
        width: 1200,
        height: 630,
        alt: "AIWK | AI Work Models for Professionals",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "FreddieAI: AI Recruiter for Hiring Automation | AIWK",
    description:
    "FreddieAI streamlines hiring with automated job postings, resume screening, testing, communication, and onboarding—speeding up decisions and saving resources",
    images: ["/og-logo.png"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "https://ai-wk.com/freddie",
  },
};
import React from "react";
import LucaLandingHeroSection from "./_components/LucaLandingHeroSection";
import LucaGitHub from "./_components/LucaGitHub";
import LucaCTA from "./_components/LucaCTA";
import HowLucaWorks from "./_components/HowLucaWorks";
import LucaComparison from "./_components/LucaComparison";
import LucaSmartAccounting from "./_components/LucaSmartAccounting";
import LucaKeyFeatures from "./_components/LucaKeyFeatures";
import LucaFAQ from "./_components/LucaFaq";
import Header from "@/components/Header";
import HeaderBlack from "@/components/Header/HeaderBlack";

function LucaHomePage() {
  return (
    <div className="overflow-hidden space-y-[100px]">
      <div>
        <HeaderBlack bgColor="#F6FAF3" />
        <LucaLandingHeroSection />
      </div>

      <LucaGitHub />

      <LucaKeyFeatures />

      <LucaSmartAccounting />

      <LucaComparison />
      <HowLucaWorks />
      <LucaFAQ />
      <LucaCTA />
    </div>
  );
}

export default LucaHomePage;
