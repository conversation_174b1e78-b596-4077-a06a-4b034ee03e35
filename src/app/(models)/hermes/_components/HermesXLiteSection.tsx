import Image from 'next/image';
import Link from 'next/link';
import { FC } from 'react';
import { useTranslations } from 'next-intl';

const HermesXLiteSection = () => {

  // use next-intl for i18n
  const t = useTranslations("Hermes.HermesXLiteSection");

  return (
    <div className="px-[5%] bg-[#F7F7F7] min-h-[428px] flex flex-col justify-center py-12">
      <div className="max-w-[1300px] w-full relative mx-auto flex flex-col md:flex-row justify-between items-center gap-7 ">
      <div className="space-y-3 pr-4 max-w-[700px]">
        <h2 className="text-[30px] md:text-[40px] font-[700] text-[#1E1E1E]">{t("title")}</h2>
        <p className="text-[#22263F] text-[16px] leading-[32px]">
          {t("description")}
        </p>
        <div>
          <Link 
            href="https://x.com/ai_wk_com" 
            target='_blank'
            className="inline-block text-blue-500 hover:text-blue-700 text-[16px] underline"
          >
            {t("ClickToFollow")}
          </Link>
        </div>
      </div>
      <div className="flex-shrink-0">
        <Image
          src="/x-logo-dirty.svg" 
          alt="X Logo"
          width={300}
          height={300}
          className="rounded-lg"
        />
      </div>
      </div>
    </div>
  );
};

export default HermesXLiteSection;