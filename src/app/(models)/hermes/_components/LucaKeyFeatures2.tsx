"use client";

import React, { useRef, useState, useEffect } from "react";
import { useTranslations } from 'next-intl';
import Slider from "react-slick";
import LucaFeatureCard from "./LucaFeatureCard";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const LucaKeyFeatures2: React.FC = () => {

  // use next-intl for i18n
  const t = useTranslations("Hermes.LucaKeyFeatures2");
  const tGlobal = useTranslations("global");
  
  const features = [
    "feature1", "feature2", "feature3", "feature4"
  ].map((key, index) => {
    return {
      number: `0${index+1}`,
      title: t(`features.${key}.title`),
      description: t(`features.${key}.description`)
    }
  });

  const sliderRef = useRef<Slider>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [visibleSlides, setVisibleSlides] = useState(3.2);

  // Check if on mobile viewport and set visibleSlides
  useEffect(() => {
    const checkViewport = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      if (width < 767) {
        setVisibleSlides(1);
      } else if (width < 1024) {
        setVisibleSlides(2.3);
      } else if (width < 1280) {
        setVisibleSlides(3.2);
      } else {
        setVisibleSlides(3.2);
      }
    };
    checkViewport();
    window.addEventListener('resize', checkViewport);
    return () => window.removeEventListener('resize', checkViewport);
  }, []);

  const settings = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 3.2, // Show 3 full + partial 4th slide
    slidesToScroll: 1,
    afterChange: (current: number) => setCurrentSlide(current),
    responsive: [
      {
        breakpoint: 1280, // XL screens
        settings: { 
          slidesToShow: 3.2, // 3 full cards + peek of 4th
        },
      },
      {
        breakpoint: 1024, // Large screens
        settings: { 
          slidesToShow: 2.3, // 2 full cards + peek of 3rd
        },
      },
      {
        breakpoint: 767, // Tablets
        settings: { 
          slidesToShow: 1,
        },
      },
    ],
  };

  const isFirstSlide = currentSlide === 0;
  const isLastSlide = currentSlide >= features.length - visibleSlides;

  return (
    <div className="px-[5%]">
      <div className="max-w-[1300px] relative mx-auto flex flex-col justify-start items-start gap-7 ">
        <div className="justify-start text-zinc-800 text-3xl font-semibold">
          {t("title")}
        </div>
        <p className="text-[#22263F] text-[16px] leading-[32px] max-w-[700px]">
          {t("description")}
        </p>

        <div className="justify-start text-zinc-800 text-3xl font-[600]">
          {tGlobal("KeyFeatures")}
        </div>
        {isMobile ? (
          // Mobile view: Show as vertical list of cards
          <div className="w-full space-y-4">
            {features.map((feature) => (
              <div key={feature.number}>
                <LucaFeatureCard
                  number={feature.number}
                  title={feature.title}
                  description={feature.description}
                />
              </div>
            ))}
          </div>
        ) : (
          // Desktop/Tablet view: Show as carousel
          <div className="w-full  lg:w-full gap-10">
            <Slider ref={sliderRef} {...settings} className="gap-10">
              {features.map((feature) => (
                <div key={feature.number} className="">
                  <LucaFeatureCard
                    number={feature.number}
                    title={feature.title}
                    description={feature.description}
                  />
                </div>
              ))}
            </Slider>

            <div className="flex gap-4 mt-6">
              {/* Left Arrow - gray when at first slide */}
              <button
                onClick={() => sliderRef.current?.slickPrev()}
                className={`cursor-pointer ${
                  isFirstSlide ? "opacity-30" : "opacity-100"
                }`}
                disabled={isFirstSlide}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                >
                  <path
                    d="M26.6667 16H5.33333M5.33333 16L13.3333 8M5.33333 16L13.3333 24"
                    stroke={isFirstSlide ? "#9CA3AF" : "#1E1E1E"}
                    strokeWidth="2.2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>

              {/* Right Arrow - gray when at last slide */}
              <button
                onClick={() => sliderRef.current?.slickNext()}
                className={`cursor-pointer ${
                  isLastSlide ? "opacity-30" : "opacity-100"
                }`}
                disabled={isLastSlide}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                >
                  <path
                    d="M5.33333 16L26.6667 16M26.6667 16L18.6667 24M26.6667 16L18.6667 8"
                    stroke={isLastSlide ? "#9CA3AF" : "#1E1E1E"}
                    strokeWidth="2.2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LucaKeyFeatures2;
