export const metadata = {
  title: "HermesAI: Market News Monitor & Trading Alerts | AIWK",
  description:
    "HermesAI scans macro news 24/7 for market-moving events. Hermes X covers global alerts, while Hermes C tracks stock-specific catalysts and earnings events.",
  openGraph: {
    title: "HermesAI: Market News Monitor & Trading Alerts | AIWK",
    description:
    "HermesAI scans macro news 24/7 for market-moving events. Hermes X covers global alerts, while Hermes C tracks stock-specific catalysts and earnings events.",
    url: "https://ai-wk.com/hermes",
    siteName: "AIWK",
    images: [
      {
        url: "/og-logo.png",
        width: 1200,
        height: 630,
        alt: "AIWK | AI Work Models for Professionals",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "HermesAI: Market News Monitor & Trading Alerts | AIWK",
    description:
    "HermesAI scans macro news 24/7 for market-moving events. Hermes X covers global alerts, while Hermes C tracks stock-specific catalysts and earnings events.",
    images: ["/og-logo.png"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "https://ai-wk.com/hermes",
  },
};
import React from "react";
import LucaLandingHeroSection from "./_components/LucaLandingHeroSection";
import LucaGitHub from "./_components/LucaGitHub";
import LucaCTA from "./_components/LucaCTA";
import HowLucaWorks from "./_components/HowLucaWorks";
import LucaComparison from "./_components/LucaComparison";
import LucaKeyFeatures from "./_components/LucaKeyFeatures";
import LucaFAQ from "./_components/LucaFaq";
import HeaderBlack from "@/components/Header/HeaderBlack";
import LucaKeyFeatures2 from "./_components/LucaKeyFeatures2";
import HermesXLiteSection from "./_components/HermesXLiteSection";

function LucaHomePage() {
  return (
    <div className="overflow-hidden space-y-[150px]">
      {/* <Header bgColor={}/> */}
      <div className=" bg-[#DAEBFF]">
        <HeaderBlack bgColor="#DAEBFF" />
        <LucaLandingHeroSection />
      </div>

      <LucaGitHub />

      <LucaKeyFeatures />

      <HermesXLiteSection />

      <LucaKeyFeatures2 />

      <LucaComparison />
      <HowLucaWorks />
      <LucaFAQ />
      <LucaCTA />
    </div>
  );
}

export default LucaHomePage;
