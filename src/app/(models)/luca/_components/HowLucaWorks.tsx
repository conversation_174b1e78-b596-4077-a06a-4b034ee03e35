import React from "react";
import { useTranslations } from 'next-intl';

function HowLucaWorks() {

  // use next-intl for i18n
  const t = useTranslations("Luca.HowLucaWorks");

  const steps = [
    "step1", "step2", "step3", "step4"
  ].map((key, index) => {
    return {
      number: `0${index+1}`,
      title: t(`steps.${key}.title`),
      description: t(`steps.${key}.description`)
    }
  });
  
  return (
    <div className="h-auto lg:h-[559px] px-[5%] py-8 lg:py-0 bg-[#10005D]">
      <div className="flex space-y-6 lg:space-y-10 flex-col justify-center items-start h-full max-w-[1300px] mx-auto">
        <h1 className="justify-start text-white text-3xl lg:text-[45px] font-bold leading-tight lg:leading-[55px]">
          {t("title.line1")}{" "}<br className="hidden lg:block" />{" "}{t("title.line2")}
        </h1>

        {/* Desktop view - horizontal layout */}
        <div className="hidden lg:flex w-full items-center justify-between">
          {steps.map((step, index) => (
            <React.Fragment key={`desktop-${step.number}`}>
              <div className={`flex flex-col justify-start items-start gap-4 ${index !== 0 ? "lg:pl-5" : ""} `}>
                <div
                  className={
                    index === 0 || index === 3
                      ? "flex flex-col justify-start items-start"
                      : "self-stretch flex flex-col justify-start items-start"
                  }
                >
                  <h2 className="justify-start text-[#FFFFFF7A] text-opacity-50 text-3xl font-medium">
                    {step.number}
                  </h2>
                  <h3
                    className={
                      index === 1
                        ? " justify-start text-white text-xl font-medium"
                        : "self-stretch justify-start text-white text-xl font-medium"
                    }
                  >
                    {step.title}
                  </h3>
                </div>
                <p className="justify-start text-white text-base font-normal leading-7">
                  {step.description}
                  <br />
                  {/* {step.description.split(" ").slice(-1)} */}
                </p>
              </div>
              {index < steps.length - 1 && (
                <div className="w-0.5 h-20 origin-top-left bg-white text-white outline outline-offset-[-1px] outline-white"></div>
              )}
            </React.Fragment>
          ))}
        </div>

        {/* Tablet view - 2x2 grid layout */}
        <div className="hidden md:grid lg:hidden grid-cols-2 gap-x-8 gap-y-10 w-full">
          {steps.map((step, index) => (
            <div
              key={`tablet-${step.number}`}
              className="flex flex-col border-l-[2px] pl-2"
            >
              <div className="mb-3">
                <h2 className="text-[#FFFFFF7A] text-opacity-50 text-2xl font-medium">
                  {step.number}
                </h2>
                <h3 className="text-white text-xl font-medium">{step.title}</h3>
              </div>
              <p className="text-white text-base">{step.description}</p>
              {/* {index % 2 === 0 && index < steps.length - 1 && (
                <div className="hidden md:block lg:hidden absolute right-1/2 h-full w-px bg-white opacity-50"></div>
              )} */}
            </div>
          ))}
        </div>

        {/* Mobile view - vertical layout */}
        <div className="flex flex-col w-full md:hidden space-y-6">
          {steps.map((step, index) => (
            <div key={`mobile-${step.number}`} className="flex flex-col">
              <div className="mb-2">
                <h2 className="text-[#FFFFFF7A] text-opacity-50 text-2xl font-medium">
                  {step.number}
                </h2>
                <h3 className="text-white text-xl font-medium">{step.title}</h3>
              </div>
              <p className="text-white text-sm mb-6">{step.description}</p>
              {index < steps.length - 1 && (
                <div className="w-[30%] h-px bg-white opacity-50 my-2 self-center"></div>
              )}
            </div>
          ))}
        </div>

        {/* <div className="text-white mt-3 md:mt-7">
          <span className="text-base md:text-[18px] lg:text-[20px] flex items-start">
            <span className="mr-2">•</span>
            <span>
              Important Note: Hermes-X enhances decision-making but does not
              replace human judgment. Traders should always verify news sources
              before acting.
            </span>
          </span>
        </div> */}
      </div>
    </div>
  );
}

export default HowLucaWorks;
