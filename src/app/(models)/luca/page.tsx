export const metadata = {
  title: "LucaAI: AI Accounting & Bookkeeping Assistant | AIWK",
  description:
    "LucaAI automates bookkeeping, reconciliations, invoicing, and financial reporting. Get fast, audit-ready financial insights without engineering or manual effort.",
  openGraph: {
    title: "LucaAI: AI Accounting & Bookkeeping Assistant | AIWK",
     description:
    "LucaAI automates bookkeeping, reconciliations, invoicing, and financial reporting. Get fast, audit-ready financial insights without engineering or manual effort.",
    url: "https://ai-wk.com/luca",
    siteName: "AIWK",
    images: [
      {
        url: "/og-logo.png",
        width: 1200,
        height: 630,
        alt: "AIWK | AI Work Models for Professionals",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "LucaAI: AI Accounting & Bookkeeping Assistant | AIWK",
    description:
    "LucaAI automates bookkeeping, reconciliations, invoicing, and financial reporting. Get fast, audit-ready financial insights without engineering or manual effort.",
    images: ["/og-logo.png"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "https://ai-wk.com/luca",
  },
};
import React from "react";
import LucaLandingHeroSection from "./_components/LucaLandingHeroSection";
import LucaGitHub from "./_components/LucaGitHub";
import LucaCTA from "./_components/LucaCTA";
import HowLucaWorks from "./_components/HowLucaWorks";
import LucaComparison from "./_components/LucaComparison";
import LucaSmartAccounting from "./_components/LucaSmartAccounting";
import LucaKeyFeatures from "./_components/LucaKeyFeatures";
import LucaFAQ from "./_components/LucaFaq";
import Header from "@/components/Header";
import HeaderBlack from "@/components/Header/HeaderBlack";

function LucaHomePage() {
  return (
    <div className="overflow-hidden space-y-[100px]">
      <div className=" bg-[#E8EBFF]">
        <HeaderBlack bgColor="#E8EBFF" />
        <LucaLandingHeroSection />
      </div>

      <LucaGitHub />

      <LucaKeyFeatures />

      <LucaSmartAccounting />

      <LucaComparison />
      <HowLucaWorks />
      <LucaFAQ />
      <LucaCTA />
    </div>
  );
}

export default LucaHomePage;
