export const metadata = {
  title: "OlympusAI: AI Market Simulator for Trading Strategy | AIWK",
  description:
    "OlympusAI simulates equity market scenarios with AI agents representing hedge funds, long-only funds, influencers, and companies—perfect for testing trading strategies under real-world dynamics",
  openGraph: {
    title: "OlympusAI: AI Market Simulator for Trading Strategy | AIWK",
     description:
    "OlympusAI simulates equity market scenarios with AI agents representing hedge funds, long-only funds, influencers, and companies—perfect for testing trading strategies under real-world dynamics",
    url: "https://ai-wk.com/olympus",
    siteName: "AIWK",
    images: [
      {
        url: "/og-logo.png",
        width: 1200,
        height: 630,
        alt: "AIWK | AI Work Models for Professionals",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "OlympusAI: AI Market Simulator for Trading Strategy | AIWK",
    description:
    "OlympusAI simulates equity market scenarios with AI agents representing hedge funds, long-only funds, influencers, and companies—perfect for testing trading strategies under real-world dynamics",
    images: ["/og-logo.png"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "https://ai-wk.com/olympus",
  },
};
import React from "react";
import LucaLandingHeroSection from "./_components/LucaLandingHeroSection";
import LucaGitHub from "./_components/LucaGitHub";
import LucaCTA from "./_components/LucaCTA";
import HowLucaWorks from "./_components/HowLucaWorks";
import LucaComparison from "./_components/LucaComparison";
import LucaSmartAccounting from "./_components/LucaSmartAccounting";
import LucaKeyFeatures from "./_components/LucaKeyFeatures";
import Header from "@/components/Header";
import HeaderBlack from "@/components/Header/HeaderBlack";
import OlympusFAQ from "./_components/OlympusFaq";

function LucaHomePage() {
  return (
    <div className="overflow-hidden space-y-[100px]">
      {/* <Header bgColor={}/> */}
      <div className=" bg-[#FFEED1]">
        <HeaderBlack bgColor="#FFEED1" />
        <LucaLandingHeroSection />
      </div>

      <LucaGitHub />

      <LucaKeyFeatures />

      <LucaSmartAccounting />

      <LucaComparison />
      <HowLucaWorks />
      <OlympusFAQ />
      <LucaCTA />
    </div>
  );
}

export default LucaHomePage;
