import Link from "next/link";
import React from "react";
import { useTranslations } from 'next-intl';

function LucaCTA() {

  // use next-intl for i18n
  const t = useTranslations("Orion.LucaCTA");
  const tGlobal = useTranslations("global");

  return (
    <div className="px-[5%] mb-[100px]">
      <div className="space-y-10 flex-col justify-center items-start py-10 md:py-16 px-4 md:px-8  mx-auto max-w-[1300px] bg-[#500B00] rounded-xl">
        {/* <div className="flex flex-col md:flex-row justify-between md:items-end gap-5">
          <div className=" inline-flex flex-col justify-start items-start gap-3">
            <h4 className="self-stretch justify-start text-white text-[20px] font-[500]">
              Sample Orion Output
            </h4>
            <p className="self-stretch justify-start text-white text-[16px] font-[400] leading-7">
              Download a copy of a report generated by
              <br /> Orion.
            </p>
          </div>
          <p className="justify-start text-white cursor-pointer text-[16px] font-medium underline leading-7">
            Download Here
          </p>
        </div> */}
        <div className="flex flex-col md:flex-row justify-between md:items-center gap-5">
          <h1 className=" justify-start text-white text-[32px] md:text-[35px] lg:text-[45px] font-bold ">
            {t("title.text1")}
            <br />{" "}{t("title.text2")}
            <br />{" "}{t("title.text3")}
          </h1>
          <Link href="/sign-up" className="px-4 py-3.5 max-w-[200px] w-full  text-[18px]  text-stone-900 text-lg font-semibold bg-white rounded flex justify-center items-center gap-1 cursor-pointer">
            {tGlobal("GetStartedNow")}
          </Link>
        </div>
      </div>
    </div>
  );
}

export default LucaCTA;
