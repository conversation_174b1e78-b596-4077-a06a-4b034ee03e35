"use client";

import { ExternalLink } from "lucide-react";
import Link from "next/link";
import React, { ReactNode, useState } from "react";
import { useTranslations } from 'next-intl';

type FAQAnswerItem = {
  id: number;
  content: ReactNode;
};

type FAQItem = {
  question: string;
  answer: FAQAnswerItem[];
};

const LucaFAQ: React.FC = () => {

  // use next-intl for i18n
  const t = useTranslations("Orion.LucaFAQ");
  const tFaqs = useTranslations("Orion.LucaFAQ.faqs")

  const faqs: FAQItem[] = [
    { name:"q1", answerLength: 1 },
    { name:"q2", answerLength: 0 },
  ].map((key) => {

    // There is special processing for HTML
    if (key.name === 'q2') {
      return {
        question: tFaqs(`${key.name}.question`),
        answer: [{
          id: 1,

          content: (
            <div className="flex flex-wrap text-[15px] gap-2">
              {tFaqs(`${key.name}.answer.line1`)}
            <Link 
              href="/contact-u?tab=custom-modelss" 
              onClick={(e) => {
                e.stopPropagation(); // Stop the click event from propagating up
              }}
              className="flex items-center gap-1 underline text-blue-600"
            >
              {tFaqs(`${key.name}.answer.ContactUs`)}
              <ExternalLink size={14}/>
            </Link>
          </div>
        )}]
      }
    }
    const answer = Array.from({ length: key.answerLength }).map((v, i) => {
      return {
        id: i+1,
        content: tFaqs(`${key.name}.answer.line${i+1}`)
      }
    })
    return {
      question: tFaqs(`${key.name}.question`),
      answer,
    }
  })

  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className="px-[5%]">
      {/* Left Title */}
      <div className="flex flex-col md:flex-row justify-between items-start max-w-[1300px] mx-auto gap-10">
        <div className="justify-start text-stone-900 text-[26px] md:text-[30px] lg:text-[40px] font-bold" dangerouslySetInnerHTML={{__html: t.raw("title")}}>
          {/* {t("title")} */}
        </div>

        {/* FAQ Section */}
        <div className="w-full flex flex-col gap-6">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="self-stretch pb-6 border-b border-black flex flex-col gap-4"
            
            >
              {/* Question */}
              <div className="flex justify-between items-center cursor-pointer"  onClick={() => toggleFAQ(index)}>
                <div className="text-stone-900 text-[18px] md:text-[20px] font-semibold">
                  {faq.question}
                </div>

                {/* SVG Toggle Icon */}
                <div>
                  {openIndex === index ? (
                    // Up Arrow SVG (Open)
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                    >
                      <path
                        d="M10 16.667V3.33366M10 3.33366L15 8.33366M10 3.33366L5 8.33366"
                        stroke="#1E1E1E"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  ) : (
                    // Down Arrow SVG (Closed)
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                    >
                      <path
                        d="M10 3.33301V16.6663M10 16.6663L15 11.6663M10 16.6663L5 11.6663"
                        stroke="#1E1E1E"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                </div>
              </div>

              {/* Answer Section (Only Show When Open) */}
              {openIndex === index && (
                <div className="text-[#1E1E1E] flex flex-col text-[16px] leading-normal space-y-2">
                  {faq.answer.map((item) => (
                    <div key={item.id}>{item.content}</div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LucaFAQ;