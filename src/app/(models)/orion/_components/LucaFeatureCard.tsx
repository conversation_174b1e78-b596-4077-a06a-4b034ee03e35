import React from "react";

interface LucaFeatureCardProps {
  number: string;
  title: string;
  description: string;
}

const LucaFeatureCard: React.FC<LucaFeatureCardProps> = ({ number, title, description }) => {
  return (
    <div className="min-h-[198px] md:min-h-[263px] p-5 rounded-lg mr-3 border-[2px] border-[#F7F8F6] flex flex-col justify-start items-start gap-4">
      <div className="flex flex-col justify-start items-start gap-2">
        <div className="text-neutral-400 text-[28px] font-medium">{number}</div>
        <div className="text-stone-900 text-[22px] font-semibold">{title}</div>
      </div>
      <div className="text-neutral-500 text-[18px] font-normal leading-7">{description}</div>
    </div>
  );
};

export default LucaFeatureCard;
