import React from "react";
import { useTranslations } from 'next-intl';

function LucaGitHub() {

  // use next-intl for i18n
  const t = useTranslations("Orion.LucaGitHub");

  return (
    <section className=" px-[5%] ">
      <div className="mx-auto flex text-[20px] md:text-[30px] lg:text-[40px] max-w-[1000px] flex-col items-center space-y-2 text-center">
        <h2 className=" text-center justify-start font-bold leading-10 lg:leading-16">
          <span className="text-stone-900  ">{t("title.text1")}</span>
          <span className="text-[#C61B00]">
            {" "}{t("title.text2")}
          </span>
          {" "}{t("title.text3")}
        </h2>
      </div>
    </section>
  );
}

export default LucaGitHub;
