"use client";

import React, { useState } from "react";
import { useTranslations } from 'next-intl';
import Image, { StaticImageData } from "next/image";
import {
  accountants,
  freelancers,
  smallBusiness,
  companies,
} from "@/assets/luca";
import { traders, firms,  inverstors, rename } from "@/assets/orion";

function LucaSmartAccounting() {

  // use next-intl for i18n
  const t = useTranslations("Orion.LucaSmartAccounting");
  const tFeatures = useTranslations("Orion.LucaSmartAccounting.features");
  
  const sections = [
    {
      title: tFeatures("Professional.title"),
      description: tFeatures("Professional.description"),
      image: inverstors,
    },
    {
      title: tFeatures("HedgeFunds.title"),
      description: tFeatures("HedgeFunds.description"),
      image: rename,
    },
    {
      title: tFeatures("Investment.title"),
      description: tFeatures("Investment.description"),
      image: firms,
    },
    {
      title: tFeatures("Analysts.title"),
      description: tFeatures("Analysts.description"),
      image: traders,
    },
  ];
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [selectedImage, setSelectedImage] =
    useState<StaticImageData>(inverstors); // Default image

  const handleToggle = (index: number, image: string) => {
    setActiveIndex(activeIndex === index ? null : index); // Toggle active section
    // @ts-ignore
    setSelectedImage(image); // Update side image
  };

  return (
    <div className=" bg-[#FFDFDA] py-14 px-[5%]  w-full min-h-[643px] flex flex-col place-content-center">
      <div className="flex flex-col md:flex-row items-center justify-between max-w-[1300px] mx-auto w-full gap-8">
        {/* Side Image */}

        {/* List Section */}
        <div className="max-w-[579px]  w-full flex flex-col gap-4">
          {/* Title */}
          <div className=" text-stone-900 text-[28px] md:text-[40px] font-bold">
            {t("title.line1")}<br />{t("title.line2")}
          </div>
          {sections.map((item, index) => (
            <div
              key={index}
              className="py-4 border-b border-stone-900 cursor-pointer"
              // @ts-ignore
              onClick={() => handleToggle(index, item.image)}
            >
              {/* Title Row */}
              <div className="flex justify-between items-center">
                <span className="text-stone-900 text-[18px] md:text-[20px] font-semibold">
                  {item.title}
                </span>
                <div className="w-5 h-5">
                  {activeIndex === index ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                    >
                      <path
                        d="M10 16.6665V3.33317M10 3.33317L15 8.33317M10 3.33317L5 8.33317"
                        stroke="#1E1E1E"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                    >
                      <path
                        d="M10 3.3335V16.6668M10 16.6668L15 11.6668M10 16.6668L5 11.6668"
                        stroke="#1E1E1E"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                </div>
              </div>

              {/* Description (only show if active) */}
              {activeIndex === index && (
                <p className="mt-2 text-stone-900 text-lg font-light">
                  {item.description}
                </p>
              )}
            </div>
          ))}
        </div>

        <Image
          src={selectedImage}
          alt="Accounting section"
          width={521}
          height={384}
          className=" left-[832px] top-[96px] rounded-xl transition-opacity duration-300"
        />
      </div>
    </div>
  );
}

export default LucaSmartAccounting;
