export const metadata = {
  title: "YumiAI: 24/7 AI Customer Support Assistant | AIWK",
  description:
    "YumiAI handles chat, email, ticket routing, form filling, and prioritization—your always-on AI customer support assistant that's fast, reliable, and fully documented.",
  openGraph: {
    title: "YumiAI: 24/7 AI Customer Support Assistant | AIWK",
     description:
    "YumiA<PERSON> handles chat, email, ticket routing, form filling, and prioritization—your always-on AI customer support assistant that's fast, reliable, and fully documented.",
    url: "https://ai-wk.com/yumi",
    siteName: "AIWK",
    images: [
      {
        url: "/og-logo.png",
        width: 1200,
        height: 630,
        alt: "AIWK | AI Work Models for Professionals",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "YumiAI: 24/7 AI Customer Support Assistant | AIWK",
    description:
    "<PERSON><PERSON><PERSON><PERSON> handles chat, email, ticket routing, form filling, and prioritization—your always-on AI customer support assistant that's fast, reliable, and fully documented.",
    images: ["/og-logo.png"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "https://ai-wk.com/yumi",
  },
};
import React from "react";
import LucaLandingHeroSection from "./_components/LucaLandingHeroSection";
import LucaCTA from "./_components/LucaCTA";
import Header from "@/components/Header";
import HeaderBlack from "@/components/Header/HeaderBlack";
import LucaGitHub from "./_components/LucaGitHub";
import LucaKeyFeatures from "./_components/LucaKeyFeatures";
import LucaSmartAccounting from "./_components/LucaSmartAccounting";
import LucaComparison from "./_components/LucaComparison";
import TryYumi from "./_components/TryYumi";
import LucaFAQ from "./_components/LucaFaq";

function LucaHomePage() {
  return (
    <div className="overflow-hidden space-y-[100px]">
      <div className=" bg-[#BEFFDE] ">
        <HeaderBlack bgColor="#BEFFDE" />
        <LucaLandingHeroSection />
      </div>
        <LucaGitHub />
        <LucaKeyFeatures />
        <LucaSmartAccounting />
        <LucaComparison />
        <TryYumi />
        <LucaFAQ />
        <LucaCTA />
    </div>
  );
}

export default LucaHomePage;
