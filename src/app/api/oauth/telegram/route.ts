// @ts-nocheck
// app/api/auth/telegram/route.ts
import { NextRequest, NextResponse } from "next/server";
import crypto from "crypto";

// Your bot token from environment variables
const BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;

export async function POST(request: NextRequest) {
  try {
    const userData = await request.json();
    const { hash, ...data } = userData;
    
    // Verify the authentication data from Telegram
    if (!verifyTelegramData(userData)) {
      return NextResponse.json(
        { message: "Invalid authentication data" },
        { status: 401 }
      );
    }
    
    // Here you would typically:
    // 1. Create or update user in your database
    // 2. Generate session/JWT tokens
    // 3. Set cookies or return token
    
    // Example API call to your backend service
    try {
      // You could make an API call to your own backend here
      // const apiResponse = await fetch('https://your-api.com/auth/telegram', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(userData),
      // });
      // const apiData = await apiResponse.json();
      
      // For now, simulating a successful response
      const user = {
        id: userData.id,
        firstName: userData.first_name,
        lastName: userData.last_name || "",
        username: userData.username || "",
        photoUrl: userData.photo_url || "",
        authDate: userData.auth_date,
      };
      
      // Generate JWT token (you'd need to implement this)
      const token = generateAuthToken(user);
      
      // Return user and token
      return NextResponse.json({
        user,
        token,
      });
    } catch (error: any) {
      console.error("API error:", error);
      return NextResponse.json(
        { message: "Error communicating with authentication service" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Telegram auth error:", error);
    return NextResponse.json(
      { message: "Authentication processing failed" },
      { status: 500 }
    );
  }
}

// Helper function to verify Telegram data
function verifyTelegramData(data: any): boolean {
  if (!BOT_TOKEN) {
    console.error("TELEGRAM_BOT_TOKEN is not defined");
    return false;
  }

  const { hash, ...restData } = data;
  
  // Create data check string
  const dataCheckString = Object.keys(restData)
    .sort()
    .map(key => `${key}=${restData[key]}`)
    .join('\n');
  
  // Generate secret key
  const secretKey = crypto
    .createHash("sha256")
    .update(BOT_TOKEN)
    .digest();
  
  // Calculate hash
  const calculatedHash = crypto
    .createHmac("sha256", secretKey)
    .update(dataCheckString)
    .digest("hex");
  
  return calculatedHash === hash;
}

// Function to generate authentication token (JWT example)
function generateAuthToken(user: any): string {
  // In a real implementation, you would use a JWT library
  // Example with jsonwebtoken:
  // import jwt from 'jsonwebtoken';
  // return jwt.sign(user, process.env.JWT_SECRET!, { expiresIn: '7d' });
  
  // For demo purposes:
  return `simulated-jwt-token-for-user-${user.id}`;
}

// Add the following type declaration in a .d.ts file
// types/global.d.ts
declare global {
  interface Window {
    onTelegramAuth(user: any): void;
  }
}