// @ts-nocheck

// src/app/api/translate/route.ts
import { NextRequest, NextResponse } from 'next/server';

const langCodeMap: Record<string, string> = {
  'zh': 'zh',
  'ja': 'ja', 
  'fr': 'fr',
  'es': 'es'
};

// Using Google Translate web API directly (free but unofficial)
async function translateWithGoogleWeb(text: string, targetLang: string) {
  try {
    console.log('Attempting Google Web Translate for:', text, 'to:', targetLang);
    
    const encodedText = encodeURIComponent(text);
    const url = `https://translate.googleapis.com/translate_a/single?client=gtx&sl=en&tl=${targetLang}&dt=t&q=${encodedText}`;
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('Google response data:', data);
    
    // Google Translate returns nested arrays, extract the translated text
    if (data && data[0] && data[0][0] && data[0][0][0]) {
      const translatedText = data[0][0][0];
      console.log('Google Web Translate success:', translatedText);
      return translatedText;
    }
    
    throw new Error('Invalid response format from Google');
  } catch (error) {
    console.error('Google Web Translate error:', error);
    throw error;
  }
}

// LibreTranslate fallback
async function translateWithLibre(text: string, targetLang: string) {
  console.log('Attempting LibreTranslate for:', text, 'to:', targetLang);
  
  const response = await fetch('https://libretranslate.de/translate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      q: text,
      source: 'en',
      target: targetLang,
      format: 'text'
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('LibreTranslate HTTP error:', response.status, errorText);
    throw new Error(`LibreTranslate error: ${response.status}`);
  }

  const data = await response.json();
  console.log('LibreTranslate response:', data);
  
  if (data.translatedText) {
    console.log('LibreTranslate success:', data.translatedText);
    return data.translatedText;
  }
  
  throw new Error('No translated text in LibreTranslate response');
}

export async function POST(request: NextRequest) {
  console.log('=== Translation API called ===');
  
  try {
    const body = await request.json();
    console.log('Request body:', body);
    
    const { text, targetLang } = body;
    
    if (!text) {
      console.log('No text provided, returning empty string');
      return NextResponse.json({ translatedText: '' });
    }
    
    if (targetLang === 'en') {
      console.log('Target language is English, returning original text');
      return NextResponse.json({ translatedText: text });
    }

    const mappedLang = langCodeMap[targetLang] || targetLang;
    console.log('Mapped language from', targetLang, 'to', mappedLang);

    // Try Google Web Translate first
    try {
      const translatedText = await translateWithGoogleWeb(text, mappedLang);
      console.log('✅ Translation successful via Google:', translatedText);
      return NextResponse.json({ translatedText });
    } catch (googleError) {
      console.error('❌ Google Web Translate failed:', );
      
      // Fallback to LibreTranslate
      try {
        const translatedText = await translateWithLibre(text, mappedLang);
        console.log('✅ Translation successful via LibreTranslate:', translatedText);
        return NextResponse.json({ translatedText });
      } catch (libreError) {
        console.error('❌ LibreTranslate also failed:', );
      }
    }

    // Return original text if all translation methods fail
    console.log('⚠️ All translation methods failed, returning original text');
    return NextResponse.json({ translatedText: text });

  } catch (error) {
    console.error('💥 Translation API error:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'Unknown error');
    
    return NextResponse.json({ 
      translatedText: text || 'Translation Error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 200 }); // Return 200 to avoid breaking the UI
  }
}