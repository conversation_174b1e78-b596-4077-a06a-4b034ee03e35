// src/app/upload/[encrypted_user_id]/page.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import axios, { AxiosError } from 'axios';
import { Loader, Upload } from 'lucide-react';
import ReportImage from '@/components/Logo';
import { backendUrl } from "@/config/baseUrl";

interface UserDetails {
  firstname: string;
  lastname: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  country: string;
}

interface UploadReceiptResponse {
  message: string;
  receipt: {
    id: number;
    user_id: string;
    receipt_url: string;
    credited: boolean;
    created_at: string;
  };
}

export default function UploadSlipPage() {
  const router = useRouter();
  const params = useParams();
  const encryptedUserId = params.encrypted_user_id as string;

  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  const [isUserLoading, setIsUserLoading] = useState(true);
  const [userError, setUserError] = useState<string | null>(null);

  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState<string | null>(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false); // New state for success modal

  const localApi = axios.create({
    baseURL: backendUrl,
    headers: { 'Content-Type': 'application/json' },
  });

  useEffect(() => {
    if (!encryptedUserId) {
      setIsUserLoading(false);
      setUserError("User ID not found in URL.");
      return;
    }

    const fetchUserDetails = async () => {
      setIsUserLoading(true);

      try {
        const response = await localApi.get<UserDetails>(`/auth/user-details/${encryptedUserId}`);
        setUserDetails(response.data);
      } catch (err) {
        const error = err as AxiosError<{ message: string }>;
        let errorMessage = 'Something went wrong, try again';

        if (error.response) {
          if (error.response.status === 404) {
            errorMessage = 'Invalid details. The provided user ID is not found.';
          } else if (error.response.data?.message) {
            errorMessage = error.response.data.message;
          }
        }
        setUserError(errorMessage);
      } finally {
        setIsUserLoading(false);
      }
    };

    fetchUserDetails();
  }, [encryptedUserId]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];

    setUploadError(null);

    if (selectedFile) {
      // Validate file size (max 5MB)
      if (selectedFile.size > 5 * 1024 * 1024) {
        setUploadError("File size cannot exceed 5MB. Please upload a smaller file.");
        setFile(null);
        return;
      }

      // Validate file type (PDF or JPEG only)
      if (!['application/pdf', 'image/jpeg'].includes(selectedFile.type)) {
        setUploadError("Invalid file format. Please upload a PDF or JPEG file.");
        setFile(null);
        return;
      }

      setFile(selectedFile);
    } else {
      setFile(null);
    }
  };

  /**
   * Handles the form submission to upload the file.
   */
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!file) {
      setUploadError("Please select a file to upload.");
      return;
    }

    setIsUploading(true);
    setUploadError(null);
    setUploadSuccess(null);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await localApi.post<UploadReceiptResponse>(
        `/auth/upload-transaction-image/${encryptedUserId}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      setUploadSuccess(response.data.message);
      setShowSuccessModal(true); // Show the success modal
      setFile(null);

    } catch (err) {
      const error = err as AxiosError<{ message: string }>;
      let errorMessage = 'An unexpected error occurred during upload. Please try again.';

      if (error.response) {
        if (error.response.status === 404) {
          errorMessage = 'Invalid details. The user ID for upload is not found.';
        } else if (error.response.data?.message) {
          errorMessage = error.response.data.message;
        }
      }
      setUploadError(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const handleModalOkay = () => {
    setShowSuccessModal(false);
    router.push('/login'); 
  };

  if (isUserLoading) {
    return <div className="pt-20 text-center text-gray-600"><Loader/></div>;
  }

  if (userError) {
    return (
      <div className="pt-20 flex flex-col items-center justify-center min-h-screen bg-gray-50">
        <div className="bg-white shadow-xl rounded-lg p-8 border border-gray-200 text-center">
          <p className="text-red-600 font-medium text-lg">Error: {userError}</p>
          <button
            type="button"
            onClick={() => router.back()}
            className="mt-6 px-6 py-2 border border-gray-300 text-gray-700 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-10 flex flex-col items-center justify-center min-h-screen bg-gray-50">
      <main className="w-full max-w-2xl mx-auto p-4">
        <form onSubmit={handleSubmit} className="bg-white shadow-xl rounded-lg p-8 border border-gray-200">
          <div className="flex justify-center mb-6">
            <ReportImage textColor="black" />
          </div>

          <div className="text-left mb-6">
            <h1 className="text-2xl font-bold text-gray-800">
              Hi {userDetails?.firstname || ''}!
            </h1>
            <p className="text-gray-600 mt-2 text-lg">
              Please upload your transaction slip.
            </p>
            <p className="text-sm text-gray-500 mt-1">
              Made the transfer? Upload your transfer slip or receipt below.
            </p>
          </div>

          {/* File Upload Area */}
          <div className="relative border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 transition-colors duration-200 ease-in-out bg-gray-50">
            <div className="flex flex-col items-center text-gray-500">
              <Upload className="w-10 h-10 mb-3 text-gray-400" />
              <span className="text-base font-semibold text-gray-700">
                {file ? file.name : 'Click to upload or drag & drop'}
              </span>
              <p className="text-sm mt-1 text-gray-500">PDFs and JPEGs up to a maximum size of 5MB</p>
            </div>
            <input
              type="file"
              className="opacity-0 absolute inset-0 w-full h-full cursor-pointer"
              onChange={handleFileChange}
              accept="application/pdf,image/jpeg"
              disabled={isUploading}
              aria-label="Upload transaction slip"
            />
          </div>

          {/* Display messages based on upload status */}
          {uploadError && <p className="text-red-600 text-sm mt-4 text-center animate-fade-in">{uploadError}</p>}
          {uploadSuccess && !showSuccessModal && <p className="text-green-600 text-sm mt-4 text-center animate-fade-in">{uploadSuccess}</p>}

          {/* Action Buttons */}
          <div className="flex items-center justify-between mt-8">
            <button
              type="button"
              onClick={() => router.back()}
              className="px-6 py-2 border border-gray-300 text-gray-700 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              disabled={isUploading}
            >
              Back
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-gray-800 text-white text-sm font-semibold rounded-md hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              disabled={!file || isUploading}
            >
              {isUploading ? 'Submitting...' : 'Submit'}
            </button>
          </div>
        </form>
      </main>

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in">
          <div className="bg-white rounded-lg shadow-xl p-8 max-w-sm w-full text-center">
            <h2 className="text-2xl font-bold text-green-600 mb-4">Success!</h2>
            <p className="text-gray-700 mb-6">{uploadSuccess}</p>
            <button
              onClick={handleModalOkay}
              className="px-6 py-2 bg-gray-800 text-white text-sm font-semibold rounded-md hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-800 transition-colors"
            >
              Okay
            </button>
          </div>
        </div>
      )}
    </div>
  );
}