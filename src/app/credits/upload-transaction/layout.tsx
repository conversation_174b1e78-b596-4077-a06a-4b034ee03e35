import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON>, Figtree } from "next/font/google";
import "@/app/globals.css";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

import { Festive } from "next/font/google";
import { ReactQueryProvider } from "@/Providers/ReactQueryProvider";
import { Toaster } from "sonner";
import { LanguageProvider } from "@/Providers/LanguageProvider";

export const metadata: Metadata = {
  title: "ai-wk",
  description: "",
  icons: {
    icon: "/favicon32.png", 
  },
  other: {
    cryptomus: "9a2a8d6e",
  },
};

const figtree = Figtree({
  subsets: ["latin"],
  weight: ["500", "700"], // Add the weights you need
  variable: "--font-figtree", // Optional variable name
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${figtree.className}   antialiased`}>
      <Toaster richColors />
        <LanguageProvider>
        <div>
          <ReactQueryProvider>
            <main className="flex flex-col min-h-screen">{children}</main>
          </ReactQueryProvider>
        </div>
        </LanguageProvider>
      </body>
    </html>
  );
}
