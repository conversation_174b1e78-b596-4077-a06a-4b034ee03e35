import Head from 'next/head';
import ReportImage from '@/components/Logo';
import { siteConfig } from "@/static/site";
import Link from 'next/link'

export default function ReportPage() {
    return (
        <div className="min-h-screen bg-gray-50"> {/* Changed background to a lighter gray as seen in image */}
            {/* Header */}
            <header className="w-full flex items-center px-6 py-4 border-b border-gray-200 bg-white">
                <div className="flex items-center space-x-20"> {/* Use space-x-2 for spacing between logo and text */}
                    <ReportImage textColor="black" />
                    <Link
                        href="/"
                        className="text-sm text-gray-700 hover:text-black hover:underline"
                    >
                        Return to website
                    </Link>
                </div>
            </header>

            {/* Center Card */}
            <main className="flex justify-center items-center py-20 px-4">
                <div className="bg-white shadow-md rounded-lg p-7 max-w-xl w-full">
                    <h1 className="text-xl font-semibold text-gray-800 mb-3">Sample Report</h1>
                    <p className="text-sm text-gray-600 mb-6 leading-relaxed">
                        This sample page demonstrates the <span className="font-medium">format and process</span> for
                        downloading AI-generated reports via{' '}
                        <a
                            href="https://ai-wk.com"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                        >
                            ai-wk.com
                        </a>. The downloadable file below is <span className="font-medium">representative of actual report outputs</span>.
                    </p>
                    <br />
                    <div className="pt-10">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-900">Report name.pdf</p> 
                                <p className="text-xs text-gray-500 mt-1">12 March 2025</p>
                            </div>
                            <a
                                href="/path/to/your/report-name.pdf" 
                                download="Report name.pdf"
                                className="inline-flex items-center px-4 py-2 text-sm font-medium bg-gray-50 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100 transition duration-150 ease-in-out" // Subtle background and border
                            >
                                Download Report
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="ml-2 h-4 w-4 text-gray-500"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                >
                                    <path
                                        fillRule="evenodd"
                                        d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    );
}
