"use client";
import { FC, useState } from "react";
import { useTranslations } from 'next-intl';
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useSubmitQueryHermes } from "@/services/api_hooks";
import { toast } from "sonner";
import { Loader } from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";

interface FreeTrialModalProps {
  isOpen: boolean;
  onClose: () => void;
  refetchTrialInfo: () => void; // Add this prop
}

const FreeTrialModal: FC<FreeTrialModalProps> = ({
  isOpen,
  onClose,
  refetchTrialInfo, // Destructure here
}) => {

  // use next-intl for i18n
  const t = useTranslations("DashboardHermesC.FreeTrial");
  const tGlobal = useTranslations("global");

  const { mutateAsync: startTrial, isPending: startTrialLoading } =
    useSubmitQueryHermes("api/hermesc/user/activate-trial", "POST", {
      onSuccess() {
        toast.success(t("toast.TrialStartedSuccessfully"), {
          position: "top-right",
          className: "p-4",
        });
        refetchTrialInfo(); // Refetch trial info after success
        onClose(); // Optionally close modal after success
      },
      onError(err: any) {
        toast.error(err.response?.data?.message || t("toast.notStartTrial"), {
          position: "top-right",
          className: "p-4",
        });
      },
    });

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[447px] p-0 overflow-hidden bg-white rounded-lg">
          <div className="w-full h-[267px] bg-blue-50 flex items-end justify-center">
            <Image
              src="/free_trial.svg"
              alt="HermesX Logo"
              width={40}
              height={40}
              priority
              className="object-cover w-full h-full"
            />
          </div>

          <div className="px-6 mb-1">
            <DialogHeader>
              <DialogTitle className="text-[16px] font-[500] ">
                {t("title")}
              </DialogTitle>
              <DialogDescription>
                <span className="text-start text-[#737384] text-xs leading-[20px] mt-2">
                  {t("description")}
                </span>
              </DialogDescription>
            </DialogHeader>
          </div>

          <DialogFooter className="px-6 pb-6 sm:pb-6">
            <div className="flex items-center justify-between w-full gap-4">
              {/* <Button
                className="max-w-[80px] text-black rounded-md"
                onClick={onClose}
                variant="outline"
              >
                Cancel
              </Button> */}
              <Button
                className="bg-gray-900 w-full hover:bg-gray-800 text-white rounded-md"
                onClick={() => startTrial({})}
                disabled={startTrialLoading}
              >
                {t("StartFreeTrial")}
                {startTrialLoading && <Loader className="animate-spin" />}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default FreeTrialModal;
