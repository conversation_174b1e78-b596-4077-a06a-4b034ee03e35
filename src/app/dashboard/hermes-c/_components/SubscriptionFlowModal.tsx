// @ts-nocheck

import React, { useState, useEffect } from "react";
import { useTranslations } from 'next-intl';
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useQueryClient } from "@tanstack/react-query";
import {
  useGetQuery,
  useGetQueryHermes,
  useSubmitQueryHermes,
} from "@/services/api_hooks";
import { toast } from "sonner";
import Image from "next/image";
import {
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Loader, AlertTriangle } from "lucide-react";

// Define the props for the component
const EnquireFlowModalHermesC = ({ isOpen, onClose }) => {

  // use next-intl for i18n
  const t = useTranslations("DashboardHermesC.EnquireFlow");
  const tForm = useTranslations("Form");
  const tGlobal = useTranslations("global");
  const tTable = useTranslations("Table");

  // State for tracking the current step in the flow (now only 2 steps)
  const [currentStep, setCurrentStep] = useState(1);

  // State for Schedule Meeting form
  const [meetingForm, setMeetingForm] = useState({
    organization: "",
    investorType: "",
    country: "",
    message: "",
  });

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [initializing, setInitializing] = useState(false); // No API calls needed for initialization
  const [hasAcceptedDisclaimer, setHasAcceptedDisclaimer] = useState(false);

  const queryClient = useQueryClient();

  // Determine starting step - always start at step 1 (Disclaimer)
  useEffect(() => {
    setCurrentStep(1);
    setInitializing(false);
  }, []);

  // API mutation for accepting disclaimer
  const { mutateAsync: acceptDisclaimer, isPending: disclaimerPending } =
    useSubmitQueryHermes("/api/hermesc/user/disclaimer", "POST", {
      onSuccess(response) {
        toast.success(t("toast.DisclaimerAccepted"), {
          position: "top-right",
          className: "p-4",
        });
        setHasAcceptedDisclaimer(true);
        setCurrentStep(2);
        setIsSubmitting(false);
      },
      onError(err) {
        toast.error(
          err?.response?.data?.error || t("toast.FailedToAccept"),
          {
            position: "top-right",
            className: "p-4",
          }
        );
        setIsSubmitting(false);
      },
    });

  // API mutation for saving meeting request
  const { mutateAsync: saveMeeting, isPending: meetingPending } =
    useSubmitQueryHermes("/api/hermesc/user/meeting", "POST", {
      onSuccess(response) {
        toast.success(t("toast.MeetingSuccessfully"), {
          position: "top-right",
          className: "p-4",
        });
        // Reset form
        setMeetingForm({
          organization: "",
          investorType: "",
          country: "",
          message: "",
        });
        // Close modal
        onClose();
        setIsSubmitting(false);
      },
      onError(err) {
        toast.error(
          err?.response?.data?.error || t("toast.FailedToSave"),
          {
            position: "top-right",
            className: "p-4",
          }
        );
        setIsSubmitting(false);
      },
    });

  // Handle disclaimer acceptance
  const handleAcceptDisclaimer = async () => {
    setIsSubmitting(true);
    await acceptDisclaimer({});
  };

  // Handle meeting form submission
  const handleSubmitMeeting = async () => {
    setIsSubmitting(true);
    await saveMeeting({
      organization: meetingForm.organization,
      typeOfInvestor: meetingForm.investorType,
      country: meetingForm.country,
      message: meetingForm.message,
    });
  };

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setMeetingForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Check if meeting form is valid
  const isMeetingFormValid =
    meetingForm.organization.trim() &&
    meetingForm.investorType.trim() &&
    meetingForm.country.trim() &&
    meetingForm.message.trim();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      {initializing ? (
        <DialogContent className="sm:max-w-[447px] p-6 bg-white rounded-lg">
          <div className="flex items-center justify-center h-64">
            <div className="flex flex-col items-center gap-2">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              <p className="text-sm text-gray-500">{t("Loading")}</p>
            </div>
          </div>
        </DialogContent>
      ) : (
        <div>
          {currentStep === 1 && ( // Disclaimer Step
            <DialogContent
              className="sm:max-w-[440px] p-0 pb-3 bg-white rounded-lg overflow-hidden
            "
            >
              {/* Warning Icon */}
              <div className="w-full h-[170px] bg-blue-50 flex items-end justify-center">
                <Image
                  src="/hermesbanner_image.svg"
                  alt="Hermes c disclaimer"
                  width={235}
                  height={200}
                  priority
                  className="object-contain"
                />
              </div>
              <div className="px-4 overflow-y-auto max-h-[480px]">
                <DialogHeader className="text-center mb-6 ">
                  <DialogTitle className="text-[18px] font-medium mb-4">
                    {t("ImportantDisclaimers.title")}
                  </DialogTitle>
                  <DialogDescription className="text-left">
                    <div className="text-xs text-[#737384] space-y-4">
                      <p className="text-sm leading-relaxed">
                        {t("ImportantDisclaimers.description")}
                      </p>

                      <div className="space-y-3">
                        <div className="flex items-start gap-2">
                          <div className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                          <div>
                            <span className="font-medium">
                              {t("ImportantDisclaimers.contents.line1.text1")}
                            </span>{" "}{t("ImportantDisclaimers.contents.line1.text2")}
                          </div>
                        </div>

                        <div className="flex items-start gap-2">
                          <div className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                          <div>
                            <span className="font-medium">
                              {t("ImportantDisclaimers.contents.line2.text1")}
                            </span>{" "}{t("ImportantDisclaimers.contents.line2.text2")}
                            
                          </div>
                        </div>

                        <div className="flex items-start gap-2">
                          <div className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                          <div>
                            <span className="font-medium">
                            {t("ImportantDisclaimers.contents.line3.text1")}
                            </span>{" "}{t("ImportantDisclaimers.contents.line3.text2")}
                            
                          </div>
                        </div>

                        <div className="flex items-start gap-2">
                          <div className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                          <div>
                            <span className="font-medium">
                              {t("ImportantDisclaimers.contents.line4.text1")}
                            </span>{" "}{t("ImportantDisclaimers.contents.line4.text2")}
                            
                          </div>
                        </div>
                      </div>
                    </div>
                  </DialogDescription>
                </DialogHeader>

                <DialogFooter className="p-0 m-0">
                <div className="flex items-center justify-between w-full gap-4">
                  <Button
                    variant="outline"
                    onClick={onClose}
                    className="border border-gray-300 h-[32px] text-black"
                    disabled={disclaimerPending}
                  >
                    {tGlobal("Cancel")}
                  </Button>
                  <Button
                    onClick={handleAcceptDisclaimer}
                    className="bg-gray-900 text-white h-[32px] hover:bg-gray-800 w-full max-w-[300px]"
                    disabled={disclaimerPending}
                  >
                    {disclaimerPending ? (
                      <>
                        {/* <Loader className="mr-2 w-4 h-4 animate-spin" /> */}
                        {tForm("Button.Processing")}
                      </>
                    ) : tForm("Button.AgreeAndContinue")}
                  </Button>
                  </div>
                </DialogFooter>
              </div>
            </DialogContent>
          )}

          {currentStep === 2 && ( // Schedule Meeting Step
            <DialogContent className="sm:max-w-[447px] max-h-[600px] overflow-y-auto p-6 bg-white rounded-lg">
              <DialogHeader className="mb-4">
                <DialogTitle className="text-[18px] font-[600]">
                  {t("ScheduleMeeting.title")}
                </DialogTitle>
                <DialogDescription className="text-xs text-[#737384] font-[400]">
                  {t("ScheduleMeeting.description")}
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                {/* Organization */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("Organization.text")}
                  </label>
                  <Input
                    placeholder={t("Organization.description")}
                    value={meetingForm.organization}
                    onChange={(e) =>
                      handleInputChange("organization", e.target.value)
                    }
                    className="w-full"
                    disabled={meetingPending}
                  />
                </div>

                {/* Type of Investor */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("TypeOfInvestor.text")}
                  </label>
                  <Input
                    placeholder={t("TypeOfInvestor.description")}
                    value={meetingForm.investorType}
                    onChange={(e) =>
                      handleInputChange("investorType", e.target.value)
                    }
                    className="w-full"
                    disabled={isSubmitting}
                  />
                </div>

                {/* Country */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("Country.text")}
                  </label>
                  <Input
                    placeholder={t("Country.description")}
                    value={meetingForm.country}
                    onChange={(e) =>
                      handleInputChange("country", e.target.value)
                    }
                    className="w-full"
                    disabled={isSubmitting}
                  />
                </div>

                {/* Message */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {tForm("message.text")}
                  </label>
                  <Textarea
                    placeholder="Brief description of your interest or any questions"
                    value={meetingForm.message}
                    onChange={(e) =>
                      handleInputChange("message", e.target.value)
                    }
                    className="w-full min-h-[100px] resize-none"
                    disabled={isSubmitting}
                  />
                </div>
              </div>

              <DialogFooter className="p-0 m-0">
              <div className="flex items-center justify-between w-full gap-4">
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="border border-gray-300 h-[32px] text-black"
                  disabled={isSubmitting}
                >
                  {tGlobal("Cancel")}
                </Button>
                <Button
                  onClick={handleSubmitMeeting}
                  className="bg-gray-900 text-white h-[32px] hover:bg-gray-800 w-full max-w-[300px]"
                  disabled={!isMeetingFormValid || meetingPending}
                >
                  {meetingPending ? (
                    <>
                      {/* <Loader className="mr-2 w-4 h-4 animate-spin" /> */}
                      {tForm("Button.Submitting")}
                    </>
                  ) : (
                    tForm("Button.Submit")
                  )}
                </Button>
                </div>
              </DialogFooter>
            </DialogContent>
          )}
        </div>
      )}
    </Dialog>
  );
};

export default EnquireFlowModalHermesC;
