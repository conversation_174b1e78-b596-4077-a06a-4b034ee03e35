import React, { useState } from "react";
import { useTranslations } from 'next-intl';
import { Skeleton } from "@/components/ui/skeleton";
import { useGetQueryHermes } from "@/services/api_hooks";
import { Info, ZoomIn } from "lucide-react";
import View<PERSON><PERSON> from "./ViewChart";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import moment from "moment";

interface Alert {
  time_news: string;
  ticker: string;
  event: string;
  direction: string;
  condition: string;
  strategy_return: string;
  bnh2close: string;
  bnh2max: string;
  chart_link: string;
  confidence: string;
}

interface HermesCTableComponentProps {
  alerts: Alert[];
  isLoading: boolean;
  // isFetching: boolean;
  timezone: string;
}

const Table: React.FC<HermesCTableComponentProps> = ({
  alerts,
  isLoading,
  // isFetching,
  timezone,
}) => {

  // use next-intl for i18n
  const t = useTranslations("DashboardHermesC.AlertsTable.Table");
  const tForm = useTranslations("Form");
  const tGlobal = useTranslations("global");
  const tTable = useTranslations("Table");

  const [showChart, setShowChart] = useState(false);
  const [chartLink, setChartLink] = useState("");
  const truncateText = (text: string, maxLength = 100) => {
    if (!text) return "";
    return text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text;
  };

  const { data: savedPreferences, isLoading: preferencesLoading } =
    useGetQueryHermes("/api/hermesc/user/filters", ["get-filters"], {
      staleTime: 1000 * 60 * 5, // 5 minutes
      onError() {
        // toast.error("Could not load saved filters");
      },
    });

  const roundUpPercent = (value: string | null | undefined): string => {
    if (!value || typeof value !== "string") {
      return "-";
    }
    const number = parseFloat(value.replace("%", ""));
    if (isNaN(number) || number === 0) {
      return "-";
    }
    const rounded = Math.ceil(number * 100) / 100;
    return `${rounded.toFixed(2)}%`;
  };

  const getTimezoneDisplay = () => {
    if (
      timezone === "UserTime" ||
      savedPreferences?.filters?.timezone === "UserTime"
    )
      return "User time";
    return timezone || savedPreferences?.filters?.timezone || "NYT";
  };

  const handleOpenChart = (link: string) => {
    setShowChart(true);
    setChartLink(link);
  };

  // Mobile card view for alerts
  const renderAlertCard = (alert: Alert, index: number) => (
    <div key={index} className="bg-white p-4 rounded-lg border space-y-3">
      <div className="flex justify-between items-start">
        <div className="text-xs text-gray-600">
          {moment(alert.time_news).format("YYYY-MM-DD HH:mm")}
        </div>
        <span
          className={`px-2 py-1 text-xs font-medium rounded-full ${
            alert.direction === "up"
              ? "bg-green-100 text-green-600"
              : "bg-red-100 text-red-600"
          }`}
        >
          {alert.direction}
        </span>
      </div>
      <div className="flex flex-wrap gap-x-4 gap-y-1 text-xs">
        <div>
          <span className="font-medium text-gray-700">{t("Header.Ticker")}: </span>
          <span>{alert.ticker}</span>
        </div>
        <div>
          <span className="font-medium text-gray-700">{t("Header.Confidence")}: </span>
          <span>{alert.confidence}</span>
        </div>
      </div>
      <div className="text-xs">
        <span className="font-medium text-gray-700">{t("Header.Event")}: </span>
        <span>{alert.event}</span>
      </div>
      <div className="text-xs">
        <span className="font-medium text-gray-700">{t("Header.Condition")}: </span>
        <span>{alert.condition}</span>
      </div>
      <div className="flex flex-wrap gap-x-4 gap-y-1 text-xs">
        <div>
          <span className="font-medium text-gray-700">{t("RawStrategyReturn.text")}: </span>
          <span>{roundUpPercent(alert.strategy_return)}</span>
        </div>
        <div>
          <span className="font-medium text-gray-700">{t("PassiveReturnClose.text")}: </span>
          <span>{roundUpPercent(alert.bnh2close)}</span>
        </div>
        <div>
          <span className="font-medium text-gray-700">{t("ActiveReturnToMax.text")}: </span>
          <span>{roundUpPercent(alert.bnh2max)}</span>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <ZoomIn
          size={20}
          className="cursor-pointer"
          onClick={() => handleOpenChart(alert.chart_link)}
        />
        {/* <span className="text-xs text-gray-500">{t("Header.ViewChart")}</span> */}
      </div>
    </div>
  );

  return (
    <div className="border rounded-lg">
      {/* Desktop Table View */}
      <div className="hidden lg:block overflow-x-auto border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200 text-black">
          <thead className="bg-gray-50 font-[600] text-[#6C7788] text-xs">
            <tr>
              <th scope="col" className="px-3 py-3 text-left w-[120px]">
                {t("Header.Time")} ({getTimezoneDisplay()})
              </th>
              <th scope="col" className="px-3 py-3 text-left w-[80px]">
                {t("Header.Ticker")}
              </th>
              <th scope="col" className="px-3 py-3 text-left w-[300px]">
                {t("Header.Event")}
              </th>
              <th scope="col" className="px-3 py-3 text-center w-[80px]">
                {t("Header.Direction")}
              </th>
              <th scope="col" className="px-3 py-3 text-center w-[90px]">
                {t("Header.Confidence")}
              </th>
              <th scope="col" className="px-3 py-3 text-left w-[80px]">
                {t("Header.Condition")}
              </th>
              <th scope="col" className="px-3 py-3 text-center w-[90px]">
                <TooltipProvider delayDuration={100}>
                  <Tooltip>
                    <TooltipTrigger asChild className="">
                      <div className="flex items-center   cursor-pointer gap-1">
                        {t("RawStrategyReturn.text")}
                        <div className="min-w-4">
                          <Info color="#CB680C" size={18} />
                        </div>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent
                      className="max-w-[300px] gap-3 flex flex-col p-3 text-xs text-start font-[400] leading-5 bg-white text-black"
                      side="top"
                    >
                      <span className="flex flex-col gap-1 font-semibold">
                        <Info size={14} color="#CB680C" /> {t("RawStrategyReturn.text")}
                      </span>
                      <span>
                        {t("RawStrategyReturn.description")}
                      </span>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </th>
              <th scope="col" className="px-3 py-3 text-center w-[90px]">
                <TooltipProvider delayDuration={100}>
                  <Tooltip>
                    <TooltipTrigger asChild className="">
                      <div className="flex items-center  cursor-pointer gap-1">
                        {t("PassiveReturnClose.text")}
                        <div className="min-w-4">
                          <Info color="#CB680C" size={18} />
                        </div>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent
                      className="max-w-[300px] gap-2 flex flex-col p-3 text-xs text-start font-[400] leading-5 bg-white text-black"
                      side="top"
                    >
                      <span className="flex flex-col gap-1 font-semibold">
                        <Info size={14} color="#CB680C" /> {t("PassiveReturnClose.text")}</span>
                      <span>
                        {t("PassiveReturnClose.description")}
                      </span>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </th>
              <th scope="col" className="px-3 py-3 text-center w-[90px]">
                <TooltipProvider delayDuration={100}>
                  <Tooltip>
                    <TooltipTrigger asChild className="">
                      <div className="flex items-center ml-1 cursor-pointer gap-1">
                        {t("ActiveReturnToMax.text")}
                        <div className="min-w-4">
                          <Info color="#CB680C" size={18} />
                        </div>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent
                      className="max-w-[300px] gap-2 flex flex-col p-3 text-xs text-start font-[400] leading-5 bg-white text-black"
                      side="top"
                    >
                      <span className="flex flex-col gap-1 font-semibold">
                        <Info size={14} color="#CB680C" /> {t("ActiveReturnToMax.text")}
                      </span>
                      <span>
                        {t("ActiveReturnToMax.description")}
                      </span>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </th>
              <th className="px-3 py-3 text-left w-[50px]">
                {/* Zoom icon col */}
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200 font-[400]">
            {isLoading ? (
              Array(5)
                .fill(0)
                .map((_, index) => (
                  <tr key={`skeleton-${index}`}>
                    <td className="px-3 py-2 h-[84px]">
                      <Skeleton className="h-4 w-10" />
                    </td>
                    <td className="px-3 py-2 h-[84px]">
                      <Skeleton className="h-4 w-10" />
                    </td>
                    <td className="px-3 py-2 h-[84px]">
                      <Skeleton className="h-4 w-10 lg:w-40" />
                    </td>
                    <td className="px-3 py-2 h-[84px]">
                      <Skeleton className="h-4 w-10" />
                    </td>
                    <td className="px-3 py-2 h-[84px]">
                      <Skeleton className="h-4 w-10" />
                    </td>
                    <td className="px-3 py-2 h-[84px]">
                      <Skeleton className="h-6 w-10" />
                    </td>
                    <td className="px-3 py-2 h-[84px]">
                      <Skeleton className="h-6 w-10" />
                    </td>
                    <td className="px-3 py-2 h-[84px]">
                      <Skeleton className="h-6 w-10" />
                    </td>
                    <td className="px-3 py-2 h-[84px]">
                      <Skeleton className="h-6 w-10" />
                    </td>
                    <td className="px-3 py-2 h-[84px]">
                      <Skeleton className="h-6 w-10" />
                    </td>
                  </tr>
                ))
            ) : alerts.length > 0 ? (
              alerts.map((alert, index) => (
                <tr key={index}>
                  <td className="px-3 py-1 h-[74px] text-[12px]">
                    {moment(alert.time_news).format("YY-MM-DD HH:mm")}
                  </td>
                  <td className="px-3 py-1 h-[74px] text-[12px]">
                    {alert.ticker}
                  </td>
                  <td className="px-3 py-1 h-[74px] text-[12px]">
                    {alert.event}
                  </td>
                  <td className="px-3 py-1 h-[74px] text-[12px] text-center">
                    {alert.direction}
                  </td>
                  <td className="px-3 py-1 h-[74px] text-[12px] text-center">
                    {alert.confidence}
                  </td>
                  <td className="px-3 py-1 h-[74px] text-[12px]">
                    {alert.condition}
                  </td>
                  <td className="px-3 py-1 h-[74px] text-[12px] text-center">
                    {roundUpPercent(alert.strategy_return)}
                  </td>
                  <td className="px-3 py-1 h-[74px] text-[12px] text-center">
                    {roundUpPercent(alert.bnh2close)}
                  </td>
                  <td className="px-3 py-1 h-[74px] text-[12px] text-center">
                    {roundUpPercent(alert.bnh2max)}
                  </td>
                  <td className="px-3 py-1 h-[74px]">
                    <ZoomIn
                      size={20}
                      className="cursor-pointer"
                      onClick={() => handleOpenChart(alert.chart_link)}
                    />
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={10}
                  className="px-3 py-10 text-center text-gray-500"
                >
                  {t("NoResultsFound")}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="lg:hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-sm font-semibold text-gray-700">
            ({getTimezoneDisplay()})
          </h3>
        </div>
        <div className="p-4 space-y-4 max-h-[70vh] overflow-y-auto">
          {isLoading ? (
            Array(5)
              .fill(0)
              .map((_, index) => (
                <div key={`skeleton-card-${index}`} className="bg-white p-4 rounded-lg border space-y-3">
                  <div className="flex justify-between items-start">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-16 rounded-full" />
                  </div>
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <div className="flex flex-wrap gap-2">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <Skeleton className="h-4 w-full" />
                </div>
              ))
          ) : alerts.length > 0 ? (
            alerts.map((alert, index) => renderAlertCard(alert, index))
          ) : (
            <div className="text-center py-10 text-gray-500">
              {t("NoResultsFound")}
            </div>
          )}
        </div>
      </div>

      <ViewChart
        isOpen={showChart}
        onClose={() => setShowChart(false)}
        chartLink={chartLink}
      />
    </div>
  );
};

export default Table;
