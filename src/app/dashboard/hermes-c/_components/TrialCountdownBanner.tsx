import React, { useEffect, useState } from "react";
import { useTranslations } from "next-intl";

interface TrialCountdownBannerProps {
  trialStartDate: string | null;
  trialEndDate: string | null;
}

const getTimeLeft = (end: string | null) => {
  if (!end) return null;
  const endDate = new Date(end);
  const now = new Date();
  const diff = endDate.getTime() - now.getTime();
  if (diff <= 0) return null;
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);
  const minutes = Math.floor((diff / (1000 * 60)) % 60);
  const seconds = Math.floor((diff / 1000) % 60);
  return { days, hours, minutes, seconds };
};

const TrialCountdownBanner: React.FC<TrialCountdownBannerProps> = ({
  trialStartDate,
  trialEndDate,
}) => {
  const t = useTranslations("DashboardHermesC.TrialCountdownBanner");
  const [timeLeft, setTimeLeft] = useState(() => getTimeLeft(trialEndDate));

  useEffect(() => {
    if (!trialEndDate) return;
    const interval = setInterval(() => {
      setTimeLeft(getTimeLeft(trialEndDate));
    }, 1000);
    return () => clearInterval(interval);
  }, [trialEndDate]);

  if (!trialStartDate || !trialEndDate || !timeLeft) return null;

  return (
    <div className="w-full bg-[#D6EAFF] text-[#0F172A] px-4 py-2 mb-4 text-center font-medium">
      <span className="font-bold">
        {t("countdown", {
          days: timeLeft.days,
          hours: timeLeft.hours,
          minutes: timeLeft.minutes,
          seconds: timeLeft.seconds,
        })}
      </span>
    </div>
  );
};

export default TrialCountdownBanner;
