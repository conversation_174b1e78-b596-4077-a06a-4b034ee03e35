// components/WelcomeModal.tsx
"use client";
import { FC, useState, useEffect } from "react";
import { useTranslations } from 'next-intl';
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { hermesAPI, useGetQueryHermes } from "@/services/api_hooks";
import { Loader } from "lucide-react";

interface FilterAlertsModalProps {
  isOpen: boolean;
  onClose: () => void;
  chartLink: string;
}

const ViewChart = ({ isOpen, onClose, chartLink }: FilterAlertsModalProps) => {

  // use next-intl for i18n
  const t = useTranslations("DashboardHermesC.AlertsTable.Table");
  const tForm = useTranslations("Form");
  const tGlobal = useTranslations("global");
  const tTable = useTranslations("Table");

  const [showModal, setShowModal] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);

  // Reset loading state when chartLink changes
  useEffect(() => {
    if (chartLink) {
      setLoading(true);
    }
  }, [chartLink]);

  const handleLoad = () => {
    setLoading(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[847px] h-[80%] p-0 overflow-hidden bg-white rounded-lg ">
          <DialogTitle className="sr-only">{t("ViewChart")}</DialogTitle>
        <div className="grid place-content-center h-full">
          {loading && <div className="absolute inset-0 flex items-center justify-center animate-pulse text-black">{tGlobal("Loading")}</div>}
          <img
            src={chartLink || ''}
            alt="HermesX Logo"
            width={800}
            height={628}
            className="object-contain"
            onLoad={handleLoad}
            onError={() => setLoading(false)} // Fail-safe
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
  
export default ViewChart;
