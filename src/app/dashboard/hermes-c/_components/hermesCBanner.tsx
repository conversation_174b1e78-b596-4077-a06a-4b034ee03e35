import { AlertTriangle } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useTranslations } from 'next-intl';

export default function HermesCBanner() {

  // use next-intl for i18n
  const t = useTranslations("DashboardHermesC");

  return (
    <div className="px-[5%]   w-full">
      <div className="relative w-full overflow-hidden min-h-[264px] flex flex-col md:flex-row items-start md:items-center justify-between  mx-auto max-w-[1400px] bg-gradient-to-r from-[#E1EFFF] to-white">
        <div className="flex-1 py-6 pl-6">
          <h2 className="text-[32px] font-[600] text-gray-800 mb-1">
            {t("title.line1")}
            <br /> {t("title.line2")}
          </h2>
          <p className="text-[#737384] mb-3 text-[18px]">
            {t("description")}
          </p>
        </div>

        <div className="absolute right-0 bottom-0 md:relative md:right-auto md:bottom-auto">
          <Image
            src="/hermescbanner.svg"
            alt="banner-hermes"
            width={371}
            height={260}
            className="object-cover w-[250px] h-[175px] sm:w-[300px] sm:h-[210px] md:w-[371px] md:h-[260px] opacity-5 md:opacity-100"
          />
        </div>
      </div>
    </div>
  );
}
