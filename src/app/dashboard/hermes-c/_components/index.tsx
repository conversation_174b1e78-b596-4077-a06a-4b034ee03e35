"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import React, { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useRef, useCallback } from "react";
import { useBottomObserver } from "@/hooks/useBottomObserver";
import { useTranslations } from "next-intl";
import { hermesAPI, useGetQueryHermes, useSubmitQueryHermes } from "@/services/api_hooks";
import { toast } from "sonner";
import SubscriptionFlowModalHermesC from "./SubscriptionFlowModal";
import EnquireFlowModalHermesC from "./SubscriptionFlowModal";
import FilterAlertsModalHermesC from "./filterAlertsModalHermesC";
import Table from "./Table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Link from "next/link";
import SubscribeModalHermesC from "./subscribeModalHermesC";
import FreeTrialModal from "./FreeTrialModal";
import { get } from "http";

// Define TypeScript interfaces
interface Alert {
  timestamp: string;
  ticker: string;
  event: string;
  direction: string;
  condition: string;
  strategy_return: string;
  bnh2close: string;
  bnh2max: string;
}

interface AlertsTableProps {
  alerts?: Alert[];
  currentPage?: number;
  totalPages?: number;
}

export default function AlertsTable({
  trialInfo,
  subscriptionStatus,
}: {
  trialInfo?: any;
  subscriptionStatus?: any;
}) {
  // use next-intl for i18n
  const t = useTranslations("DashboardHermesC.AlertsTable");
  const tGlobal = useTranslations("global");
  const tTable = useTranslations("Table");

  const [showModal, setShowModal] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [showEnquireModalFlow, setShowEnquireModalFlow] = useState(false);
  const [timezone, setTimezone] = useState("");
  const [showFreeTrialModal, setShowFreeTrialModal] = useState(false);
  const [showSubscribeModal, setShowSubscribeModal] = useState(false);

  const {
    data: alerts,
    isLoading,
    // isFetching,
    refetch,
  } = useGetQueryHermes(
    `/api/hermesc/alert/tables?page=${page}&timezone=${timezone}`,
    ["hermes-c-alerts", page, limit, timezone],
    {
      onError() {
        toast.error(t("toast.notLoadAlerts"));
      },
    }
  );

  const { data: savedPreferences, isLoading: preferencesLoading } =
    useGetQueryHermes("/api/hermesc/user/filters", ["get-filters"], {
      staleTime: 1000 * 60 * 5, // 5 minutes
      onError() {
        toast.error(t("toast.notLoadSavedFilters"));
      },
    });

  const queryClient = useQueryClient();
  const initialTrialInfo = queryClient.getQueryData(["get-trial-info"]);
  const {
    data: trialInfoData,
    isLoading: trialLoading,
    refetch: refetchTrialInfo,
    error: trialInfoError,
  } = useGetQueryHermes("/api/hermesc/user/trial", ["get-trial-info"], {
    initialData: initialTrialInfo,
    onError() {
      console.error("Could not load trial information");
    },
    enabled: true,
    retry: false, // Do not retry on any error
  });

  // Add this for subscription status
  const {
    data: getSubscriptionStatus,
    isLoading: subscriptionStatusLoading,
    refetch: refetchSubscriptionStatus,
  } = useGetQueryHermes(
    "/api/hermesc/user/subscription",
    ["get-hermesc-subscription-status"],
    {
      onError() {
        toast.error(t("toast.notLoadSubscriptionStatus"));
      },
      enabled: true,
    }
  );

  const currentPage = alerts?.pagination?.currentPage || 1;
  const totalPages = alerts?.pagination?.totalPages || 1;
  const hasNextPage = alerts?.pagination?.hasNextPage || false;
  const hasPrevPage = alerts?.pagination?.hasPrevPage || false;

  // Prefetch next two pages when bottom is in view
  const bottomRef = useRef<HTMLDivElement>(null);
  const prefetchNextPages = useCallback(() => {
    if (!alerts?.pagination?.hasNextPage) return;
    // Prefetch next page
    queryClient.prefetchQuery({
      queryKey: ["hermes-c-alerts", page + 1, limit, timezone],
      queryFn: async () => {
        const res = await hermesAPI.get(`/api/hermesc/alert/tables?page=${page + 1}&timezone=${timezone}`);
        return res.data;
      },
      gcTime: 30 * 1000 * 60, // 30 minutes
    });
    // Prefetch the page after next if it exists
    if (alerts?.pagination?.totalPages && page + 1 < alerts.pagination.totalPages) {
      queryClient.prefetchQuery({
        queryKey: ["hermes-c-alerts", page + 2, limit, timezone],
        queryFn: async () => {
          const res = await hermesAPI.get(`/api/hermesc/alert/tables?page=${page + 2}&timezone=${timezone}`);
          return res.data;
        },
        gcTime: 30 * 1000 * 60, // 30 minutes
      });
    }
  }, [alerts, page, limit, timezone, queryClient]);
  useBottomObserver(bottomRef as React.RefObject<Element>, prefetchNextPages, Boolean(alerts?.pagination?.hasNextPage));

  const handleNextPage = () => {
    if (alerts?.pagination?.hasNextPage) {
      setPage((prev) => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (alerts?.pagination?.hasPrevPage) {
      setPage((prev) => prev - 1);
    }
  };

  const handleEnquire = () => {
    setShowEnquireModalFlow(true);
  };

  return (
    <div className="px-[5%] w-full">
      <div className="flex flex-col mx-auto w-full max-w-[1400px] ">
        <div className="flex justify-between items-center mb-4">
          <div className="flex w-full flex-col space-y-4 md:flex-row md:justify-between md:items-center md:space-y-0 mb-4">
            {/* Left Side - Title and Timezone */}
            <div className="space-y-3 md:space-y-2">
              <div>
                <div className="text-xl font-medium">{t("title")}</div>

                {getSubscriptionStatus?.active ? (
                  <div className="text-sm text-[#6C7788]">{t("Latest")}</div>
                ) : (
                  <div className="text-sm text-[#6C7788]">{t("delayed")}</div>
                )}
              </div>
              <Select
                value={timezone || savedPreferences?.filters?.timezone}
                onValueChange={(value) => setTimezone(value)}
              >
                <SelectTrigger className="w-full md:w-40 bg-white h-[30px] hidden md:flex rounded-[4px]">
                  <SelectValue placeholder={t("TimeZone")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="UTC">{t("UTC")}</SelectItem>
                  <SelectItem value="NYT">{t("NYT")}</SelectItem>
                  <SelectItem value="UserTime">{t("UserTime")}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Right Side - Actions */}

            <div className="flex items-center gap-2 ">
              <Link
                href="/dashboard/hermes-c/performance"
                className="font-medium text-sm cursor-pointer p-2 rounded-[6px] bg-[#F1F5F9] text-black hover:bg-[#F1F5F9]"
              >
                {t("ViewPerformance")}
              </Link>

              {!getSubscriptionStatus?.active &&
                (trialLoading ? (
                  <Button disabled>{t("Button.Loading")}</Button>
                ) : trialInfoError?.response?.status === 404 ? (
                  <Button onClick={handleEnquire}>
                    {t("Button.EnquireNow")}
                  </Button>
                ) : trialInfoData?.enable_trial &&
                  trialInfoData?.subscription_status === "trial" ? (
                  <Button onClick={() => setShowSubscribeModal(true)}>
                    {t("Button.SubscribeNow")}
                  </Button>
                ) : trialInfoData?.enable_trial ? (
                  <Button onClick={() => setShowFreeTrialModal(true)}>
                    {t("Button.StartFreeTrial")}
                  </Button>
                ) : trialInfoData?.status ? (
                  <Button onClick={() => setShowSubscribeModal(true)}>
                    {t("Button.SubscribeNow")}
                  </Button>
                ) : (
                  <Button onClick={handleEnquire}>
                    {t("Button.EnquireNow")}
                  </Button>
                ))}

              <Button
                className="font-medium text-sm cursor-pointer focus:outline-none"
                onClick={() => setShowFilter(true)}
              >
                {t("Filter")}
              </Button>
            </div>

            <Select
              value={timezone}
              onValueChange={(value) => setTimezone(value)}
            >
              <SelectTrigger className="w-full md:w-40 bg-white h-[30px]  md:hidden rounded-[4px]">
                <SelectValue placeholder="Time zone" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="UTC">{t("UTC")}</SelectItem>
                <SelectItem value="NYT">{t("NYT")}</SelectItem>
                <SelectItem value="UserTime">{t("UserTime")}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>


        <Table
          alerts={alerts?.data || []}
          isLoading={isLoading}
          // isFetching={isFetching}
          timezone={timezone}
        />
        {/* Invisible div at the bottom for observer */}
        <div ref={bottomRef} style={{ height: 1 }} />

        <div className="flex items-center justify-between px-6 border-t py-4">
          <div className="text-sm text-gray-700 pointer">
            {tTable("Page")} {currentPage} {tTable("of")} {totalPages}
          </div>
          <div className="flex space-x-2">
            <button
              className={`px-4 py-2 border border-gray-300 rounded text-sm font-medium ${
                hasPrevPage
                  ? "text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
                  : "text-gray-400 bg-gray-100 cursor-not-allowed"
              }`}
              onClick={handlePrevPage}
              disabled={!hasPrevPage}
            >
              {tTable("Previous")}
            </button>
            <button
              className={`px-4 py-2 border border-gray-300 rounded text-sm font-medium ${
                hasNextPage
                  ? "text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
                  : "text-gray-400 bg-gray-100 cursor-not-allowed"
              }`}
              onClick={handleNextPage}
              disabled={!hasNextPage}
            >
              {tTable("Next")}
            </button>
          </div>
        </div>
      </div>

      {/* Modals */}
      <FreeTrialModal
        isOpen={showFreeTrialModal}
        onClose={() => setShowFreeTrialModal(false)}
        refetchTrialInfo={refetchTrialInfo}
      />
      <SubscribeModalHermesC
        isOpen={showSubscribeModal}
        onClose={() => {
          setShowSubscribeModal(false);
          refetchSubscriptionStatus(); // Refetch subscription status after modal closes
        }}
      />
      <EnquireFlowModalHermesC
        isOpen={showEnquireModalFlow}
        onClose={() => setShowEnquireModalFlow(false)}
      />

      <FilterAlertsModalHermesC
        isOpen={showFilter}
        onClose={() => setShowFilter(false)}
        refetch={refetch}
      />
    </div>
  );
}
