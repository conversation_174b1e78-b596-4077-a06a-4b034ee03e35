"use client";
import { FC, useState } from "react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useGetQueryHermes, useSubmitQueryHermes } from "@/services/api_hooks";
import { toast } from "sonner";
import { Loader } from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";

interface SubscribeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SubscribeModalHermesC: FC<SubscribeModalProps> = ({
  isOpen,
  onClose,
}) => {
  // use next-intl for i18n
  const t = useTranslations("DashboardHermesC.Subscribe");
  const tGlobal = useTranslations("global");

  const [selectedPlan, setSelectedPlan] = useState<"monthly" | "yearly">(
    "monthly"
  );

  const queryClient = useQueryClient();

  const {
    data: subscriptionStatus,
    isLoading: subscriptionStatusLoading,
    refetch: refetchSubscriptionStatus, // <-- get refetch function
  } = useGetQueryHermes(
    "/api/hermesc/user/subscription",
    ["get-hermesc-subscription-status"],
    {
      onError() {
        console.error("Could not load trial information");
      },
      enabled: true,
    }
  );

  const { mutateAsync: subscribe, isPending: loading } = useSubmitQueryHermes(
    "/api/hermesc/user/subscribe",
    "POST",
    {
      onSuccess(response: any) {
        toast.success(t("toast.Successful"), {
          position: "top-right",
          className: "p-4",
        });
        queryClient.invalidateQueries({
          queryKey: ["get-hermesc-subscription-status"],
        });
        refetchSubscriptionStatus(); // <-- refetch subscription status here
        onClose();
      },
      onError(err: any) {
        toast.error(
          err?.response?.data?.error || t("toast.SubscriptionFailed"),
          {
            position: "top-right",
            className: "p-4",
          }
        );
      },
    }
  );

  const handlesubscribe = async () => {
    subscribe({
      plan: selectedPlan,
    });
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[447px] p-0 overflow-hidden bg-white rounded-lg">
          <div className="w-full h-[267px] bg-blue-50 flex items-end justify-center">
            <Image
              src="/dollar_icon.svg"
              alt="HermesX Logo"
              width={135}
              height={259}
              priority
              className="object-contain"
            />
          </div>

          <div className="px-6 mb-1">
            <DialogHeader>
              <DialogTitle className="text-[16px] font-[500] ">
                {t("title")}
              </DialogTitle>
              <DialogDescription>
                <span className="text-start text-[#737384] text-xs leading-[20px] mt-2">
                  {t("description")}
                </span>
              </DialogDescription>
            </DialogHeader>
          </div>

          <div className="flex items-center justify-between gap-4 mb-9 px-6">
            <div
              className={`border rounded-md p-2 flex items-center max-w-[200px] w-full justify-between cursor-pointer ${
                selectedPlan === "monthly" ? "border-blue-500 bg-blue-50" : ""
              }`}
              onClick={() => setSelectedPlan("monthly")}
            >
              <div className="grid">
                <span className="text-xs font-[300] text-[#737384]">
                  {t("Monthly.text")}
                </span>
                <span className="text-[16px] font-[500] text-black">
                  {t("Monthly.description")}
                </span>
              </div>
              <Image
                src="/dollar_icon.svg"
                alt="HermesX Logo"
                width={28}
                height={53}
                priority
                className="object-contain"
              />
            </div>

            <div
              className={`border rounded-md p-2 flex items-center max-w-[200px] w-full justify-between cursor-pointer ${
                selectedPlan === "yearly" ? "border-blue-500 bg-blue-50" : ""
              }`}
              onClick={() => setSelectedPlan("yearly")}
            >
              <div className="grid">
                <span className="text-xs font-[300] text-[#737384]">
                  {t("Annually.text")}
                </span>
                <span className="text-[16px] font-[500] text-black">
                  {t("Annually.description")}
                </span>
              </div>
              <Image
                src="/dollar_icon.svg"
                alt="HermesX Logo"
                width={28}
                height={53}
                priority
                className="object-contain"
              />
            </div>
          </div>

          <DialogFooter className="px-6 pb-6 sm:pb-6">
            <div className="flex items-center justify-between w-full gap-4">
              <Button
                className="max-w-[80px] text-black rounded-md"
                onClick={onClose}
                variant="outline"
              >
                {tGlobal("Cancel")}
              </Button>
              <Button
                className="bg-gray-900 max-w-[300px] w-full hover:bg-gray-800 text-white rounded-md"
                onClick={handlesubscribe}
              >
                {tGlobal("Proceed")}
                {loading && <Loader className="animate-spin" />}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SubscribeModalHermesC;
