"use client";
import React, { useEffect } from "react";
import HeaderModels from "../_components/HeaderModels";
import { useQueryClient } from "@tanstack/react-query";
import { hermesAPI } from "@/services/api_hooks";

interface OrionDashboardLayoutProps {
  children: React.ReactNode;
}

const HermesDashboardLayout = ({ children }: OrionDashboardLayoutProps) => {
  const queryClient = useQueryClient();
  useEffect(() => {
    queryClient.prefetchQuery({
      queryKey: ["get-cumulative", "", "", ""],
      queryFn: async () => {
        const res = await hermesAPI.get("/api/hermesc/performance/cumulative");
        return res.data;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 60 * 60 * 1000, // 1 hour
    });

    queryClient.prefetchQuery({
      queryKey: ["get-performance", "daily", "", "", ""],
      queryFn: async () => {
        const res = await hermesAPI.get("/api/hermesc/performance/daily");
        return res.data;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 60 * 60 * 1000, // 1 hour
    });
  }, [queryClient]);
  return (
    <div className=" ">
      <main className="min-h-screen">
        <HeaderModels avatarUrl="/hermes_thumbnail.svg" userName="Hermes C" />
        <div className="h-full">{children}</div>
      </main>
      {/* <WelcomeModalHermesX/> */}
    </div>
  );
};

export default HermesDashboardLayout;
