'use client'
import React from "react";
import HermesCBanner from "./_components/hermesCBanner";
import AlertsTable from "./_components";
import TrialCountdownBanner from "./_components/TrialCountdownBanner";
import { useGetQueryHermes } from "@/services/api_hooks";

const HermeesCPage = () => {
  const { data: trialInfo } = useGetQueryHermes(
    "/api/hermesc/user/trial",
    ["get-trial-info"]
  );

  const { data: subscriptionStatus } = useGetQueryHermes(
    "/api/hermesc/user/subscription",
    ["get-hermesc-subscription-status"]
  );

  const showBanner = trialInfo?.subscription_status === "trial" && !subscriptionStatus?.active;

  return (
    <div
      className={`flex flex-col justify-center items-center h-full space-y-[100px] ${
        !showBanner ? "py-[30px]" : ""
      }`}
    >
      {/* Show banner if on trial and not subscribed */}
      {showBanner && (
        <TrialCountdownBanner
          trialStartDate={trialInfo.trial_start_date}
          trialEndDate={trialInfo.trial_end_date}
        />
      )}
      <HermesCBanner />
      <AlertsTable trialInfo={trialInfo} subscriptionStatus={subscriptionStatus} />
    </div>
  );
};

export default HermeesCPage;
