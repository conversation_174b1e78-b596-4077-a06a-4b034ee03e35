import { Button } from "@/components/ui/button";
import { useGetQueryHermes } from "@/services/api_hooks";
import { Info } from "lucide-react";
import React, { useState } from "react";
import {
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
  Area,
  ComposedChart,
} from "recharts";

import {
  Tooltip as UITooltip,
  <PERSON>ltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTranslations } from "next-intl";

interface RawDataPoint {
  date: string;
  constant: {
    strategy: number;
    close: number;
    max: number;
  };
  compounding: {
    strategy: number;
    close: number;
    max: number;
  };
}

interface ChartData {
  date: string;
  strategyReturn: number;
  passiveReturnToClose: number;
  passiveReturnToMax: number;
}

type DataType = "constant" | "compounding";

// Function to transform raw data for chart consumption
const transformData = (
  rawData: RawDataPoint[],
  dataType: DataType
): ChartData[] => {
  return rawData.map((item) => {
    const date = new Date(item.date);
    const year = date.getFullYear().toString().slice(-2);
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    const strat = item[dataType]?.strategy ?? 0;
    const close = item[dataType]?.close ?? 0;
    const max = item[dataType]?.max ?? 0;
    return {
      date: `${year}-${month}-${day}`,
      strategyReturn: Number((strat).toFixed(2)),
      passiveReturnToClose: Number((close).toFixed(2)),
      passiveReturnToMax: Number((max).toFixed(2)),
    };
  });
};

// Custom tick formatter to ensure first and last dates always show
const formatXAxisTick = (value: string, index: number, dataLength: number) => {
  if (dataLength <= 4) {
    // if there are 4 or fewer points, show all *except* the last
    return index === dataLength - 1 ? "" : value;
  }

  // never show the last tick
  if (index === dataLength - 1) {
    return "";
  }

  // always show the first tick
  if (index === 0) {
    return value;
  }

  // calculate roughly 3 evenly spaced middle ticks
  const interval = Math.floor((dataLength - 1) / 3);
  if (index % interval === 0) {
    return value;
  }

  // hide everything else
  return "";
};

const PerformanceChart = ({ data, isLoading, isFetching }: any) => {
  const [dataType, setDataType] = useState<DataType>("constant");
  const t = useTranslations("DashboardHermesC.Performance.PerformanceChart");

  const chartData = transformData(data || [], dataType);

  const calculateGridBounds = (data: ChartData[]) => {
    if (!data || data.length === 0)
      return { min: 90, max: 150, ticks: [90, 100, 110, 120, 130, 140, 150] };

    const allValues = data.flatMap((item) => [
      item.strategyReturn,
      item.passiveReturnToClose,
      item.passiveReturnToMax,
    ]);

    const minValue = Math.min(...allValues);
    const maxValue = Math.max(...allValues);

    const gridMin = Math.floor(minValue / 10) * 10;
    const gridMax = Math.ceil(maxValue / 10) * 10;

    const ticks: number[] = [];
    for (let i = gridMin; i <= gridMax; i += 10) {
      ticks.push(i);
    }

    return {
      min: minValue,
      max: maxValue,
      ticks,
    };
  };

  const gridBounds = calculateGridBounds(chartData);

  return (
    <div className="w-full flex flex-col justify-between pb-6 h-full">
      <div className="pl-6">
        <ToggleButtons activeType={dataType} onToggle={setDataType} t={t} />
      </div>
      <CustomLegend t={t} />
      <div className="flex-1 min-h-0 flex flex-col">
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart
            data={chartData}
            margin={{
              top: 0,
              right: 30,
              left: -20,
              bottom: 20,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
            <XAxis
              dataKey="date"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#666", dy: 15 }}
              tickFormatter={(value, index) =>
                formatXAxisTick(value, index, chartData.length)
              }
              interval={0}
            />
            <YAxis
              domain={[gridBounds.min, gridBounds.max]}
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#666" }}
              ticks={gridBounds.ticks}
            />
            <Tooltip content={<CustomTooltip t={t} />} />

            {/* Area fill between the two lines */}
            <Area
              type="linear"
              dataKey="passiveReturnToMax"
              name={t("legend.activeAlphaZone")}
              stroke="none"
              fill="#f3d372c5"
              fillOpacity={0.6}
            />
            <Area
              type="linear"
              dataKey="passiveReturnToClose"
              stroke="none"
              fill="white"
              fillOpacity={1}
            />

            <Line
              type="linear"
              dataKey="strategyReturn"
              stroke="#3b82f6"
              strokeWidth={2}
              dot={false}
              name={t("legend.rawStrategyReturn")}
              activeDot={{
                r: 4,
                stroke: "#3b82f6",
                strokeWidth: 2,
                fill: "#fff",
              }}
            />
            <Line
              type="linear"
              dataKey="passiveReturnToClose"
              stroke="#686767"
              strokeWidth={2}
              dot={false}
              name={t("legend.passiveReturnToClose")}
              activeDot={{
                r: 4,
                stroke: "#686767",
                strokeWidth: 2,
                fill: "#fff",
              }}
            />
            <Line
              type="linear"
              dataKey="passiveReturnToMax"
              stroke="#eab308"
              strokeWidth={2}
              dot={false}
              name={t("legend.activeReturnToMax")}
              activeDot={{
                r: 4,
                stroke: "#eab308",
                strokeWidth: 2,
                fill: "#fff",
              }}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </div>
      <div className="text-center text-sm text-gray-600">
        {t("accumulativePerformance")}
      </div>
    </div>
  );
};

// Toggle buttons component
const ToggleButtons: React.FC<{
  activeType: DataType;
  onToggle: (type: DataType) => void;
  t: ReturnType<typeof useTranslations>;
}> = ({ activeType, onToggle, t }) => {
  return (
    <div className="flex gap-2 mb-8 py-2 px-2 border rounded-[12px] w-fit">
      <Button
        onClick={() => onToggle("constant")}
        className="border-none"
        variant={activeType === "constant" ? "default" : "outline"}
      >
        {t("constantCapital")}
      </Button>
      <Button
        onClick={() => onToggle("compounding")}
        className="border-none"
        variant={activeType === "compounding" ? "default" : "outline"}
      >
        {t("compounding")}
      </Button>
    </div>
  );
};

// Custom legend component
const CustomLegend: React.FC<{ t: ReturnType<typeof useTranslations> }> = ({
  t,
}) => {
  const legendItems = [
    {
      name: t("legend.rawStrategyReturn"),
      color: "#3b82f6",
      key: "rawStrategyReturn",
    },
    {
      name: t("legend.passiveReturnToClose"),
      color: "#686767",
      key: "passiveReturnToClose",
    },
    {
      name: t("legend.activeReturnToMax"),
      color: "#eab308",
      key: "activeReturnToMax",
    },
    {
      name: t("legend.activeAlphaZone"),
      color: "#f3d372c5",
      key: "activeAlphaZone",
    },
  ];

  return (
    <div className="flex justify-center flex-wrap gap-6 mb-6">
      {legendItems.map((item, index) => (
        <div key={index} className="flex items-center gap-2">
          <div className="w-5 h-2" style={{ backgroundColor: item.color }} />
          <span className="text-xs text-gray-600 flex items-center gap-1">
            {item.name}{" "}
            {item.key === "activeAlphaZone" && (
              <TooltipProvider delayDuration={100}>
                <UITooltip>
                  <TooltipTrigger asChild className="cursor-pointer">
                    <Info size={13} />
                  </TooltipTrigger>
                  <TooltipContent
                    className="max-w-[300px] gap-3 flex flex-col p-3 text-xs text-start font-[400] leading-5 bg-white text-black"
                    side="top"
                  >
                    <span>{t("legend.activeAlphaZoneTooltip")}</span>
                  </TooltipContent>
                </UITooltip>
              </TooltipProvider>
            )}
          </span>
        </div>
      ))}
    </div>
  );
};

// Custom tooltip component
const CustomTooltip: React.FC<
  TooltipProps<any, any> & { t: ReturnType<typeof useTranslations> }
> = ({ active, payload, label, t }) => {
  if (active && payload && payload.length) {
    // Filter out the "Active Alpha Zone" entry by translated name
    const filtered = payload.filter(
      (entry) => entry.name !== t("legend.activeAlphaZone")
    );

    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm font-medium text-gray-700 mb-2">
          {t("customTooltip.date", { label })}
        </p>
        {filtered.map((entry, index) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {`${entry.name}: ${entry.value}`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

export default PerformanceChart;
