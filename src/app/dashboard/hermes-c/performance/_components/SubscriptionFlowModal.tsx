// @ts-nocheck

import React, { useState, useEffect } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useQueryClient } from "@tanstack/react-query";
import {
  useGetQuery,
  useGetQueryHermes,
  useSubmitQueryHermes,
} from "@/services/api_hooks";
import { toast } from "sonner";
import Image from "next/image";
import {
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Loader, AlertTriangle } from "lucide-react";
import { useTranslations } from "next-intl";

// Define the props for the component
const EnquireFlowModalHermesC = ({ isOpen, onClose }) => {
  const t = useTranslations(
    "DashboardHermesC.Performance.SubscriptionFlowModal"
  );

  // State for tracking the current step in the flow (now only 2 steps)
  const [currentStep, setCurrentStep] = useState(1);

  // State for Schedule Meeting form
  const [meetingForm, setMeetingForm] = useState({
    organization: "",
    investorType: "",
    country: "",
    message: "",
  });

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [initializing, setInitializing] = useState(false); // No API calls needed for initialization
  const [hasAcceptedDisclaimer, setHasAcceptedDisclaimer] = useState(false);

  const queryClient = useQueryClient();

  // Determine starting step - always start at step 1 (Disclaimer)
  useEffect(() => {
    setCurrentStep(1);
    setInitializing(false);
  }, []);

  // API mutation for accepting disclaimer
  const { mutateAsync: acceptDisclaimer, isPending: disclaimerPending } =
    useSubmitQueryHermes("/api/hermesc/user/disclaimer", "POST", {
      onSuccess(response) {
        toast.success("Disclaimer accepted successfully!", {
          position: "top-right",
          className: "p-4",
        });
        setHasAcceptedDisclaimer(true);
        setCurrentStep(2);
        setIsSubmitting(false);
      },
      onError(err) {
        toast.error(
          err?.response?.data?.error || "Failed to accept disclaimer",
          {
            position: "top-right",
            className: "p-4",
          }
        );
        setIsSubmitting(false);
      },
    });

  // API mutation for saving meeting request
  const { mutateAsync: saveMeeting, isPending: meetingPending } =
    useSubmitQueryHermes("/api/hermesc/user/meeting", "POST", {
      onSuccess(response) {
        toast.success("Meeting request submitted successfully!", {
          position: "top-right",
          className: "p-4",
        });
        // Reset form
        setMeetingForm({
          organization: "",
          investorType: "",
          country: "",
          message: "",
        });
        // Close modal
        onClose();
        setIsSubmitting(false);
      },
      onError(err) {
        toast.error(
          err?.response?.data?.error || "Failed to save meeting request",
          {
            position: "top-right",
            className: "p-4",
          }
        );
        setIsSubmitting(false);
      },
    });

  // Handle disclaimer acceptance
  const handleAcceptDisclaimer = async () => {
    setIsSubmitting(true);
    await acceptDisclaimer({});
  };

  // Handle meeting form submission
  const handleSubmitMeeting = async () => {
    setIsSubmitting(true);
    await saveMeeting({
      organization: meetingForm.organization,
      typeOfInvestor: meetingForm.investorType,
      country: meetingForm.country,
      message: meetingForm.message,
    });
  };

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setMeetingForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Check if meeting form is valid
  const isMeetingFormValid =
    meetingForm.organization.trim() &&
    meetingForm.investorType.trim() &&
    meetingForm.country.trim() &&
    meetingForm.message.trim();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      {initializing ? (
        <DialogContent className="sm:max-w-[447px] p-6 bg-white rounded-lg">
          <div className="flex items-center justify-center h-64">
            <div className="flex flex-col items-center gap-2">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              <p className="text-sm text-gray-500">Loading...</p>
            </div>
          </div>
        </DialogContent>
      ) : (
        <div>
          {currentStep === 1 && ( // Disclaimer Step
            <DialogContent
              className="sm:max-w-[440px] p-0 pb-3 bg-white rounded-lg overflow-hidden
            "
            >
              {/* Warning Icon */}
              <div className="w-full h-[170px] bg-blue-50 flex items-end justify-center">
                <Image
                  src="/hermesbanner_image.svg"
                  alt="Hermes c disclaimer"
                  width={235}
                  height={200}
                  priority
                  className="object-contain"
                />
              </div>
              <div className="px-4 overflow-y-auto max-h-[480px]">
                <DialogHeader className="text-center mb-6 ">
                  <DialogTitle className="text-[18px] font-medium mb-4">
                    {t("disclaimer.title")}
                  </DialogTitle>
                  <DialogDescription className="text-left">
                    <div className="text-xs text-[#737384] space-y-4">
                      <p className="text-sm leading-relaxed">
                        {t("disclaimer.desc1")}
                      </p>

                      <div className="space-y-3">
                        <div className="flex items-start gap-2">
                          <div className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                          <div>
                            <span className="font-medium">
                              {t("disclaimer.noAdvice")}
                            </span>{" "}
                            {t("disclaimer.noAdviceDesc")}
                          </div>
                        </div>

                        <div className="flex items-start gap-2">
                          <div className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                          <div>
                            <span className="font-medium">
                              {t("disclaimer.misled")}
                            </span>{" "}
                            {t("disclaimer.misledDesc")}
                          </div>
                        </div>

                        <div className="flex items-start gap-2">
                          <div className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                          <div>
                            <span className="font-medium">
                              {t("disclaimer.ownData")}
                            </span>{" "}
                            {t("disclaimer.ownDataDesc")}
                          </div>
                        </div>

                        <div className="flex items-start gap-2">
                          <div className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                          <div>
                            <span className="font-medium">
                              {t("disclaimer.notRetail")}
                            </span>{" "}
                            {t("disclaimer.notRetailDesc")}
                          </div>
                        </div>
                      </div>
                    </div>
                  </DialogDescription>
                </DialogHeader>

                <DialogFooter className="p-0 m-0">
                  <div className="flex items-center justify-between w-full gap-4">
                    <Button
                      variant="outline"
                      onClick={onClose}
                      className="border border-gray-300 h-[32px] text-black"
                      disabled={disclaimerPending}
                    >
                      {t("disclaimer.cancel")}
                    </Button>
                    <Button
                      onClick={handleAcceptDisclaimer}
                      className="bg-gray-900 text-white h-[32px] hover:bg-gray-800 w-full max-w-[300px]"
                      disabled={disclaimerPending}
                    >
                      {disclaimerPending
                        ? t("disclaimer.processing")
                        : t("disclaimer.agree")}
                    </Button>
                  </div>
                </DialogFooter>
              </div>
            </DialogContent>
          )}

          {currentStep === 2 && ( // Schedule Meeting Step
            <DialogContent className="sm:max-w-[447px] max-h-[600px] overflow-y-auto p-6 bg-white rounded-lg">
              <DialogHeader className="mb-4">
                <DialogTitle className="text-[18px] font-[600]">
                  {t("meeting.title")}
                </DialogTitle>
                <DialogDescription className="text-xs text-[#737384] font-[400]">
                  {t("meeting.description")}
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                {/* Organization */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("meeting.organization")}
                  </label>
                  <Input
                    placeholder={t("meeting.organizationPlaceholder")}
                    value={meetingForm.organization}
                    onChange={(e) =>
                      handleInputChange("organization", e.target.value)
                    }
                    className="w-full"
                    disabled={meetingPending}
                  />
                </div>

                {/* Type of Investor */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("meeting.typeOfInvestor")}
                  </label>
                  <Input
                    placeholder={t("meeting.typeOfInvestorPlaceholder")}
                    value={meetingForm.investorType}
                    onChange={(e) =>
                      handleInputChange("investorType", e.target.value)
                    }
                    className="w-full"
                    disabled={isSubmitting}
                  />
                </div>

                {/* Country */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("meeting.country")}
                  </label>
                  <Input
                    placeholder={t("meeting.countryPlaceholder")}
                    value={meetingForm.country}
                    onChange={(e) =>
                      handleInputChange("country", e.target.value)
                    }
                    className="w-full"
                    disabled={isSubmitting}
                  />
                </div>

                {/* Message */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("meeting.message")}
                  </label>
                  <Textarea
                    placeholder={t("meeting.messagePlaceholder")}
                    value={meetingForm.message}
                    onChange={(e) =>
                      handleInputChange("message", e.target.value)
                    }
                    className="w-full min-h-[100px] resize-none"
                    disabled={isSubmitting}
                  />
                </div>
              </div>

              <DialogFooter className="p-0 m-0">
                <div className="flex items-center justify-between w-full gap-4">
                  <Button
                    variant="outline"
                    onClick={onClose}
                    className="border border-gray-300 h-[32px] text-black"
                    disabled={isSubmitting}
                  >
                    {t("meeting.cancel")}
                  </Button>
                  <Button
                    onClick={handleSubmitMeeting}
                    className="bg-gray-900 text-white h-[32px] hover:bg-gray-800 w-full max-w-[300px]"
                    disabled={!isMeetingFormValid || meetingPending}
                  >
                    {meetingPending
                      ? t("meeting.submitting")
                      : t("meeting.submit")}
                  </Button>
                </div>
              </DialogFooter>
            </DialogContent>
          )}
        </div>
      )}
    </Dialog>
  );
};

export default EnquireFlowModalHermesC;
