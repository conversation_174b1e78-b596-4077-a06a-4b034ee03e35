import React, { useState } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { useGetQueryHermes } from "@/services/api_hooks";
import { Info, ZoomIn } from "lucide-react";
import View<PERSON>hart from "./ViewChart";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  TooltipP<PERSON>ider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import moment from "moment";
import ViewGraph from "./ViewGraph";
import PerformanceChart from "./PerformanceChart";
import { useTranslations } from "next-intl";

interface Alert {
  timestamp: string;
  ticker: string;
  event: string;
  direction: string;
  condition: string;
  strategy_return: string;
  bnh2close: string;
  bnh2max: string;
  chart_link: string;
  confidence: string;
}

interface HermesCTableComponentProps {
  alerts: any;
  isLoading: boolean;
  // isFetching: boolean;
  period: string;
  timezone: string;
}

const Table: React.FC<HermesCTableComponentProps> = ({
  alerts,
  isLoading,
  // isFetching,
  period,
  timezone,
}) => {
  const t = useTranslations("DashboardHermesC.Performance.Table");

  const [showChart, setShowChart] = useState(false);
  const [chartLink, setChartLink] = useState("");
  const truncateText = (text: string, maxLength = 100) => {
    if (!text) return "";
    return text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text;
  };

  const { data: savedPreferences, isLoading: preferencesLoading } =
    useGetQueryHermes("/api/hermesc/user/filters", ["get-filters"], {
      staleTime: 1000 * 60 * 5, // 5 minutes
      onError() {
        // toast.error("Could not load saved filters");
      },
    });

  const roundUpPercent = (value: string): string => {
    const number = parseFloat(value);

    // Check if the number is zero, NaN, or invalid
    if (isNaN(number) || number === 0) {
      return "-";
    }

    const rounded = Math.ceil(number * 100) / 100; // round up to 2 decimal places
    return `${rounded.toFixed(2)}%`;
  };

  const getTimezoneDisplay = () => {
    if (
      timezone === "UserTime" ||
      savedPreferences?.filters?.timezone === "UserTime"
    )
      return "User time";
    return timezone || savedPreferences?.filters?.timezone || "NYT";
  };

  const handleOpenChart = (link: string) => {
    setShowChart(true);
    setChartLink(link);
  };

  return (
    <div className="border rounded-lg h-full flex flex-col">
      {" "}
      {/* Use h-full */}
      <div className="w-full overflow-x-auto lg:overflow-none border-gray-200 rounded-lg">
        <table className="w-full table-auto divide-y divide-gray-200 text-black">
          <thead className="bg-gray-50 font-[600] text-[#6C7788] text-xs">
            <tr>
              <th className="px-2 py-3 text-left w-[100px]">{t("Time")}</th>
              <th className="px-3 py-3 text-left ">{t("NumberOfAlerts")}</th>
              <th className="px-3 py-3 text-left ">{t("LongPositions")}</th>
              <th className="px-3 py-3 text-left ">{t("NormalConditions")}</th>
              <th className="px-3 py-3 text-left ">{t("RawStrategyReturn")}</th>
              <th className="px-3 py-3 text-left ">
                {t("PassiveReturnToClose")}
              </th>
              <th className="px-3 py-3 text-left ">{t("ActiveReturnToMax")}</th>
            </tr>
          </thead>

          <tbody className="bg-white divide-y divide-gray-200 font-[400]">
            {isLoading ? (
              Array.from({ length: 5 }).map((_, idx) => (
                <tr key={idx}>
                  <td className="px-3 py-2 h-[84px]">
                    <Skeleton className="h-4 w-10" />
                  </td>
                  <td className="px-3 py-2 h-[84px]">
                    <Skeleton className="h-4 w-10" />
                  </td>
                  <td className="px-3 py-2 h-[84px]">
                    <Skeleton className="h-4 w-10" />
                  </td>
                  <td className="px-4 py-2 h-[84px]">
                    <Skeleton className="h-6 w-10" />
                  </td>
                  <td className="px-3 py-2 h-[84px]">
                    <Skeleton className="h-4 w-10" />
                  </td>
                  <td className="px-3 py-2 h-[84px]">
                    <Skeleton className="h-4 w-10" />
                  </td>
                  <td className="px-3 py-2 h-[84px]">
                    <Skeleton className="h-4 w-10" />
                  </td>
                  {/* <td className="px-3 py-2 h-[84px]">
                    <Skeleton className="h-4 w-16" />
                  </td> */}
                </tr>
              ))
            ) : alerts && alerts.length > 0 ? (
              alerts?.map((alert: any, index: number) => (
                <tr key={index}>
                  <td className="px-2 py-2 text-[12px]">
                    {moment(alert.period).format(
                      period === "weekly"
                        ? "[W/C] YY-MM-DD"
                        : period === "monthly"
                        ? "YY-MM"
                        : "YY-MM-DD"
                    )}
                  </td>
                  <td className="px-3 py-2 text-center text-[12px] w-fit">
                    {alert.num_alerts}
                  </td>
                  <td className="px-3 py-2  text-[12px]">
                    {roundUpPercent(alert.long_pct)}
                  </td>
                  <td className="px-4 py-2  text-[12px]">
                    {roundUpPercent(alert.normal_pct)}
                  </td>
                  <td className="px-3 py-2  text-[12px]">
                    {roundUpPercent(alert.strategy_return_avg)}
                  </td>
                  <td className="px-3 py-2 text-[12px]">
                    {roundUpPercent(alert.passive_close_avg)}
                  </td>
                  <td className="px-3  text-[12px]">
                    {roundUpPercent(alert.passive_max_avg)}
                  </td>
                  {/* <td className="px-3 h-[84px]">
                    <ZoomIn
                      size={20}
                      className="cursor-pointer"
                      onClick={() => handleOpenChart(alert.chart_link)}
                    />
                  </td> */}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={8}
                  className="px-3 py-10 text-center text-gray-500"
                >
                  {t("NoResultsFound")}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {/* <ViewGraph
        isOpen={showChart}
        onClose={() => setShowChart(false)}
        chartLink={chartLink}
      /> */}
    </div>
  );
};

export default Table;
