// components/WelcomeModal.tsx
"use client";
import { FC, useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { hermesAPI, useGetQueryHermes } from "@/services/api_hooks";
import { Loader } from "lucide-react";
import { useTranslations } from "next-intl";

interface FilterAlertsModalProps {
  isOpen: boolean;
  onClose: () => void;
  chartLink: string;
}

interface ChartData {
  date: string;
  strategyReturn: number;
  passiveReturnToClose: number;
  passiveReturnToMax: number;
}

const data: ChartData[] = [
  {
    date: "21-05-25",
    strategyReturn: 101,
    passiveReturnToClose: 101,
    passiveReturnToMax: 101,
  },
  {
    date: "22-05-25",
    strategyReturn: 104,
    passiveReturnToClose: 108,
    passiveReturnToMax: 105,
  },
  {
    date: "23-05-25",
    strategyReturn: 106,
    passiveReturnToClose: 115,
    passiveReturnToMax: 107,
  },
  {
    date: "24-05-25",
    strategyReturn: 108,
    passiveReturnToClose: 119,
    passiveReturnToMax: 108,
  },
  {
    date: "25-05-25",
    strategyReturn: 110,
    passiveReturnToClose: 121,
    passiveReturnToMax: 108.5,
  },
  {
    date: "26-05-25",
    strategyReturn: 112,
    passiveReturnToClose: 126,
    passiveReturnToMax: 109,
  },
  {
    date: "27-05-25",
    strategyReturn: 112,
    passiveReturnToClose: 126,
    passiveReturnToMax: 109,
  },
  {
    date: "28-05-25",
    strategyReturn: 112,
    passiveReturnToClose: 126,
    passiveReturnToMax: 109,
  },
  {
    date: "29-05-25",
    strategyReturn: 112,
    passiveReturnToClose: 126,
    passiveReturnToMax: 109,
  },
  {
    date: "30-05-25",
    strategyReturn: 112,
    passiveReturnToClose: 126,
    passiveReturnToMax: 109,
  },
];

const ViewGraph = ({ isOpen, onClose, chartLink }: FilterAlertsModalProps) => {
  const [showModal, setShowModal] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const t = useTranslations("DashboardHermesC.Performance.ViewGraph");

  // Reset loading state when chartLink changes
  useEffect(() => {
    if (chartLink) {
      setLoading(true);
    }
  }, [chartLink]);

  const handleLoad = () => {
    setLoading(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[847px] h-[80%] p-0 overflow-hidden bg-white rounded-lg ">
        <div className="flex flex-col items-center justify-center h-full ">
          {/* {loading && (
            <div className="absolute inset-0 flex items-center justify-center animate-pulse text-black">
              {t("loading")}
            </div>
          )} */}
          <div className="w-full h-96  bg-white">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={data}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 60,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                <XAxis
                  dataKey="date"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: "#666" }}
                  interval={1}
                />
                <YAxis
                  domain={[90, 130]}
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: "#666" }}
                  tickCount={5}
                />
                <Legend
                  verticalAlign="top"
                  height={36}
                  iconType="line"
                  wrapperStyle={{
                    paddingBottom: "20px",
                    fontSize: "14px",
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="strategyReturn"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={false}
                  name={t("strategyReturn") || "Strategy Return"}
                />
                <Line
                  type="monotone"
                  dataKey="passiveReturnToClose"
                  stroke="#ef4444"
                  strokeWidth={2}
                  dot={false}
                  name={t("passiveReturnToClose") || "Passive return to close"}
                />
                <Line
                  type="monotone"
                  dataKey="passiveReturnToMax"
                  stroke="#eab308"
                  strokeWidth={2}
                  dot={false}
                  name={t("passiveReturnToMax") || "Passive return to max"}
                />
              </LineChart>
            </ResponsiveContainer>
            <div className="mt-4 text-center text-sm text-gray-600">
              {t("averageReturns")}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ViewGraph;
