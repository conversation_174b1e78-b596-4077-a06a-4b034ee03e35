"use client";
import { FC, useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { RotateCw } from "lucide-react";

import { useQueryClient } from "@tanstack/react-query";
import { useGetQueryHermes, useSubmitQueryHermes } from "@/services/api_hooks";
import { toast } from "sonner";
import { useTranslations } from "next-intl";

interface FilterAlertsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyFilter?: (filters: FilterOptions) => void;
  refetch: any;
  setConfidence: any;
  setDirection: any;
  setCondition: any;
  data: any;
  loadingFilter: boolean;
}

interface FilterOptions {
  direction: string[];
  condition: string[];
  confidence: any[];
}

const DIRECTION_OPTIONS = ["Long", "Short"];
const CONDITION_OPTIONS = ["normal", "breakthrough"];
const CONFIDENCE_OPTIONS = [1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5];

const DEFAULT_OPTIONS = {
  direction: ["Long", "Short"],
  condition: ["normal", "breakthrough"],
  confidence: [1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5],
};

const FilterAlertsModalHermesC: FC<FilterAlertsModalProps> = ({
  isOpen,
  onClose,
  onApplyFilter,
  setConfidence,
  setDirection,
  setCondition,
  data,
  loadingFilter,
  refetch,
}) => {
  const t = useTranslations(
    "DashboardHermesC.Performance.FilterAlertsModalHermesC"
  );

  const queryClient = useQueryClient();

  // Fetch saved preferences

  // Apply filters - convert arrays to comma-separated strings and set in parent
  const handleApplyFilter = () => {
    // Convert arrays to comma-separated strings for API calls
    const directionString =
      filters.direction.length > 0 ? filters.direction.join(",") : ""; // default values

    const conditionString =
      filters.condition.length > 0 ? filters.condition.join(",") : ""; // default values

    const confidenceString =
      filters.confidence.length > 0 ? filters.confidence.join(",") : ""; // default empty

    // Set the values in parent component
    setDirection(directionString);
    setCondition(conditionString);
    setConfidence(confidenceString);

    queryClient.invalidateQueries({
      queryKey: ["get-performance-filters"],
    });

    refetch();

    // Close modal
    onClose();
  };

  // Check if a direction is selected
  const isDirectionSelected = (direction: string) => {
    return filters.direction.includes(direction);
  };

  // Check if a condition is selected
  const isConditionSelected = (condition: string) => {
    return filters.condition.includes(condition);
  };

  const isConfidenceSelected = (confidence: number) => {
    return filters.confidence.includes(confidence);
  };

  // Initialize filter state
  const [filters, setFilters] = useState<FilterOptions>({
    direction: [],
    condition: [],
    confidence: [],
  });

  // Initialize loading state
  const [initializing, setInitializing] = useState<boolean>(true);

  // Track if any filter is selected (for Apply button enabling)
  const [isAnyFilterSelected, setIsAnyFilterSelected] =
    useState<boolean>(false);

  const handleDefaultFilter = () => {
    setFilters({
      direction: [...DEFAULT_OPTIONS.direction],
      condition: [...DEFAULT_OPTIONS.condition],
      confidence: [...DEFAULT_OPTIONS.confidence],
    });
  };

  // Effect to load saved preferences when data is available
  useEffect(() => {
    if (data && !loadingFilter) {
      // If we have saved preferences from the server, use those
      setFilters({
        direction: data?.direction || [],
        condition: data?.condition || [],
        confidence: data?.confidence || [],
      });
      setInitializing(false);
    } else if (!loadingFilter) {
      // If no saved preferences, set to default values immediately
      setFilters({
        direction: [...DEFAULT_OPTIONS.direction], // ["Long", "Short"]
        condition: [...DEFAULT_OPTIONS.condition], // ["normal", "breakthrough"]
        confidence: [...DEFAULT_OPTIONS.confidence], // [1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5]
      });
      setInitializing(false);
    }
  }, [data, loadingFilter]);

  // Effect to update the Apply button state
  useEffect(() => {
    const hasSelection =
      filters.direction.length > 0 ||
      filters.condition.length > 0 ||
      filters.confidence.length > 0;
    setIsAnyFilterSelected(hasSelection);
  }, [filters]);

  // Toggle direction selection
  const toggleDirection = (direction: string) => {
    const newDirections = [...filters.direction];

    if (newDirections.includes(direction)) {
      // Remove if already selected
      const index = newDirections.indexOf(direction);
      newDirections.splice(index, 1);
    } else {
      // Add if not selected
      newDirections.push(direction);
    }

    setFilters({ ...filters, direction: newDirections });
  };

  // Toggle condition selection
  const toggleCondition = (condition: string) => {
    const newConditions = [...filters.condition];

    if (newConditions.includes(condition)) {
      // Remove if already selected
      const index = newConditions.indexOf(condition);
      newConditions.splice(index, 1);
    } else {
      // Add if not selected
      newConditions.push(condition);
    }

    setFilters({ ...filters, condition: newConditions });
  };

  const toggleConfidence = (confidence: number) => {
    const newConfidence = [...filters.confidence];

    if (newConfidence.includes(confidence)) {
      // Remove if already selected
      const index = newConfidence.indexOf(confidence);
      newConfidence.splice(index, 1);
    } else {
      // Add if not selected
      newConfidence.push(confidence);
    }

    setFilters({ ...filters, confidence: newConfidence });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[440px] p-6 bg-white rounded-lg max-h-[600px] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between mt-5">
          <DialogTitle className="text-[18px] font-medium">
            {t("filterTable")}
          </DialogTitle>
          <Button
            onClick={handleDefaultFilter}
            className="bg-gray-900 text-xs flex items-center gap-1 text-white px-3 h-[30px] rounded-[4px] hover:bg-gray-800"
            disabled={initializing || loadingFilter}
          >
            {t("reset")}
            <RotateCw className="w-3 h-3" />
          </Button>
        </DialogHeader>

        <div className="space-y-6 mt-4">
          {initializing || loadingFilter ? (
            <div className="flex items-center justify-center h-64">
              <div className="flex flex-col items-center gap-2">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                <p className="text-sm text-gray-500">{t("loading")}</p>
              </div>
            </div>
          ) : (
            <>
              {/* Direction Section */}
              <div>
                <h3 className="text-xs font-[400] mb-1">
                  {t("direction.text")}
                </h3>
                <p className="text-xs text-gray-500 mb-2">
                  {t("direction.description")}
                </p>
                <div className="flex flex-wrap gap-2">
                  {DIRECTION_OPTIONS.map((direction) => (
                    <Button
                      key={direction}
                      size="sm"
                      className={`rounded-[4px] border flex items-center py-0 h-[25px] px-2 font-[400] text-xs capitalize ${
                        isDirectionSelected(direction)
                          ? "bg-black text-white"
                          : "bg-white text-black hover:bg-gray-100"
                      }`}
                      onClick={() => toggleDirection(direction)}
                    >
                      {t(`direction.${direction}`)}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Condition Section */}
              <div>
                <h3 className="text-xs font-medium mb-1">
                  {t("condition.text")}
                </h3>
                <p className="text-xs text-gray-500 mb-2">
                  {t("condition.description")}
                </p>
                <div className="flex flex-wrap gap-2">
                  {CONDITION_OPTIONS.map((condition) => (
                    <Button
                      key={condition}
                      size="sm"
                      className={`rounded-[4px] border flex items-center py-0 h-[25px] px-2 font-[400] text-xs capitalize ${
                        isConditionSelected(condition)
                          ? "bg-black text-white"
                          : "bg-white text-black hover:bg-gray-100"
                      }`}
                      onClick={() => toggleCondition(condition)}
                    >
                      {t(`condition.${condition}`)}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Confidence Section */}
              <div>
                <h3 className="text-xs font-medium mb-1">
                  {t("confidence.text")}
                </h3>
                <p className="text-xs text-gray-500 mb-2">
                  {t("confidence.description")}
                </p>
                <div className="flex flex-wrap gap-2">
                  {CONFIDENCE_OPTIONS.map((confidence) => (
                    <Button
                      key={confidence}
                      size="sm"
                      className={`rounded-[4px] border flex items-center py-0 h-[25px] px-2 font-[400] text-xs capitalize ${
                        isConfidenceSelected(confidence)
                          ? "bg-black text-white"
                          : "bg-white text-black hover:bg-gray-100"
                      }`}
                      onClick={() => toggleConfidence(confidence)}
                    >
                      {confidence}
                    </Button>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>

        <DialogFooter className="mt-10">
          <div className="flex items-center justify-between w-full gap-4">
            <Button
              variant="outline"
              onClick={onClose}
              className="border border-gray-300 h-[32px] text-black"
            >
              {t("cancel")}
            </Button>
            <Button
              className="bg-gray-900 text-white h-[32px] hover:bg-gray-800 w-full max-w-[300px]"
              onClick={handleApplyFilter}
              disabled={!isAnyFilterSelected || initializing || loadingFilter}
            >
              {t("apply")}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FilterAlertsModalHermesC;
