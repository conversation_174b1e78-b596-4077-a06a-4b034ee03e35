"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import React, { useEffect, useState } from "react";
import { hermesAPI, useGetQueryHermes } from "@/services/api_hooks";
import { toast } from "sonner";
import SubscriptionFlowModalHermesC from "./SubscriptionFlowModal";
import EnquireFlowModalHermesC from "./SubscriptionFlowModal";
import Table from "./Table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import PerformanceChart from "./PerformanceChart";
import FilterAlertsModalHermesC from "./filterAlertsModalHermesC";
import { useTranslations } from "next-intl";
import { useQueryClient } from "@tanstack/react-query";

// Define TypeScript interfaces
interface Alert {
  timestamp: string;
  ticker: string;
  event: string;
  direction: string;
  condition: string;
  strategy_return: string;
  bnh2close: string;
  bnh2max: string;
}

interface AlertsTableProps {
  alerts?: Alert[];
  currentPage?: number;
  totalPages?: number;
}

export default function AlertsTable({}: AlertsTableProps) {
  const t = useTranslations("DashboardHermesC.Performance.Table");

  const [performancePeriod, setPerformancePeriod] = useState<string>("daily");
  const [direction, setDirection] = useState<string>("");
  const [condition, setCondition] = useState<string>("");
  const [confidence, setConfidence] = useState<string>("");

  // pagination / filters
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [timezone, setTimezone] = useState("");

  // modals
  const [showFilter, setShowFilter] = useState(false);
  const [showEnquireModalFlow, setShowEnquireModalFlow] = useState(false);

  // fetch alerts table with filter state
  const {
    data: alerts,
    isLoading,
    // isFetching,
    refetch,
  } = useGetQueryHermes(
    `/api/hermesc/performance/${performancePeriod}?direction=${direction}&condition=${condition}&confidence=${confidence}`,
    ["get-performance", performancePeriod, direction, condition, confidence],

    {
      onSuccess() {},
      onError() {
        toast.error("Could not load alerts");
      },
    }
  );

  const {
    data: cumulative,
    isLoading: loadingCumulative,
    // isFetching: fetchingCumulative,
    refetch: refetchCumulative,
  } = useGetQueryHermes(
    `/api/hermesc/performance/cumulative?direction=${direction}&condition=${condition}&confidence=${confidence}`,
    ["get-cumulative", direction, condition, confidence],
    {
      onError() {
        // toast.error("Could not load alerts");
      },
    }
  );

  const {
    data: filtersData,
    isLoading: filtersDataLoading,
    refetch: refetchFiltersData,
  } = useGetQueryHermes(
    "/api/hermesc/performance/filters",
    ["get-performance-filters"],
    {
      // enabled: showFilter,
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 60, // 1 hour
      onError() {
        toast.error("Could not load saved filters");
      },
    }
  );

  const period = [
    { label: t("Period.Daily"), value: "daily" },
    { label: t("Period.Weekly"), value: "weekly" },
    { label: t("Period.Monthly"), value: "monthly" },
  ];

  const currentPage = alerts?.pagination?.currentPage || 1;
  const totalPages = alerts?.pagination?.totalPages || 1;
  const hasNextPage = alerts?.pagination?.hasNextPage || false;
  const hasPrevPage = alerts?.pagination?.hasPreviousPage || false;

  const handleNextPage = () => {
    if (hasNextPage) setPage((prev) => prev + 1);
  };

  const handlePrevPage = () => {
    if (hasPrevPage) setPage((prev) => prev - 1);
  };
  const queryClient = useQueryClient();

  useEffect(() => {
    queryClient.prefetchQuery({
      queryKey: ["get-performance", "weekly", "", "", ""],
      queryFn: async () => {
        const res = await hermesAPI.get("/api/hermesc/performance/weekly");
        return res.data;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 60 * 60 * 1000, // 1 hour
    });

    queryClient.prefetchQuery({
      queryKey: ["get-performance", "monthly", "", "", ""],
      queryFn: async () => {
        const res = await hermesAPI.get("/api/hermesc/performance/monthly");
        return res.data;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 60 * 60 * 1000, // 1 hour
    });
  }, [queryClient]);

  useEffect(() => {
    const directionString =
      filtersData?.direction?.length > 0
        ? filtersData?.direction?.join(",")
        : ""; // default values

    const conditionString =
      filtersData?.condition?.length > 0
        ? filtersData?.condition?.join(",")
        : ""; // default values

    const confidenceString =
      filtersData?.confidence?.length > 0
        ? filtersData?.confidence?.join(",")
        : ""; // default empty
    // Set the values in parent component
    setDirection(directionString);
    setCondition(conditionString);
    setConfidence(confidenceString);
  }, [filtersData]);

  return (
    <div className="px-[5%] w-full flex flex-col lg:flex-row gap-4 h-[600px]">
      {/* LEFT COLUMN */}
      <div className="flex flex-col h-full justify-between w-full max-w-[1400px] mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="border rounded-[12px] w-fit py-2 px-2 space-x-2">
              {period?.map((item) => (
                <Button
                  variant={
                    performancePeriod === item.value ? "default" : "outline"
                  }
                  className=" border-none"
                  key={item.value}
                  onClick={() => setPerformancePeriod(item.value)}
                >
                  {item.label}
                </Button>
              ))}
            </div>
            <Button
              className=" border-none"
              onClick={() => setShowFilter(true)}
            >
              {t("Filter")}
            </Button>
          </div>
        </div>

        {/* Table */}
        <div className="flex-1 min-h-0">
          <Table
            alerts={alerts || []}
            isLoading={isLoading}
            // isFetching={isFetching}
            timezone={timezone}
            period={performancePeriod}
          />
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between px-6 border-t py-4">
          <div className="text-sm text-gray-700">
            {t("Page")} {currentPage} {t("of")} {totalPages}
          </div>
          <div className="flex space-x-2">
            <button
              className={`px-4 py-2 border rounded text-sm font-medium ${
                hasPrevPage
                  ? "text-gray-700 bg-white hover:bg-gray-50"
                  : "text-gray-400 bg-gray-100 cursor-not-allowed"
              }`}
              onClick={handlePrevPage}
              disabled={!hasPrevPage}
            >
              {t("Previous")}
            </button>
            <button
              className={`px-4 py-2 border rounded text-sm font-medium ${
                hasNextPage
                  ? "text-gray-700 bg-white hover:bg-gray-50"
                  : "text-gray-400 bg-gray-100 cursor-not-allowed"
              }`}
              onClick={handleNextPage}
              disabled={!hasNextPage}
            >
              {t("Next")}
            </button>
          </div>
        </div>
      </div>

      {/* RIGHT COLUMN */}
      <PerformanceChart
        data={cumulative || []}
        isLoading={loadingCumulative}
        // isFetching={fetchingCumulative}
        className="h-full"
      />

      {/* Modals */}
      <EnquireFlowModalHermesC
        isOpen={showEnquireModalFlow}
        onClose={() => setShowEnquireModalFlow(false)}
      />
      <FilterAlertsModalHermesC
        isOpen={showFilter}
        onClose={() => setShowFilter(false)}
        setDirection={setDirection}
        setCondition={setCondition}
        setConfidence={setConfidence}
        data={filtersData}
        loadingFilter={filtersDataLoading}
        refetch={refetchFiltersData}
      />
    </div>
  );
}
