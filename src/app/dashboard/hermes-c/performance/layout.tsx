import React from "react";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useTranslations } from "next-intl";

interface OrionDashboardLayoutProps {
  children: React.ReactNode;
}

const HermesDashboardLayout = ({ children }: OrionDashboardLayoutProps) => {
  const t = useTranslations("DashboardHermesC.Performance");
  return (
    <div className="space-y-[30px] ">
      <div className="flex items-center min-h-[70px] bg-[#FAFAFA]  px-[5%] mt-[20px]">
        <Link
          href="/dashboard/hermes-c"
          className="flex items-center text-sm gap-2 font-[500]"
        >
          <ArrowLeft className=" " size={20} />
          <span className="border-l-2 border-black pl-2">{t("Title")}</span>
        </Link>
      </div>
      <div className="h-full">{children}</div>

      {/* <WelcomeModalHermesX/> */}
    </div>
  );
};

export default HermesDashboardLayout;
