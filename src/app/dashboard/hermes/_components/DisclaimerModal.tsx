"use client";
import { FC } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { useSubmitQueryHermes } from "@/services/api_hooks";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Loader } from "lucide-react";
import { useTranslations } from "next-intl";

interface DisclaimerModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const DisclaimerModal: FC<DisclaimerModalProps> = ({ isOpen, onClose }) => {
  const t = useTranslations("DashboardHermesX.Modals.DisclaimerModal");

  const { mutateAsync: saveAgreement, isPending: loading } =
    useSubmitQueryHermes("/api/choice/terms", "POST", {
      onSuccess(response: any) {
        toast.success(t("success"), {
          position: "top-right",
          className: "p-4",
        });
        onClose();
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || t("error"), {
          position: "top-right",
          className: "p-4",
        });
      },
    });

  const handleSaveAgreement = async () => {
    saveAgreement({
      termsAccepted: true,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[477px] p-6 bg-white rounded-lg [&>button]:hidden">
        <DialogHeader>
          <DialogTitle className="text-[16px] font-[500] text-">
            {t("title")}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 text-sm">
          <div>
            <h3 className="font-medium mb-1">{t("sections.noAdvice.title")}</h3>
            <p className="text-[#737384] text-xs leading-[20px]">
              {t("sections.noAdvice.text")}
            </p>
          </div>

          <div>
            <h3 className="font-medium mb-1">
              {t("sections.limitations.title")}
            </h3>
            <p className="text-[#737384] text-xs leading-[20px]">
              {t("sections.limitations.text")}
            </p>
            <ul className="text-[#737384] text-xs leading-[20px] list-disc pl-5 mt-1">
              <li>{t("sections.limitations.list.line1")}</li>
              <li> {t("sections.limitations.list.line2")}</li>
              <li>{t("sections.limitations.list.line3")}</li>
            </ul>
            <p className="text-[#737384] text-xs leading-[20px] mt-1">
              {t("sections.limitations.footer")}
            </p>
          </div>

          <div>
            <h3 className="font-medium mb-1">
              {t("sections.userResponsibility.title")}
            </h3>
            <p className="text-[#737384] text-xs leading-[20px]">
              {t("sections.userResponsibility.text")}
            </p>
          </div>
        </div>

        <DialogFooter className="mt-6">
          <div className="flex items-center justify-between w-full gap-4">
            <Button
              className="max-w-[80px] text-black rounded-md"
              onClick={onClose}
              variant="outline"
            >
              {t("cancel")}
            </Button>
            <Button
              className="bg-gray-900 max-w-[300px] w-full hover:bg-gray-800 text-white rounded-md"
              onClick={handleSaveAgreement}
            >
              {t("agree")}
              {loading && <Loader className="animate-spin" />}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DisclaimerModal;
