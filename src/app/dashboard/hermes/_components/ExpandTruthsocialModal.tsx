// components/WelcomeModal.tsx

// @ts-nocheck
"use client";
import { FC, useState, useEffect, useRef } from "react";


declare global {
  interface Window {
    twttr: {
      widgets: {
        load: (element: HTMLElement) => Promise<void>;
        // Add other properties if needed
      };
      // Add other twttr properties if needed
    };
  }
}

import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ExternalLink } from "lucide-react";
import Script from "next/script";

interface TwitterEmbedProps {
  tweetUrl: string;
  maxWidth?: string; // Custom width prop
  theme?: "light" | "dark"; // Theme option
  hideThread?: boolean; // Option to hide conversation thread
  showExpandModal: boolean;
  setShowExpandModal: any
}

const ExpandTruthsocialModal: FC<TwitterEmbedProps> = ({
  tweetUrl,
  maxWidth = "300px",
  theme = "light",
  hideThread = true,
  showExpandModal,
  setShowExpandModal
}: TwitterEmbedProps) => {



  const embedUrl = `https://truthsocial.com/@realDonaldTrump/114609088288599288/embed`

  return (
    <Dialog open={showExpandModal} onOpenChange={setShowExpandModal}>
      <DialogContent className="pb-2 max-w-[450px] overflow-auto max-h-[600px] h-full bg-white rounded-lg">
        <DialogHeader className="space-y-0 p-0 m-0">
          <DialogTitle className="text-[16px] font-[500] sr-only">
          Truth Social Post
          </DialogTitle>
        </DialogHeader>

        <main style={{ padding: "2rem" }}>
        <iframe
        src={tweetUrl}
        className="truthsocial-embed"
        style={{ maxWidth: "100%", border: 0 }}
        width="100%"
        height="540px"
        allowFullScreen
      />
      <Script
        src="https://truthsocial.com/embed.js"
        strategy="afterInteractive"
        async
      />
    </main>
      </DialogContent>
    </Dialog>
  );
};

export default ExpandTruthsocialModal;