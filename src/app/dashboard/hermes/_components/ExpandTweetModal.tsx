// components/WelcomeModal.tsx

// @ts-nocheck
"use client";
import { FC, useState, useEffect, useRef } from "react";

declare global {
  interface Window {
    twttr: {
      widgets: {
        load: (element: HTMLElement) => Promise<void>;
        // Add other properties if needed
      };
      // Add other twttr properties if needed
    };
  }
}

import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface TwitterEmbedProps {
  tweetUrl: string;
  maxWidth?: string; // Custom width prop
  theme?: "light" | "dark"; // Theme option
  hideThread?: boolean; // Option to hide conversation thread
  showExpandModal: boolean;
  setShowExpandModal: any
}

const ExpandTweetModal: FC<TwitterEmbedProps> = ({
  tweetUrl,
  maxWidth = "300px",
  theme = "light",
  hideThread = true,
  showExpandModal,
  setShowExpandModal
}: TwitterEmbedProps) => {

  const tweetRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);

  console.log(tweetUrl)

  useEffect(() => {
    // Only attempt to render tweet when modal is open and we have a URL
    if (!showExpandModal || !tweetUrl) return;
    
    // Reset loading state when tweet URL changes or modal opens
    setIsLoading(true);
    
    // Function to load the Twitter widgets script
    const loadScript = () => {
      return new Promise<void>((resolve) => {
        // Check if script already exists
        if (document.getElementById('twitter-widget-script')) {
          resolve();
          return;
        }
        
        const script = document.createElement('script');
        script.id = 'twitter-widget-script';
        script.src = 'https://platform.twitter.com/widgets.js';
        script.async = true;
        script.onload = () => resolve();
        document.body.appendChild(script);
      });
    };

    // Inject custom CSS for Twitter embeds
    const injectCSS = () => {
      const styleId = 'twitter-embed-custom-styles';
      if (!document.getElementById(styleId)) {
        const style = document.createElement('style');
        style.id = styleId;
        style.innerHTML = `
          .twitter-embed .twitter-tweet {
            max-width: ${maxWidth} !important;
            width: 100% !important;
            margin: 0 auto !important;
        
          }
          .twitter-embed .twitter-tweet iframe {
            max-width: 100% !important;
          }
          /* Makes embedded tweets more compact */
          .twitter-embed iframe {
            border: none !important;
          }
      
        `;
        document.head.appendChild(style);
      }
    };

    // Function to render the tweet
    const renderTweet = async () => {
      try {
        // Make sure twttr is available
        await loadScript();

        // Inject custom CSS
        injectCSS();

        // Clear previous tweet
        if (tweetRef.current) {
          tweetRef.current.innerHTML = '';
          
          // Create a fresh blockquote element
          const blockquote = document.createElement('blockquote');
          blockquote.className = 'twitter-tweet';
          blockquote.setAttribute('data-dnt', 'true');
          blockquote.setAttribute('data-theme', theme);
          
          // Add additional options
          if (hideThread) {
            blockquote.setAttribute('data-conversation', 'none');
          }
          
          // Add the link that Twitter widgets will transform
          const link = document.createElement('a');
          link.href = tweetUrl;
          blockquote.appendChild(link);
          
          // Add to the DOM
          tweetRef.current.appendChild(blockquote);
          
          // Force Twitter widgets to process it by ensuring it's initialized and then calling load
          if (window.twttr) {
            if (window.twttr.widgets) {
              await window.twttr.widgets.load(tweetRef.current);
            } else {
              // If widgets object isn't ready yet, wait for it
              const checkTwttr = setInterval(() => {
                if (window.twttr.widgets) {
                  clearInterval(checkTwttr);
                  window.twttr.widgets.load(tweetRef.current);
                  setTimeout(() => setIsLoading(false), 400);
                }
              }, 100);
            }
          }
          
          // Set a timeout to ensure the tweet has time to render
          setTimeout(() => {
            setIsLoading(false);
          }, 1000);
        }
      } catch (error) {
        console.error('Error loading tweet:', error);
        setIsLoading(false);
      }
    };

    // Start the process
    renderTweet();
    
    // Fallback timeout in case something goes wrong
    const fallbackTimer = setTimeout(() => {
      setIsLoading(false);
    }, 5000);

    // Cleanup
    return () => {
      clearTimeout(fallbackTimer);
    };
    
  }, [tweetUrl, maxWidth, theme, hideThread, showExpandModal]);

  return (
    <Dialog open={showExpandModal} onOpenChange={setShowExpandModal}>
      <DialogContent className=" pb-2 max-w-[450px] overflow-auto max-h-[600px] h-full bg-white rounded-lg">
   
          <DialogHeader className="space-y-0 p-0 m-0">
            <DialogTitle className="text-[16px] font-[500] sr-only"></DialogTitle>
          
          </DialogHeader>
   

        <div className="twitter-embed-container">
      {isLoading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      )}
      <div 
        ref={tweetRef} 
        className={`twitter-embed  ${isLoading ? 'opacity-0' : 'opacity-100 transition-opacity duration-300'}`}
      >
        {/* Twitter widget script will replace this with the embedded tweet */}
      </div>
    </div>

        {/* <DialogFooter className="px-6 pb-6 sm:pb-6">
          <Button
            className="w-full bg-gray-900 hover:bg-gray-800 text-white rounded-md"
            // onClick={handleCloseModal}
          >
            Close
          </Button>
        </DialogFooter> */}
      </DialogContent>
    </Dialog>
  );
};

export default ExpandTweetModal;
