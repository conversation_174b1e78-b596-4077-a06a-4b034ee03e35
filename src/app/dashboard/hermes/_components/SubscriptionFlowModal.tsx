// @ts-nocheck

import React, { useState, useEffect } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useQueryClient } from "@tanstack/react-query";
import {
  useGetQuery,
  useGetQueryHermes,
  useSubmitQueryHermes,
} from "@/services/api_hooks";
import { toast } from "sonner";
import Image from "next/image";
import {
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Loader, Send } from "lucide-react";
import { telegramBot } from "@/config/baseUrl";
import { useTranslations } from "next-intl";

// Define the props for the component
const SubscriptionFlowModal = ({ isOpen, onClose }) => {
  // State for tracking the current step in the flow
  const [currentStep, setCurrentStep] = useState(1);

  // State for TelegramSetupModal
  const [telegramId, setTelegramId] = useState("");

  // State for SubscribeModal
  const [selectedPlan, setSelectedPlan] = useState("monthly");

  // State for TelegramPreferencesModal
  const [filters, setFilters] = useState({
    categories: [],
    significance: [],
    region: [],
    assets: [],
    rateLimit: "No",
    customizeForTelegram: false,
  });

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [initializing, setInitializing] = useState(true);
  const [submitError, setSubmitError] = useState("");
  const [hasAttemptedConnect, setHasAttemptedConnect] = useState(false);

  const queryClient = useQueryClient();
  const t = useTranslations("DashboardHermesX.Modals.SubscriptionFlowModal");

  // Constants for filter options (same as original)

  const CATEGORY_OPTIONS = ["Market", "Sector", "Company"];
  const SIGNIFICANCE_OPTIONS = ["High", "Medium", "Low"];
  const REGION_OPTIONS = [
    "US",
    "China",
    "Japan",
    "India",
    "UK",
    "Germany",
    "Saudi Arabia",
    "Canada",
    "Australia",
    "Southeast Asia",
    "Latin America",
    "EU",
    "Global",
  ];

  const ASSETS_OPTIONS = [
    "Equities",
    "Bonds",
    "FX",
    "Commodities",
    "Crypto",
    "Private",
  ];

  // Fetch user data to determine the starting ste
  const {
    data: choiceData,
    isLoading: choiceLoading,
    isRefetching,
    refetch: refetchTelegram,
    error: telegramError,
  } = useGetQueryHermes("/api/choice", ["get-choice"], {
    refetchOnWindowFocus: true,

    onError() {
      toast.error("Could not load user data");
    },
  });

  const { data: profile, isLoading } = useGetQuery(
    "/auth/profile",
    ["profile"],
    {
      onError() {
        // toast.error("Could not load your profile");
      },
    }
  );

  const { data: subscriptionStatus, isLoading: subscriptionStatusLoading } =
    useGetQueryHermes("/api/user/subscribe", ["get-subscription-status"], {
      onError() {
        toast.error("Could not load subscription status");
      },
    });

  // Fetch saved preferences for telegram preferences step
  const {
    data: savedPreferences,
    isLoading: preferencesLoading,
    refetch,
  } = useGetQueryHermes("/api/choice", ["get-telegram-filters"], {
    enabled: currentStep === 3,
    staleTime: 1000 * 60 * 60,

    onError() {
      toast.error("Could not load saved preferences");
    },
  });

  // Determine the starting step based on user data
  useEffect(() => {
    if (choiceLoading || subscriptionStatusLoading) return;

    if (choiceData?.telegramId === null) {
      // No telegram ID, start at step 1
      setCurrentStep(1);
    } else if (subscriptionStatus?.active !== true) {
      // Has telegram ID but no subscription, start at step 2
      setCurrentStep(2);
    } else {
      // Has telegram ID and subscription, start at step 3
      setCurrentStep(3);
    }

    setInitializing(false);
  }, [
    choiceData,
    subscriptionStatus,
    choiceLoading,
    subscriptionStatusLoading,
  ]);

  // Load saved telegram preferences when on step 3
  useEffect(() => {
    if (
      currentStep === 3 &&
      savedPreferences?.telegramCustomization &&
      !preferencesLoading
    ) {
      setFilters({
        categories: savedPreferences?.telegramCustomization?.categories || [],
        significance: savedPreferences.telegramCustomization.significance || [],
        region: savedPreferences?.telegramCustomization?.region || [],
        rateLimit: savedPreferences?.telegramCustomization?.rate_limit || "No",
        assets: savedPreferences.telegramCustomization.assets || [],
        customizeForTelegram: filters.customizeForTelegram, // Maintain current value
      });
    }
  }, [savedPreferences, preferencesLoading, currentStep]);

  const { mutateAsync: subscribe, isPending } = useSubmitQueryHermes(
    "/api/user/subscribe",
    "POST",
    {
      onSuccess(response) {
        toast.success("Successful!", {
          position: "top-right",
          className: "p-4",
        });
        queryClient.invalidateQueries({
          queryKey: ["get-subscription-status"],
        });
        // Move to next step
        setCurrentStep(3);
        setIsSubmitting(false);
      },
      onError(err) {
        toast.error(err?.response?.data?.error || "Subscription failed", {
          position: "top-right",
          className: "p-4",
        });
        setIsSubmitting(false);
      },
    }
  );

  const { mutateAsync: saveTelegramFilters } = useSubmitQueryHermes(
    "/api/choice/telegram-filters",
    "POST",
    {
      onSuccess(response) {
        toast.success("Successful!", {
          position: "top-right",
          className: "p-4",
        });
        // refetch();
        queryClient.invalidateQueries({
          queryKey: ["hermes-x-alerts"],
        });
        queryClient.invalidateQueries({
          queryKey: ["get-choice"],
        });

        // Close modal on completion of all steps
        onClose();
        setIsSubmitting(false);
      },
      onError(err) {
        toast.error(
          err?.response?.data?.error || "Failed to save preferences",
          {
            position: "top-right",
            className: "p-4",
          }
        );
        setIsSubmitting(false);
      },
    }
  );

  // Handle submissions for each step
  const handleSubmitStep = () => {
    setIsSubmitting(true);

    if (currentStep === 1) {
      setHasAttemptedConnect(true);
      refetchTelegram();
    } else if (currentStep === 2) {
      // Process subscription
      subscribe({
        plan: selectedPlan,
      });
    } else if (currentStep === 3) {
      // Save Telegram preferences
      saveTelegramFilters({
        categories: filters.categories,
        significance: filters.significance,
        region: filters.region,
        assets: filters.assets,
        rate_limit: filters.rateLimit,
      });
    }
  };

  // Helper functions for Telegram Preferences
  const toggleCategory = (category) => {
    if (filters.rateLimit === "Yes") return; // Prevent toggling if Rate Limit is Yes

    const newCategories = [...filters.categories];

    if (newCategories.includes(category)) {
      // Remove if already selected
      const index = newCategories.indexOf(category);
      newCategories.splice(index, 1);
    } else {
      // Add if not selected
      newCategories.push(category);
    }

    setFilters({ ...filters, categories: newCategories });
  };

  const toggleSignificance = (significance) => {
    if (filters.rateLimit === "Yes") return; // Prevent toggling if Rate Limit is Yes

    const newSignificance = [...filters.significance];

    if (newSignificance.includes(significance)) {
      // Remove if already selected
      const index = newSignificance.indexOf(significance);
      newSignificance.splice(index, 1);
    } else {
      // Add if not selected
      newSignificance.push(significance);
    }

    setFilters({ ...filters, significance: newSignificance });
  };

  const toggleRegion = (region) => {
    if (filters.rateLimit === "Yes") return; // Prevent toggling if Rate Limit is Yes

    const newRegions = [...filters.region];

    if (newRegions.includes(region)) {
      // Remove if already selected
      const index = newRegions.indexOf(region);
      newRegions.splice(index, 1);
    } else {
      // Add if not selected
      newRegions.push(region);
    }

    setFilters({ ...filters, region: newRegions });
  };

  const toggleAsset = (asset: string) => {
    if (filters.rateLimit === "Yes") return;
    const newAssets = [...filters.assets];
    const idx = newAssets.indexOf(asset);
    if (idx > -1) newAssets.splice(idx, 1);
    else newAssets.push(asset);
    setFilters({ ...filters, assets: newAssets });
  };

  const handleDefaultFilter = () => {
    setFilters({
      categories: [...DEFAULT_OPTIONS.categories],
      significance: [...DEFAULT_OPTIONS.significance],
      region: [...DEFAULT_OPTIONS.region],
      assets: [...DEFAULT_OPTIONS.assets],
      rateLimit: "No", // Keeping default rate limit as "No"
      customizeForTelegram: false, // Reset this option too
    });
  };

  // Check if a category is selected
  const isCategorySelected = (category) => {
    return filters.categories.includes(category);
  };

  // Check if a significance level is selected
  const isSignificanceSelected = (significance) => {
    return filters.significance.includes(significance);
  };

  // Check if a region is selected
  const isRegionSelected = (region) => {
    return filters.region.includes(region);
  };

  // Check if a asset is selected
  const isAssetSelected = (asset: string) => {
    return filters.assets.includes(asset);
  };

  // Check if any filter is selected for enabling Apply button
  const isAnyFilterSelected =
    filters.rateLimit === "Yes" ||
    filters.categories.length > 0 ||
    filters.significance.length > 0 ||
    filters.region.length > 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      {initializing || choiceLoading || subscriptionStatusLoading ? (
        <DialogContent className="sm:max-w-[447px] p-6 bg-white rounded-lg">
          <div className="flex items-center justify-center h-64">
            <div className="flex flex-col items-center gap-2">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              <p className="text-sm text-gray-500">Loading...</p>
            </div>
          </div>
        </DialogContent>
      ) : (
        <div>
          {currentStep === 1 && ( // Telegram Setup Step
            <DialogContent className="sm:max-w-[447px] p-0 overflow-hidden bg-white rounded-lg">
              <div className="w-full h-[230px] bg-[#F7FAFF] rounded-t-lg  flex items-center justify-center">
                <Image
                  src="/profile-image.svg"
                  alt="HermesX Logo"
                  width={416}
                  height={153}
                  priority
                  className="object-contain"
                />
              </div>

              <div className="px-6 mb-1">
                <DialogHeader>
                  <DialogTitle className="text-[16px] font-[500]">
                    {t("connectTelegram.title")}
                  </DialogTitle>
                  <DialogDescription>
                    <span className="text-start text-[#737384] text-xs leading-[20px] mt-2">
                      {t("connectTelegram.description")}
                    </span>
                  </DialogDescription>
                </DialogHeader>
              </div>

              <DialogFooter className="px-6 pb-6 sm:pb-6">
                <div className=" w-full gap-4">
                  {/* <Button
                    className="bg-gray-900  w-full hover:bg-gray-800 text-white rounded-md"
                    onClick={handleSubmitStep}
                    // disabled={!telegramId.trim() || isSubmitting}
                  >
                    Connect
                    {isRefetching && (
                      <>
                        {" "}
                        <Loader className="ml-2 w-4 h-4 animate-spin" />
                      </>
                    )}
                  </Button> */}
                  <Button
                    disabled={isLoading}
                    className="bg-transparent w-full hover:bg-transparent p-0"
                  >
                    {!isRefetching ? (
                      <a
                        href={`${telegramBot}?start=link_${profile?.profile?.user_id}`}
                        target="_blank"
                        className="flex items-center justify-center w-full text-sm gap-2 bg-[#0088CC] py-1 px-3 rounded-sm text-white"
                      >
                        {t("connectTelegram.linkTelegram")}
                        <Send className="w-4 h-4" color="white" />
                      </a>
                    ) : (
                      <p className="flex items-center justify-center w-full text-sm gap-2 bg-[#0088CC] py-1 px-3 rounded-sm text-white">
                        {t("connectTelegram.checkingStatus")}
                      </p>
                    )}
                  </Button>
                  {/* {choiceData && (
                    <p className="text-red-600 text-sm mt-2">
                      No Telegram ID found for this user.
                    </p>
                  )} */}

                  {/* {telegramError?.response?.data?.error ===
                    "No Telegram ID found for this user." && (
                    <div className="text-sm text-red-500">
                      Please link your telegram first
                    </div>
                  )} */}
                </div>
              </DialogFooter>
            </DialogContent>
          )}

          {currentStep === 2 && ( // Subscribe Step
            <DialogContent className="sm:max-w-[447px] p-0 overflow-hidden bg-white rounded-lg">
              <div className="w-full h-[267px] bg-blue-50 flex items-end justify-center">
                <Image
                  src="/dollar_icon.svg"
                  alt="HermesX Logo"
                  width={135}
                  height={259}
                  priority
                  className="object-contain"
                />
              </div>

              <div className="px-6 mb-1">
                <DialogHeader>
                  <DialogTitle className="text-[16px] font-[500]">
                    {t("subscribe.title")}
                  </DialogTitle>
                  <DialogDescription>
                    <span className="text-start text-[#737384] text-xs leading-[20px] mt-2">
                      {t("subscribe.description")}
                    </span>
                  </DialogDescription>
                </DialogHeader>
              </div>

              <div className="flex items-center justify-between gap-4 mb-9 px-6">
                <div
                  className={`border rounded-md p-2 flex items-center max-w-[200px] w-full justify-between cursor-pointer ${
                    selectedPlan === "monthly"
                      ? "border-blue-500 bg-blue-50"
                      : ""
                  }`}
                  onClick={() => setSelectedPlan("monthly")}
                >
                  <div className="grid">
                    <span className="text-xs font-[300] text-[#737384]">
                      {t("subscribe.monthly")}
                    </span>
                    <span className="text-[16px] font-[500] text-black">
                      {t("subscribe.monthlyCredits")}
                    </span>
                  </div>
                  <Image
                    src="/dollar_icon.svg"
                    alt="HermesX Logo"
                    width={28}
                    height={53}
                    priority
                    className="object-contain"
                  />
                </div>

                <div
                  className={`border rounded-md p-2 flex items-center max-w-[200px] w-full justify-between cursor-pointer ${
                    selectedPlan === "yearly"
                      ? "border-blue-500 bg-blue-50"
                      : ""
                  }`}
                  onClick={() => setSelectedPlan("yearly")}
                >
                  <div className="grid">
                    <span className="text-xs font-[300] text-[#737384]">
                      {t("subscribe.annually")}
                    </span>
                    <span className="text-[16px] font-[500] text-black">
                      {t("subscribe.annualCredits")}
                    </span>
                  </div>
                  <Image
                    src="/dollar_icon.svg"
                    alt="HermesX Logo"
                    width={28}
                    height={53}
                    priority
                    className="object-contain"
                  />
                </div>
              </div>

              <DialogFooter className="px-6 pb-6 sm:pb-6">
                <div className="flex items-center justify-between w-full gap-4">
                  <Button
                    className="max-w-[80px] text-black rounded-md"
                    onClick={onClose}
                    variant="outline"
                    disabled={isSubmitting}
                  >
                    {t("subscribe.cancel")}
                  </Button>
                  <Button
                    className="bg-gray-900 max-w-[300px] w-full hover:bg-gray-800 text-white rounded-md"
                    onClick={handleSubmitStep}
                    disabled={isPending}
                  >
                    {t("subscribe.proceed")}
                    {isPending && (
                      <Loader className="ml-2 w-4 h-4 animate-spin" />
                    )}
                  </Button>
                </div>
              </DialogFooter>
            </DialogContent>
          )}

          {currentStep === 3 && ( // Telegram Preferences Step
            <DialogContent className="sm:max-w-[440px] p-6 bg-white rounded-lg max-h-[700px] overflow-y-auto">
              {preferencesLoading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="flex flex-col items-center gap-2">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                    <p className="text-sm text-gray-500">
                      {t("preferences.loading")}
                    </p>
                  </div>
                </div>
              ) : (
                <>
                  <DialogHeader className="flex flex-row items-center justify-between mt-5">
                    <DialogTitle className="text-[18px] font-medium">
                      {t("preferences.title")}
                    </DialogTitle>
                    <Button
                      onClick={handleDefaultFilter}
                      className="bg-gray-900 text-xs flex items-center gap-1 text-white px-3 h-[30px] rounded-[4px] hover:bg-gray-800"
                      disabled={isSubmitting}
                    >
                      {t("preferences.reset")}
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="12"
                        height="12"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="w-3 h-3"
                      >
                        <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                        <path d="M3 3v5h5"></path>
                      </svg>
                    </Button>
                  </DialogHeader>

                  <div className="space-y-6 mt-4">
                    {/* Category Section */}
                    <div>
                      <h3 className="text-xs font-[400] mb-1">
                        {t("preferences.category.text")}
                      </h3>
                      <p className="text-xs text-gray-500 mb-2">
                        {t("preferences.category.description")}
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {CATEGORY_OPTIONS.map((category) => (
                          <Button
                            key={category}
                            size="sm"
                            className={`rounded-[4px] border flex items-center py-0 h-[25px] px-2 font-[400] text-xs ${
                              isCategorySelected(category)
                                ? "bg-black text-white"
                                : "bg-white text-black hover:bg-gray-100"
                            } ${
                              filters.rateLimit === "Yes"
                                ? "opacity-80 cursor-not-allowed"
                                : ""
                            }`}
                            onClick={() => toggleCategory(category)}
                            disabled={
                              filters.rateLimit === "Yes" || isSubmitting
                            }
                          >
                            {category}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Significance Section */}
                    <div>
                      <h3 className="text-xs font-medium mb-1">
                        {t("preferences.significance.text")}
                      </h3>
                      <p className="text-xs text-gray-500 mb-2">
                        {t("preferences.significance.description")}
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {SIGNIFICANCE_OPTIONS.map((significance) => (
                          <Button
                            key={significance}
                            size="sm"
                            className={`rounded-[4px] border flex items-center py-0  h-[25px] px-2 font-[400] text-xs ${
                              isSignificanceSelected(significance)
                                ? "bg-black text-white"
                                : "bg-white text-black hover:bg-gray-100"
                            } ${
                              filters.rateLimit === "Yes"
                                ? "opacity-80 cursor-not-allowed"
                                : ""
                            }`}
                            onClick={() => toggleSignificance(significance)}
                            disabled={
                              filters.rateLimit === "Yes" || isSubmitting
                            }
                          >
                            {significance}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Assets */}
                    <div>
                      <h3 className="text-xs font-medium mb-1">
                        {t("preferences.assets.text")}
                      </h3>
                      <p className="text-xs text-gray-500 mb-2">
                        {t("preferences.assets.description")}
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {ASSETS_OPTIONS.map((asset) => (
                          <Button
                            key={asset}
                            size="sm"
                            className={`rounded-[4px] border flex items-center py-0 h-[25px] px-2 font-[400] text-xs ${
                              isAssetSelected(asset)
                                ? "bg-black text-white"
                                : "bg-white text-black hover:bg-gray-100"
                            } ${
                              filters.rateLimit === "Yes"
                                ? "opacity-80 cursor-not-allowed"
                                : ""
                            }`}
                            onClick={() => toggleAsset(asset)}
                            disabled={filters.rateLimit === "Yes"}
                          >
                            {asset}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Region Section */}
                    <div>
                      <h3 className="text-xs font-[400] mb-1">
                        {t("preferences.region.text")}
                      </h3>
                      <p className="text-xs text-gray-500 mb-2">
                        {t("preferences.region.description")}
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {REGION_OPTIONS.map((region) => (
                          <Button
                            key={region}
                            size="sm"
                            className={`rounded-[4px] border flex items-center py-0 h-[25px] px-2 font-[400] text-xs ${
                              isRegionSelected(region)
                                ? "bg-black text-white"
                                : "bg-white text-black hover:bg-gray-100"
                            } ${
                              filters.rateLimit === "Yes"
                                ? "opacity-80 cursor-not-allowed"
                                : ""
                            }`}
                            onClick={() => toggleRegion(region)}
                            disabled={
                              filters.rateLimit === "Yes" || isSubmitting
                            }
                          >
                            {region}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>

                  <DialogFooter className="mt-10">
                    <div className="flex items-center justify-between w-full gap-4">
                      <Button
                        variant="outline"
                        onClick={onClose}
                        className="border border-gray-300 h-[32px] text-black"
                        disabled={isSubmitting}
                      >
                        {t("preferences.cancel")}
                      </Button>
                      <Button
                        className="bg-gray-900 text-white h-[32px] hover:bg-gray-800 w-full max-w-[300px]"
                        onClick={handleSubmitStep}
                        disabled={!isAnyFilterSelected || isSubmitting}
                      >
                        {isSubmitting
                          ? t("preferences.saving")
                          : t("preferences.apply")}
                        {isSubmitting && (
                          <Loader className="ml-2 w-4 h-4 animate-spin" />
                        )}
                      </Button>
                    </div>
                  </DialogFooter>
                </>
              )}
            </DialogContent>
          )}
        </div>
      )}
    </Dialog>
  );
};

export default SubscriptionFlowModal;
