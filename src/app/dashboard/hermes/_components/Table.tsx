import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Toolt<PERSON>Trigger,
} from "@/components/ui/tooltip";
import { SquareArrowOutUpRight } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";
import Image from "next/image";
import moment from "moment";
import { useGetQueryHermes } from "@/services/api_hooks";
import { useTranslations } from "next-intl";

interface Alert {
  timestamp: string;
  tweet_text: string;
  event: string;
  category: string;
  asset: string;
  region: string;
  direction: string;
  significance: string;
  affected_assets: string;
  link: string;
}

interface AlertsTableComponentProps {
  alerts: Alert[];
  isLoading: boolean;
  // isFetching: boolean;
  timezone: string;
  onExpandTweet: (link: string) => void;
}

const Table: React.FC<AlertsTableComponentProps> = ({
  alerts,
  isLoading,
  // isFetching,
  timezone,
  onExpandTweet,
}) => {
  const t = useTranslations("DashboardHermesX");
  const {
    data: choiceData,
    isLoading: choiceLoading,
    refetch: refetchhoice,
  } = useGetQueryHermes("/api/choice", ["get-choice"], {
    onError() {
      // toast.error("Could not load alerts");
    },
  });

  const truncateText = (text: string, maxLength = 100) => {
    if (!text) return "";
    return text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text;
  };

  const getTimezoneDisplay = () => {
    if (
      timezone === "UserTime" ||
      choiceData?.tableCustomization?.timezone === "UserTime"
    )
      return t("AlertsTable.UserTime");
    return (
      timezone ||
      choiceData?.tableCustomization?.timezone ||
      t("AlertsTable.UTC")
    );
  };

  // Helper function to get platform info
  const getPlatformInfo = (link: string) => {
    if (link.includes("truthsocial.com") || link.includes("truth.social")) {
      return {
        name: "truthsocial.com",
        icon: (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 100 100"
            width="18"
            height="18"
          >
            <rect x="12" y="16" width="20" height="20" fill="#5448EE" />
            <rect x="34" y="16" width="12" height="68" fill="#5448EE" />
            <rect x="46" y="16" width="34" height="20" fill="#5448EE" />
            <rect x="70" y="70" width="18" height="18" fill="#2FEECC" />
          </svg>
        ),
      };
    } else if (link.includes("twitter.com") || link.includes("x.com")) {
      return {
        name: "twitter.com",
        icon: (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
          </svg>
        ),
      };
    }
    return {
      name: "News Summary",
      icon: (
        <Image
          src="/reuters.png"
          alt="News Summary"
          width={18}
          height={18}
          className="inline-block h-[24px] w-[24px] rounded-full"
        />
      ),
    };
  };

  const renderPlatformIcon = (link: any) => {
    if (link.includes("truthsocial.com") || link.includes("truth.social")) {
      return (
        <div className="truth-social-info">
          <div>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
              <rect x="12" y="16" width="20" height="20" fill="#5448EE" />
              <rect x="34" y="16" width="12" height="68" fill="#5448EE" />
              <rect x="46" y="16" width="34" height="20" fill="#5448EE" />
              <rect x="70" y="70" width="18" height="18" fill="#2FEECC" />
            </svg>
          </div>
        </div>
      );
    } else if (link.includes("twitter.com") || link.includes("x.com")) {
      return (
        <div className="twitter-info">
          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
            </svg>
          </div>
        </div>
      );
    }
    return null;
  };

  const renderSkeletonCard = (index: number) => (
    <div
      key={`skeleton-card-${index}`}
      className="bg-white p-4 rounded-lg border space-y-3"
    >
      <div className="flex justify-between items-start">
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-6 w-16 rounded-full" />
      </div>
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-3/4" />
      <div className="flex flex-wrap gap-2">
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-4 w-24" />
      </div>
      <Skeleton className="h-4 w-full" />
    </div>
  );

  const renderAlertCard = (alert: Alert, index: number) => {
    const platformInfo = getPlatformInfo(alert.link);

    return (
      <div key={index} className="bg-white p-4 rounded-lg border space-y-3">
        {/* Header with time and direction */}
        <div className="flex justify-between items-start">
          <div className="text-xs text-gray-600">
            {moment(alert.timestamp).format("YYYY-MM-DD HH:mm")}
          </div>
          <span
            className={`px-2 py-1 text-xs font-medium rounded-full ${
              alert.direction === "up"
                ? "bg-green-100 text-green-600"
                : "bg-red-100 text-red-600"
            }`}
          >
            {alert.direction}
          </span>
        </div>

        {/* Tweet text */}
        <div className="space-y-1">
          <div className="text-xs font-medium text-gray-700">
            {t("AlertsTable.Table.Header.TweetText")}
          </div>
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                {platformInfo.name === "News Summary" ? (
                  <span
                    className="text-sm cursor-default"
                    dangerouslySetInnerHTML={{
                      __html: truncateText(alert.tweet_text, 120),
                    }}
                  />
                ) : (
                  <div className="text-sm cursor-default">
                    {truncateText(alert.tweet_text, 120)}
                  </div>
                )}
              </TooltipTrigger>
              <TooltipContent
                className="max-w-[300px] p-3 text-sm bg-white text-black"
                side="bottom"
              >
                <div className="flex items-center justify-between mb-5">
                  {platformInfo.icon}
                  {(alert.link.includes("twitter.com") ||
                    alert.link.includes("x.com")) && (
                    <span
                      className="flex items-center text-[12px] text-blue-600 gap-1 cursor-pointer"
                      onClick={() => onExpandTweet(alert.link)}
                    >
                      Expand Tweet <SquareArrowOutUpRight size={12} />
                    </span>
                  )}
                </div>

                <div className="text-[13px] text-blue-600 mb-3">
                  {platformInfo.name !== "News Summary" && (
                    <Link
                      href={alert.link}
                      target="_blank"
                      className="font-[500] truncate"
                    >
                      {truncateText(alert.link, 42)}
                    </Link>
                  )}
                  <div className="text-[#72757A]">{platformInfo.name}</div>
                </div>

                {platformInfo.name === "News Summary" ? (
                  <span
                    className="text-xs"
                    dangerouslySetInnerHTML={{ __html: alert.tweet_text }}
                  />
                ) : (
                  alert.tweet_text
                )}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {/* Alert */}
        <div className="space-y-1">
          <div className="text-xs font-medium text-gray-700">
            {t("AlertsTable.Table.Header.Alert")}
          </div>
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="text-sm cursor-default">
                  {truncateText(alert.event, 120)}
                </div>
              </TooltipTrigger>
              <TooltipContent
                className="max-w-[300px] p-3 text-xs leading-5 bg-white text-black space-y-2"
                side="bottom"
              >
                <div className="shrink-0 rounded-full h-[25px] w-[25px] overflow-hidden">
                  <Image
                    src="/hermes_thumbnail.svg"
                    height={24}
                    width={20}
                    alt="hermes logo"
                    className="h-full w-full"
                  />
                </div>
                <p className="font-semibold text-xs">Hermes Alert</p>
                <p>{alert.event}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {/* Category, Region, Significance */}
        <div className="flex flex-wrap gap-x-4 gap-y-1 text-xs">
          <div>
            <span className="font-medium text-gray-700">
              {t("AlertsTable.Table.Header.Category")}:{" "}
            </span>
            <span>{alert.category}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">
              {t("AlertsTable.Table.Header.Region")}:{" "}
            </span>
            <span>{alert.region}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">
              {t("AlertsTable.Table.Header.Significance")}:{" "}
            </span>
            <span>{alert.significance}</span>
          </div>
        </div>

        <div className="text-xs">
          <span className="font-medium text-gray-700 ">
            {t("AlertsTable.Table.Header.Assets")}:{" "}
          </span>
          <span>{alert.asset}</span>
        </div>

        {/* Affected Assets */}
        <div className="space-y-1">
          <div className="text-xs font-medium text-gray-700">
            {t("AlertsTable.Table.Header.AffectedAssets")}
          </div>
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="text-sm cursor-default">
                  {truncateText(alert.affected_assets, 80)}
                </div>
              </TooltipTrigger>
              <TooltipContent
                className="max-w-[300px] p-3 text-sm bg-white text-black"
                side="bottom"
              >
                <p className="font-semibold text-xs mb-2">Affected Assets</p>
                <p className="text-xs">{alert.affected_assets}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    );
  };

  return (
    <div className="border rounded-lg">
      {/* Desktop Table View */}
      <div className="hidden lg:block overflow-x-auto border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200 text-black">
          <thead className="bg-gray-50 font-[600] text-[#6C7788] text-xs">
            <tr>
              <th
                scope="col"
                className="py-3 px-3 text-left w-[80px] tracking-wider"
              >
                {t("AlertsTable.Table.Header.Time")} ({getTimezoneDisplay()})
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left w-[300px] tracking-wider"
              >
                {t("AlertsTable.Table.Header.TweetText")}
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left w-[300px] tracking-wider"
              >
                {t("AlertsTable.Table.Header.Alert")}
              </th>
              <th scope="col" className="px-3 py-3 text-left w-[120px]">
                {t("AlertsTable.Table.Header.Category")}
              </th>
              <th scope="col" className="px-3 py-3 text-left w-[120px]">
                {t("AlertsTable.Table.Header.Assets")}
              </th>
              <th scope="col" className="px-3 py-3 text-left w-[100px] ">
                {t("AlertsTable.Table.Header.Region")}
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left w-[90px] tracking-wider"
              >
                {t("AlertsTable.Table.Header.Direction")}
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left w-[100px] tracking-wider"
              >
                {t("AlertsTable.Table.Header.Significance")}
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left w-[230px] tracking-wider"
              >
                {t("AlertsTable.Table.Header.AffectedAssets")}
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200 font-[400]">
            {isLoading  ? (
              // Skeleton loader rows while loading
              Array(5)
                .fill(0)
                .map((_, index) => (
                  <tr key={`skeleton-${index}`}>
                    <td className="px-4 py-2 h-[84px]">
                      <Skeleton className="h-4 w-20" />
                    </td>
                    <td className="px-4 py-2 h-[84px]">
                      <Skeleton className="h-4 w-48" />
                    </td>
                    <td className="px-4 py-2 h-[84px]">
                      <Skeleton className="h-4 w-40" />
                    </td>
                    <td className="px-4 py-2 h-[84px]">
                      <Skeleton className="h-4 w-20" />
                    </td>
                    <td className="px-4 py-2 h-[84px]">
                      <Skeleton className="h-4 w-20" />
                    </td>
                    <td className="px-4 py-2 h-[84px]">
                      <Skeleton className="h-4 w-16" />
                    </td>
                    <td className="px-4 py-2 h-[84px]">
                      <Skeleton className="h-6 w-16 rounded-full" />
                    </td>
                    <td className="px-6 h-[84px]">
                      <Skeleton className="h-4 w-16" />
                    </td>
                    <td className="px-6 h-[84px]">
                      <Skeleton className="h-4 w-36" />
                    </td>
                  </tr>
                ))
            ) : alerts?.length > 0 ? (
              alerts.map((alert: Alert, index: number) => {
                const platformInfo = getPlatformInfo(alert.link);

                return (
                  <tr key={index}>
                    <td className="px-3 py-2 h-[84px] whitespace-nowrap text-[12px]">
                      {moment(alert.timestamp).format("YY-MM-DD HH:mm")}
                    </td>
                    <td className="px-3 py-2 h-[84px] text-[12px] max-w-xs">
                      <TooltipProvider delayDuration={100}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            {platformInfo.name === "News Summary" ? (
                              <span
                                className="block max-w-[260px] cursor-default"
                                dangerouslySetInnerHTML={{
                                  __html: truncateText(alert.tweet_text),
                                }}
                              />
                            ) : (
                              <span className="block max-w-[260px] cursor-default">
                                {truncateText(alert.tweet_text)}
                              </span>
                            )}
                          </TooltipTrigger>
                          <TooltipContent
                            className="max-w-[300px] p-3 text-sm bg-white text-black"
                            side="bottom"
                          >
                            <div className="flex items-center justify-between mb-5">
                              {platformInfo.icon}
                              {(alert.link.includes("twitter.com") ||
                                alert.link.includes("x.com")) && (
                                <span
                                  className="flex items-center text-[12px] text-blue-600 gap-1 cursor-pointer"
                                  onClick={() => onExpandTweet(alert.link)}
                                >
                                  Expand Tweet{" "}
                                  <SquareArrowOutUpRight size={12} />
                                </span>
                              )}
                            </div>

                            <div className="text-[13px] text-blue-600 mb-3">
                              {platformInfo.name !== "News Summary" && (
                                <Link
                                  href={alert.link}
                                  target="_blank"
                                  className="font-[500] truncate"
                                >
                                  {truncateText(alert.link, 42)}
                                </Link>
                              )}
                              <div className="text-[#72757A]">
                                {platformInfo.name}
                              </div>
                            </div>

                            {platformInfo.name === "News Summary" ? (
                              <span
                                className="text-xs"
                                dangerouslySetInnerHTML={{
                                  __html: alert.tweet_text,
                                }}
                              />
                            ) : (
                              alert.tweet_text
                            )}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="px-3 py-2 h-[84px] text-[12px] max-w-xs">
                      <TooltipProvider delayDuration={100}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block max-w-[240px] cursor-default">
                              {truncateText(alert.event)}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent
                            className="max-w-[300px] p-3 text-xs leading-5 bg-white text-black space-y-2"
                            side="bottom"
                          >
                            <div className="shrink-0 rounded-full h-[25px] w-[25px] overflow-hidden">
                              <Image
                                src="/hermes_thumbnail.svg"
                                height={24}
                                width={20}
                                alt="hermes logo"
                                className="h-full w-full"
                              />
                            </div>
                            <p className="font-semibold text-xs">
                              Hermes Alert
                            </p>
                            <p>{alert.event}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="px-3 py-2 h-[84px] truncate whitespace-nowrap text-[12px]">
                      {alert.category}
                    </td>
                    <td className="px-3 py-2 h-[84px] truncate whitespace-nowrap text-[12px]">
                      {alert.asset}
                    </td>
                    <td className="px-3 py-2 h-[84px]   text-[12px]">
                      {alert.region}
                    </td>
                    <td className="px-3 py-2 h-[84px] whitespace-nowrap text-[12px]">
                      <span
                        className={`px-2 py-2 font-medium rounded-full ${
                          alert.direction === "up"
                            ? "bg-green-100 text-green-600"
                            : "bg-red-100 text-red-600"
                        }`}
                      >
                        {alert.direction}
                      </span>
                    </td>
                    <td className="px-3 h-[84px] whitespace-nowrap text-[12px]">
                      {alert.significance}
                    </td>
                    <td className="px-3 h-[84px] text-[12px]">
                      <TooltipProvider delayDuration={100}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block max-w-[210px] cursor-default">
                              {truncateText(alert.affected_assets)}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent
                            className="max-w-[300px] p-3 text-sm bg-white text-black"
                            side="bottom"
                          >
                            <p className="font-semibold text-xs mb-2">
                              Affected Assets
                            </p>
                            <p className="text-xs">{alert.affected_assets}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                  </tr>
                );
              })
            ) : (
              // No results message
              <tr>
                <td
                  colSpan={8}
                  className="px-6 py-10 text-center text-gray-500"
                >
                  {t("AlertsTable.Table.NoResultsFound")}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="lg:hidden">
        {/* Header for mobile */}
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-sm font-semibold text-gray-700">
            {t("AlertsTable.title")} ({getTimezoneDisplay()})
          </h3>
        </div>

        <div className="p-4 space-y-4 max-h-[70vh] overflow-y-auto">
          {isLoading  ? (
            // Skeleton loader cards while loading
            Array(5)
              .fill(0)
              .map((_, index) => renderSkeletonCard(index))
          ) : alerts?.length > 0 ? (
            alerts.map((alert: Alert, index: number) =>
              renderAlertCard(alert, index)
            )
          ) : (
            // No results message
            <div className="text-center py-10 text-gray-500">
              {t("AlertsTable.Table.NoResultsFound")}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Table;
