"use client";
import { FC } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Header,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { DialogTitle } from "@radix-ui/react-dialog";
import Image from "next/image";
import { useTranslations } from "next-intl";

interface TelegramConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContinue: () => void;
}

const TelegramConfirmationModal: FC<TelegramConfirmationModalProps> = ({
  isOpen,
  onClose,
  onContinue,
}) => {
  const t = useTranslations(
    "DashboardHermesX.Modals.TelegramConfirmationModal"
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[447px] p-0 pb-5  bg-white rounded-lg [&>button]:hidden">
        <DialogTitle className="sr-only"> Telegram confirmation</DialogTitle>

        <div className="w-full h-[230px] bg-[#F7FAFF] rounded-t-lg  flex items-center justify-center">
          <Image
            src="/profile-image.svg"
            alt="HermesX Logo"
            width={416}
            height={153}
            priority
            className="object-contain"
          />
        </div>

        <DialogHeader>
          <h2 className=" text-[16px] font-medium px-4">{t("title")}</h2>
        </DialogHeader>

        <p className="text-start text-[#737384] text-xs leading-[20px] mt-2 px-4">
          {t("description")}
        </p>

        <DialogFooter className="px-4 mt-13">
          <Button
            className="w-full bg-gray-900 hover:bg-gray-800 text-white rounded-md "
            onClick={onContinue}
          >
            {t("continue")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TelegramConfirmationModal;
