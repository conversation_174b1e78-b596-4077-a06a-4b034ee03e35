"use client";
import { FC, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Image from "next/image";
import { useSubmitQueryHermes } from "@/services/api_hooks";
import { toast } from "sonner";
import { Loader } from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

interface TelegramSetupModalProps {
  isOpen?: boolean;
  onClose: () => void;
}

const TelegramSetupModal: FC<TelegramSetupModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [telegramId, setTelegramId] = useState<string>("");

  const queryClient = useQueryClient();
  const t = useTranslations("DashboardHermesX.Modals.TelegramSetupModal");

  const { mutateAsync: saveTelegramId, isPending: loading } =
    useSubmitQueryHermes("/api/user/save-telegram-id", "POST", {
      onSuccess(response: any) {
        toast.success(t("success"), {
          position: "top-right",
          className: "p-4",
        });
        onClose();
        queryClient.invalidateQueries({
          queryKey: ["get-telegram-hermes"],
        });
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || t("error"), {
          position: "top-right",
          className: "p-4",
        });
      },
    });

  const handlesaveTelegramId = async () => {
    saveTelegramId({
      telegram_id: telegramId,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[447px] p-0 pb-5 bg-white rounded-lg [&>button]:hidden">
        <DialogTitle className="sr-only"> Telegram setup</DialogTitle>
        <div className="w-full h-[230px] rounded-t-lg bg-[#F7FAFF] flex items-center justify-center">
          <Image
            src="/profile-image.svg"
            alt="HermesX Logo"
            width={416}
            height={153}
            priority
            className="object-contain"
          />
        </div>

        <DialogHeader>
          <h2 className=" text-[16px] font-medium px-4">{t("title")}</h2>
        </DialogHeader>

        <p className="text-start text-[#737384] text-xs leading-[20px] mt-2 px-4">
          {t("description")}
        </p>

        <DialogFooter className="px-4 mt-10">
          <Button
            className="w-full bg-gray-900  hover:bg-gray-800 text-white rounded-md"
            onClick={handlesaveTelegramId}
            // disabled={loading || !telegramId.trim()}
          >
            {t("button")}
            {loading && <Loader className="animate-spin" />}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TelegramSetupModal;
