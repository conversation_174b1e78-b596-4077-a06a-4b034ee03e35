"use client";
import { FC, useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertCircle, RotateCw } from "lucide-react";

import { useQueryClient } from "@tanstack/react-query";
import { useGetQueryHermes, useSubmitQueryHermes } from "@/services/api_hooks";
import { toast } from "sonner";
import { useTranslations } from "next-intl";

interface FilterAlertsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyFilter?: (filters: FilterOptions) => void;
  refetch: any;
}

interface FilterOptions {
  categories: string[];
  significance: string[];
  region: string[];
  assets: string[]; // ← NEW
  rateLimit: "Yes" | "No";
  customizeForTelegram: boolean;
}

const CATEGORY_OPTIONS = ["Market", "Sector", "Company"];
const SIGNIFICANCE_OPTIONS = ["High", "Medium", "Low"];
const REGION_OPTIONS = [
  "US",
  "China",
  "Japan",
  "India",
  "UK",
  "Germany",
  "Saudi Arabia",
  "Canada",
  "Australia",
  "Southeast Asia",
  "Latin America",
  "EU",
  "Global",
];

const DEFAULT_OPTIONS = {
  categories: ["Market", "Sector", "Company"],
  assets: ["Equities", "Bonds", "FX", "Commodities", "Crypto", "Private"],
  significance: ["High"],
  region: [
    "Global",
    "US",
    "China",
    "Japan",
    "India",
    "UK",
    "Germany",
    "Saudi Arabia",
    "Canada",
    "Australia",
    "EU",
    "Southeast Asia",
    "Latin America",
  ],
};

const ASSETS_OPTIONS = [
  "Equities",
  "Bonds",
  "FX",
  "Commodities",
  "Crypto",
  "Private",
];

const FilterAlertsModal: FC<FilterAlertsModalProps> = ({
  isOpen,
  onClose,
  onApplyFilter,
  //   refetch,
}) => {
  const queryClient = useQueryClient();
  const t = useTranslations("DashboardHermesX.FilterAlerts");

  // Fetch saved preferences
  const {
    data: savedPreferences,
    isLoading: preferencesLoading,
    refetch,
  } = useGetQueryHermes("/api/choice", ["get-choice"], {
    enabled: false,
    staleTime: 1000 * 60 * 60,
    onError() {
      toast.error(t("toast.notLoadSavedFilters"));
    },
  });

  // API hook for submitting preferences
  const { mutateAsync: subscribe, isPending: loading } = useSubmitQueryHermes(
    "/api/choice/save-filters",
    "POST",
    {
      onSuccess(response: any) {
        refetch();
        queryClient.invalidateQueries({
          queryKey: ["get-choice"],
        });
        queryClient.invalidateQueries({
          queryKey: ["hermes-x-alerts"],
        });

        onClose();
      },
      onError(err: any) {
        toast.error(
          err?.response?.data?.error || t("toast.FailedToSaveFilters"),
          {
            position: "top-right",
            className: "p-4",
          }
        );
      },
    }
  );

  // Initialize filter state
  const [filters, setFilters] = useState<FilterOptions>({
    categories: [],
    significance: [],
    region: [],
    assets: [], // ← NEW
    rateLimit: "No",
    customizeForTelegram: false,
  });

  // Initialize loading state
  const [initializing, setInitializing] = useState<boolean>(true);

  // Track if any filter is selected (for Apply button enabling)
  const [isAnyFilterSelected, setIsAnyFilterSelected] =
    useState<boolean>(false);

  const handleDefaultFilter = () => {
    setFilters({
      categories: [...DEFAULT_OPTIONS.categories],
      significance: [...DEFAULT_OPTIONS.significance],
      region: [...DEFAULT_OPTIONS.region],
      assets: [...DEFAULT_OPTIONS.assets],
      rateLimit: "No", // Keeping default rate limit as "No"
      customizeForTelegram: false, // Reset this option too
    });
  };

  // Effect to load saved preferences when data is available
  useEffect(() => {
    if (savedPreferences?.tableCustomization && !preferencesLoading) {
      setFilters({
        categories: savedPreferences.tableCustomization.categories || [],
        significance: savedPreferences.tableCustomization.significance || [],
        region: savedPreferences.tableCustomization.region || [],
        assets: savedPreferences.tableCustomization.assets || [], // ← NEW
        rateLimit: savedPreferences.tableCustomization.rate_limit || "No",
        customizeForTelegram: savedPreferences.tableCustomization.both_filters,
      });
      setInitializing(false);
    } else if (!preferencesLoading) {
      setInitializing(false);
    }
  }, [savedPreferences, preferencesLoading]);

  // Effect to update the Apply button state
  useEffect(() => {
    if (filters.rateLimit === "Yes") {
      setIsAnyFilterSelected(true);
      return;
    }

    const hasSelection =
      filters.categories.length > 0 ||
      filters.significance.length > 0 ||
      filters.region.length > 0;
    setIsAnyFilterSelected(hasSelection);
  }, [filters]);

  // Handle rate limit change
  const handleRateLimitChange = (value: "Yes" | "No") => {
    const newFilters = { ...filters, rateLimit: value };

    // Auto-select all categories and High/Medium significance when "Yes" is selected
    if (value === "Yes") {
      newFilters.categories = [...CATEGORY_OPTIONS];
      newFilters.significance = ["High", "Medium"];
      newFilters.region = ["Global"];
    }

    setFilters(newFilters);
  };

  const toggleAsset = (asset: string) => {
    if (filters.rateLimit === "Yes") return;
    const newAssets = [...filters.assets];
    const idx = newAssets.indexOf(asset);
    if (idx > -1) newAssets.splice(idx, 1);
    else newAssets.push(asset);
    setFilters({ ...filters, assets: newAssets });
  };

  const isAssetSelected = (asset: string) => {
    return filters.assets.includes(asset);
  };

  // Toggle category selection
  const toggleCategory = (category: string) => {
    if (filters.rateLimit === "Yes") return; // Prevent toggling if Rate Limit is Yes

    const newCategories = [...filters.categories];

    if (newCategories.includes(category)) {
      // Remove if already selected
      const index = newCategories.indexOf(category);
      newCategories.splice(index, 1);
    } else {
      // Add if not selected
      newCategories.push(category);
    }

    setFilters({ ...filters, categories: newCategories });
  };

  // Toggle significance selection
  const toggleSignificance = (significance: string) => {
    if (filters.rateLimit === "Yes") return; // Prevent toggling if Rate Limit is Yes

    const newSignificance = [...filters.significance];

    if (newSignificance.includes(significance)) {
      // Remove if already selected
      const index = newSignificance.indexOf(significance);
      newSignificance.splice(index, 1);
    } else {
      // Add if not selected
      newSignificance.push(significance);
    }

    setFilters({ ...filters, significance: newSignificance });
  };

  // Toggle region selection
  const toggleRegion = (region: string) => {
    if (filters.rateLimit === "Yes") return; // Prevent toggling if Rate Limit is Yes

    const newRegions = [...filters.region];

    if (newRegions.includes(region)) {
      // Remove if already selected
      const index = newRegions.indexOf(region);
      newRegions.splice(index, 1);
    } else {
      // Add if not selected
      newRegions.push(region);
    }

    setFilters({ ...filters, region: newRegions });
  };

  // Apply filters
  const handleApplyFilter = () => {
    subscribe({
      categories: filters.categories,
      significance: filters.significance,
      region: filters.region,
      assets: filters.assets, // ← NEW
      rate_limit: filters.rateLimit,
      both_filters: filters.customizeForTelegram,
    });
  };

  // Check if a category is selected
  const isCategorySelected = (category: string) => {
    return filters.categories.includes(category);
  };

  // Check if a significance level is selected
  const isSignificanceSelected = (significance: string) => {
    return filters.significance.includes(significance);
  };

  // Check if a region is selected
  const isRegionSelected = (region: string) => {
    return filters.region.includes(region);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[440px] p-6 bg-white rounded-lg max-h-[700px] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between mt-5">
          <DialogTitle className="text-[18px] font-medium ">
            {t("FilterTable")}
          </DialogTitle>
          <Button
            onClick={handleDefaultFilter}
            className="bg-gray-900 text-xs flex items-center gap-1 text-white px-3 h-[30px] rounded-[4px] hover:bg-gray-800"
            disabled={initializing || preferencesLoading}
          >
            {t("Reset")}
            <RotateCw className="w-3 h-3" />
          </Button>
        </DialogHeader>

        <div className="space-y-6 mt-4 ">
          {initializing || preferencesLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="flex flex-col items-center gap-2">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                <p className="text-sm text-gray-500">{t("LoadingFilters")}</p>
              </div>
            </div>
          ) : (
            <>
              {/* Category Section */}
              <div>
                <h3 className="text-xs font-[400] mb-1">
                  {t("Category.text")}
                </h3>
                <p className="text-xs text-gray-500 mb-2">
                  {t("Category.description")}
                </p>
                <div className="flex flex-wrap gap-2">
                  {CATEGORY_OPTIONS.map((category) => (
                    <Button
                      key={category}
                      size="sm"
                      className={`rounded-[4px] border flex items-center py-0 h-[25px] px-2 font-[400] text-xs ${
                        isCategorySelected(category)
                          ? "bg-black text-white"
                          : "bg-white text-black hover:bg-gray-100"
                      } ${
                        filters.rateLimit === "Yes"
                          ? "opacity-80 cursor-not-allowed"
                          : ""
                      }`}
                      onClick={() => toggleCategory(category)}
                      disabled={filters.rateLimit === "Yes"}
                    >
                      {t(`Category.${category}`)}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Significance Section */}
              <div>
                <h3 className="text-xs font-medium mb-1">
                  {t("Significance.text")}
                </h3>
                <p className="text-xs text-gray-500 mb-2">
                  {t("Significance.description")}
                </p>
                <div className="flex flex-wrap gap-2">
                  {SIGNIFICANCE_OPTIONS.map((significance) => (
                    <Button
                      key={significance}
                      size="sm"
                      className={`rounded-[4px] border flex items-center py-0  h-[25px] px-2 font-[400] text-xs ${
                        isSignificanceSelected(significance)
                          ? "bg-black text-white"
                          : "bg-white text-black hover:bg-gray-100"
                      } ${
                        filters.rateLimit === "Yes"
                          ? "opacity-80 cursor-not-allowed"
                          : ""
                      }`}
                      onClick={() => toggleSignificance(significance)}
                      disabled={filters.rateLimit === "Yes"}
                    >
                      {t(`Significance.${significance}`)}
                    </Button>
                  ))}
                </div>
              </div>

              {/* ────────── Assets Section ────────── */}
              <div>
                <h3 className="text-xs font-medium mb-1">{t("Assets.text")}</h3>
                <p className="text-xs text-gray-500 mb-2">
                  {t("Assets.description")}
                </p>
                <div className="flex flex-wrap gap-2">
                  {ASSETS_OPTIONS.map((asset) => (
                    <Button
                      key={asset}
                      size="sm"
                      className={`rounded-[4px] border flex items-center py-0 h-[25px] px-2 font-[400] text-xs ${
                        isAssetSelected(asset)
                          ? "bg-black text-white"
                          : "bg-white text-black hover:bg-gray-100"
                      } ${
                        filters.rateLimit === "Yes"
                          ? "opacity-80 cursor-not-allowed"
                          : ""
                      }`}
                      onClick={() => toggleAsset(asset)}
                      disabled={filters.rateLimit === "Yes"}
                    >
                      {t(`Assets.${asset}`)}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Region Section */}
              <div>
                <h3 className="text-xs font-[400] mb-1">{t("Region.text")}</h3>
                <p className="text-xs text-gray-500 mb-2">
                  {t("Region.description")}
                </p>
                <div className="flex flex-wrap gap-2">
                  {REGION_OPTIONS.map((region) => (
                    <Button
                      key={region}
                      size="sm"
                      className={`rounded-[4px] border flex items-center py-0 h-[25px] px-2 font-[400] text-xs ${
                        isRegionSelected(region)
                          ? "bg-black text-white"
                          : "bg-white text-black hover:bg-gray-100"
                      } ${
                        filters.rateLimit === "Yes"
                          ? "opacity-80 cursor-not-allowed"
                          : ""
                      }`}
                      onClick={() => toggleRegion(region)}
                      disabled={filters.rateLimit === "Yes"}
                    >
                      {t(`Region.${region}`)}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Customize for Telegram */}
              <div className="flex items-center space-x-2 mt-3">
                <Checkbox
                  id="customizeFilter"
                  checked={filters.customizeForTelegram}
                  onCheckedChange={(checked) =>
                    setFilters({
                      ...filters,
                      customizeForTelegram: checked === true,
                    })
                  }
                />
                <label
                  htmlFor="customizeFilter"
                  className="text-xs font-normal leading-none text-[#737384]"
                >
                  {t("CheckBox")}
                </label>
              </div>
            </>
          )}
        </div>

        <DialogFooter className="mt-3">
          <div className="flex items-center justify-between w-full gap-4">
            <Button
              variant="outline"
              onClick={onClose}
              className="border border-gray-300 h-[32px] text-black"
              disabled={loading}
            >
              {t("Button.Cancel")}
            </Button>
            <Button
              className="bg-gray-900 text-white h-[32px] hover:bg-gray-800 w-full max-w-[300px]"
              onClick={handleApplyFilter}
              disabled={
                !isAnyFilterSelected ||
                loading ||
                initializing ||
                preferencesLoading
              }
            >
              {loading ? t("Button.Saving") : t("Button.Filter")}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FilterAlertsModal;
