import { Alert<PERSON>riangle } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";

export default function HermesBanner() {
  const t = useTranslations("DashboardHermesX");

  return (
    <div className="px-[5%] w-full">
      <div className="relative w-full overflow-hidden min-h-[200px] sm:min-h-[240px] md:min-h-[264px] flex flex-col md:flex-row items-start md:items-center justify-between mx-auto max-w-[1400px] bg-gradient-to-r from-[#E1EFFF] to-white">
        <div className="flex-1 py-4 sm:py-6 pl-4 sm:pl-6 relative z-10">
          <h2 className="text-[20px] sm:text-[24px] md:text-[28px] lg:text-[32px] font-[600] text-gray-800 mb-1 leading-tight">
            {t("title.line1")}
            <br className="hidden sm:block" />
            <span className="sm:hidden"> </span>
            {t("title.line2")}
          </h2>
          <p className="text-[#737384] mb-3 text-[14px] sm:text-[16px] md:text-[18px] pr-4 md:pr-0">
            {t("description")}
          </p>

          <div className="flex flex-col space-y-1 mt-4 sm:mt-7">
            <Link
              href="https://x.com/ai_wk_com"
              target="_blank"
              className="w-fit"
            >
              <div className="flex items-center hover:opacity-80 transition-opacity">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="15"
                  height="15"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                </svg>
              </div>
            </Link>
            <div className="text-xs sm:text-sm text-gray-600">
              {t("Social.Follow1")}
            </div>
            <div className="text-xs sm:text-sm text-gray-600 font-[300]">
              {t("Social.Follow2")}
            </div>
          </div>
        </div>

        <div className="absolute right-0 bottom-0 md:relative md:right-auto md:bottom-auto">
          <Image
            src="/hermesbanner_image.svg"
            alt="banner-hermes"
            width={371}
            height={260}
            className="object-cover w-[250px] h-[175px] sm:w-[300px] sm:h-[210px] md:w-[371px] md:h-[260px] opacity-5 md:opacity-100"
          />
        </div>
      </div>
    </div>
  );
}
