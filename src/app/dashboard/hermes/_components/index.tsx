"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import React, { useState } from "react";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { CircleAlert } from "lucide-react";
import SubscribeModal from "./subscribeModal";
import FilterAlertsModal from "./filterAlertsModal";
import { hermesAPI, useGetQueryHermes } from "@/services/api_hooks";
import { toast } from "sonner";
import ExpandTweetModal from "./ExpandTweetModal";
import TelegramSetupModal from "./TelegramSetupModal";
import TelegramPreferencesModal from "./telegramPreferencesModal";
import { useQueryClient } from "@tanstack/react-query";
import SubscriptionFlowModal from "./SubscriptionFlowModal";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Table from "./Table";
import { useRef, useCallback } from "react";
import { useBottomObserver } from "@/hooks/useBottomObserver";
import ExpandTruthsocialModal from "./ExpandTruthsocialModal";
import { useTranslations } from "next-intl";

// Define TypeScript interfaces
interface Alert {
  timestamp: string;
  tweet_text: string;
  event: string;
  category: string;
  region: string;
  direction: string;
  significance: string;
  affected_assets: string;
  link: string;
}

interface AlertsTableProps {
  alerts?: Alert[];
  currentPage?: number;
  totalPages?: number;
}

export default function AlertsTable({}: AlertsTableProps) {
  const t = useTranslations("DashboardHermesX");

  const [showSubscribeModal, setShowSubscribeModal] = useState(false);
  const [showTelegramModal, setShowTelegramModal] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [showTelegramFilter, setShowTelegramFilter] = useState(false);
  const [tweetUrl, setTweetUrl] = useState("");
  const [showExpandModal, setShowExpandModal] = useState<boolean>(false);
  const [showTruthSocialModal, setTruthSocialModal] = useState<boolean>(false);
  const [timezone, setTimezone] = useState("");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [showSubscriptionFlow, setShowSubscriptionFlow] = useState(false);

  const queryClient = useQueryClient();
  const bottomRef = useRef<HTMLDivElement>(null);

  const handleSubscribe = () => {
    setShowSubscriptionFlow(true);
  };

  const {
    data: choiceData,
    isLoading: choiceLoading,
    refetch: refetchhoice,
  } = useGetQueryHermes("/api/choice", ["get-choice"], {
    onError() {
      toast.error(t("AlertsTable.toast.notLoadAlerts"));
    },
  });

  const initialAlertsData =
    page === 1 && limit === 10 && timezone === ""
      ? queryClient.getQueryData(["hermes-x-alerts", 1, 10, "UTC"])
      : undefined;

  const {
    data: alerts,
    isLoading,
    isFetching,
    refetch,
  } = useGetQueryHermes(
    `/api/user/filter-alert?page=${page}&timezone=${timezone}`,
    ["hermes-x-alerts", page, limit, timezone],
    {
      initialData: initialAlertsData,
      gcTime: 30 * 1000 * 60, // 30 minutes
      onSuccess() {
        refetchhoice();
        queryClient.invalidateQueries({
          queryKey: ["get-choice"],
        });
      },
      onError() {
        toast.error(t("AlertsTable.toast.notLoadAlerts"));
      },
    }
  );

  const currentPage = alerts?.pagination?.currentPage || 1;
  const totalPages = alerts?.pagination?.totalPages || 1;
  const hasNextPage = alerts?.pagination?.hasNextPage || false;
  const hasPrevPage = alerts?.pagination?.hasPrevPage || false;

  const { data: subscriptionStatus, isLoading: subscriptionStatusLoading } =
    useGetQueryHermes("/api/user/subscribe", ["get-subscription-status"], {
      onError() {
        toast.error(t("AlertsTable.toast.notLoadAlerts"));
      },
    });

  // Prefetch next page when bottom is in view and hasNextPage
  const prefetchNextPage = useCallback(() => {
    if (!alerts?.pagination?.hasNextPage) return;
    // Prefetch next page
    queryClient.prefetchQuery({
      queryKey: ["hermes-x-alerts", page + 1, limit, timezone],
      queryFn: async () => {
        const res = await hermesAPI.get(`/api/user/filter-alert?page=${page + 1}&timezone=${timezone}`);
        return res.data;
      },
      gcTime: 30 * 1000 * 60, // 30 minutes
    });
    // Prefetch the page after next if it exists
    if (alerts?.pagination?.totalPages && page + 1 < alerts.pagination.totalPages) {
      queryClient.prefetchQuery({
        queryKey: ["hermes-x-alerts", page + 2, limit, timezone],
        queryFn: async () => {
          const res = await hermesAPI.get(`/api/user/filter-alert?page=${page + 2}&timezone=${timezone}`);
          return res.data;
        },
        gcTime: 30 * 1000 * 60, // 30 minutes
      });
    }
  }, [alerts, page, limit, timezone, queryClient]);

  useBottomObserver(bottomRef as React.RefObject<Element>, prefetchNextPage, Boolean(alerts?.pagination?.hasNextPage));

  const handleNextPage = () => {
    if (alerts?.pagination?.hasNextPage) {
      setPage((prev) => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (alerts?.pagination?.hasPrevPage) {
      setPage((prev) => prev - 1);
    }
  };

  const handleExpand = (link: string) => {
    setTweetUrl(link);

    if (link?.includes("truthsocial.com") || link?.includes("truth.social")) {
      setTruthSocialModal(true);
    } else if (link?.includes("twitter.com") || link?.includes("x.com")) {
      setShowExpandModal(true);
    }
  };

  const closeTelegramPreferences = () => {
    queryClient.invalidateQueries({
      queryKey: ["get-telegram-filters"],
    });
    setShowTelegramFilter(false);
  };

  return (
    <div className="px-[5%] w-full">
      <div className="flex flex-col mx-auto max-w-[1400px]">
        <div className="flex flex-col space-y-4 md:flex-row md:justify-between md:items-center md:space-y-0 mb-4">
          {/* Left Side - Title and Timezone */}
          <div className="space-y-3 md:space-y-1.5">
            <div>
              <div className="text-xl font-medium">
                {t("AlertsTable.title")}
              </div>
              {subscriptionStatus != null && alerts?.data?.[0]?.timestamp ? (
                subscriptionStatus?.active ? (
                  <div className="text-sm text-[#6C7788]">
                    {t("AlertsTable.Latest")}
                  </div>
                ) : (
                  <div className="text-sm text-[#6C7788]">
                    {t("AlertsTable.delayed")}
                  </div>
                )
              ) : null}
            </div>
            <Select
              value={timezone || choiceData?.tableCustomization?.timezone}
              onValueChange={(value) => setTimezone(value)}
            >
              <SelectTrigger className="w-full md:w-40 bg-white h-[30px] hidden md:flex rounded-[4px]">
                <SelectValue placeholder={t("AlertsTable.UTC")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="UTC">{t("AlertsTable.UTC")}</SelectItem>
                <SelectItem value="NYT">{t("AlertsTable.NYT")}</SelectItem>
                <SelectItem value="UserTime">
                  {t("AlertsTable.UserTime")}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Right Side - Actions */}
          <div className="flex gap-5 flex-row items-center md:space-y-0 space-x-4">
            {/* Info Tooltip */}
            <div className="flex justify-center md:justify-start">
              <TooltipProvider delayDuration={100}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <CircleAlert color="#CB680C" size={22} />
                  </TooltipTrigger>
                  <TooltipContent
                    className="max-w-[225px] space-y-3 pt-3 text-[#5B616A]"
                    side="bottom"
                    align="end"
                  >
                    <CircleAlert color="#CB680C" size={22} />
                    <p>{t("AlertsTable.Table.PreviewWarning.line2")}</p>
                    <p>{t("AlertsTable.Table.PreviewWarning.line1")}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-3 w-full">
              {subscriptionStatus?.active !== true && (
                <Button
                  className="bg-gray-900 text-white px-4 py-2 rounded text-sm  md:w-auto"
                  onClick={() => handleSubscribe()}
                  disabled={choiceLoading || subscriptionStatusLoading}
                >
                  {t("AlertsTable.Button.SubscribeNow")}
                </Button>
              )}
              {subscriptionStatus?.active === true && (
                <Button
                  className="bg-gray-900 text-white px-4 py-2 rounded text-sm w-full md:w-auto"
                  onClick={() => setShowTelegramFilter(true)}
                  disabled={choiceLoading}
                >
                  {t("AlertsTable.TelegramPreferences")}
                </Button>
              )}
              <Button
                className="bg-gray-900 text-white px-4 py-2 rounded text-sm w-full md:w-auto"
                onClick={() => setShowFilter(true)}
              >
                {t("AlertsTable.Button.FilterTable")}
              </Button>
            </div>
          </div>
          <Select
            value={timezone}
            onValueChange={(value) => setTimezone(value)}
          >
            <SelectTrigger className="w-full md:w-40 bg-white h-[30px]  md:hidden rounded-[4px]">
              <SelectValue placeholder={t("AlertsTable.UTC")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="UTC">{t("AlertsTable.UTC")}</SelectItem>
              <SelectItem value="NYT">{t("AlertsTable.NYT")}</SelectItem>
              <SelectItem value="UserTime">
                {t("AlertsTable.UserTime")}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Table
          alerts={alerts?.data || []}
          isLoading={isLoading}
          // isFetching={isFetching}
          timezone={timezone}
          onExpandTweet={handleExpand}
        />
        {/* Invisible div at the bottom for observer */}
        <div ref={bottomRef} style={{ height: 1 }} />

        <div className="flex items-center justify-between px-6 border-t py-4">
          <div className="text-sm text-gray-700 pointer">
            {t("AlertsTable.Table.Page")} {currentPage}{" "}
            {t("AlertsTable.Table.of")} {totalPages}
          </div>
          <div className="flex space-x-2">
            <button
              className={`px-4 py-2 border border-gray-300 rounded text-sm font-medium ${
                hasPrevPage
                  ? "text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
                  : "text-gray-400 bg-gray-100 cursor-not-allowed"
              }`}
              onClick={handlePrevPage}
              disabled={!hasPrevPage}
            >
              {t("AlertsTable.Table.Previous")}
            </button>
            <button
              className={`px-4 py-2 border border-gray-300 rounded text-sm font-medium ${
                hasNextPage
                  ? "text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
                  : "text-gray-400 bg-gray-100 cursor-not-allowed"
              }`}
              onClick={handleNextPage}
              disabled={!hasNextPage}
            >
              {t("AlertsTable.Table.Next")}
            </button>
          </div>
        </div>
      </div>
      <ExpandTruthsocialModal
        tweetUrl={tweetUrl}
        showExpandModal={showTruthSocialModal}
        setShowExpandModal={setTruthSocialModal}
      />

      <SubscribeModal
        isOpen={showSubscribeModal}
        onClose={() => setShowSubscribeModal(false)}
      />
      <FilterAlertsModal
        isOpen={showFilter}
        onClose={() => setShowFilter(false)}
        refetch={refetch}
      />
      <TelegramPreferencesModal
        isOpen={showTelegramFilter}
        onClose={closeTelegramPreferences}
      />
      <ExpandTweetModal
        tweetUrl={tweetUrl}
        showExpandModal={showExpandModal}
        setShowExpandModal={setShowExpandModal}
      />
      <TelegramSetupModal
        isOpen={showTelegramModal}
        onClose={() => setShowTelegramModal(false)}
      />
      <SubscriptionFlowModal
        isOpen={showSubscriptionFlow}
        onClose={() => setShowSubscriptionFlow(false)}
      />
    </div>
  );
}
