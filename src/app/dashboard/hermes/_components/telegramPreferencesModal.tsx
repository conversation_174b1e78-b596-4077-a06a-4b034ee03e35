"use client";
import { FC, useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertCircle, RotateCw } from "lucide-react";

import { useQueryClient } from "@tanstack/react-query";
import { useGetQueryHermes, useSubmitQueryHermes } from "@/services/api_hooks";
import { toast } from "sonner";
import { useTranslations } from "next-intl";

interface FilterAlertsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyFilter?: (filters: FilterOptions) => void;
}

interface FilterOptions {
  categories: string[];
  significance: string[];
  region: string[];
  assets: string[]; // ← NEW
  rateLimit: "Yes" | "No";
  customizeForTelegram: boolean;
}


const CATEGORY_OPTIONS = [
  { label: "FilterAlerts.Category.Market", value: "Market" },
  { label: "FilterAlerts.Category.Sector", value: "Sector" },
  { label: "FilterAlerts.Category.Company", value: "Company" },
];
const SIGNIFICANCE_OPTIONS = [
  { label: "FilterAlerts.Significance.High", value: "High" },
  { label: "FilterAlerts.Significance.Medium", value: "Medium" },
  { label: "FilterAlerts.Significance.Low", value: "Low" },
];
const REGION_OPTIONS = [
  { label: "FilterAlerts.Region.US", value: "US" },
  { label: "FilterAlerts.Region.China", value: "China" },
  { label: "FilterAlerts.Region.Japan", value: "Japan" },
  { label: "FilterAlerts.Region.India", value: "India" },
  { label: "FilterAlerts.Region.UK", value: "UK" },
  { label: "FilterAlerts.Region.Germany", value: "Germany" },
  { label: "FilterAlerts.Region.Saudi Arabia", value: "Saudi Arabia" },
  { label: "FilterAlerts.Region.Canada", value: "Canada" },
  { label: "FilterAlerts.Region.Australia", value: "Australia" },
  { label: "FilterAlerts.Region.Southeast Asia", value: "Southeast Asia" },
  { label: "FilterAlerts.Region.Latin America", value: "Latin America" },
  { label: "FilterAlerts.Region.EU", value: "EU" },
  { label: "FilterAlerts.Region.Global", value: "Global" },
];
const ASSETS_OPTIONS = [
  { label: "FilterAlerts.Assets.Equities", value: "Equities" },
  { label: "FilterAlerts.Assets.Bonds", value: "Bonds" },
  { label: "FilterAlerts.Assets.FX", value: "FX" },
  { label: "FilterAlerts.Assets.Commodities", value: "Commodities" },
  { label: "FilterAlerts.Assets.Crypto", value: "Crypto" },
  { label: "FilterAlerts.Assets.Private", value: "Private" },
];

const DEFAULT_OPTIONS = {
  categories: CATEGORY_OPTIONS.map(opt => opt.value),
  significance: [SIGNIFICANCE_OPTIONS[0].value],
  assets: ASSETS_OPTIONS.map(opt => opt.value),
  region: REGION_OPTIONS.map(opt => opt.value),
};

const TelegramPreferencesModal: FC<FilterAlertsModalProps> = ({
  isOpen,
  onClose,
  onApplyFilter,
}) => {
  const queryClient = useQueryClient();
  const t = useTranslations("DashboardHermesX.Modals.TelegramPreferencesModal");
  const tFilter = useTranslations("DashboardHermesX");

  // Fetch saved preferences
  const {
    data: savedPreferences,
    isLoading: preferencesLoading,
    refetch,
  } = useGetQueryHermes("/api/choice", ["get-choice"], {
    enabled: false,
    staleTime: 1000 * 60 * 60,
    onError() {
      toast.error("Could not load saved preferences");
    },
  });

  // API hook for submitting preferences
  const { mutateAsync: subscribe, isPending: loading } = useSubmitQueryHermes(
    "/api/choice/telegram-filters",
    "POST",
    {
      onSuccess(response: any) {
        // toast.success("Telegram preferences saved successfully!", {
        //   position: "top-right",
        //   className: "p-4",
        // });
        refetch();
        queryClient.invalidateQueries({
          queryKey: ["get-choice"],
        });
        onClose();
      },
      onError(err: any) {
        toast.error(
          err?.response?.data?.error || "Failed to save preferences",
          {
            position: "top-right",
            className: "p-4",
          }
        );
      },
    }
  );

  // Initialize filter state
  const [filters, setFilters] = useState<FilterOptions>({
    categories: [],
    significance: [],
    region: [],
    assets: [],
    rateLimit: "No",
    customizeForTelegram: false,
  });
  // Initialize loading state
  const [initializing, setInitializing] = useState<boolean>(true);

  // Track if any filter is selected (for Apply button enabling)
  const [isAnyFilterSelected, setIsAnyFilterSelected] =
    useState<boolean>(false);

  const handleDefaultFilter = () => {
    setFilters({
      categories: [...DEFAULT_OPTIONS.categories],
      significance: [...DEFAULT_OPTIONS.significance],
      region: [...DEFAULT_OPTIONS.region],
      assets: [...DEFAULT_OPTIONS.assets],
      rateLimit: "No", // Keeping default rate limit as "No"
      customizeForTelegram: false, // Reset this option too
    });
  };

  // Effect to load saved preferences when data is available
  useEffect(() => {
    if (savedPreferences?.telegramCustomization && !preferencesLoading) {
      setFilters({
        categories: savedPreferences.telegramCustomization.categories || [],
        significance: savedPreferences.telegramCustomization.significance || [],
        region: savedPreferences.telegramCustomization.region || [],
        assets: savedPreferences.telegramCustomization.assets || [],
        rateLimit: savedPreferences.telegramCustomization.rate_limit || "No",
        customizeForTelegram:
          savedPreferences.telegramCustomization.both_filters,
      });
      setInitializing(false);
    } else if (!preferencesLoading) {
      setInitializing(false);
    }
  }, [savedPreferences, preferencesLoading]);

  // Effect to update the Apply button state
  useEffect(() => {
    if (filters.rateLimit === "Yes") {
      setIsAnyFilterSelected(true);
      return;
    }

    const hasSelection =
      filters.categories.length > 0 ||
      filters.significance.length > 0 ||
      filters.region.length > 0;
    setIsAnyFilterSelected(hasSelection);
  }, [filters]);

  // Handle rate limit change
  // const handleRateLimitChange = (value: "Yes" | "No") => {
  //   const newFilters = { ...filters, rateLimit: value };

  //   // Auto-select all categories and High/Medium significance when "Yes" is selected
  //   if (value === "Yes") {
  //     newFilters.categories = CATEGORY_OPTIONS.map(opt => opt.value);
  //     newFilters.significance = [
  //       "FilterAlerts.Significance.High",
  //       "FilterAlerts.Significance.Medium",
  //     ];
  //     newFilters.region = ["FilterAlerts.Region.Global"];
  //   }

  //   setFilters(newFilters);
  // };

  const toggleAsset = (assetValue: string) => {
    if (filters.rateLimit === "Yes") return;
    const newAssets = [...filters.assets];
    const idx = newAssets.indexOf(assetValue);
    if (idx > -1) newAssets.splice(idx, 1);
    else newAssets.push(assetValue);
    setFilters({ ...filters, assets: newAssets });
  };

  const isAssetSelected = (assetValue: string) => {
    return filters.assets.includes(assetValue);
  };

  // Toggle category selection
  const toggleCategory = (categoryValue: string) => {
    if (filters.rateLimit === "Yes") return; // Prevent toggling if Rate Limit is Yes

    const newCategories = [...filters.categories];

    if (newCategories.includes(categoryValue)) {
      // Remove if already selected
      const index = newCategories.indexOf(categoryValue);
      newCategories.splice(index, 1);
    } else {
      // Add if not selected
      newCategories.push(categoryValue);
    }

    setFilters({ ...filters, categories: newCategories });
  };

  // Toggle significance selection
  const toggleSignificance = (significanceValue: string) => {
    if (filters.rateLimit === "Yes") return; // Prevent toggling if Rate Limit is Yes

    const newSignificance = [...filters.significance];

    if (newSignificance.includes(significanceValue)) {
      // Remove if already selected
      const index = newSignificance.indexOf(significanceValue);
      newSignificance.splice(index, 1);
    } else {
      // Add if not selected
      newSignificance.push(significanceValue);
    }

    setFilters({ ...filters, significance: newSignificance });
  };

  // Toggle region selection
  const toggleRegion = (regionValue: string) => {
    if (filters.rateLimit === "Yes") return; // Prevent toggling if Rate Limit is Yes

    const newRegions = [...filters.region];

    if (newRegions.includes(regionValue)) {
      // Remove if already selected
      const index = newRegions.indexOf(regionValue);
      newRegions.splice(index, 1);
    } else {
      // Add if not selected
      newRegions.push(regionValue);
    }

    setFilters({ ...filters, region: newRegions });
  };

  // Apply filters
  const handleApplyFilter = () => {
    subscribe({
      categories: filters.categories,
      significance: filters.significance,
      region: filters.region,
      assets: filters.assets,
      rate_limit: filters.rateLimit,
      both_filters: filters.customizeForTelegram,
    });
  };

  // Check if a category is selected
  const isCategorySelected = (categoryValue: string) => {
    return filters.categories.includes(categoryValue);
  };

  // Check if a significance level is selected
  const isSignificanceSelected = (significanceValue: string) => {
    return filters.significance.includes(significanceValue);
  };

  // Check if a region is selected
  const isRegionSelected = (regionValue: string) => {
    return filters.region.includes(regionValue);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[440px] p-6 bg-white rounded-lg max-h-[700px] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between mt-5">
          <DialogTitle className="text-[18px] font-medium ">
            {t("title")}
          </DialogTitle>
          <Button
            onClick={handleDefaultFilter}
            className="bg-gray-900 text-xs flex items-center gap-1 text-white px-3 h-[30px] rounded-[4px] hover:bg-gray-800"
            disabled={initializing || preferencesLoading}
          >
            {t("reset")}
            <RotateCw className="w-3 h-3" />
          </Button>
        </DialogHeader>

        <div className="space-y-6 mt-4">
          {initializing || preferencesLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="flex flex-col items-center gap-2">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                <p className="text-sm text-gray-500">{t("loading")}</p>
              </div>
            </div>
          ) : (
            <>
              {/* Category Section */}
              <div>
                <h3 className="text-xs font-[400] mb-1">
                  {t("category.text")}
                </h3>
                <p className="text-xs text-gray-500 mb-2">
                  {t("category.description")}
                </p>
                <div className="flex flex-wrap gap-2">
                  {CATEGORY_OPTIONS.map((category) => (
                    <Button
                      key={category.value}
                      size="sm"
                      className={`rounded-[4px] border flex items-center py-0 h-[25px] px-2 font-[400] text-xs ${
                        isCategorySelected(category.value)
                          ? "bg-black text-white"
                          : "bg-white text-black hover:bg-gray-100"
                      } ${
                        filters.rateLimit === "Yes"
                          ? "opacity-80 cursor-not-allowed"
                          : ""
                      }`}
                      onClick={() => toggleCategory(category.value)}
                      disabled={filters.rateLimit === "Yes"}
                    >
                      {tFilter(category.label)}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Significance Section */}
              <div>
                <h3 className="text-xs font-medium mb-1">
                  {t("significance.text")}
                </h3>
                <p className="text-xs text-gray-500 mb-2">
                  {t("significance.description")}
                </p>
                <div className="flex flex-wrap gap-2">
                  {SIGNIFICANCE_OPTIONS.map((significance) => (
                    <Button
                      key={significance.value}
                      size="sm"
                      className={`rounded-[4px] border flex items-center py-0  h-[25px] px-2 font-[400] text-xs ${
                        isSignificanceSelected(significance.value)
                          ? "bg-black text-white"
                          : "bg-white text-black hover:bg-gray-100"
                      } ${
                        filters.rateLimit === "Yes"
                          ? "opacity-80 cursor-not-allowed"
                          : ""
                      }`}
                      onClick={() => toggleSignificance(significance.value)}
                      disabled={filters.rateLimit === "Yes"}
                    >
                      {tFilter(significance.label)}
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-xs font-medium mb-1">{t("assets.text")}</h3>
                <p className="text-xs text-gray-500 mb-2">
                  {t("assets.description")}
                </p>
                <div className="flex flex-wrap gap-2">
                  {ASSETS_OPTIONS.map((asset) => (
                    <Button
                      key={asset.value}
                      size="sm"
                      className={`rounded-[4px] border flex items-center py-0 h-[25px] px-2 font-[400] text-xs ${
                        isAssetSelected(asset.value)
                          ? "bg-black text-white"
                          : "bg-white text-black hover:bg-gray-100"
                      } ${
                        filters.rateLimit === "Yes"
                          ? "opacity-80 cursor-not-allowed"
                          : ""
                      }`}
                      onClick={() => toggleAsset(asset.value)}
                      disabled={filters.rateLimit === "Yes"}
                    >
                      {tFilter(asset.label)}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Region Section */}
              <div>
                <h3 className="text-xs font-[400] mb-1">{t("region.text")}</h3>
                <p className="text-xs text-gray-500 mb-2">
                  {t("region.description")}
                </p>
                <div className="flex flex-wrap gap-2">
                  {REGION_OPTIONS.map((region) => (
                    <Button
                      key={region.value}
                      size="sm"
                      className={`rounded-[4px] border flex items-center py-0 h-[25px] px-2 font-[400] text-xs ${
                        isRegionSelected(region.value)
                          ? "bg-black text-white"
                          : "bg-white text-black hover:bg-gray-100"
                      } ${
                        filters.rateLimit === "Yes"
                          ? "opacity-80 cursor-not-allowed"
                          : ""
                      }`}
                      onClick={() => toggleRegion(region.value)}
                      disabled={filters.rateLimit === "Yes"}
                    >
                      {tFilter(region.label)}
                    </Button>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>

        <DialogFooter className="mt-3">
          <div className="flex items-center justify-between w-full gap-4">
            <Button
              variant="outline"
              onClick={onClose}
              className="border border-gray-300 h-[32px] text-black"
              disabled={loading}
            >
              {t("cancel")}
            </Button>
            <Button
              className="bg-gray-900 text-white h-[32px] hover:bg-gray-800 w-full max-w-[300px]"
              onClick={handleApplyFilter}
              disabled={
                !isAnyFilterSelected ||
                loading ||
                initializing ||
                preferencesLoading
              }
            >
              {loading
                ? tFilter("Modals.SubscriptionFlowModal.preferences.saving")
                : tFilter("Modals.SubscriptionFlowModal.preferences.apply")}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TelegramPreferencesModal;
