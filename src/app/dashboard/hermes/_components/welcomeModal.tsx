// components/WelcomeModal.tsx
"use client";
import { FC, useState, useEffect } from "react";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { hermesAPI, useGetQueryHermes } from "@/services/api_hooks";
import { Loader } from "lucide-react";
import { useTranslations } from "next-intl";

const WelcomeModalHermesX: FC = () => {
  const [showModal, setShowModal] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const t = useTranslations("DashboardHermesX.Modals.WelcomeModal");

  // Fetch choice data from API
  const {
    data: choiceData,
    isLoading: choiceLoading,
    refetch,
  } = useGetQueryHermes("/api/choice", ["get-choice"], {
    onError() {
      // Error handling if needed
    },
  });

  useEffect(() => {
    // Check localStorage first
    const hasSeenWelcomeModal = localStorage.getItem("hasSeenWelcomeModal");

    // If not in localStorage, check API response
    if (!hasSeenWelcomeModal && !choiceLoading && choiceData?.data) {
      setShowModal(choiceData.data.welcome_dialog);
    }
  }, [choiceData, choiceLoading]);

  const handleCloseModal = () => {
    // Immediately close the modal
    setShowModal(false);

    // Set localStorage to remember this decision
    localStorage.setItem("hasSeenWelcomeModal", "true");

    // Start API call in the background
    setLoading(true);

    // Fire and forget - don't wait for the response
    hermesAPI
      .post("/api/choice/show-welcome-message")
      .then(() => {
        // Optionally refetch on success
        refetch();
      })
      .catch((error) => {
        console.error("Failed to update welcome message status:", error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // Don't render anything while loading initial data
  if (choiceLoading) {
    return null;
  }

  // Don't show if we've seen it before according to localStorage
  if (localStorage.getItem("hasSeenWelcomeModal") === "true") {
    return null;
  }

  return (
    <Dialog open={showModal} onOpenChange={setShowModal}>
      <DialogContent className="sm:max-w-[447px] p-0 overflow-hidden bg-white rounded-lg [&>button]:hidden">
        <div className="w-full h-[267px] bg-blue-50 flex items-end justify-center">
          <Image
            src="/hermes_alert.svg"
            alt="HermesX Logo"
            width={301}
            height={228}
            priority
            className="object-contain"
          />
        </div>

        <div className="px-6 py-4">
          <DialogHeader>
            <DialogTitle className="text-[16px] font-[500] text-">
              {t("title")}
            </DialogTitle>
            <DialogDescription className="text-start text-[#737384] text-xs leading-[20px] mt-2">
              {t("description")}
            </DialogDescription>
          </DialogHeader>
        </div>

        <DialogFooter className="px-6 pb-6 sm:pb-6">
          <Button
            className="w-full bg-gray-900 hover:bg-gray-800 text-white rounded-md"
            onClick={handleCloseModal}
            disabled={loading}
          >
            {t("continue")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default WelcomeModalHermesX;
