import React from "react";
import HeaderModels from "../_components/HeaderModels";
import WelcomeModalHermesX from "./_components/welcomeModal";

interface OrionDashboardLayoutProps {
  children: React.ReactNode;
}

const HermesDashboardLayout = ({ children }: OrionDashboardLayoutProps) => {
  return (
    <div className=" ">
      <main className="min-h-screen">
        <HeaderModels
          avatarUrl="/hermes_thumbnail.svg"
          userName="Hermes X"
        />
        <div className="h-full">{children}</div>
      </main>
      {/* <WelcomeModalHermesX/> */}
    </div>
  );
};

export default HermesDashboardLayout;
