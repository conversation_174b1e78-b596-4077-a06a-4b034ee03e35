"use client";

import { ModelCard } from "@/components/forms/dashboard-home-model-card";
import ProfileDialog from "@/components/modals/profileModal";
import { useGetQuery} from "@/services/api_hooks";
import { useEffect, useState, useMemo } from "react";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";
import { ModelCardSkeleton } from "./_components/modelcardskeleton";

import { Card, CardContent } from "@/components/ui/card";
import { Gift, Loader } from "lucide-react";
import TopUp from "./credits/_components/Topup/topup";
import { dollarCurrencyFormat } from "@/lib/utils";
import RedeemParkModal from "./credits/_components/RedeemParkModal";
import { useTranslations } from "next-intl";
import { freddieFrontendUrl } from "@/config/baseUrl";
import Cookies from "js-cookie";

export default function HomePage() {
  const t = useTranslations("DashboardHome.DashboardHeader");
  const tHome = useTranslations("DashboardHome.DashboardHeader");
  const tMember = useTranslations("DashboardHome");
  const [modelsData, setModelData] = useState<any>(null);
  const [recentlyUsedData, setRecentlyUsedData] = useState<any>(null);
  const [modelsCacheLoaded, setModelsCacheLoaded] = useState(false);
  const [recentCacheLoaded, setRecentCacheLoaded] = useState(false);

  // Load cached modelsData and recentlyUsedData on client mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.removeItem("authCred");

      const cachedModels = localStorage.getItem("modelsData");
      if (cachedModels) setModelData(JSON.parse(cachedModels));
      setModelsCacheLoaded(true);
      const cachedRecent = localStorage.getItem("recentlyUsedData");
      if (cachedRecent) setRecentlyUsedData(JSON.parse(cachedRecent));
      setRecentCacheLoaded(true);
    }
  }, []);

  const accessToken = Cookies.get("accessToken")
  const refreshToken = Cookies.get("refreshToken")

  // Define modelCards using useMemo to recreate it when userCred changes
  const modelCards = useMemo(
    () => [
      {
        id: "market-monitor",
        tab_label: tHome("cards.HermesX.tabLabel"),
        label: tHome("cards.HermesX.label"),
        title: tHome("cards.HermesX.title"),
        img: "/hermes_rec.svg",
        link: "/dashboard/hermes",
      },
      {
        id: "trader",
        tab_label: tHome("cards.HermesC.tabLabel"),
        label: tHome("cards.HermesC.label"),
        title: tHome("cards.HermesC.title"),
        img: "/hermes_rec.svg",
        link: "/dashboard/hermes-c",
      },
      {
        id: "accountant-ai",
        tab_label: tHome("cards.Luca.tabLabel"),
        label: tHome("cards.Luca.label"),
        title: tHome("cards.Luca.title"),
        img: "/luca_rec.svg",
        link: accessToken
          ? `https://luca.ai-wk.com/?token=${accessToken}&refresh_token=${refreshToken}`
          : "/dashboard/luca",
      },
      {
        id: "hr",
        tab_label: tHome("cards.Freddie.tabLabel"),
        label: tHome("cards.Freddie.label"),
        title: tHome("cards.Freddie.title"),
        img: "/freddie_rec.svg",
        link: accessToken
          ? `${freddieFrontendUrl}/?token=${accessToken}&refresh_token=${refreshToken}`
          : "/dashboard/home",
      },
      {
        id: "equity-analyst",
        tab_label: tHome("cards.Orion.tabLabel"),
        label: tHome("cards.Orion.label"),
        title: tHome("cards.Orion.title"),
        img: "/orion_rec.svg",
        link: "/dashboard/orion",
        isLocked: false,
      },
      {
        id: "olympus",
        tab_label: tHome("cards.Olympus.tabLabel"),
        label: tHome("cards.Olympus.label"),
        title: tHome("cards.Olympus.title"),
        img: "/olympus_rec.svg",
        link: "/dashboard/olympus",
        isLocked: true,
      },
      {
        id: "yumi-sandbox",
        tab_label: tHome("cards.Yumi.tabLabel"),
        label: tHome("cards.Yumi.label"),
        title: tHome("cards.Yumi.title"),
        img: "/yumi_rec.svg",
        link: "/dashboard/yumi/setup",
        isLocked: true,
      },
    ],
    [accessToken, refreshToken, tHome]
  );

  // Fetch all models
  const { data: models, isLoading: isModelsLoading } = useGetQuery(
    "/models",
    ["get-models"],
    {
      onError() {
        toast.error(t("toast.notLoadModels"));
      },
    }
  );

  // Fetch recently used models
  const { data: recentModels, isLoading: isRecentModelsLoading } = useGetQuery(
    "/models/recent",
    ["get-recently-used"],
    {
      onError() {
        toast.error(t("toast.notLoadRecently"));
      },
    }
  );

  // Process all models data
  useEffect(() => {
    if (models && models?.length > 0) {
      // Create the enhanced array by mapping through API models
      const newEnhancedModels = models.map((apiModel: any) => {
        // Find the matching static model data by slug/id
        const staticModel = modelCards.find(
          (staticModel) => staticModel.id === apiModel.slug
        );

        // Return merged object with API data + image and link from static data
        return {
          ...apiModel,
          // Add properties from static model if available, otherwise use defaults
          img: staticModel?.img || "/default.svg",
          link: staticModel?.link || `/dashboard/home`,
          tab_label: staticModel?.tab_label || apiModel.name,
          label: staticModel?.label || apiModel.description,
          isLocked: apiModel?.is_unlocked === false, // Convert is_unlocked to isLocked
        };
      });

      setModelData(newEnhancedModels);
      if (typeof window !== "undefined") {
        localStorage.setItem("modelsData", JSON.stringify(newEnhancedModels));
      }
    }
  }, [models, modelCards]);

  // Process recently used models data
  useEffect(() => {
    if (recentModels && recentModels.length > 0) {
      const enhancedRecentModels = recentModels?.map((apiModel: any) => {
        // Find the matching static model data by slug/id
        const staticModel = modelCards?.find(
          (staticModel) => staticModel?.id === apiModel?.slug
        );

        // Return merged object with API data + image and link from static data
        return {
          ...apiModel,
          // Add properties from static model if available, otherwise use defaults
          img: staticModel?.img || "/default.svg",
          link: staticModel?.link || `/dashboard/home`,
          tab_label: staticModel?.tab_label || apiModel.name,
          label: staticModel?.label,
          title: staticModel?.title,
          is_unlocked: apiModel?.is_unlocked === false, // Convert is_unlocked to isLocked
        };
      });

      setRecentlyUsedData(enhancedRecentModels);
      if (typeof window !== "undefined") {
        localStorage.setItem("recentlyUsedData", JSON.stringify(enhancedRecentModels));
      }
    }
  }, [recentModels, modelCards]);

  const filteredRecentFromAllModels =
    modelsData?.filter(
      (model: any) =>
        !recentlyUsedData?.some(
          (recentModel: any) => recentModel?.id === model?.id
        )
    ) || [];

  const newModelsData = [
    ...(recentlyUsedData?.map((model: any) => {
      return {
        isRecent: true,
        ...model,
      };
    }) || []),
    ...filteredRecentFromAllModels,
  ];

  const [profile, setProfile] = useState<any>(null);
  const [profileCacheLoaded, setProfileCacheLoaded] = useState(false);
  useEffect(() => {
    if (typeof window !== "undefined") {
      const cached = localStorage.getItem("profileData");
      if (cached) setProfile(JSON.parse(cached));
      setProfileCacheLoaded(true);
    }
  }, []);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);
  const { data: profileData, isLoading: isProfileLoading } = useGetQuery(
    "/auth/profile",
    ["profile"],
    {
      onError() {
        // toast.error("Could not load your profile");
      },
    }
  );
  useEffect(() => {
    if (profileData) {
      setProfile(profileData);
      if (typeof window !== "undefined") {
        localStorage.setItem("profileData", JSON.stringify(profileData));
      }
    }
    setIsLoadingProfile(isProfileLoading);
  }, [profileData, isProfileLoading]);

  const { isLoading: loadingPaymentData } = useGetQuery(
    "/api/payment/default",
    ["payment-default-methods"],
    {
      onError() {
        toast.error("Could not load exchange rates");
      },
    }
  );

  const { data: subscriptionsData, isLoading: isLoadingSubscription } =
    useGetQuery("/api/subscribe/subscription", ["get-subscriptions"], {
      
      onError() {
        toast.error("Could not load subscriptions");
      },
    });

  const subscriptions = (subscriptionsData?.subscriptions || []).filter(
    (sub: any) => sub.active === true
  );

  const [isModalRedeemModalOpen, setRedeemModalOpen] = useState(false);

  return (
    <>
      <div className="border rounded-[8px] bg-white p-4">
        <div className="mb-4">
          {(!profileCacheLoaded || (!profile && isLoadingProfile)) ? (
            <Skeleton className="h-9 w-32 mb-2" />
          ) : (
            <h1 className="text-[25px] font-[500]">
              {tMember("Hi")} {profile?.profile?.firstname},
            </h1>
          )}
          <p className="text-sm text-[#a7a7a7]">{tMember("Description")}</p>
        </div>

        <hr />
        <div className="grid grid-cols-1 lg:grid-cols-9 gap-6 my-8">
          <div className="lg:col-span-4 grid grid-cols-2 gap-6">
            <Card className="bg-[#ffffff] border-[#e2e8f0] shadow-none">
              <CardContent className="p-6 flex flex-col justify-between h-full">
                <div className="flex items-center justify-between mb-10">
                  <h3 className="text-sm font-medium text-[#525252] truncate">
                    {tMember("Cards.ModelManagement.Heading")}
                  </h3>
                </div>
                <div>
                  <div className="text-3xl text-[#1e1e1e] mb-3">
                    {(recentCacheLoaded && typeof recentlyUsedData?.length === 'number')
                      ? recentlyUsedData.length
                      : <Loader size={15} className="animate-spin" />}
                  </div>
                  <p className="text-sm text-[#a7a7a7]">
                    {" "}
                    {tMember("Cards.ModelManagement.Sub")}
                  </p>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-[#ffffff] border-[#e2e8f0] shadow-none">
              <CardContent className="p-6 flex flex-col justify-between h-full">
                <div className="flex items-center justify-between mb-10">
                  <h3 className="text-sm font-medium text-[#525252] truncate">
                    {tMember("Cards.Subscriptions.Heading")}
                  </h3>
                </div>
                <div>
                  <div className="text-3xl text-[#1e1e1e] mb-3">
                    {typeof subscriptions?.length === 'number' ? subscriptions.length : <Loader size={15} className="animate-spin" />}
                  </div>
                  <p className="text-sm text-[#a7a7a7] truncate">
                    {tMember("Cards.Subscriptions.Sub")}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
          <Card className="bg-[#ffffff] border-[#e2e8f0] shadow-none lg:col-span-3">
            <CardContent className="p-6 flex flex-col justify-between h-full">
              <div className="flex items-center justify-between mb-10">
                <h3 className="text-sm font-medium text-[#525252]">
                  {" "}
                  {tMember("Cards.Credits.Heading")}
                </h3>
                <TopUp />
              </div>
              <div>
                <div className="text-3xl text-[#1e1e1e] mb-3">
                  {" "}
                  {profile?.profile?.balance !== undefined
                    ? dollarCurrencyFormat(profile?.profile?.balance)
                    : <Loader size={15} className="animate-spin" />
                  }
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-sm text-[#a7a7a7]">
                    {" "}
                    {tMember("Cards.Credits.Sub")}
                  </p>
                  <div
                    onClick={() => setRedeemModalOpen(true)}
                    className="text-[#a7a7a7] text-xs gap-1 flex items-center cursor-pointer"
                  >
                    <Gift className="w-3 h-3" />
                    {tMember("Cards.Credits.Redeem")}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <hr />

        {/* All Models Section */}
        <section>
          <h2 className="font-size/text-sm font-weight/font-medium font-medium my-5">
            {tMember("AllModels")}
          </h2>

          {(modelsCacheLoaded && recentCacheLoaded && newModelsData?.length > 0) ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
              {newModelsData?.map((model: any) =>
                model?.isRecent ? (
                  <Link
                    href={model?.link || "/dashboard/home"}
                    key={`recent-${model?.id}`}
                  >
                    <ModelCard
                      key={`recent-${model?.id}`}
                      model={model}
                      isRecentlyUsed={true}
                    />
                  </Link>
                ) : (
                  <ModelCard key={model?.id} model={model} />
                )
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
              {Array(5)
                .fill(0)
                .map((_, index) => (
                  <ModelCardSkeleton key={`all-skeleton-${index}`} />
                ))}
            </div>
          )}
        </section>
      </div>

      <RedeemParkModal
        isOpen={isModalRedeemModalOpen}
        onClose={() => {
          setRedeemModalOpen(false);
        }}
        refetchPaymentHistory={() => {}}
      />
    </>
  );
}
