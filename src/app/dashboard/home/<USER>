
"use client";
import DashboardHeader from "@/components/Header/DashboardHeader";
import { Sidebar } from "@/components/Sidebar";
import { FileQuestion } from "lucide-react";
import React, { useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { api, hermesAPI } from "@/services/api_hooks";


interface HomeLayoutProps {
  children: React.ReactNode;
}

const HomeLayout = ({ children }: HomeLayoutProps) => {
  const queryClient = useQueryClient();
  useEffect(() => {
    queryClient.prefetchQuery({
      queryKey: ["payment-history", 1, "10", ""],
      queryFn: async () => {
        const res = await api.get("/api/payment/history?page=1&limit=10&search=");
        return res.data;
      },
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: Infinity, // 1 hour
    });

    queryClient.prefetchQuery({
      queryKey: ["get-timezones"],
      queryFn: async () => {
        const res = await api.get("/auth/timezones");
        return res.data;
      },
      staleTime: Infinity,
      gcTime: Infinity,
    });

    queryClient.prefetchQuery({
      queryKey: ["get-telegram-id"],
      queryFn: async () => {
        const res = await api.get("/api/user/telegramId");
        return res.data;
      },
      staleTime: Infinity,
      gcTime: Infinity,
    });

    queryClient.prefetchQuery({
      queryKey: ["get-choice"],
      queryFn: async () => {
        const res = await hermesAPI.get("/api/choice");
        return res.data;
      },
      staleTime: Infinity,
      gcTime: Infinity,
    });

    queryClient.prefetchQuery({
      queryKey: ["get-subscription-status"],
      queryFn: async () => {
        const res = await hermesAPI.get("/api/user/subscribe");
        return res.data;
      },
      staleTime: Infinity,
      gcTime: Infinity,
    });

    queryClient.prefetchQuery({
      queryKey: ["hermes-x-alerts", 1, 10, "UTC"],
      queryFn: async () => {
        const res = await hermesAPI.get("/api/user/filter-alert?page=1&timezone=UTC");
        return res.data;
      },
      staleTime: Infinity,
      gcTime: Infinity,
    });

    queryClient.prefetchQuery({
      queryKey: ["hermes-c-alerts", 1, 10, ""],
      queryFn: async () => {
        const res = await hermesAPI.get("/api/hermesc/alert/tables?page=1&timezone=UTC");
        return res.data;
      },
      staleTime: Infinity,
      gcTime: Infinity,
    });

    queryClient.prefetchQuery({
      queryKey: ["get-hermesc-subscription-status"],
      queryFn: async () => {
        const res = await hermesAPI.get("/api/hermesc/user/subscription");
        return res.data;
      },
      staleTime: Infinity,
      gcTime: Infinity,
    });

    queryClient.prefetchQuery({
      queryKey: ["get-trial-info"],
      queryFn: async () => {
        const res = await hermesAPI.get("/api/hermesc/user/trial");
        return res.data;
      },
      staleTime: Infinity,
      gcTime: Infinity,
    });

    queryClient.prefetchQuery({
      queryKey: ["get-sandbox-subscription-status"],
      queryFn: async () => {
        const res = await api.get("/api/subscribe/yumi-sandbox-subscription");
        return res.data;
      },
      staleTime: Infinity,
      gcTime: Infinity,
    });

    queryClient.prefetchQuery({
      queryKey: ["get-payment-methods"],
      queryFn: async () => {
        const res = await api.get("/api/payment/payment_method");
        return res.data;
      },
      staleTime: Infinity,
      gcTime: Infinity,
    });
  }, [queryClient]);
  return (
    <div className="flex min-h-screen bg-[#FAFAFA] ">
      <Sidebar />
      <main className="flex-1 overflow-auto ">
        <DashboardHeader />
        <div className="mx-auto max-w-[1400px] px-2">{children}</div> 
      </main>
    </div>
  );
};

export default HomeLayout;
