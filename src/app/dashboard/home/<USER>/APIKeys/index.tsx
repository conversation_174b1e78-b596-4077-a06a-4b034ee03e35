import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Copy } from "lucide-react";
import React from "react";

const APIKeys = () => {
  return (
    <div>
      <div className="flex items-center justify-between mb-10">
        <div className="">
          <p className="text-[16px] font-[500]">Your API Keys</p>
          <p className="text-[#7F7F81] text-[14px] font-[500]">
            Set your preferred language and timezone.
          </p>
        </div>
        <Button
          className="w-fit bg-[#121212] text-sm hover:bg-primary text-white py-2 px-3  h-[32px] cursor-pointer"
          type="submit"
          // disabled={updating}
        >
          {false ? "Updating…" : "Update preference"}
        </Button>
      </div>

      <div className="max-w-[626px] space-y-7">
        <hr className="my-10" />

        <div className="grid gap-5">
          <div className="flex items-end ">
            <div className="grid grid-cols-1">
              <span className="text-sm">Key</span>
              <Input
                type="password"
                placeholder="*********"
                className="w-full"
              />
            </div>
            <Button variant="outline" className="flex items-center">
              Copy
              <Copy />
            </Button>
          </div>
          <Button
            className="w-fit bg-[#121212] text-sm hover:bg-primary text-white py-2 px-3  h-[32px] cursor-pointer"
            type="submit"
            // disabled={updating}
          >
            {false ? "Updating…" : "Update preference"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default APIKeys;
