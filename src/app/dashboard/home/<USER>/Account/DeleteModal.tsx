
"use client";

import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

interface DeleteAccountModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  loading?: boolean;
}

export function DeleteAccountModal({
  isOpen,
  onOpenChange,
  onConfirm,
  loading
}: DeleteAccountModalProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent className="w-[450px]  rounded-md p-6">
        <AlertDialogHeader className="space-y">
          <div className="flex ">
            <div className="rounded-full border border-red-100 bg-red-50 p-2">
              <AlertCircle className="h-6 w-6 text-red-500" />
            </div>
          </div>
          <AlertDialogTitle className=" text-[16px] font-medium">
            Delete Account?
          </AlertDialogTitle>
          <AlertDialogDescription className=" text-sm text-[#737384]">
            Are you sure you want to delete your account? This action is
            permanent and will remove all your data and access.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex flex-col-reverse space-y-2 space-y-reverse sm:flex-row sm:justify-between sm:space-x-2 sm:space-y-0">
          <AlertDialogCancel className="mt-2 w-full rounded-md border border-gray-200 bg-white px-4 py-2 text-sm font-normal text-gray-800 hover:bg-gray-50 sm:mt-0 sm:w-auto">
            Cancel
          </AlertDialogCancel>
          <Button
            onClick={onConfirm}
            className="w-full rounded-md bg-red-600 px-4 py-2 text-sm font-normal text-white hover:bg-red-600 sm:w-auto"
          >
            {loading ? "Deleting..." : "Delete My Account"}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
