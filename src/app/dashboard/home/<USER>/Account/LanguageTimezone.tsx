import { useState } from "react";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Label } from "@/components/ui/label";
import Image from "next/image";

const languages = [
  { value: "english", label: "English", flag: "" },
//   { value: "french", label: "French", flag: "🇫🇷" },
];

const timeZones = [
    // Pure offset-based
    { value: "utc",  label: "UTC (GMT +0)" },
    { value: "gmt+1", label: "GMT +1" },
  
    // Region-based names
    { value: "wat",   label: "West Africa Time (WAT, UTC +1)" },
    { value: "cat",   label: "Central Africa Time (CAT, UTC +2)" },
    { value: "eet",   label: "Eastern Europe Time (EET, UTC +2)" },
  
    // European standard vs. summer
    { value: "cet",   label: "Central European Time (CET, UTC +1)" },
    { value: "cest",  label: "Central European Summer Time (CEST, UTC +2)" },
  
    // North American central
    { value: "cst",   label: "Central Standard Time (US/Canada, UTC -6)" },
    { value: "cdt",   label: "Central Daylight Time (US/Canada, UTC -5)" },
  
    // You can add more US/Canada zones too:
    { value: "est",   label: "Eastern Standard Time (EST, UTC -5)" },
    { value: "edt",   label: "Eastern Daylight Time (EDT, UTC -4)" },
    { value: "pst",   label: "Pacific Standard Time (PST, UTC -8)" },
    { value: "pdt",   label: "Pacific Daylight Time (PDT, UTC -7)" },
  
    // Etc., including any half- or quarter-hour offsets:
    { value: "ist",   label: "India Standard Time (IST, UTC +5:30)" },
    { value: "nzt",   label: "New Zealand Time (NZT, UTC +12)" },
    { value: "nzd",   label: "New Zealand Daylight Time (NZDT, UTC +13)" },
  ];

const LanguageTimezone = () => {
  const [language, setLanguage] = useState(languages[0]);
  const [timeZone, setTimeZone] = useState(timeZones[0]);
  const [openLanguage, setOpenLanguage] = useState(false);
  const [openTimeZone, setOpenTimeZone] = useState(false);
  return (
    <div>
      <div className="flex items-center justify-between mb-10">
        <div className="">
          <p className="text-[16px] font-[500]">Languages & Time Zone</p>
          <p className="text-[#7F7F81] text-[14px] font-[500]">
            Set your preferred language and timezone.
          </p>
        </div>
        <Button
          className="w-fit bg-[#121212] text-sm hover:bg-primary text-white py-2 px-3  h-[32px] cursor-pointer"
          type="submit"
          // disabled={updating}
        >
          {false ? "Updating…" : "Update preference"}
        </Button>
      </div>

      <div className="max-w-[626px] space-y-7">

        <div className="space-y-2">
          <Label htmlFor="language">Language</Label>
          <Popover open={openLanguage} onOpenChange={setOpenLanguage}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={openLanguage}
                className="w-full justify-between"
              >
                <span className="flex items-center">
                  <span className="mr-2">{language.flag}</span>
                  {language.label}
                </span>
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="p-0 w-[var(--radix-popover-trigger-width)]">
              <Command className="w-full">
                <CommandInput placeholder="Search language..." />
                <CommandEmpty>No language found.</CommandEmpty>
                <CommandGroup>
                  {languages.map((lang) => (
                    <CommandItem
                      key={lang.value}
                      value={lang.value}
                      onSelect={() => {
                        setLanguage(lang);
                        setOpenLanguage(false);
                      }}
                    >
                      <span className="flex items-center">
                        <span className="mr-2">{lang.flag}</span>
                        {lang.label}
                      </span>
                      <Check
                        className={cn(
                          "ml-auto h-4 w-4",
                          language.value === lang.value
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
        </div>

        <div className="space-y-2">
          <Label htmlFor="timezone">Time Zone</Label>
          <Popover open={openTimeZone} onOpenChange={setOpenTimeZone}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={openTimeZone}
                className="w-full justify-between"
              >
                {timeZone.label}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[var(--radix-popover-trigger-width)]  p-0">
              <Command>
                <CommandInput placeholder="Search time zone..." />
                <CommandEmpty>No time zone found.</CommandEmpty>
                <CommandGroup className="max-h-[200px] overflow-y-auto">
                  {timeZones.map((tz) => (
                    <CommandItem
                      key={tz.value}
                      value={tz.value}
                      onSelect={() => {
                        setTimeZone(tz);
                        setOpenTimeZone(false);
                      }}
                    >
                      {tz.label}
                      <Check
                        className={cn(
                          "ml-auto h-4 w-4",
                          timeZone.value === tz.value
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <hr className="my-6" />

      <div className="">
        <p className=" font-[500] text-sm ">Linked Logins</p>
        <p className="text-[#7F7F81] text-[14px] font-[500]">
          Your linked logins
        </p>
      </div>

      <div className="flex mt-2 border md:max-w-[220px] text-sm w-full items-center rounded-md justify-center gap-2 cursor-pointer h-[40px]">
        Log in with Google
        <Image src="/google-icon.svg" alt="Google" width={20} height={20} />
      </div>

    </div>
  );
};

export default LanguageTimezone;
