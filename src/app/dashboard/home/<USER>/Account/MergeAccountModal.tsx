import { useState } from "react";
import { AlertCircle } from "lucide-react";
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";

export default function MergeAccountsModal({ isOpen, setIsOpen }: any) {
  const [email, setEmail] = useState("");
  const [apiKey, setApiKey] = useState("");

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleMerge = () => {
    console.log("Merging accounts with:", { email, apiKey });
    // Implement actual merge logic here
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <Alert className="border-none mb-4">
            <AlertCircle className="h-4 w-4 " color="#A35F07"/>
            <AlertDescription className="text-[#A35F07] text-sm ">
              Please note this is an irrevocable action.
            </AlertDescription>
          </Alert>
          <DialogTitle className="text-[18px] font-[500]">
            Merge Accounts
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 text-[#7F7F81]">
          <p className="text-sm ">
            Enter the other account&apos;s email address below to merge it with this
            current account.
          </p>

          <div className="space-y-4">
            <div className="space-y-1.5">
              <p className="text-sm font-medium">
                The following will be merged:
              </p>
              <ul className="text-sm  list-disc pl-5 space-y-1">
                <li>Credits</li>
                <li>Payment Methods</li>
                <li>Preferences</li>
                <li>Organization Memberships</li>
                <li>API Keys</li>
              </ul>
            </div>

            <div className="space-y-1.5">
              <p className="text-sm font-medium">Enter second email address</p>
              <Input
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email address"
                className="w-full"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="customizeFilter"
                //   checked={filters.customizeForTelegram}
                //   onCheckedChange={(checked) =>
                //     setFilters({
                //       ...filters,
                //       customizeForTelegram: checked === true,
                //     })
                //   }
              />
              <label
                htmlFor="customizeFilter"
                className="text-xs font-normal leading-none text-[#737384]"
              >
                Prioritize Second account API keys
              </label>
            </div>
          </div>

          <DialogFooter className="flex justify-between sm:justify-between pt-4">
            <Button variant="outline" onClick={handleClose}>
              Back
            </Button>
            <Button
              onClick={handleMerge}
              className="bg-[#A35F07] hover:bg-amber-700 text-white"
            >
              Merge Account
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
