import { useState } from "react";
import { Ch<PERSON><PERSON>Down, Trash2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import MergeAccountsModal from "./MergeAccountModal";
import { DeleteAccountModal } from "./DeleteModal";

export default function Notifications() {
  const [notificationChannel, setNotificationChannel] = useState("email");
  const [isOpen, setIsOpen] = useState(false);
  const [secondaryEmails, setSecondaryEmails] = useState([
    "<EMAIL>",
    "<EMAIL>",
  ]);

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const handleDeleteAccount = () => {
    console.log("Account deleted");
    setIsDeleteModalOpen(false);
    // Add your delete account logic here
  };

  const handleMergeAccount = () => {
    // In a real app, you would add merge account logic
    setIsOpen(true);
    console.log("Merge account requested");
  };

  return (
    <div className="">
      {/* Notifications Section */}
      <section>
        <div className="mb-5">
          <p className="text-[16px] font-[500]">Notifications</p>
          <p className="text-[#7F7F81] text-[14px] font-[500]">
            Select your preferred Channel of receiving notifications about news
            and updates.
          </p>
        </div>

        <div className="mt-4 max-w-[626px]">
          <div className="text-sm font-medium mb-2">Select Channel</div>
          <Select
            value={notificationChannel}
            onValueChange={setNotificationChannel}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a notification channel" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="email">Email Address</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </section>

      <Separator className="my-10" />

      <div className="max-w-[626px]">
        <div className="mb-5">
          <p className="text-[16px] font-[500]">Secondary Emails</p>
          <p className="text-[#7F7F81] text-[14px] font-[500]">
            A list of other email addresses you’ve used across the different
            models
          </p>
        </div>
        {secondaryEmails.length > 0 ? (
          <div className="space-y-4">
            {secondaryEmails.map((email) => (
              <div
                key={email}
                className="flex items-center justify-between border-b border-gray-100 pb-4"
              >
                <span className="text-sm text-gray-900">{email}</span>
                <button
                  // onClick={() => onRemoveEmail(email)}
                  className="flex items-center text-xs font-medium text-red-500 hover:text-red-600"
                >
                  <Trash2 className="mr-1 h-4 w-4" />
                  Remove
                </button>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-sm text-gray-500">No secondary emails added.</p>
        )}
      </div>

      <Separator className="my-10" />

      {/* Merge Accounts Section */}
      <section className="flex justify-between items-center">
        <div>
          <h2 className="text-[16px] font-[500]">Merge Accounts</h2>
          <p className="text-gray-500 text-sm mt-1">
            Have multiple accounts under different emails? Merge them to have
            one unified account
          </p>
        </div>
        <Button
          onClick={handleMergeAccount}
          className="bg-gray-900 hover:bg-gray-800 text-white"
        >
          Merge Account
        </Button>
      </section>

      <Separator className="my-10" />

      {/* Delete Account Section */}
      <section className="flex justify-between items-center">
        <div>
          <h2 className="text-[16px] font-[500]">Delete Account</h2>
          <p className="text-gray-500 text-sm mt-1">
            Remove your account permanently from ai-wk
          </p>
        </div>
        <Button
          //   onClick={handleDeleteAccount}
          onClick={() => setIsDeleteModalOpen(true)}
          variant="destructive"
          className="bg-red-500 hover:bg-red-600 text-white"
        >
          Delete Account
        </Button>
      </section>
      <MergeAccountsModal isOpen={isOpen} setIsOpen={setIsOpen} />
      <DeleteAccountModal
        isOpen={isDeleteModalOpen}
        onOpenChange={setIsDeleteModalOpen}
        onConfirm={handleDeleteAccount}
      />
    </div>
  );
}
