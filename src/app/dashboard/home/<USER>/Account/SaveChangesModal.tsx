import React from "react";
import { useTranslations } from 'next-intl';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

interface SaveChangesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  loading?: boolean;
}

export function SaveChangesModal({
  open,
  onOpenChange,
  onConfirm,
  loading
}: SaveChangesModalProps) {


  // use next-intl for i18n
  const t = useTranslations("DashboardSettings.tabs.Account.SaveChangesModal");
  const tForm = useTranslations("Form");
  const tGlobal = useTranslations("global");

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="!max-w-md rounded-lg p-6">
        <div className="space-y-1">
          <AlertCircle className="h-5 w-5 text-gray-600" />
          <p className="text-[16px] font-[500]">{t("title")}</p>
          <p className="text-sm font-[400] text-[#737384]">
            {t("description")}
          </p>
        </div>
        <AlertDialogFooter className="!flex !flex-row !w-full !justify-between gap-2 ">
          <AlertDialogCancel className="mt-0 border border-gray-200 bg-white text-gray-700 hover:bg-gray-50">
            {tGlobal("Cancel")}
          </AlertDialogCancel>
          <Button
            className="mt-0 bg-gray-900 text-white hover:bg-gray-800"
            onClick={onConfirm}
            disabled={loading}
          >
            {loading ? tForm("Button.Saving") : tForm("Button.SaveChanges")}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
