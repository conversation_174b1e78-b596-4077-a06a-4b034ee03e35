// @ts-nocheck

"use client";

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";
import { Check, ChevronsUpDown, Plus, User, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import TransactionTable from "@/components/table/CreditsTable";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { COUNTRIES } from "@/lib/countries";
import { cn } from "@/lib/utils";
import { Switch } from "@/components/ui/switch";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { PhoneCode } from "@/components/phone-input";
import {
  useGetQuery,
  useSubmitQuery,
  useSubmitQueryFormData,
} from "@/services/api_hooks";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import TelegramLinkButton from "@/components/button/TelegramLinkButton";

const schema = yup
  .object({
    firstName: yup.string().required("First Name is required"),
    lastName: yup.string().required("Last Name is required"),
    email: yup
      .string()
      .email("Invalid email format")
      .required("Email is required")
      .matches(
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,

        "Invalid email format"
      ),
  })
  .required();

export default function YourDetails() {
  const queryClient = useQueryClient();

  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [error, setError] = useState("");
  const [selectedCountry, setSelectedCountry] = useState("CN");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogStep, setDialogStep] = useState<"topUp" | "payment">("topUp");
  const [image, setImage] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  console.log(imageFile, "oppp");

  const {
    register,
    handleSubmit,
    control,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setImageFile(file);

    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleRemoveImage = () => {
    setImage(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleTopUpClick = () => {
    setIsDialogOpen(true);
    setDialogStep("topUp");
  };

  const handleProceed = () => {
    if (dialogStep === "topUp") {
      setDialogStep("payment");
    } else {
      // Handle the payment completion logic here
      setIsDialogOpen(false);
      setDialogStep("topUp");
    }
  };

  const handleCancel = () => {
    if (dialogStep === "payment") {
      setDialogStep("topUp");
    } else {
      setIsDialogOpen(false);
    }
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setDialogStep("topUp");
  };

  const filteredCountries = useMemo(() => {
    if (!searchQuery) return COUNTRIES;

    const lowerQuery = searchQuery.toLowerCase();
    return COUNTRIES.filter((country) =>
      country.title.toLowerCase().includes(lowerQuery)
    );
  }, [searchQuery]);

  // // Use useCallback for event handlers
  const handleSelect = useCallback(
    (country) => {
      console.log(country?.value, "couu");
      setSelectedCountry(country?.value);
      setValue("country", country);
      setOpen(false); // close the dropdown
      setSearchQuery(""); // reset search
    },
    [setValue]
  );

  // 1️⃣ Fetch profile on mount
  // 1️⃣ fetch once on mount, "profile" === response.data
  const { data: profile, isLoading } = useGetQuery(
    "/auth/profile",
    ["profile"],
    {
      onError() {
        toast.error("Could not load your profile");
      },
    }
  );

  // 2️⃣ when "profile" arrives, write into form
  useEffect(() => {
    if (!profile) return;
    // adjust these keys to whatever your API actually returns
    setValue("firstName", profile?.profile?.firstname);
    setValue("lastName", profile?.profile?.lastname);
    setValue("email", profile?.profile?.email);
  }, [profile, setValue]);

  // 2️⃣ Prepare the mutation for updating
  const { mutateAsync: updateProfile, isPending: updating } =
    useSubmitQueryFormData("/auth/profile", "PUT", {
      onSuccess(response) {
        toast.success("Profile updated!", {
          position: "top-right",
          className: "p-4",
        });

        setImage(null);

        queryClient.invalidateQueries({
          queryKey: ["profile"],
        });
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || "Update failed", {
          position: "top-right",
          className: "p-4",
        });
      },
    });

  // 3️⃣ Form submission handler
  const onSubmit = async (vals: FormValues) => {
    const formData = new FormData();
    formData.append("firstname", vals.firstName);
    formData.append("lastname", vals.lastName);
    formData.append("email", vals.email);
    formData.append("avatar", imageFile);
    if (vals.password) {
      formData.append("password", vals.password);
    }
    await updateProfile(formData);
  };

  if (isLoading) {
    return <div className="text-center">Loading…</div>;
  }

  return (
    <>
      <div>
        <div className=" ">
          <form className="space-y-3" onSubmit={handleSubmit(onSubmit)}>
            <div className="flex justify-between">
              <div className="flex items-end gap-3 mb-10">
                <div className="relative">
                  <div className="w-30 relative h-30 rounded-full overflow-hidden bg-gray-100 grid place-content-center ">
                    {image || profile?.profile?.avatar_url ? (
                      <Image
                        src={image ? image : profile?.profile?.avatar_url}
                        alt="Profile"
                        className="w-full h-full object-contain"
                        height={120}
                        width={120}
                      />
                    ) : (
                      <div className="text-gray-400">
                        <User size={32} />
                      </div>
                    )}
                    <div className="absolute bg-[#006AF3] font-[700] text-[10px] text-white w-full p-1 bottom-0 flex justify-center">
                      BASIC
                    </div>
                  </div>

                  {image && (
                    <button
                      onClick={handleRemoveImage}
                      className="absolute cursor-pointer -top-0 -right-0 bg-red-500 text-white rounded-full p-[2px] hover:bg-red-600 transition-colors duration-200"
                      aria-label="Remove photo"
                    >
                      <X size={12} />
                    </button>
                  )}
                </div>

                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  accept="image/*"
                  className="hidden"
                />

                <Button
                  type="button"
                  onClick={handleButtonClick}
                  className="w-fit bg-[#121212] text-sm hover:bg-primary text-white py-2 px-3  h-[32px] cursor-pointer"
                >
                  {image ? "Change Profile Photo" : "Upload Profile Photo"}
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between mb-10">
              <div className="">
                <p className="text-[16px] font-[500]">Your Details</p>
                <p className="text-[#7F7F81] text-[14px] font-[500]">
                  Update your account settings. Set your preferred language and
                  timezone.
                </p>
              </div>
              <Button
                className="w-fit bg-[#121212] text-sm hover:bg-primary text-white py-2 px-3  h-[32px] cursor-pointer"
                type="submit"
                disabled={updating}
              >
                {updating ? "Updating…" : "Update Details"}
              </Button>
            </div>

            <div className="max-w-[626px] space-y-7">
              <div className="grid grid-cols-2 gap-4 ">
                <div>
                  <label
                    htmlFor="firstName"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    First Name
                  </label>
                  <Input
                    id="firstName"
                    type="text"
                    className={`w-full h-[34px] ${
                      errors.firstName ? "border-red-500" : ""
                    }`}
                    {...register("firstName")}
                  />
                  {errors.firstName && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.firstName.message}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="lastName"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Last Name
                  </label>
                  <Input
                    id="lastName"
                    type="text"
                    className={`w-full h-[34px] ${
                      errors.lastName ? "border-red-500" : ""
                    }`}
                    {...register("lastName")}
                  />
                  {errors.lastName && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.lastName.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="">
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Email
                </label>
                <Input
                  id="email"
                  type="email"
                  className={`w-full h-[34px] ${
                    errors.email ? "border-red-500" : ""
                  }`}
                  {...register("email")}
                />
                {errors.email && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.email.message}
                  </p>
                )}
              </div>

              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Password
                </label>
                <div className="flex items-center justify-between border rounded-md h-[34px] px-2">
                  <div className="text-gray-400 mt-1">********</div>
                  <div
                    className=" bg-transparent text-black hover:bg-transparent  cursor-pointer text-sm"
                    //   type="submit"
                    //   disabled={loading}
                    onClick={(e) => {
                      handleTopUpClick();
                      e.preventDefault();
                    }}
                  >
                    Change password
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>

        <Dialog open={isDialogOpen} onOpenChange={handleDialogClose}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader className="flex flex-row items-center justify-between">
              <DialogTitle className="text-[18px] font-[500]">
                Change Password
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-6">
              <div className="">
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Old Password
                </label>
                <div className="relative">
                  <Input id="password" placeholder="********" type="password" />
                </div>
              </div>

              <div className="">
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  New Password
                </label>
                <div className="relative">
                  <Input id="password" placeholder="********" type="password" />
                </div>
              </div>

              <div className="">
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Confirm New Password
                </label>
                <div className="relative">
                  <Input id="password" placeholder="********" type="password" />
                </div>
              </div>

              <div className="flex justify-between pt-4">
                <Button variant="outline" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button
                  className="bg-[#0F172A] hover:bg-[#1E293B] text-white px-6 cursor-pointer"
                  onClick={handleProceed}
                >
                  Proceed
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
}
