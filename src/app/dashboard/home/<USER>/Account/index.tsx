// @ts-nocheck

import { useState, useEffect, useRef, useTransition } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import { Check, ChevronsUpDown, User, PenLine, Mail, Info } from "lucide-react";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { toast } from "sonner";
import { SaveChangesModal } from "./SaveChangesModal";
import { useQueryClient } from "@tanstack/react-query"; // Add this import
import {
  useGetQuery,
  useGetQueryOrionBackend,
  useSubmitQueryFormData,
} from "@/services/api_hooks";
import { timeZones } from "@/utils/data";
import useLocalStorage from "@/hooks/use-local-storage";
import Link from "next/link";

// Language and timezone data
const languages = [
  { value: "en", label: "English" /*  flag: "🇬🇧" */ },
  { value: "zh-CN", label: "中文" /* flag: "🇨🇳" */ },
];

import { FixedSizeList } from "react-window";
import { botName, telegramBot } from "@/config/baseUrl";
import { setUserLocale } from "@/services/locale";
import { useLanguage } from "@/Providers/LanguageProvider";

const Account = () => {
  // use next-intl for i18n
  const t = useTranslations("DashboardSettings.tabs.Account");
  const tForm = useTranslations("Form");
  const tFormSignUp = useTranslations("Form.SignUp");
  const tGlobal = useTranslations("global");

  // Add query client
  const queryClient = useQueryClient();

  // State management
  const [isEditing, setIsEditing] = useState(false);
  const [formValues, setFormValues] = useState({
    firstName: "",
    lastName: "",
    email: "",
    telegramId: "",
  });
  const [originalValues, setOriginalValues] = useState({
    firstName: "",
    lastName: "",
    email: "",
    telegramId: "",
  });
  const [errors, setErrors] = useState({
    firstName: "",
    lastName: "",
    email: "",
    telegramId: "",
  });
  const [isLoading, setIsLoading] = useState(true);
  const [image, setImage] = useState(null);
  const [imageFile, setImageFile] = useState(null);
  const [originalImage, setOriginalImage] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [language, setLanguage] = useState(languages[0]);
  const [timeZone, setTimeZone] = useState({ name: "Spain", timezone: "UTC" }); // Default to Spain
  const [initialLanguage, setInitialLanguage] = useState(languages[0]);
  const [initialTimeZone, setInitialTimeZone] = useState({
    name: "Spain",
    timezone: "UTC",
  }); // Default to Spain
  const [openLanguage, setOpenLanguage] = useState(false);
  const [openTimeZone, setOpenTimeZone] = useState(false);
  const [openSaveModal, setOpenSaveModal] = useState(false);
  // State for search term
  const [searchTerm, setSearchTerm] = useState("");

  interface AuthCredentials {
    user?: {
      identities?: Array<{ provider: string }>;
    };
  }

  const [userCredentials, setName] = useLocalStorage("authCred", {});

  // Refs
  const fileInputRef = useRef(null);

  // API integration for profile update
  const { mutateAsync: updateProfile, isPending: updating } =
    useSubmitQueryFormData("/auth/profile", "PUT", {
      onSuccess() {
        toast.success(t("toast.ProfileUpdated"), {
          position: "top-right",
          className: "p-4",
        });

        setImageFile(null);
        setOpenSaveModal(false);

        queryClient.invalidateQueries({
          queryKey: ["profile"],
        });

        // Exit edit mode after successful update
        setIsEditing(false);
      },
      onError(err) {
        toast.error(err.response?.data?.error || t("toast.UpdateFailed"), {
          position: "top-right",
          className: "p-4",
        });
      },
    });

  // Fetch profile data from API
  const { data: profileData, isLoading: profileLoading } = useGetQuery(
    "/auth/profile",
    ["profile"],
    {
      onError() {
        toast.error(t("toast.notLoadProfile"));
      },
    }
  );

  // Get timezones - Fixed: Better error handling
  const {
    data: timezoneData,
    isLoading: timezonesLoading,
    error: timezonesError,
  } = useGetQuery("/auth/timezones", ["get-timezones"], {
    onSuccess: (data) => {
      console.log("Timezones loaded:", data);
    },
    onError: (error) => {
      console.error("Failed to fetch timezones:", error);
      toast.error(t("toast.FailedLoadTimezones"));
    },
  });

  const {
    data: orionData,
    isLoading: orionLoading,
    error: errorData,
  } = useGetQuery("/api/user/telegramId", ["get-telegram-id"], {
    retry: false,
    onSuccess: (data) => {
      console.log("Telegram ID loaded:", data);
    },
    onError: () => {
      console.error("Failed to fetch Telegram ID");
    },
  });

  // Update form values when profile data changes
  useEffect(() => {
    if (profileData?.profile) {
      const profile = profileData.profile;

      const initialValues = {
        firstName: profile.firstname || "",
        lastName: profile.lastname || "",
        email: profile.email || "",
        telegramId: profile.telegram_id || "",
      };

      setFormValues(initialValues);
      setOriginalValues(initialValues);

      // Set language and timezone if available
      if (profile.language) {
        const foundLanguage = languages.find(
          (l) => l.value === profile.language
        );
        if (foundLanguage) {
          setLanguage(foundLanguage);
          setInitialLanguage(foundLanguage);
        }
      }

      // Fixed: Handle timezone with proper structure matching API response
      if (profile?.timezone && timezoneData?.timezones) {
        // Parse the timezone format "{timezone} {name}"
        const timezoneValue = profile.timezone;
        const foundTimezone = timezoneData.timezones.find(
          (tz) => `${tz.timezone} ${tz.name}` === timezoneValue
        );
        if (foundTimezone) {
          setTimeZone(foundTimezone);
          setInitialTimeZone(foundTimezone);
        }
      }

      if (profile?.avatar_url) {
        setImage(profile?.avatar_url);
        setOriginalImage(profile?.avatar_url);
      }
    }
  }, [profileData, timezoneData]); // Added timezoneData dependency

  // Update loading state based on profile loading
  useEffect(() => {
    setIsLoading(profileLoading || timezonesLoading);
  }, [profileLoading, timezonesLoading]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormValues({
      ...formValues,
      [name]: value,
    });

    // Only validate email format
    if (name === "email" && value && !validateEmail(value)) {
      setErrors({
        ...errors,
        email: tForm("email.dataFormatError"),
      });
    } else {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
  };

  // Email validation
  const validateEmail = (email) => {
    const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return re.test(String(email).toLowerCase());
  };

  // Handle profile picture change
  const handleFileChange = (e) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
    }

    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleProfilePictureClick = () => {
    // Only allow changing picture when in edit mode
    if (isEditing) {
      fileInputRef.current?.click();
    }
  };

  // Check if form values have changed
  const hasFormChanged = () => {
    return (
      formValues.firstName !== originalValues.firstName ||
      formValues.lastName !== originalValues.lastName ||
      formValues.telegramId !== originalValues.telegramId ||
      language?.value !== initialLanguage?.value ||
      timeZone?.timezone !== initialTimeZone?.timezone || // Fixed: Use timezone property with chain operator
      image !== originalImage
    );
  };

  // Form validation
  const validateForm = () => {
    let hasErrors = false;
    const newErrors = { ...errors };

    // Only validate email format if there is an email
    if (formValues.email && !validateEmail(formValues.email)) {
      newErrors.email = tForm("email.dataFormatError");
      hasErrors = true;
    }

    setErrors(newErrors);
    return !hasErrors;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    if (e) e.preventDefault();

    // Validate form
    if (!validateForm()) return;

    // If nothing has changed, just exit edit mode
    if (!hasFormChanged()) {
      setIsEditing(false);
      return;
    }

    // Only show save modal if changes were made
    setOpenSaveModal(true);
  };

  // Handle confirmation from SaveChangesModal
  const handleConfirmSave = async () => {
    try {
      // Create form data
      const formData = new FormData();
      formData.append("firstname", formValues.firstName);
      formData.append("lastname", formValues.lastName);
      formData.append("email", formValues.email);
      formData.append("timezone", `${timeZone?.timezone} ${timeZone?.name}`); // Send "{timezone} {name}" format
      formData.append("language", language?.value || "");

      if (imageFile) {
        formData.append("avatar", imageFile);
      }

      // Submit the form data
      await updateProfile(formData);

      // Update original values to match current values
      setOriginalValues({ ...formValues });
      if (image) {
        setOriginalImage(image);
      }
      setInitialLanguage(language);
      setInitialTimeZone(timeZone); // Fixed: Update initial timezone

      // Close modal
      setOpenSaveModal(false);
    } catch (error) {
      console.error("Error saving profile:", error);
    }
  };

  const handleChangeTelegramId = () => {
    // Implement logic to change Telegram ID
    const newTelegramId = prompt("Enter new Telegram ID");
    if (newTelegramId) {
      setFormValues({
        ...formValues,
        telegramId: newTelegramId,
      });
    }
  };

  // Password dialog handlers
  const handleChangePasswordClick = (e) => {
    e.preventDefault();
    setIsDialogOpen(true);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
  };

  const handleChangePassword = () => {
    // Implement password change logic
    toast.success(t("toast.PasswordChanged"));
    setIsDialogOpen(false);
  };

  // Handle edit/save toggle
  const toggleEditMode = () => {
    if (isEditing) {
      // If currently editing, handle form submission
      const formElement = document.getElementById("profile-form");
      if (formElement) {
        formElement.dispatchEvent(
          new Event("submit", { cancelable: true, bubbles: true })
        );
      }
    } else {
      // Enable editing
      setIsEditing(true);
    }
  };
  const { onChange: onChangeGeneral, currentLocale, isPending } = useLanguage();

  useEffect(() => {
    const findLocale = languages.find((itm) => itm.value === currentLocale);
    setLanguage(findLocale);
  }, [currentLocale]);

  if (isLoading) {
    return <div className="text-center py-10">Loading…</div>;
  }

  return (
    <div className="p-6">
      <form id="profile-form" onSubmit={handleSubmit}>
        {/* User Details Section */}
        <div>
          <div className="flex justify-between mb-6">
            <div className="flex items-end gap-3 mb-10">
              <div className="relative">
                <div
                  className={`w-30 h-30 rounded-full overflow-hidden bg-gray-100 grid place-content-center  relative ${
                    isEditing ? "cursor-pointer" : ""
                  }`}
                  onClick={handleProfilePictureClick}
                >
                  {image ? (
                    <Image
                      src={image}
                      alt="Profile"
                      className="w-full h-full object-cover"
                      height={120}
                      width={120}
                    />
                  ) : (
                    <div className="text-gray-400">
                      <User size={32} />
                    </div>
                  )}
                  <div className="absolute bg-[#006AF3] font-[700] text-[10px] text-white w-full p-1 bottom-0 flex justify-center">
                    {t("BASIC")}
                  </div>
                  <div
                    className={`absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 ${
                      isEditing ? "hover:opacity-100" : ""
                    }  transition-opacity duration-300`}
                  >
                    <div className="text-white text-xs font-medium text-center">
                      <PenLine size={16} className="mx-auto mb-1" />
                      {t("ChangeProfilePicture")}
                    </div>
                  </div>
                </div>
              </div>

              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                accept="image/*"
                className="hidden"
              />
            </div>
            <div className="flex items-center h-fit gap-2 ">
              {isEditing && (
                <span className="text-sm flex items-center gap-1">
                  {" "}
                  <Info size={14} /> {t("currentlyOnEditMode")}
                </span>
              )}
              <Button
                type="button"
                onClick={toggleEditMode}
                className="w-fit bg-[#121212] text-sm hover:bg-primary text-white py-2 px-3 h-[32px] cursor-pointer"
                disabled={updating}
              >
                {isEditing
                  ? tForm("Button.SaveDetails")
                  : tForm("Button.EditDetails")}
              </Button>
            </div>
          </div>

          <div className="flex items-center justify-between mb-10">
            <div>
              <p className="text-[16px] font-[500]">{t("YourDetails.title")}</p>
              <p className="text-[#7F7F81] text-[14px] font-[500]">
                {t("YourDetails.description")}
              </p>
            </div>
          </div>

          <div className="max-w-[626px] space-y-7">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="firstName"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  {tFormSignUp("firstName.text")}
                </label>
                <Input
                  id="firstName"
                  name="firstName"
                  type="text"
                  value={formValues.firstName}
                  onChange={handleInputChange}
                  disabled={true}
                  className={`w-full h-[34px] ${
                    errors.firstName ? "border-red-500" : ""
                  }`}
                />
                {errors.firstName && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.firstName}
                  </p>
                )}
              </div>
              <div>
                <label
                  htmlFor="lastName"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  {tFormSignUp("lastName.text")}
                </label>
                <Input
                  id="lastName"
                  name="lastName"
                  type="text"
                  value={formValues.lastName}
                  onChange={handleInputChange}
                  disabled={true}
                  className={`w-full h-[34px] ${
                    errors.lastName ? "border-red-500" : ""
                  }`}
                />
                {errors.lastName && (
                  <p className="text-red-500 text-xs mt-1">{errors.lastName}</p>
                )}
              </div>
            </div>

            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                {tFormSignUp("email.text")}
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formValues.email}
                onChange={handleInputChange}
                disabled={true}
                className={`w-full h-[34px] ${
                  errors.email ? "border-red-500" : ""
                }`}
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">{errors.email}</p>
              )}
            </div>

            {/* Telegram ID Section */}
            <div>
              <label
                htmlFor="telegramId"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                {t("TelegramID.text")}
              </label>
              <div className="flex items-center justify-between border rounded-md h-[34px] px-2">
                <div className="text-gray-700 text-xs">
                  {orionData?.telegram_id}
                </div>
                <a
                  href={`${telegramBot}?start=link_${profileData?.profile?.user_id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-transparent text-black hover:bg-transparent cursor-pointer text-sm"
                >
                  {t("TelegramID.ChangeID")}
                </a>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {t("TelegramID.description", { botName })}
              </p>
            </div>
          </div>
        </div>
      </form>

      <hr className="my-10" />

      {/* Language & Timezone Section */}
      <div>
        <div className="flex items-center justify-between mb-10">
          <div>
            <p className="text-[16px] font-[500]">
              {t("LanguagesAndTimeZone.title")}
            </p>
            <p className="text-[#7F7F81] text-[14px] font-[500]">
              {t("LanguagesAndTimeZone.description")}
            </p>
          </div>
        </div>

        <div className="max-w-[626px] space-y-7">
          <div className="space-y-2">
            <Label htmlFor="language">{t("Language")}</Label>
            <Popover
              open={isEditing && openLanguage}
              onOpenChange={(open) => isEditing && setOpenLanguage(open)}
            >
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={openLanguage}
                  className="w-full justify-between"
                  disabled={!isEditing}
                >
                  <span className="flex items-center">
                    <span className="mr-2">{language.flag}</span>
                    {language.label}
                  </span>
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="p-0 w-[var(--radix-popover-trigger-width)]">
                <Command className="w-full">
                  <CommandInput
                    placeholder={t("LanguagesAndTimeZone.searchLanguage")}
                  />
                  <CommandEmpty>{t("NoLanguageFound")}</CommandEmpty>
                  <CommandGroup>
                    {languages.map((lang) => (
                      <CommandItem
                        key={lang.value}
                        value={lang.value}
                        onSelect={() => {
                          setLanguage(lang);
                          setOpenLanguage(false);
                          onChangeGeneral(lang.value);

                          localStorage.setItem("autoDetect", "detect");
                        }}
                      >
                        <span className="flex items-center">
                          <span className="mr-2">{lang.flag}</span>
                          {lang.label}
                        </span>
                        <Check
                          className={cn(
                            "ml-auto h-4 w-4",
                            language.value === lang.value
                              ? "opacity-100"
                              : "opacity-0"
                          )}
                        />
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </Command>
              </PopoverContent>
            </Popover>
          </div>

          {/* Fixed: Timezone Section with React Window Virtualization and Search */}
          <div className="space-y-2">
            <Label htmlFor="timezone">{t("TimeZone")}</Label>
            <Popover
              open={isEditing && openTimeZone}
              onOpenChange={(open) => {
                if (isEditing) {
                  setOpenTimeZone(open);
                  if (!open) setSearchTerm(""); // Clear search when closing
                }
              }}
            >
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={openTimeZone}
                  className="w-full justify-between"
                  disabled={!isEditing}
                >
                  {timeZone?.name
                    ? `${timeZone.name} (${timeZone.timezone})`
                    : t("SelectTimezone")}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                <Command>
                  <CommandInput
                    placeholder={t("SearchTimeZone")}
                    value={searchTerm}
                    onValueChange={setSearchTerm}
                  />
                  <CommandEmpty>{t("NoTimeZoneFound")}</CommandEmpty>
                  <CommandGroup>
                    <div className="h-[200px]">
                      {timezoneData?.timezones &&
                        (() => {
                          // Filter timezones based on search term
                          const filteredTimezones =
                            timezoneData.timezones.filter(
                              (tz) =>
                                !searchTerm ||
                                tz.name
                                  ?.toLowerCase()
                                  .includes(searchTerm.toLowerCase()) ||
                                tz.timezone
                                  ?.toLowerCase()
                                  .includes(searchTerm.toLowerCase())
                            );

                          return (
                            <FixedSizeList
                              height={200}
                              itemCount={filteredTimezones.length}
                              itemSize={35}
                              itemData={{
                                timezones: filteredTimezones,
                                selectedTimezone: timeZone?.timezone,
                                onSelect: (tz) => {
                                  setTimeZone(tz);
                                  setOpenTimeZone(false);
                                  setSearchTerm("");
                                },
                              }}
                            >
                              {({ index, style, data }) => {
                                const tz = data.timezones?.[index];
                                if (!tz) return null;

                                return (
                                  <div style={style}>
                                    <CommandItem
                                      key={index}
                                      value={`${tz.timezone} ${tz.name}`}
                                      onSelect={() => data.onSelect(tz)}
                                      className="cursor-pointer"
                                    >
                                      {tz.name} ({tz.timezone})
                                      <Check
                                        className={cn(
                                          "ml-auto h-4 w-4",
                                          data.selectedTimezone === tz.timezone
                                            ? "opacity-100"
                                            : "opacity-0"
                                        )}
                                      />
                                    </CommandItem>
                                  </div>
                                );
                              }}
                            </FixedSizeList>
                          );
                        })()}
                    </div>
                  </CommandGroup>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
        </div>

        <hr className="my-6" />

        {/* Sign-in Method Section */}
        <div className="mb-4">
          <p className="font-[500] text-sm">{t("SignInMethod.title")}</p>
          <p className="text-[#7F7F81] text-[14px] font-[500]">
            {t("SignInMethod.description")}
          </p>
        </div>

        {profileData?.profile?.signin_method === "email" && (
          <div className="flex mt-2 border md:max-w-[220px] text-sm w-full items-center rounded-md justify-center gap-2 cursor-pointer h-[40px]">
            {t("SignedInWithEmail")}
            <Mail />
          </div>
        )}
        {profileData?.profile?.signin_method === "google" && (
          <div className="flex mt-2 border md:max-w-[220px] text-sm w-full items-center rounded-md justify-center gap-2 cursor-pointer h-[40px]">
            {t("SignedInWithGoogle")}
            <Image src="/google-icon.svg" alt="Google" width={20} height={20} />
          </div>
        )}
      </div>

      {/* Change Password Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle className="text-[18px] font-[500]">
              {t("ChangePassword")}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <div>
              <label
                htmlFor="oldPassword"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                {t("OldPassword")}
              </label>
              <div className="relative">
                <Input
                  id="oldPassword"
                  placeholder="********"
                  type="password"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="newPassword"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                {t("NewPassword")}
              </label>
              <div className="relative">
                <Input
                  id="newPassword"
                  placeholder="********"
                  type="password"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                {t("ConfirmNewPassword")}
              </label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  placeholder="********"
                  type="password"
                />
              </div>
            </div>

            <div className="flex justify-between pt-4">
              <Button variant="outline" onClick={handleDialogClose}>
                {tGlobal("Cancel")}
              </Button>
              <Button
                className="bg-[#0F172A] hover:bg-[#1E293B] text-white px-6 cursor-pointer"
                onClick={handleChangePassword}
              >
                {t("ChangePassword")}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Save Changes Confirmation Modal */}
      <SaveChangesModal
        open={openSaveModal}
        onOpenChange={setOpenSaveModal}
        onConfirm={handleConfirmSave}
        loading={updating}
      />
    </div>
  );
};

export default Account;
