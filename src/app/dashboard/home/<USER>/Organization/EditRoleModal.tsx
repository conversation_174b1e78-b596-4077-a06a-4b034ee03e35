"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";

type UserRole = "Owner" | "Admin" | "Member" | "Billing";

interface EditRoleModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  initialRole: any;
  onSave: (role: UserRole) => void;
  onCancel: () => void;
  availableRoles?: UserRole[];
}

export function EditRoleModal({
  isOpen,
  onOpenChange,
  initialRole,
  onSave,
  onCancel,
  availableRoles = ["Owner", "Admin", "Member", "Billing"],
}: EditRoleModalProps) {
  const [selectedRole, setSelectedRole] = useState<UserRole>(initialRole);

  const handleSave = () => {
    onSave(selectedRole);
    onOpenChange(false);
  };

  const handleCancel = () => {
    setSelectedRole(initialRole); // Reset to initial value
    onCancel();
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md rounded-lg p-6 sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-[18px] font-[500] text-gray-900">
            Edit Role
          </DialogTitle>
          <p className="text-sm text-gray-500">
            Select the role you want to assign to this user
          </p>
        </DialogHeader>

        <div className="py-4">
          <div className="space-y-2">
            <label
              htmlFor="role-select"
              className="block text-sm font-medium text-gray-700"
            >
              Role
            </label>
            <Select
              value={selectedRole}
              onValueChange={(value) => setSelectedRole(value as UserRole)}
            >
              <SelectTrigger
                id="role-select"
                className="w-full rounded-md border border-gray-300 py-2"
              >
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                {availableRoles.map((role) => (
                  <SelectItem key={role} value={role}>
                    {role}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter className="flex justify-between sm:justify-between mt-10">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Back
          </Button>
          <Button
            onClick={handleSave}
            className="rounded-md bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-navy-900"
          >
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

