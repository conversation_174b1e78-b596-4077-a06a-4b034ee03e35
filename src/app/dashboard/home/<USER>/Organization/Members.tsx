"use client";

import React, { useState } from "react";
import { User, MoreV<PERSON>ical, Pencil, Trash2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { EditRoleModal } from "./EditRoleModal";
import { DeleteAccountModal } from "../Account/DeleteModal";

type MemberRole = "Owner" | "Admin" | "Member" | "Billing";
type MemberStatus = "Active" | "Invite Sent";

interface Member {
  id: string;
  email: string;
  role: MemberRole;
  status: MemberStatus;
  isCurrentUser?: boolean;
}

interface MembersListProps {
  members: any;
  onEditRole: (memberId: string) => void;
  onRemoveMember: (memberId: string) => void;
}

export default function MembersList({
  members,
  onEditRole,
  onRemoveMember,
}: MembersListProps) {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentUserRole, setCurrentUserRole] = useState("Admin");

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const handleDeleteAccount = () => {
    console.log("Account deleted");
    setIsDeleteModalOpen(false);
    // Add your delete account logic here
  };

  const handleSaveRole = (newRole: any) => {
    console.log(`Role changed from ${currentUserRole} to ${newRole}`);
    setCurrentUserRole(newRole);
    // Here you would typically make an API call to update the role
  };

  const handleCancel = () => {
    console.log("Edit canceled");
  };

  const handleMoreClick = (memberId: string) => {
    setActiveDropdown(activeDropdown === memberId ? null : memberId);
  };

  return (
    <div className=" bg-white max-w-[626px]">
      <h2 className=" border-gray-200 text-[16px] font-[400] mb-2">Members</h2>
      <div className="rounded-md border border-gray-200">
        {members.map((member: any) => (
          <div
            key={member.id}
            className="flex items-center justify-between border-b border-gray-100 p-4 last:border-b-0"
          >
            <div className="flex items-center space-x-1">
              <div className="flex h-10 w-10 items-center justify-center rounded-full ">
                <User className="h-5 w-5 text-gray-400" />
              </div>
              <div>
                <p className="text-sm text-gray-800">{member.email}</p>
                <p className="text-xs text-gray-500">
                  {member.role}
                  {member.isCurrentUser && " (You)"}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {member.status === "Active" ? (
                <div className="flex items-center space-x-1">
                  <span className="h-1.5 w-1.5 rounded-full bg-green-500"></span>
                  <span className="text-xs font-medium text-green-500">
                    Active
                  </span>
                </div>
              ) : (
                <span className="text-xs text-gray-500 bg-[#D7D7D740] rounded-md p-1 px-2">
                  Invite Sent
                </span>
              )}

              <DropdownMenu
                open={activeDropdown === member.id}
                onOpenChange={() => handleMoreClick(member.id)}
              >
                <DropdownMenuTrigger asChild>
                  <button className="rounded-full p-1 hover:bg-gray-100">
                    <MoreVertical className="h-5 w-5 text-gray-400" />
                  </button>
                </DropdownMenuTrigger>
                {!member.isCurrentUser && (
                  <DropdownMenuContent
                    align="end"
                    className="w-40 rounded-md p-1 shadow-md"
                  >
                    <>
                      <DropdownMenuItem
                        onClick={() => setIsModalOpen(true)}
                        className="flex cursor-pointer items-center space-x-2 rounded px-2 py-1.5 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <Pencil className="h-4 w-4 text-gray-500" />
                        <span>Edit Role</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        // onClick={() => onRemoveMember(member.id)}
                        onClick={() => setIsDeleteModalOpen(true)}
                        className="flex cursor-pointer items-center space-x-2 rounded px-2 py-1.5 text-sm text-red-600 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                        <span>Remove</span>
                      </DropdownMenuItem>
                    </>
                  </DropdownMenuContent>
                )}
              </DropdownMenu>
            </div>
          </div>
        ))}
      </div>

      <EditRoleModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        initialRole={currentUserRole}
        onSave={handleSaveRole}
        onCancel={handleCancel}
      />

      <DeleteAccountModal
        isOpen={isDeleteModalOpen}
        onOpenChange={setIsDeleteModalOpen}
        onConfirm={handleDeleteAccount}
      />
    </div>
  );
}
