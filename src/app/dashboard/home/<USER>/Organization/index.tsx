import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import React, { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import MembersList from "./Members";

const Organization = () => {
  const [members, setMembers] = useState([
    {
      id: "1",
      email: "<EMAIL>",
      role: "Owner",
      status: "Active",
      isCurrentUser: false,
    },
    {
      id: "2",
      email: "<EMAIL>",
      role: "Admin",
      status: "Active",
    },
    {
      id: "3",
      email: "<EMAIL>",
      role: "Member",
      status: "Active",
    },
    {
      id: "4",
      email: "<EMAIL>",
      role: "Billing",
      status: "Invite Sent",
    },
  ]);

  const handleEditRole = (memberId: string) => {
    console.log("Edit role for member:", memberId);
    // Implement your edit role logic here
  };

  const handleRemoveMember = (memberId: string) => {
    console.log("Remove member:", memberId);
    setMembers(members.filter((member) => member.id !== memberId));
    // You would typically make an API call here
  };
  return (
    <div className="space-y-7">
      <div>
        <div className="mb-5">
          <p className="text-[16px] font-[500]">Organization</p>
          <p className="text-[#7F7F81] text-[14px] font-[500]">
            Your company overview
          </p>
        </div>

        <div className="mt-4 max-w-[626px]">
          <div className="text-sm font-medium mb-2">Organization Name</div>
          <Input id="firstName" type="text" className={`w-full h-[34px] `} />
        </div>
      </div>

      <hr className="my-10" />

      <div className="mb-5">
        <p className="text-[16px] font-[500]">Members</p>
        <p className="text-[#7F7F81] text-[14px] font-[500]">
          Invite and collaborate with team members
        </p>
      </div>

      <div className=" grid grid-cols-2   items-end-safe justify-between">
        <div className="max-w-[636px] w-full grid grid-cols-2 gap-5 ">
          <div>
            <label
              htmlFor="firstName"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Email
            </label>
            <Input
              id="firstName"
              type="text"
              className={`w-full h-[34px]  ${
                "" //   errors.firstName ? "border-red-500" : ""
              }`}
              // {...register("firstName")}
            />
            {/* {errors.firstName && (
            <p className="text-red-500 text-xs mt-1">
              {errors.firstName.message}
            </p>
          )} */}
          </div>
          <div>
            <label
              htmlFor="lastName"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Role
            </label>
            <Select
            // value={notificationChannel}
            // onValueChange={setNotificationChannel}
            >
              <SelectTrigger className="w-full h-[34px]">
                <SelectValue placeholder="" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="email">Role</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Button
          className="w-fit bg-[#121212] text-sm hover:bg-primary text-white py-2 px-3  h-[32px] cursor-pointer place-self-end"
          type="submit"
          //   disabled={updating}
        >
          {false ? "Updating…" : "Invite Team Members"}
        </Button>
      </div>

      <MembersList
        members={members}
        onEditRole={handleEditRole}
        onRemoveMember={handleRemoveMember}
      />
    </div>
  );
};

export default Organization;
