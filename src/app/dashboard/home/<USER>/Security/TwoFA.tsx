"use client";

import { Switch } from "@/components/ui/switch";
import React, { useState } from "react";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import Image from "next/image";

const TwoFA = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  return (
    <div>
      <section className="flex flex-row items-center justify-between max-w-[626px] mt-5">
        <div>
          <p className="text-[16px] font-[500]">2FA Authentication</p>
          <p className="text-[#7F7F81] text-[12px] font-[500]">
            Set up another layer of authentication for extra protection of your
            account.
          </p>
        </div>
        <Switch
          id="airplane-mode"
          checked={isDialogOpen}
          onCheckedChange={(checked) => setIsDialogOpen(checked)}
        />
      </section>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle className="text-[18px] font-[500]">
              <p className="flex flex-col space-y-2">
                Set Up 2FA{" "}
                <span className="text-[14px] font-[400] text-[#7F7F81]">
                  Scan the QR Code below with your authenticator app.
                </span>
              </p>
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            <div>
              <Image src="/qrCode.svg" alt="qr" height={220} width={420} />
            </div>
            <div>
              <p className="text-[12px] font-[500] text-[#101828]">
                Enter OTP Code from your Authenticator
              </p>
              <InputOTP maxLength={6}>
                <InputOTPGroup className="gap-2">
                  <InputOTPSlot
                    index={0}
                    className="border w-[60px] h-[60px] rounded-[8px]"
                  />
                  <InputOTPSlot
                    index={1}
                    className="border w-[60px] h-[60px] rounded-[8px]"
                  />
                  <InputOTPSlot
                    index={2}
                    className="border w-[60px] h-[60px] rounded-[8px]"
                  />
                  <InputOTPSlot
                    index={3}
                    className="border w-[60px] h-[60px] rounded-[8px]"
                  />
                  <InputOTPSlot
                    index={4}
                    className="border w-[60px] h-[60px] rounded-[8px]"
                  />
                  <InputOTPSlot
                    index={5}
                    className="border w-[60px] h-[60px] rounded-[8px]"
                  />
                </InputOTPGroup>
              </InputOTP>
            </div>

            <div className="flex justify-between pt-4">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                className="bg-[#0F172A] hover:bg-[#1E293B] text-white px-6"
                // onClick={handleProceed}
              >
                Complete 2FA
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TwoFA;
