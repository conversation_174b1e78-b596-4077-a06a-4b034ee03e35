import React from "react";
import TwoFA from "./TwoFA";
import { Trash2 } from "lucide-react";

const Security = () => {
//   const [secondaryEmails, setSecondaryEmails] = React.useState([
//     "<EMAIL>",
//     "<EMAIL>",
//   ]);
  return (
    <div className="">
      <TwoFA />
      <hr className="my-10" />

      {/* <div className="max-w-[626px]">
        {secondaryEmails.length > 0 ? (
          <div className="space-y-4">
            {secondaryEmails.map((email) => (
              <div
                key={email}
                className="flex items-center justify-between border-b border-gray-100 pb-4"
              >
                <span className="text-sm text-gray-900">{email}</span>
                <button
                  // onClick={() => onRemoveEmail(email)}
                  className="flex items-center text-xs font-medium text-red-500 hover:text-red-600"
                >
                  <Trash2 className="mr-1 h-4 w-4" />
                  Remove
                </button>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-sm text-gray-500">No secondary emails added.</p>
        )}
      </div> */}
    </div>
  );
};

export default Security;
