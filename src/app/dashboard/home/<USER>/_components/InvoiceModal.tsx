"use client"

import React, { useState } from "react";
import { useTranslations } from 'next-intl';
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { CalendarIcon, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import axios from "axios";
import { api } from "@/services/api_hooks";

const InvoiceModal = ({ isOpen, onOpenChange }: any) => {

  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.InvoiceModal");
  const tGlobal = useTranslations("global");
  const tForm = useTranslations("Form");

  const [fromDate, setFromDate] = useState<Date | undefined>(undefined);
  const [toDate, setToDate] = useState<Date | undefined>(undefined);
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  
  // Reset toDate if fromDate changes to after toDate
  const handleFromDateChange = (date: Date | undefined) => {
    setFromDate(date);
    // Reset toDate if fromDate is after toDate or if fromDate is cleared
    if ((date && toDate && date > toDate) || !date) {
      setToDate(undefined);
    }
  };
  
  // Add an email validation check
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };
  
  // Format date to YYYY-MM-DD for API
  const formatDateForAPI = (date: Date) => {
    return format(date, "yyyy-MM-dd");
  };

  // Function to download the statement
  const downloadStatement = async () => {
    if (!fromDate || !toDate) return;

    setLoading(true);
    
    try {
      const response = await api({
        method: 'GET',
        url: `/api/payment/statement`,
        params: {
          fromDate: formatDateForAPI(fromDate),
          toDate: formatDateForAPI(toDate)
        },
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        },
        responseType: 'blob' // Important for handling binary data like PDF documents
      });
      
      // Create a blob URL from the response data
      const blob = new Blob([response.data], { 
        type: 'application/pdf' // Changed to PDF MIME type
      });
      const url = window.URL.createObjectURL(blob);
      
      // Create a link and trigger download
      const a = document.createElement('a');
      a.href = url;
      a.download = `statement_${formatDateForAPI(fromDate)}_to_${formatDateForAPI(toDate)}.pdf`; // Changed extension to .pdf
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      // toast.success("Statement downloaded successfully");
      onOpenChange(false);
      setFromDate(undefined);
      setToDate(undefined);
    } catch (error: any) {
      toast.error(error?.response?.data?.error || t("toast.DownloadFailed"), {
        position: "top-right",
        className: "p-4",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-[18px] font-[500]">
            {t("GenerateStatement")}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* From Date Picker */}
          <div className="grid w-full items-center gap-1.5">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal bg-gray-50",
                    !fromDate && "text-gray-400"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {fromDate ? format(fromDate, "PPP") : tForm("FromDate.text")}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={fromDate}
                  onSelect={handleFromDateChange}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* To Date Picker */}
          <div className="grid w-full items-center gap-1.5">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline" 
                  className={cn(
                    "w-full justify-start text-left font-normal bg-gray-50",
                    !toDate && "text-gray-400"
                  )}
                  disabled={!fromDate}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {toDate ? format(toDate, "PPP") : tForm("ToDate.text")}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={toDate}
                  onSelect={setToDate}
                  initialFocus
                  disabled={(date) => {
                    // Disable dates before the fromDate
                    return date < fromDate!;
                  }}
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Email input was commented out in original code, keeping it that way */}
          {/* <div className="grid w-full items-center gap-1.5 relative">
            <Input
              type="email"
              placeholder="Email Address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full bg-gray-50 pr-12"
            />
          </div> */}
        </div>

        <DialogFooter className="flex justify-between sm:justify-between">
          <Button
            variant="outline"
            className="text-gray-700"
            onClick={() => onOpenChange(false)}
          >
            {tGlobal("Cancel")}
          </Button>
          <Button 
            className="bg-gray-900 text-white hover:bg-gray-800 disabled:bg-gray-400"
            disabled={!fromDate || !toDate || loading}
            onClick={downloadStatement}
          >
            {loading ? (
              <>
                {/* <Loader2 className="mr-2 h-4 w-4 animate-spin" /> */}
                {tForm("Button.Generating")}
              </>
            ) : (
              tForm("Button.Generate")
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InvoiceModal;