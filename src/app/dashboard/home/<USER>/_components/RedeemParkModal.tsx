"use client";
import { FC, useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  useGetQuery,
  useSubmitQuery,
  useSubmitQueryHermes,
} from "@/services/api_hooks";
import { toast } from "sonner";
import { Loader } from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";
import { Input } from "@/components/ui/input";

interface RedeemParkModalProps {
  isOpen: boolean;
  onClose: any;
  refetchPaymentHistory: () => void;
}

const RedeemParkModal: FC<RedeemParkModalProps> = ({ isOpen, onClose }) => {
  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.RedeemParkModal");
  const tGlobal = useTranslations("global");
  const tForm = useTranslations("Form");

  const [code, setCode] = useState("");
  const [isSuccess, setIsSuccess] = useState(false);
  const [creditAmount, setCreditAmount] = useState("");

  const queryClient = useQueryClient();

  const {
    mutateAsync: redeem,
    isPending: loading,
    data,
  } = useSubmitQuery("/api/promo/redeem-public", "POST", {
    onSuccess(response: any) {
      toast.success(t("toast.Successful"), {
        position: "top-right",
        className: "p-4",
      });

      queryClient.invalidateQueries({
        queryKey: ["profile"],
      });
      // Set success state and credit amount from response
      setCreditAmount(response?.data?.creditsAdded);
      setIsSuccess(true);
      //refetchPaymentHistory();
    },
    onError(err: any) {
      toast.error(err?.response?.data?.error || t("toast.SubscriptionFailed"), {
        position: "top-right",
        className: "p-4",
      });
    },
  });

  const handleRedeem = async () => {
    if (!code.trim()) {
      toast.error(t("toast.PleaseEnterCode"), {
        position: "top-right",
        className: "p-4",
      });
      return;
    }

    redeem({
      promoCode: code.trim(),
    });
  };

  const handleClose = () => {
    setIsSuccess(false);
    setCode("");
    setCreditAmount("");
    onClose();
  };

  useEffect(() => {
    setCreditAmount(data?.data?.creditsAdded);
  }, [data]);

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-[447px] p-0 overflow-hidden bg-white rounded-lg">
          {!isSuccess ? (
            // Initial redeem form
            <>
              <div className="w-full h-[240px] bg-blue-50 flex items-end justify-center">
                <Image
                  src="/open-gift.svg"
                  alt="HermesX Logo"
                  width={243}
                  height={264}
                  priority
                  className="object-contain"
                />
              </div>

              <div className="px-6 mb-1 space-y-5">
                <DialogHeader>
                  <DialogTitle className="text-[16px] font-[500] ">
                    {t("RedeemPerks.title")}
                  </DialogTitle>
                  <DialogDescription>
                    <span className="text-start text-[#737384] text-xs leading-[20px] mt-2">
                      {t("RedeemPerks.description")}
                    </span>
                  </DialogDescription>
                </DialogHeader>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("EnterCode.text")}
                  </label>
                  <Input
                    placeholder={t("EnterCode.description")}
                    value={code}
                    onChange={(e) => setCode(e.target.value)}
                    className="w-full"
                    disabled={loading}
                  />
                </div>
              </div>

              <DialogFooter className="px-6 pb-6 sm:pb-6">
                <div className="flex items-center justify-between w-full gap-4">
                  <Button
                    className="max-w-[80px] text-black rounded-md"
                    onClick={handleClose}
                    variant="outline"
                    disabled={loading}
                  >
                    {tGlobal("Cancel")}
                  </Button>
                  <Button
                    className="bg-gray-900 max-w-[150px] w-full hover:bg-gray-800 text-white rounded-md"
                    onClick={handleRedeem}
                    disabled={loading}
                  >
                    {tGlobal("Proceed")}
                    {loading && <Loader className="animate-spin ml-2" />}
                  </Button>
                </div>
              </DialogFooter>
            </>
          ) : (
            // Success state
            <>
              <div className="w-full h-[200px] bg-blue-50 flex items-end justify-center">
                <Image
                  src="/success-confetti.svg"
                  alt="HermesX Logo"
                  width={250}
                  height={250}
                  priority
                  className="object-contain"
                />
              </div>

              <div className="px-6 mb-1 ">
                <DialogHeader className="space-y-5">
                  <DialogTitle className=" font-[600] text-center text-[44px]">
                    +${creditAmount}
                  </DialogTitle>
                  <p className="text-[20px] font-[500] text-center">
                    {" "}
                    {t("RedeemSuccessful")}
                  </p>
                  <p className="text-[14px] text-[#828282] text-center">
                    {t("creditAmount.text1")} ${creditAmount}{" "}
                    {t("creditAmount.text2")}
                  </p>
                </DialogHeader>
              </div>

              <DialogFooter className="px-6 pb-6 sm:pb-6">
                <div className="flex items-center justify-between w-full gap-4">
                  <Button
                    className="bg-gray-900  w-full hover:bg-gray-800 text-white rounded-md"
                    onClick={handleClose}
                  >
                    {t("ViewCredits")}
                  </Button>
                </div>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default RedeemParkModal;
