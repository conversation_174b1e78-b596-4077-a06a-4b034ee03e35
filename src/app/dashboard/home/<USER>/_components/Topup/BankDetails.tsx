"use client";

import React, { useState, useRef } from "react";
import { useTranslations } from 'next-intl';
import { But<PERSON> } from "@/components/ui/button";
import { Copy, Upload } from "lucide-react";
import { MoonLoader } from "react-spinners";
import { toast } from "sonner";
import { useSubmitQueryFormData } from "@/services/api_hooks";

interface BankDetailsProps {
  onCancel: () => void;
  onProceed: () => void;
  currency: string;
  amount: number;
  credits: string;
}

export const BankDetails: React.FC<BankDetailsProps> = ({
  onCancel,
  onProceed,
  currency,
  amount,
  credits,
}) => {

  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.TopUp.BankDetails");
  const tTopUp = useTranslations("DashboardCredits.TopUp");
  const tGlobal = useTranslations("global");
  const tForm = useTranslations("Form");

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Custom hook for file upload
  const { mutateAsync: submitReceipt, isPending: submittingReceipt } =
    useSubmitQueryFormData("/api/payment/bank_wire/upload", "POST", {
      onSuccess(response: any) {
        toast.success(tTopUp("toast.ReceiptUploaded"), {
          position: "top-right",
          className: "p-4",
        });
        onProceed(); // Close modal after successful upload
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || tTopUp("toast.UploadFailed"), {
          position: "top-right",
          className: "p-4",
        });
      },
    });

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(tTopUp("toast.CopiedToClipboard", { label }), {
      position: "top-right",
      className: "p-4",
    });
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = [
        "application/pdf",
        "image/jpeg",
        "image/jpg",
        "image/png",
      ];
      if (!allowedTypes.includes(file.type)) {
        toast.error(tTopUp("toast.UploadPDForJPEGFile"), {
          position: "top-right",
          className: "p-4",
        });
        return;
      }

      // Validate file size (5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        toast.error(tTopUp("toast.FileSizeLessThan5MB"), {
          position: "top-right",
          className: "p-4",
        });
        return;
      }

      setSelectedFile(file);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleSubmitReceipt = async () => {
    console.log("handleSubmitReceipt called");
    if (!selectedFile) {
      toast.error(tTopUp("toast.SelectFileToUpload"), {
        position: "top-right",
        className: "p-4",
      });
      return;
    }

    const formData = new FormData();
    formData.append("file", selectedFile);
    formData.append("currency", currency ?? "");
    formData.append("amount", (amount ?? 0).toString());
    formData.append("credits", credits ?? "");

    console.log("Submitting receipt with formData:", formData);

    try {
      await submitReceipt(formData);
    } catch (error) {
      // Error handling is done in the hook's onError callback
    }
  };

  return (
    <div className="space-y-1 overflow-auto max-h-[80vh]">
      <div className=" mb-2">
        <p className="text-sm text-gray-600">
          {t("description")}
        </p>
      </div>
      <>
        <div className="space-y-2">
          {/* Bank Name */}
          <div className="flex justify-between items-center py-2 border-b border-gray-100">
            <span className="text-sm font-medium text-gray-700">
              {t("BankName.text")}
            </span>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-900">
                {t("BankName.description")}
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-auto"
                onClick={() =>
                  copyToClipboard("DBS BANK (HONG KONG) LTD", "Bank name")
                }
              >
                <Copy className="h-4 w-4 text-gray-500" />
              </Button>
            </div>
          </div>

          {/* Account Number */}
          <div className="flex justify-between items-center py-2 border-b border-gray-100">
            <span className="text-sm font-medium text-gray-700">
              {t("AccountNumber.text")}
            </span>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-900">
                {t("AccountNumber.description")}
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-auto"
                onClick={() =>
                  copyToClipboard("*********", "Account number")
                }
              >
                <Copy className="h-4 w-4 text-gray-500" />
              </Button>
            </div>
          </div>

          {/* Account Name */}
          <div className="flex justify-between items-center py-2 border-b border-gray-100">
            <span className="text-sm font-medium text-gray-700">
              {t("AccountName.text")}
            </span>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-900">
                {t("AccountName.description")}
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-auto"
                onClick={() =>
                  copyToClipboard("Alpharithm Investments Limited", "Account name")
                }
              >
                <Copy className="h-4 w-4 text-gray-500" />
              </Button>
            </div>
          </div>

          {/* SWIFT Code */}
          <div className="flex justify-between items-center py-2 border-b border-gray-100">
            <span className="text-sm font-medium text-gray-700">
              {t("SWIFTCode.text")}
            </span>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-900">
                {t("SWIFTCode.description")}
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-auto"
                onClick={() =>
                  copyToClipboard("DHBKHKHH", "SWIFT code")
                }
              >
                <Copy className="h-4 w-4 text-gray-500" />
              </Button>
            </div>
          </div>

          {/* Bank Code */}
          <div className="flex justify-between items-center py-2 border-b border-gray-100">
            <span className="text-sm font-medium text-gray-700">
              {t("BankCode.text")}
            </span>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-900">
                {t("BankCode.description")}
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-auto"
                onClick={() =>
                  copyToClipboard("016", "Bank code")
                }
              >
                <Copy className="h-4 w-4 text-gray-500" />
              </Button>
            </div>
          </div>

          {/* Branch */}
          <div className="flex justify-between items-center py-2 border-b border-gray-100">
            <span className="text-sm font-medium text-gray-700">
              {t("Branch.text")}
            </span>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-900">
                {t("Branch.description")}
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-auto"
                onClick={() =>
                  copyToClipboard("478", "Branch")
                }
              >
                <Copy className="h-4 w-4 text-gray-500" />
              </Button>
            </div>
          </div>

          {/* Address */}
          <div className="flex justify-between items-start py-2 border-b border-gray-100">
            <span className="text-sm font-medium text-gray-700">
              {t("Address.text")}
            </span>
            <div className="flex items-start gap-2 max-w-[60%]">
              <span className="text-sm text-gray-900 text-right">
                {t("Address.description")}
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-auto flex-shrink-0"
                onClick={() =>
                  copyToClipboard("G/F, The Center, 99 Queen's Road Central, Central, Hong Kong", "Address")
                }
              >
                <Copy className="h-4 w-4 text-gray-500" />
              </Button>
            </div>
          </div>
        </div>

        {/* Upload section */}
        <div className="mt-2">
          <div className="text-center mb-4">
            <p className="text-xs text-gray-600">
              — {t("UploadInfo.description")}
              —
            </p>
          </div>

          <div
            className={`border-2 border-dashed rounded-lg p-3 text-center cursor-pointer transition-colors ${
              selectedFile
                ? "border-green-300 bg-green-50"
                : "border-gray-300 hover:border-gray-400"
            }`}
            onClick={handleUploadClick}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept=".pdf,.jpeg,.jpg,.png"
              onChange={handleFileSelect}
              className="hidden"
            />

            <div className="space-y-2">
              <div
                className={`${
                  selectedFile ? "text-green-500" : "text-gray-400"
                }`}
              >
                {selectedFile ? (
                  <Upload className="mx-auto h-8 w-8" />
                ) : (
                  <svg
                    className="mx-auto h-8 w-8"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                    />
                  </svg>
                )}
              </div>
              {selectedFile ? (
                <div>
                  <p className="text-sm text-green-700 font-medium">
                    {selectedFile.name}
                  </p>
                  <p className="text-xs text-green-600">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)}{t("UploadInfo.MB")}
                  </p>
                </div>
              ) : (
                <div>
                  <p className="text-sm text-gray-600">
                    {t("UploadInfo.notes.note1")}
                  </p>
                  <p className="text-xs text-gray-400">
                    {t("UploadInfo.notes.note2")}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Buttons */}
        <div className="flex justify-between mt-8">
          <Button variant="outline" onClick={onCancel}>
            {tGlobal("Back")}
          </Button>
          <Button
            className="bg-[#0F172A] hover:bg-[#1E293B] text-white flex-1 ml-4 disabled:opacity-50"
            onClick={handleSubmitReceipt}
            disabled={!selectedFile || submittingReceipt}
          >
            {submittingReceipt ? (
              <div className="flex items-center gap-2">
                <MoonLoader color="white" size={15} />
                {tForm("Button.Uploading")}
              </div>
            ) : tForm("Button.MadePayment")}
          </Button>
        </div>
      </>
    </div>
  );
};
