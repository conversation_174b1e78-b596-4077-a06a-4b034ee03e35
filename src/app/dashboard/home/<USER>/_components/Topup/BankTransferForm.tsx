import { useState } from "react";
import { useTranslations } from 'next-intl';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import ReactCountryFlag from "react-country-flag";
import { Button } from "@/components/ui/button";
import {
  ChevronDown,
  CoinsIcon,
  CreditCard,
  Landmark,
  QrCode,
  WalletCards,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import MoonLoader from "react-spinners/MoonLoader";
import { useSubmitQuery } from "@/services/api_hooks";
import { toast } from "sonner";
import Image from "next/image";
import { BankDetails } from "./BankDetails";

export const BankTransferForm = ({
  numCredits,
  setNumCredits,
  selectedCurrency,
  setSelectedCurrency,
  currencies,
  flagMap,
  onProceed,
  onCancel,
  isLoading,
  paymentMethod,
  setSelectedPaymentMethod,
  selectedPaymentMethod,
  recentlyUsed,
  onNavigateToStep,
  closeDialog,
}: any) => {

  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.TopUp");
  const tBankTransferForm = useTranslations("DashboardCredits.TopUp.BankTransferForm");
  const tGlobal = useTranslations("global");
  const tForm = useTranslations("Form");

  const handleCreditsChange = (e: any) => {
    const value = e.target.value.replace(/[^\d.]/g, "");
    setNumCredits(value);
  };

  const isRecentlyUsed = recentlyUsed?.is_active === "bank";

  const [isSuccess, setIsSuccess] = useState(false);
  const [creditAmount, setCreditAmount] = useState("");

  // Payment options for the dropdown
  const paymentOptions = [
    {
      value: "usd",
      id: "usd",
      icon: <CreditCard className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.usd"),
    },
    {
      value: "stable",
      id: "stable",
      icon: <CoinsIcon className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.stable"),
    },
    {
      value: "bank",
      id: "bank",
      icon: <Landmark className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.bank"),
    },
    {
      value: "yedpay",
      id: "yedpay",
      icon: <QrCode className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.yedpay"),
    },
  ];

  const handlePaymentMethodChange = (newMethod: string) => {
    setSelectedPaymentMethod(newMethod);

    if (newMethod === "usd") {
      onNavigateToStep("topUp");
    } else if (newMethod === "yedpay") {
      onNavigateToStep("topUp");
      // Force HKD currency for Wechat/UnionPay
      const hkd = currencies.find((c: any) => c.code === "HKD");
      if (hkd) setSelectedCurrency(hkd);
      // Stay on topUp step
    } else if (newMethod === "stable") {
      onNavigateToStep("cryptoTopUp");
    }
    // Stay on current step for "bank"
  };

  const findIcon = () => {
    return paymentOptions.find(
      (paymt) => paymt.value === selectedPaymentMethod
    );
  };

  const {
    mutateAsync: request,
    isPending: loading,
    data,
  } = useSubmitQuery("/api/payment/bank_wire", "POST", {
    onSuccess(response: any) {
      // toast.success("Request sent successfully", {
      //   position: "top-right",
      //   className: "p-4",
      // });

      // Only show BankDetails if recently used is active (i.e., "Proceed to Pay" flow)
      if (isRecentlyUsed) {
        setIsSuccess(true);
      } else if (closeDialog) {
        closeDialog();
      }
    },
    onError(err: any) {
      toast.error(err?.response?.data?.error || t("toast.NotSuccessful"), {
        position: "top-right",
        className: "p-4",
      });
    },
  });

  const handleBankRequest = async () => {
    const calculatedAmount =
      numCredits && selectedCurrency
        ? parseFloat(numCredits) * parseFloat(selectedCurrency.rate)
        : 0;

    request({
      amount: calculatedAmount,
      currency: selectedCurrency?.code,
    });
  };

  // Calculate “minimum allowed” in the current currency:
  // (1,000 USD) × (selectedCurrency.rate)
  const minUsdEquivalent = 1000;
  const minCreditsNeeded = selectedCurrency
    ? minUsdEquivalent / parseFloat(selectedCurrency.rate)
    : minUsdEquivalent;

  const bankDetailsProps = {
    onCancel,
    onProceed,
    currency: selectedCurrency?.code ?? "",
    amount:
      numCredits && selectedCurrency
        ? parseFloat(numCredits) * parseFloat(selectedCurrency.rate)
        : 0,
    credits: numCredits ?? "",
  };

  return (
    <>
      {isSuccess ? (
        <BankDetails {...bankDetailsProps} />
      ) : (
        <div className="space-y-6">
          <div className="space-y-4">
            {/* Credits to add field */}
            <div className="space-y-2">
              <label
                htmlFor="credits"
                className="text-sm font-medium text-gray-700"
              >
                {t("CreditsToAdd")}
              </label>
              <div className="relative flex flex-col md:flex-row justify-between">
                <input
                  id="credits"
                  type="text"
                  value={
                    numCredits
                      ? new Intl.NumberFormat("en-US").format(
                          parseFloat(numCredits)
                        )
                      : ""
                  }
                  onChange={handleCreditsChange}
                  className="pl-2 h-10 w-full border-b border-t-0 border-l-0 border-r-0 rounded-none focus:outline-none focus:ring-transparent focus-visible:ring-transparent outline-none ring-transparent shadow-none focus:shadow-none"
                  placeholder="Minimum of 1,000 credits"
                />
              </div>
            </div>

            {/* Amount to add field */}
            <div className="space-y-2">
              <label
                htmlFor="bank-amount"
                className="text-sm font-medium text-gray-700"
              >
                {t("AmountToAdd")}
              </label>

              <DropdownMenu>
                <div className="flex w-full">
                  <input
                    id="bank-amount"
                    type="text"
                    value={
                      numCredits && selectedCurrency
                        ? new Intl.NumberFormat("en-US", {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          }).format(
                            parseFloat(numCredits) *
                              parseFloat(selectedCurrency.rate)
                          )
                        : "0.00"
                    }
                    readOnly
                    className="pl-2 w-full border-b border-t-0 border-l-0 border-r-0 rounded-none focus:outline-none focus:ring-transparent focus-visible:ring-transparent outline-none ring-transparent shadow-none focus:shadow-none bg-gray-50 text-gray-600"
                    placeholder="0"
                  />

                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-25 h-10 px-3 justify-between border-gray-300 border-none hover:bg-gray-50 rounded-[5px]"
                    >
                      <div className="flex items-center">
                        <ReactCountryFlag
                          countryCode={flagMap[selectedCurrency?.code]}
                          svg
                          style={{ width: "1.25em", height: "1em" }}
                          className="mr-2 rounded-sm"
                          aria-label={selectedCurrency?.code}
                        />
                        <span className="font-medium text-xs">
                          {selectedCurrency?.code}
                        </span>
                      </div>
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </Button>
                  </DropdownMenuTrigger>
                </div>

                <DropdownMenuContent
                  className="min-w-[350px] p-1 bg-white border border-gray-200 shadow-lg rounded-md"
                  align="end"
                  sideOffset={4}
                >
                  {currencies.map((currency: any) => (
                    <DropdownMenuItem
                      key={currency.code}
                      className="flex items-center justify-between px-3 py-2.5 cursor-pointer hover:bg-gray-50 rounded-sm focus:bg-gray-50 min-h-[44px]"
                      onClick={() => setSelectedCurrency(currency)}
                    >
                      <div className="flex items-center">
                        <ReactCountryFlag
                          countryCode={flagMap[currency.code]}
                          svg
                          style={{ width: "1.25em", height: "1em" }}
                          className="mr-2 rounded-sm"
                          aria-label={currency.code}
                        />
                        <span className="font-medium text-sm text-gray-900">
                          {currency.code}
                        </span>
                      </div>
                      <span className="text-sm text-gray-400 ml-4">
                        {currency.rate}
                      </span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Payment Method dropdown - only show if recentlyUsed.is_active exists */}
            {recentlyUsed?.is_active && (
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  {t("PaymentMethod.text")}
                </label>
                <DropdownMenu>
                  <div className="flex w-full">
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full h-10 px-3 justify-between rounded-[5px] border-none border-gray-300 hover:bg-gray-50"
                      >
                        <div className="flex items-center gap-2">
                          <div className="bg-[#0F172A] p-1 rounded-full">
                            {findIcon()?.icon}
                          </div>
                          <span className="font-medium text-sm">
                            {findIcon()?.label}
                          </span>
                        </div>
                        <ChevronDown className="h-4 w-4 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                  </div>

                  <DropdownMenuContent
                    className="min-w-[390px] p-1 bg-white border border-gray-200 shadow-lg rounded-md"
                    align="end"
                    sideOffset={4}
                  >
                    {paymentOptions.map((method: any) => (
                      <DropdownMenuItem
                        key={method.id}
                        className="flex items-center px-3 py-2.5 cursor-pointer hover:bg-gray-50 rounded-sm focus:bg-gray-50 min-h-[44px]"
                        onClick={() => handlePaymentMethodChange(method.value)}
                      >
                        <div className="bg-[#0F172A] p-2 rounded-full">
                          {method.icon}
                        </div>
                        <span className="text-sm ml-4">{method.label}</span>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}

            {/* Warning note */}
            {!isRecentlyUsed && (
              <div className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded-md">
                <p className="text-sm text-orange-700">
                  <strong>{tBankTransferForm("Note.text1")}</strong> {tBankTransferForm("Note.text2")}
                </p>
              </div>
            )}
          </div>

          <div
            className={`flex ${
              recentlyUsed?.is_active ? "justify-end" : "justify-between"
            } mt-10`}
          >
            {!recentlyUsed?.is_active && (
              <Button variant="outline" onClick={onCancel}>
                {tGlobal("Back")}
              </Button>
            )}
            <Button
              className={`bg-[#0F172A] max-w-[200px] hover:bg-[#1E293B] text-white disabled:opacity-[0.5] ${
                recentlyUsed?.is_active ? "w-full" : "flex-1 ml-4"
              }`}
              onClick={isRecentlyUsed ? onProceed : handleBankRequest}
              disabled={
                !numCredits ||
                parseFloat(numCredits) < 1000 ||
                (isRecentlyUsed ? false : loading) // Don't disable for recently used
              }
            >
              {isRecentlyUsed ? (
                t("ProceedToPay")
              ) : loading ? (
                <div className="flex items-center gap-2">
                  <MoonLoader color="white" size={15} loading={isLoading} />
                  {tForm("Button.Sending")}
                </div>
              ) : tForm("Button.SendRequest")}
            </Button>
          </div>
        </div>
      )}

      {/* BankDetails component - show only if isSuccess */}
      {isSuccess && <BankDetails {...bankDetailsProps} />}
    </>
  );
};
