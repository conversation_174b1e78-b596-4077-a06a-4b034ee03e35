import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useTranslations } from 'next-intl';

export const CoinSelection = ({ selectedCoin, setSelectedCoin, onCancel, onProceed }: any) => {

  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.TopUp.CoinSelection");
  const tGlobal = useTranslations("global");

  return (
    <div className="space-y-6">
      <RadioGroup
        value={selectedCoin}
        onValueChange={setSelectedCoin}
        className="space-y-4"
      >
        <Label
          htmlFor="usdt"
          className={`flex items-center justify-between rounded-lg border p-4 ${
            selectedCoin === "USDT"
              ? "border-primary"
              : "border-gray-200"
          }`}
        >
          <div className="flex items-center gap-3">
            <div className="bg-black p-2 h-10 w-10 grid place-content-center rounded-full">
              <span className="text-white font-bold">{t("T")}</span>
            </div>
            <div>
              <p className="font-medium">{t("USDT.text")}</p>
              <p className="text-sm text-gray-500">{t("USDT.description")}</p>
            </div>
          </div>
          <RadioGroupItem
            value="USDT"
            id="usdt"
            className={selectedCoin === "USDT" ? " text-green-500" : ""}
          />
        </Label>

        <Label
          htmlFor="usdc"
          className={`flex items-center justify-between rounded-lg border p-4 ${
            selectedCoin === "USDC"
              ? "border-primary"
              : "border-gray-200"
          }`}
        >
          <div className="flex items-center gap-3">
            <div className="bg-blue-500 p-2 h-10 w-10 grid place-content-center rounded-full">
              <span className="text-white font-bold">{t("C")}</span>
            </div>
            <div>
              <p className="font-medium">{t("USDC.text")}</p>
              <p className="text-sm text-gray-500">{t("USDC.description")}</p>
            </div>
          </div>
          <RadioGroupItem
            value="USDC"
            id="usdc"
            className={selectedCoin === "USDC" ? " text-green-500" : ""}
          />
        </Label>
      </RadioGroup>

      <div className="flex justify-between mt-4">
        <Button variant="outline" onClick={onCancel}>
          {tGlobal("Back")}
        </Button>
        <Button
          className="bg-[#0F172A] hover:bg-[#1E293B] text-white flex gap-1"
          onClick={onProceed}
          disabled={!selectedCoin}
        >
          {tGlobal("Proceed")}
        </Button>
      </div>
    </div>
  );
};