import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import ReactCountryFlag from "react-country-flag";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import Moon<PERSON>oa<PERSON> from "react-spinners/MoonLoader";
import {
  ChevronDown,
  CoinsIcon,
  CreditCard,
  Landmark,
  QrCode,
  WalletCards,
} from "lucide-react";
import { useTranslations } from 'next-intl';

export const CryptoTopUpForm = ({
  numCredits,
  setNumCredits,
  selectedCoin,
  setSelectedCoin,
  onProceed,
  onCancel,
  isLoading,
  paymentMethod,
  setSelectedPaymentMethod,
  selectedPaymentMethod,
  recentlyUsed,
  onNavigateToStep,
}: any) => {

  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.TopUp");
  const tCryptoTopUpForm = useTranslations("DashboardCredits.TopUp.CryptoTopUpForm");
  const tGlobal = useTranslations("global");
  const tForm = useTranslations("Form");

  const handleCreditsChange = (e: any) => {
    const value = e.target.value.replace(/[^\d.]/g, "");
    setNumCredits(value);
  };

  // Crypto coin options
  const cryptoCoins = [
    {
      code: "USDT",
      name: "USDT",
      rate: "1.00",
      icon: "T",
      bgColor: "bg-black",
    },
    {
      code: "USDC",
      name: "USDC",
      rate: "1.00",
      icon: "C",
      bgColor: "bg-blue-500",
    },
  ];

  // Set default to USDT if no coin is selected
  const currentCoin = selectedCoin || "USDT";
  if (!selectedCoin) {
    setSelectedCoin("USDT");
  }

  const paymentOptions = [
    {
      value: "usd",
      id: "usd",
      icon: <CreditCard className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.usd"),
    },
    {
      value: "stable",
      id: "stable",
      icon: <CoinsIcon className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.stable"),
    },
    {
      value: "bank",
      id: "bank",
      icon: <Landmark className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.bank"),
    },
    {
      value: "yedpay",
      id: "yedpay",
      icon: <QrCode className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.yedpay"),
    },
  ];

  const handlePaymentMethodChange = (newMethod: string) => {
    setSelectedPaymentMethod(newMethod);

    if (newMethod === "usd" || newMethod === "yedpay") {
      onNavigateToStep("topUp");
    } else if (newMethod === "bank") {
      onNavigateToStep("bankTransfer");
    }
    // Stay on current step for "stable"
  };

  const findIcon = () => {
    return paymentOptions.find(
      (paymt) => paymt.value === selectedPaymentMethod
    );
  };

  return (
    <div>
      <div className="space-y-7">
        {/* Credits to add field */}
        <div className="space-y-2">
          <label
            htmlFor="credits"
            className="text-sm font-medium text-gray-700"
          >
            {t("CreditsToAdd")}
          </label>
          <div className="relative flex flex-col md:flex-row justify-between">
            <input
              id="credits"
              type="text"
              value={
                numCredits
                  ? new Intl.NumberFormat("en-US").format(
                      parseFloat(numCredits)
                    )
                  : ""
              }
              onChange={handleCreditsChange}
              className="pl-2 h-10 w-full border-b border-t-0 border-l-0 border-r-0 rounded-none focus:outline-none focus:ring-transparent focus-visible:ring-transparent outline-none ring-transparent shadow-none focus:shadow-none"
              placeholder="10"
            />
          </div>
        </div>

        {/* Amount to add field */}
        <div className="space-y-2">
          <label
            htmlFor="crypto-amount"
            className="text-sm font-medium text-gray-700"
          >
            {t("AmountToAdd")}
          </label>

          <DropdownMenu>
            <div className="flex w-full">
              <input
                id="crypto-amount"
                type="text"
                value={numCredits || "0"}
                readOnly
                className="pl-2 w-full border-b border-t-0 border-l-0 border-r-0 rounded-none focus:outline-none focus:ring-transparent focus-visible:ring-transparent outline-none ring-transparent shadow-none focus:shadow-none bg-gray-50 text-gray-600"
                placeholder="0"
              />

              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="w-25 h-10 px-3 justify-between border-gray-300 border-none hover:bg-gray-50 rounded-[5px]"
                >
                  <div className="flex items-center">
                    <div
                      className={`${
                        cryptoCoins.find((c) => c.code === currentCoin)?.bgColor
                      } p-1 h-5 w-5 grid place-content-center rounded-full mr-2`}
                    >
                      <span className="text-white font-bold text-xs">
                        {cryptoCoins.find((c) => c.code === currentCoin)?.icon}
                      </span>
                    </div>
                    <span className="font-medium text-xs">{currentCoin}</span>
                  </div>
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
            </div>

            <DropdownMenuContent
              className="min-w-[350px] p-1 bg-white border border-gray-200 shadow-lg rounded-md"
              align="end"
              sideOffset={4}
            >
              {cryptoCoins.map((coin) => (
                <DropdownMenuItem
                  key={coin.code}
                  className="flex items-center justify-between px-3 py-2.5 cursor-pointer hover:bg-gray-50 rounded-sm focus:bg-gray-50 min-h-[44px]"
                  onClick={() => setSelectedCoin(coin.code)}
                >
                  <div className="flex items-center">
                    <div
                      className={`${coin.bgColor} p-1 h-6 w-6 grid place-content-center rounded-full mr-2`}
                    >
                      <span className="text-white font-bold text-xs">
                        {coin.icon}
                      </span>
                    </div>
                    <span className="font-medium text-sm text-gray-900">
                      {coin.code}
                    </span>
                  </div>
                  <span className="text-sm text-gray-400 ml-4">1:1</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Rate display below dropdown */}
          <p className="text-xs text-end text-gray-500 mt-2">
            {tCryptoTopUpForm("AmountToAdd.description", { currentCoin })}
          </p>
        </div>

        {/* Payment Method dropdown - only show if recentlyUsed.is_active exists */}
        {recentlyUsed?.is_active && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              {t("PaymentMethod.text")}
            </label>
            <DropdownMenu>
              <div className="flex w-full">
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full h-10 px-3 justify-between rounded-[5px] border-none border-gray-300 hover:bg-gray-50"
                  >
                    <div className="flex items-center gap-2">
                      <div className="bg-[#0F172A] p-1 rounded-full">
                        {findIcon()?.icon}
                      </div>
                      <span className="font-medium text-sm">
                        {findIcon()?.label}
                      </span>
                    </div>
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  </Button>
                </DropdownMenuTrigger>
              </div>

              <DropdownMenuContent
                className="min-w-[390px]  p-1 bg-white border border-gray-200 shadow-lg rounded-md"
                align="end"
                sideOffset={4}
              >
                {paymentOptions.map((method: any) => (
                  <DropdownMenuItem
                    key={method.id}
                    className="flex items-center px-3 py-2.5 cursor-pointer hover:bg-gray-50 rounded-sm focus:bg-gray-50 min-h-[44px]"
                    onClick={() => handlePaymentMethodChange(method.value)}
                  >
                    <div className="bg-[#0F172A] p-2 rounded-full">
                      {method.icon}
                    </div>
                    <span className="text-sm ml-4">{method.label}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      <div
        className={`flex ${
          recentlyUsed?.is_active ? "justify-end" : "justify-between"
        } mt-10`}
      >
        {!recentlyUsed?.is_active && (
          <Button variant="outline" onClick={onCancel}>
            {tGlobal("Back")}
          </Button>
        )}
        <Button
          className={`bg-[#0F172A] max-w-[200px] hover:bg-[#1E293B] text-white disabled:opacity-[0.5] ${
            recentlyUsed?.is_active ? "w-full" : "flex-1 ml-4"
          }`}
          onClick={onProceed}
          disabled={
            numCredits === "0" || !numCredits || !currentCoin || isLoading
          }
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <MoonLoader color="white" size={15} loading={isLoading} />
              {tForm("Button.Processing")}
            </div>
          ) : tGlobal("Proceed")}
        </Button>
      </div>
    </div>
  );
};
