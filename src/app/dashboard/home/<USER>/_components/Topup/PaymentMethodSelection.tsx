import { useTranslations } from 'next-intl';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Building, CoinsIcon, CreditCard, Landmark, QrCode, WalletCards } from "lucide-react";


interface PaymentOption {
  value: string;
  id: string;
  icon: React.ReactNode;
  label: string;
}

export const PaymentMethodSelection = ({
  paymentMethod,
  setPaymentMethod,
  onCancel,
  onProceed,
}: any) => {

  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.TopUp");
  const tGlobal = useTranslations("global");

  const paymentOptions: PaymentOption[] = [
    {
      value: "usd",
      id: "usd",
      icon: <CreditCard className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.usd"),
    },
    {
      value: "stable",
      id: "stable",
      icon: <CoinsIcon className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.stable"),
    },
    {
      value: "bank",
      id: "bank",
      icon: <Landmark className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.bank"),
    },
    {
      value: "yedpay",
      id: "yedpay",
      icon: <QrCode className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.yedpay"),
    },
  ];

  return (
    <div className="space-y-6">
      <RadioGroup
        value={paymentMethod}
        onValueChange={setPaymentMethod}
        className="space-y-4"
      >
        {paymentOptions.map((opt) => (
          <Label
            key={opt.value}
            htmlFor={opt.id}
            className={`flex items-center justify-between rounded-lg border py-2 px-4 ${
              paymentMethod === opt.value ? "border-primary" : "border-gray-200"
            }`}
          >
            <div className="flex items-center gap-3">
              <div className="bg-[#0F172A] p-2 rounded-full">{opt.icon}</div>
              {opt.label}
            </div>

            <RadioGroupItem
              value={opt.value}
              id={opt.id}
              className={paymentMethod === opt.value ? "text-green-500" : ""}
            />
          </Label>
        ))}
      </RadioGroup>

      <div className="flex justify-between pt-4">
        <Button variant="outline" onClick={onCancel}>
          {tGlobal("Cancel")}
        </Button>
        <Button
          className="bg-[#0F172A] hover:bg-[#1E293B] text-white px-6"
          onClick={onProceed}
        >
          {tGlobal("Proceed")}
        </Button>
      </div>
    </div>
  );
};
