import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useSubmitQuery } from "@/services/api_hooks";
import { Copy } from "lucide-react";
import QRCode from "react-qr-code";
import { toast } from "sonner";
import { useState } from "react";
import { useTranslations } from 'next-intl';

export const ScanToPay = ({
  paymentURI,
  walletAddress,
  onCancel,
  onFinish,
  copyToClipboard,
}: any) => {

  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.TopUp.ScanToPay");
  const tTopUp = useTranslations("DashboardCredits.TopUp")
  const tGlobal = useTranslations("global");

  const [txHash, setTxHash] = useState("");

  const { mutateAsync: submitHash, isPending: submittingHash } = useSubmitQuery(
    "/api/payment/crypto/manual-credit",
    "POST",
    {
      onSuccess(response: any) {
        toast.success(tTopUp("toast.ReceiptUploaded"), {
          position: "top-right",
          className: "p-4",
        });
        onFinish();
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || tTopUp("toast.UploadFailed"), {
          position: "top-right",
          className: "p-4",
        });
      },
    }
  );

  const handleConfirmPayment = async () => {
    if (!txHash.trim()) {
      toast.error(tTopUp("toast.EnterTransactionHash"), {
        position: "top-right",
        className: "p-4",
      });
      return;
    }

    try {
      await submitHash({ txHash: txHash.trim() });
    } catch (error) {
      // Error handling is already done in the mutation's onError callback
      console.error("Payment confirmation failed:", error);
    }
  };

  return (
    <div className="space-y-2">
      <div className="space-y-2">
        <p className="text-xs text-gray-600">
          {t("contents.line1")}
        </p>
      </div>

      <div className="text-xs text-[#753700] font-[500] bg-[#FFF4E1] p-3 rounded-sm">
        {t("contents.line2.text1")} <span className="font-semibold">{t("contents.line2.text2")}</span> {t("contents.line2.text3")}
      </div>

      <div className="flex justify-center p-4">
        <div className=" w-[250px] h-[250px]  rounded-lg">
          <QRCode
            value={paymentURI}
            size={200}
            style={{ height: "auto", maxWidth: "100%", width: "100%" }}
            viewBox={`0 0 200 200`}
            level="H"
            bgColor="#FFFFFF"
            fgColor="#000000"
          />
        </div>
      </div>

      <div className="text-center text-sm text-gray-600">
        <p>{tGlobal("ai-wk")}</p>
        <p className="text-xs mt-1 break-all">{walletAddress}</p>
        <Button
          variant="outline"
          size="sm"
          className="mt-2 flex items-center gap-1 text-xs mx-auto text-blue-700"
          onClick={() => copyToClipboard(walletAddress)}
        >
          <Copy className="h-4 w-4" color="blue" /> {t("CopyAddress")}
        </Button>
      </div>

      <div className="space-y-2 mt-5">
        <p className="text-xs text-gray-600">
          {t("contents.line3")}
        </p>

        <div>
          <label htmlFor="hash" className="text-sm">
            {t("TransactionHash.text")}
          </label>
          <Input
            id="hash"
            placeholder={t("TransactionHash.description")}
            value={txHash}
            onChange={(e) => setTxHash(e.target.value)}
            disabled={submittingHash}
            className=""
          />
        </div>
      </div>

      <div className="flex justify-between pt-2">
        <Button variant="outline" onClick={onCancel} disabled={submittingHash}>
          {tGlobal("Back")}
        </Button>
        <Button
          className="bg-[#0F172A] hover:bg-[#1E293B] text-white px-6"
          onClick={handleConfirmPayment}
          disabled={submittingHash || !txHash.trim()}
        >
          {submittingHash ? t("Confirming") : t("ConfirmPayment")}
        </Button>
      </div>
    </div>
  );
};
