import { useTranslations } from 'next-intl';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import ReactCountryFlag from "react-country-flag";
import { Button } from "@/components/ui/button";
import {
  ChevronDown,
  CoinsIcon,
  CreditCard,
  Landmark,
  QrCode,
  WalletCards,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import MoonLoader from "react-spinners/MoonLoader";


export const TopUpForm = ({
  numCredits,
  setNumCredits,
  selectedCurrency,
  setSelectedCurrency,
  currencies,
  flagMap,
  autoRenew,
  setAutoRenew,
  onProceed,
  onCancel,
  isLoading,
  paymentMethod,
  setSelectedPaymentMethod,
  selectedPaymentMethod,
  recentlyUsed,
  onNavigateToStep,
}: any) => {

  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.TopUp");
  const tGlobal = useTranslations("global");

  const paymentOptions = [
    {
      value: "usd",
      id: "usd",
      icon: <CreditCard className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.usd"),
    },
    {
      value: "stable",
      id: "stable",
      icon: <CoinsIcon className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.stable"),
    },
    {
      value: "bank",
      id: "bank",
      icon: <Landmark className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.bank"),
    },
    {
      value: "yedpay",
      id: "yedpay",
      icon: <QrCode className="h-5 w-5 text-white" />,
      label: t("PaymentMethod.yedpay"),
    },
  ];

  const handleCreditsChange = (e: any) => {
    const value = e.target.value.replace(/[^\d.]/g, "");
    setNumCredits(value);
  };

  const getButtonText = () => {
    if (isLoading) {
      return (
        <div className="flex items-center gap-2">
          <MoonLoader color="white" size={15} loading={isLoading} />
          {t("Processing")}
        </div>
      );
    }

    return t("ProceedToPay");
  };

  const handlePaymentMethodChange = (newMethod: string) => {
    setSelectedPaymentMethod(newMethod);

    // Handle method-specific logic and navigation
    if (newMethod === "usd") {
      // Stay on topUp step for credit/debit card
      // No navigation needed as we're already on the right step
    } else if (newMethod === "yedpay") {
      // Force HKD currency for Wechat/UnionPay
      const hkd = currencies.find((c: any) => c.code === "HKD");
      if (hkd) setSelectedCurrency(hkd);
      // Stay on topUp step
    } else if (newMethod === "stable") {
      // Navigate to crypto top up step
      onNavigateToStep("cryptoTopUp");
    } else if (newMethod === "bank") {
      // Navigate to bank transfer step
      onNavigateToStep("bankTransfer");
    }
  };

  // Filter currencies based on payment method
  const getAvailableCurrencies = () => {
    if (paymentMethod === "yedpay") {
      return currencies.filter((currency: any) => currency.code === "HKD");
    }
    return currencies;
  };

  const findIcon = () => {
    const icon = paymentOptions.find((paymt) => {
      return (
        paymt.value === selectedPaymentMethod || paymt.value === paymentMethod
      );
    });
    return icon;
  };

  const availableCurrencies = getAvailableCurrencies();

  return (
    <div>
      <div className="space-y-7">
        {/* Credits to add field - now at the top */}
        <div className="space-y-2">
          <label
            htmlFor="credits"
            className="text-sm font-medium text-gray-700"
          >
            {t("CreditsToAdd")}
          </label>
          <div className="relative flex flex-col md:flex-row justify-between">
            <input
              id="credits"
              type="text"
              value={
                numCredits
                  ? new Intl.NumberFormat("en-US").format(
                      parseFloat(numCredits)
                    )
                  : ""
              }
              onChange={handleCreditsChange}
              className="pl-2 h-10 w-full border-b border-t-0 border-l-0 border-r-0 rounded-none focus:outline-none focus:ring-transparent focus-visible:ring-transparent outline-none ring-transparent shadow-none focus:shadow-none"
              placeholder="10"
            />
          </div>
        </div>

        {/* Amount to add field - now shows calculated amount */}
        <div className="space-y-2">
          <label htmlFor="amount" className="text-sm font-medium text-gray-700">
            {t("AmountToAdd")}
          </label>

          <DropdownMenu>
            <div className="flex w-full">
              <input
                id="amount"
                type="text"
                value={
                  numCredits && selectedCurrency
                    ? new Intl.NumberFormat("en-US").format(
                        parseFloat(numCredits) *
                          parseFloat(selectedCurrency.rate)
                      )
                    : "0"
                }
                readOnly
                className="pl-2 w-full border-b border-t-0 border-l-0 border-r-0 rounded-none focus:outline-none focus:ring-transparent focus-visible:ring-transparent outline-none ring-transparent shadow-none focus:shadow-none bg-gray-50 text-gray-600"
                placeholder="0"
              />

              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="w-24 h-10 px-3 justify-between rounded-[5px] border-none border-gray-300 hover:bg-gray-50 "
                  disabled={paymentMethod === "yedpay"} // Disable dropdown for Alipay/Wechat
                >
                  <div className="flex items-center">
                    <ReactCountryFlag
                      countryCode={flagMap[selectedCurrency?.code]}
                      svg
                      style={{ width: "1.25em", height: "1em" }}
                      className="mr-2 rounded-sm"
                      aria-label={selectedCurrency?.code}
                    />
                    <span className="font-medium text-xs">
                      {selectedCurrency?.code}
                    </span>
                  </div>
                  {paymentMethod !== "yedpay" && (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                </Button>
              </DropdownMenuTrigger>
            </div>

            <DropdownMenuContent
              className="min-w-[350px] p-1 bg-white border border-gray-200 shadow-lg rounded-md"
              align="end"
              sideOffset={4}
            >
              {availableCurrencies.map((currency: any) => (
                <DropdownMenuItem
                  key={currency.code}
                  className="flex items-center justify-between px-3 py-2.5 cursor-pointer hover:bg-gray-50 rounded-sm focus:bg-gray-50 min-h-[44px]"
                  onClick={() => setSelectedCurrency(currency)}
                >
                  <div className="flex items-center">
                    <ReactCountryFlag
                      countryCode={flagMap[currency.code]}
                      svg
                      style={{ width: "1.25em", height: "1em" }}
                      className="mr-2 rounded-sm"
                      aria-label={currency.code}
                    />
                    <span className="font-medium text-sm text-gray-900">
                      {currency.code}
                    </span>
                  </div>
                  <span className="text-sm text-gray-400 ml-4">
                    {currency.rate}
                  </span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Payment method dropdown remains the same */}
        {recentlyUsed?.is_active && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              {t("PaymentMethod.text")}
            </label>
            <DropdownMenu>
              <div className="flex w-full">
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full h-10 px-3 justify-between rounded-[5px] border-none border-gray-300 hover:bg-gray-50"
                  >
                    <div className="flex items-center gap-2">
                      <div className="bg-[#0F172A] p-1 rounded-full">
                        {findIcon()?.icon}
                      </div>
                      <span className="font-medium text-sm">
                        {
                          paymentOptions.find(
                            (opt) => opt.value === selectedPaymentMethod
                          )?.label
                        }
                      </span>
                    </div>
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  </Button>
                </DropdownMenuTrigger>
              </div>

              <DropdownMenuContent
                className="min-w-[390px] p-1 bg-white border border-gray-200 shadow-lg rounded-md"
                align="end"
                sideOffset={4}
              >
                {paymentOptions.map((method: any) => (
                  <DropdownMenuItem
                    key={method.id}
                    className="flex items-center px-3 py-2.5 cursor-pointer hover:bg-gray-50 rounded-sm focus:bg-gray-50 min-h-[44px]"
                    onClick={() => handlePaymentMethodChange(method.value)}
                  >
                    <div className="bg-[#0F172A] p-2 rounded-full">
                      {method.icon}
                    </div>
                    <span className="text-sm ml-4">{method.label}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}

        {/* Auto renew section remains the same */}
        {paymentMethod !== "yedpay" && (
          <div className="flex flex-row items-center justify-between max-w-[626px] mt-5">
            <p className="text-[14px] font-[500]">{t("AutoRenewMonthly")}</p>
            <Switch
              id="auto-renew"
              checked={autoRenew}
              onCheckedChange={setAutoRenew}
            />
          </div>
        )}
      </div>

      <div
        className={`flex ${
          recentlyUsed?.is_active ? "justify-end" : "justify-between"
        } mt-10`}
      >
        {!recentlyUsed?.is_active && (
          <Button variant="outline" onClick={onCancel}>
            {tGlobal("Back")}
          </Button>
        )}
        <Button
          className="bg-[#0F172A] max-w-[200px] hover:bg-[#1E293B] text-white disabled:opacity-[0.5]"
          onClick={() => onProceed(numCredits)}
          disabled={numCredits === "0" || !numCredits || isLoading}
        >
          {getButtonText()}
        </Button>
      </div>
    </div>
  );
};
