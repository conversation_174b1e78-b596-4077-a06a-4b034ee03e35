import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Moon<PERSON>oa<PERSON> from "react-spinners/MoonLoader";
import { useTranslations } from 'next-intl';

export const WalletAddressForm = ({ walletAddress, setWalletAddress, onCancel, onProceed, isLoading }: any) => {
  
  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.TopUp.WalletAddressForm");
  const tGlobal = useTranslations("global");
  const tForm = useTranslations("Form");

  return (
    <div className="space-y-6">
      <div className="space-y-2 border border-blue-100 rounded-lg p-4 bg-[#FFF4E1]">
        <div className="text-sm text-[#753700] font-[500]">
          {t("description")}
          <p className="mt-2">
            {t("info.text1")} {" (ERC20)"} {t("info.text2")}
          </p>
        </div>
      </div>

      <div className="space-y-2">
        <label
          htmlFor="walletAddress"
          className="text-sm font-medium text-gray-700"
        >
          {t("WalletAddress.text")} {"(ERC20)"}
        </label>
        <Input
          id="walletAddress"
          value={walletAddress}
          onChange={(e) => setWalletAddress(e.target.value)}
          className="w-full"
          placeholder={t("WalletAddress.description")}
        />
      </div>

      <div className="flex justify-between pt-4">
        <Button variant="outline" onClick={onCancel}>
          {tGlobal("Back")}
        </Button>
        <Button
          className="bg-[#0F172A] hover:bg-[#1E293B] text-white px-6"
          onClick={onProceed}
          disabled={!walletAddress.trim() || isLoading}
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <MoonLoader color="white" size={15} loading={isLoading} />
              {tForm("Button.Saving")}
            </div>
          ) : tGlobal("Continue")}
        </Button>
      </div>
    </div>
  );
};