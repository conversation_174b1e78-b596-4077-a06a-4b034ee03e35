// TransactionTable.tsx
// @ts-nocheck

"use client";

import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
  DialogDescription,
} from "@/components/ui/dialog";
import { Plus } from "lucide-react";
import { useGetQuery, useSubmitQuery } from "@/services/api_hooks";
import { toast } from "sonner";
import Image from "next/image";
import { PaymentMethodSelection } from "./PaymentMethodSelection";
import { TopUpForm } from "./TopUpForm";
import { CoinSelection } from "./CoinSelection";
import { WalletAddressForm } from "./WalletAddressForm";
import { ScanToPay } from "./ScanToPay";
import { CryptoTopUpForm } from "./CryptoTopUpForm";
import { BankTransferForm } from "./BankTransferForm";
import { BankDetails } from "./BankDetails";
import { Button } from "@/components/ui/button";

interface Transaction {
  type: string;
  timestamp: string;
  amount: string;
  reason: string;
}

// map currency → ISO country code
const flagMap: Record<string, string> = {
  USD: "US",
  EUR: "EU",
  GBP: "GB",
  SGD: "SG",
  AUD: "AU",
  CAD: "CA",
  JPY: "JP",
  CNY: "CN",
  HKD: "HK",
};

const MY_WALLET_ADDRESS = "******************************************";

const TOKEN_CONTRACTS: Record<string, string> = {
  USDT: "******************************************",
  USDC: "******************************************",
};

export default function TopUp() {
  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.TopUp");
  const tGlobal = useTranslations("global");

  const [currentPage, setCurrentPage] = useState(1);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [showEmailSentDialog, setShowEmailSentDialog] = useState(false);
  const [numCredits, setNumCredits] = useState("");
  const [amount, setAmount] = useState("");
  const [value, setValue] = useState("USD");
  const [dialogStep, setDialogStep] = useState<
    | "payment"
    | "topUp"
    | "cryptoTopUp"
    | "bankTransfer"
    | "coinSelection"
    | "walletAddress"
    | "scanToPay"
    | "bankDetails"
  >("payment");
  const [selectedCoin, setSelectedCoin] = useState("");
  const [walletAddress, setWalletAddress] = useState("");
  const [paymentMethod, setPaymentMethod] = useState<
    "usd" | "yedpay" | "stable" | "bank"
  >("usd");
  const [autoRenew, setAutoRenew] = useState(false);

  // 1) NEW: state for currencies (only code + rate)
  const [currencies, setCurrencies] = useState<
    { code: string; rate: number }[]
  >([]);
  // 2) NEW: track which currency is selected (just { code, rate })
  const [selectedCurrency, setSelectedCurrency] = useState<{
    code: string;
    rate: number;
  } | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<{
    code: string;
    rate: number;
  } | null>(null);

  // Fetch exchange‐rates from your API
  const { data, isLoading } = useGetQuery(
    "/auth/exchange-rates",
    ["exchange-rates"],
    {
      onError() {
        toast.error(t("toast.notLoadExchangeRates"));
      },
    }
  );

  const { data: paymentData, isLoading: loadingPaymentData } = useGetQuery(
    "/api/payment/default",
    ["payment-default-methods"],
    {
      onError() {
        toast.error(t("toast.notLoadExchangeRates"));
      },
    }
  );

  function getActiveStatus(paymentMethod) {
    switch (paymentMethod) {
      case "stripe":
        return "usd";
      case "bank_wire":
        return "bank";
      case "crypto":
        return "stable";
      case "yedpay":
        return "yedpay";
      default:
        return "usd"; // fallback
    }
  }

  // Populate recentlyUsed from data
  const recentlyUsed = {
    is_active:
      paymentData && Object.keys(paymentData).length > 0
        ? getActiveStatus(paymentData.payment_method)
        : "",
    currency: paymentData?.currency || "",
  };

  // 3) Whenever `data` arrives, transform data.rates → array of { code, rate }
  useEffect(() => {
    if (data?.rates) {
      const ratesObj = data.rates as Record<string, number>;
      const arr = Object.entries(ratesObj).map(([code, rate]) => ({
        code,
        rate: parseFloat(rate.toFixed(6)), // you can format decimals as you like
      }));
      setCurrencies(arr);

      // Default‐select USD if it exists
      const usd = arr.find((c) => c.code === "USD");
      setSelectedCurrency(usd ?? arr[0]);
    }
  }, [data]);

  // Stripe
  const { mutateAsync: processStripePayment, isPending: processingStripe } =
    useSubmitQuery("/api/payment/stripe/pay", "POST", {
      onSuccess(response: any) {
        if (response?.data?.url) window.location.href = response.data.url;
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || t("toast.PaymentFailed"), {
          position: "top-right",
          className: "p-4",
        });
      },
    });

  // YedPay
  const { mutateAsync: processYedPayPayment, isPending: processingYedPay } =
    useSubmitQuery("/api/payment/yedpay/pay", "POST", {
      onSuccess(response: any) {
        if (response?.data?.url) window.location.href = response.data.url;
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || t("toast.PaymentFailed"), {
          position: "top-right",
          className: "p-4",
        });
      },
    });

  // Wallet address
  const { mutateAsync: saveWallet, isPending: savingWallet } = useSubmitQuery(
    "/api/payment/wallet",
    "POST",
    {
      onSuccess() {
        toast.success(t("toast.WalletAddressSavedSuccessfully"), {
          position: "top-right",
          className: "p-4",
        });
        setDialogStep("scanToPay");
      },
      onError(err: any) {
        toast.error(err.response?.data?.message || t("toast.FailedToSave"), {
          position: "top-right",
          className: "p-4",
        });
      },
    }
  );

  useEffect(() => {
    if (isDialogOpen && recentlyUsed?.is_active) {
      // Set the recently used payment method
      setPaymentMethod(recentlyUsed?.is_active);

      // Set the recently used currency
      const recentCurrency = currencies?.find(
        (c) => c.code?.toLowerCase() === recentlyUsed?.currency?.toLowerCase()
      );
      if (recentCurrency) {
        setSelectedCurrency(recentCurrency);
      }

      // Skip payment method selection and go directly to the appropriate step
      if (recentlyUsed?.is_active === "usd") {
        setDialogStep("topUp");
      } else if (recentlyUsed?.is_active === "yedpay") {
        const hkd = currencies.find((c) => c.code === "HKD");
        if (hkd) setSelectedCurrency(hkd);
        setDialogStep("topUp");
      } else if (recentlyUsed?.is_active === "stable") {
        setDialogStep("cryptoTopUp");
      } else if (recentlyUsed?.is_active === "bank") {
        setDialogStep("bankTransfer"); // Still go to bankTransfer, but it will behave differently
      }
    }
  }, [isDialogOpen, currencies, recentlyUsed?.is_active]);

  const handlePaymentMethodSelection = () => {
    if (paymentMethod === "usd") {
      setDialogStep("topUp");
    } else if (paymentMethod === "yedpay") {
      // Force HKD for Alipay/Wechat
      const hkd = currencies.find((c) => c.code === "HKD");
      if (hkd) setSelectedCurrency(hkd);
      setDialogStep("topUp");
    } else if (paymentMethod === "stable") {
      setDialogStep("cryptoTopUp");
    } else if (paymentMethod === "bank") {
      setDialogStep("bankTransfer");
    }
  };

  const handleStripePayment = async () => {
    if (!selectedCurrency) return;
    try {
      await processStripePayment({
        amount: parseFloat(numCredits) * parseFloat(selectedCurrency?.rate), // Calculate amount from credits
        currency: selectedCurrency?.code,
        auto_renew: autoRenew,
        credits: parseFloat(numCredits),
      });
    } catch (error) {}
  };

  const handleYedPayPayment = async () => {
    if (!selectedCurrency) return;
    try {
      await processYedPayPayment({
        amount: parseFloat(numCredits) * parseFloat(selectedCurrency.rate), // Calculate amount from credits
        currency: selectedCurrency.code,
        auto_renew: autoRenew,
        credits: parseFloat(numCredits),
      });
    } catch (error) {}
  };

  const handleBankTransferRequest = () => {
    // Just close dialog
    handleDialogClose();
  };

  const handleCoinSelection = (coinType: string) => {
    setSelectedCoin(coinType);
  };

  const handleWalletAddressSave = async () => {
    if (!walletAddress.trim()) {
      toast.error(t("toast.PleaseEnterAddress"), {
        position: "top-right",
        className: "p-4",
      });
      return;
    }
    await saveWallet({ wallet_address: walletAddress });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success(t("toast.AddressCopied"), {
      position: "top-right",
      className: "p-4",
    });
  };

  // Update the handleTopUpClick function:
  const handleTopUpClick = () => {
    setIsDialogOpen(true);
    // Only set to "payment" if no recently used method
    if (!recentlyUsed?.is_active) {
      setDialogStep("payment");
    }
  };

  const handleProceed = () => {
    switch (dialogStep) {
      case "payment":
        handlePaymentMethodSelection();
        break;
      case "topUp":
        if (paymentMethod === "usd") handleStripePayment();
        else if (paymentMethod === "yedpay") handleYedPayPayment();
        break;
      case "cryptoTopUp":
        setDialogStep("walletAddress");
        break;
      case "bankTransfer":
        // Check if it's recently used - if so, go to bank details
        if (recentlyUsed?.is_active === "bank") {
          setDialogStep("bankDetails");
        } else {
          handleBankTransferRequest();
        }
        break;
      case "bankDetails":
        handleDialogClose(); // Or handle payment completion
        break;
      case "coinSelection":
        setDialogStep("walletAddress");
        break;
      case "walletAddress":
        handleWalletAddressSave();
        break;
      default:
        break;
    }
  };

  const handleCancel = () => {
    switch (dialogStep) {
      case "topUp":
        setDialogStep("payment");
        break;
      case "cryptoTopUp":
        setDialogStep("payment");
        setSelectedCoin("");
        break;
      case "bankTransfer":
        setDialogStep("payment");
        break;
      case "bankDetails":
        setDialogStep("bankTransfer");
        break;
      case "coinSelection":
        setDialogStep("payment");
        setSelectedCoin("");
        break;
      case "walletAddress":
        if (paymentMethod === "stable") setDialogStep("cryptoTopUp");
        else setDialogStep("coinSelection");
        break;
      case "scanToPay":
        setDialogStep("walletAddress");
        break;
      default:
        setIsDialogOpen(false);
    }
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setDialogStep("payment");
    setSelectedCoin("");
    setWalletAddress("");
    setPaymentMethod("usd");
    setAutoRenew(false);
    refetchPaymentHistory();
  };

  const handleFinish = () => {
    setIsDialogOpen(false);
    setDialogStep("payment");
    setSelectedCoin("");
    setWalletAddress("");
    setPaymentMethod("usd");
    setAutoRenew(false);
    refetchPaymentHistory();
    // toast.success("Payment process completed");
  };

  const generatePaymentURI = () => {
    if (selectedCoin) {
      const tokenAddress = TOKEN_CONTRACTS[selectedCoin];
      let uri = `ethereum:${tokenAddress}/transfer?address=${MY_WALLET_ADDRESS}`;
      if (numCredits) {
        const amountInTokenUnits = Math.floor(
          parseFloat(numCredits) * 1e6
        ).toString();
        uri += `&uint256=${amountInTokenUnits}`;
      }
      return uri;
    } else {
      let uri = `ethereum:${MY_WALLET_ADDRESS}`;
      if (amount) uri += `?value=${amount}`;
      return uri;
    }
  };

  const getDialogTitle = () => {
    switch (dialogStep) {
      case "payment":
        return t("SelectPaymentMethod");
      case "topUp":
        return t("TopUpWallet");
      case "cryptoTopUp":
        return t("CryptoTopUp");
      case "bankTransfer":
        return t("BankTransfer");
      case "bankDetails":
        return t("BankDetails.text");
      case "coinSelection":
        return t("SelectCoinType");
      case "walletAddress":
        return t("WalletAddress");
      case "scanToPay":
        return t("ScanToPay.text");
      default:
        return t("Payment");
    }
  };

  const handleNavigateToStep = (step: string) => {
    setDialogStep(step as any);

    // Reset relevant state when switching methods
    if (step === "cryptoTopUp") {
      setSelectedCoin("");
    } else if (step === "bankTransfer") {
      // Any bank transfer specific resets
    }
  };

  const handleCloseDialogs = () => {
    setIsDialogOpen(false);
    setShowEmailSentDialog(true);
    refetchPaymentHistory();
  };

  return (
    <div>
      <div
        className="flex items-center cursor-pointer"
        onClick={handleTopUpClick}
      >
        <Plus className="w-5 h-5" color="#1774FD" />
        <span className="text-[#1774FD] text-[12px]">{t("title")}</span>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="sm:max-w-md [&>button]:hidden max-h-[670px] overflow-y-auto">
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle className="text-[18px] font-[500] p-0 m-0">
              {getDialogTitle()}
            </DialogTitle>
          </DialogHeader>

          {dialogStep === "payment" && !recentlyUsed?.is_active && (
            <PaymentMethodSelection
              paymentMethod={paymentMethod}
              setPaymentMethod={setPaymentMethod}
              onCancel={handleCancel}
              onProceed={handleProceed}
            />
          )}

          {dialogStep === "topUp" && (
            <TopUpForm
              numCredits={numCredits}
              setNumCredits={setNumCredits}
              selectedCurrency={selectedCurrency!}
              setSelectedCurrency={setSelectedCurrency}
              currencies={currencies}
              flagMap={flagMap}
              autoRenew={autoRenew}
              setAutoRenew={setAutoRenew}
              onProceed={handleProceed}
              onCancel={handleCancel}
              isLoading={processingStripe || processingYedPay}
              paymentMethod={paymentMethod}
              setSelectedPaymentMethod={setPaymentMethod}
              selectedPaymentMethod={paymentMethod}
              recentlyUsed={recentlyUsed}
              onNavigateToStep={handleNavigateToStep} // Add this prop
            />
          )}

          {dialogStep === "cryptoTopUp" && (
            <CryptoTopUpForm
              numCredits={numCredits}
              setNumCredits={setNumCredits}
              selectedCoin={selectedCoin}
              setSelectedCoin={setSelectedCoin}
              onProceed={handleProceed}
              onCancel={handleCancel}
              isLoading={false}
              paymentMethod={paymentMethod}
              setSelectedPaymentMethod={setPaymentMethod}
              selectedPaymentMethod={paymentMethod}
              recentlyUsed={recentlyUsed}
              onNavigateToStep={handleNavigateToStep}
            />
          )}

          {dialogStep === "bankTransfer" && (
            <BankTransferForm
              numCredits={numCredits}
              setNumCredits={setNumCredits}
              selectedCurrency={selectedCurrency!}
              setSelectedCurrency={setSelectedCurrency}
              currencies={currencies}
              flagMap={flagMap}
              onProceed={handleProceed}
              onCancel={handleCancel}
              isLoading={false}
              paymentMethod={paymentMethod}
              setSelectedPaymentMethod={setPaymentMethod}
              selectedPaymentMethod={paymentMethod}
              recentlyUsed={recentlyUsed}
              onNavigateToStep={handleNavigateToStep}
              closeDialog={handleCloseDialogs}
            />
          )}

          {dialogStep === "coinSelection" && (
            <CoinSelection
              selectedCoin={selectedCoin}
              setSelectedCoin={setSelectedCoin}
              onCancel={handleCancel}
              onProceed={handleProceed}
            />
          )}

          {dialogStep === "walletAddress" && (
            <WalletAddressForm
              walletAddress={walletAddress}
              setWalletAddress={setWalletAddress}
              onCancel={handleCancel}
              onProceed={handleProceed}
              isLoading={savingWallet}
            />
          )}

          {dialogStep === "bankDetails" && (
            <BankDetails
              onCancel={handleCancel}
              onProceed={handleProceed}
              currency={selectedCurrency?.code ?? ""}
              amount={
                numCredits && selectedCurrency
                  ? parseFloat(numCredits) * parseFloat(selectedCurrency.rate)
                  : 0
              }
              credits={numCredits ?? ""}
            />
          )}

          {dialogStep === "scanToPay" && (
            <ScanToPay
              paymentURI={generatePaymentURI()}
              walletAddress={MY_WALLET_ADDRESS}
              onCancel={handleCancel}
              onFinish={handleFinish}
              copyToClipboard={copyToClipboard}
            />
          )}
        </DialogContent>
      </Dialog>
      <Dialog open={showEmailSentDialog} onOpenChange={setShowEmailSentDialog}>
        <DialogContent className="bg-white rounded-lg shadow-lg max-w-md mx-auto p-0 overflow-hidden">
          {/* Full-width background with image */}
          <div className="bg-[#F7FAFF] w-full flex justify-center items-center">
            <Image
              src="/identity.png"
              alt={t("EmailSent")}
              width={200}
              height={200}
              className="mb-4"
            />
          </div>
          <DialogHeader className="px-6 pt-4">
            <DialogTitle className="text-center">
              {t("AccountDetailsRequestSent.title")}
            </DialogTitle>
            <DialogDescription className="text-sm text-gray-600 mt-2 text-center">
              {t("AccountDetailsRequestSent.description")}
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-center items-center px-6 pb-6 pt-2 w-full">
            <Button
              className="w-full"
              onClick={() => setShowEmailSentDialog(false)}
            >
              {tGlobal("Continue")}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
