"use client";

import { useEffect, useMemo, useState } from "react";
import { useTranslations } from "next-intl";
import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";
import { Gift, Loader, Plus } from "lucide-react";
import { Input } from "@/components/ui/input";
import TransactionTable from "@/components/table/CreditsTable";
import { useGetQuery } from "@/services/api_hooks";
import { toast } from "sonner";
import { dollarCurrencyFormat } from "@/lib/utils";
import MoonLoader from "react-spinners/MoonLoader";
import TopUp from "./_components/Topup/topup";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import InvoiceModal from "./_components/InvoiceModal";
import Link from "next/link";
import RedeemParkModal from "./_components/RedeemParkModal";

function debounce(fn: any, delay: any) {
  let timeout: NodeJS.Timeout;
  return (...args: any) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      fn(...args);
    }, delay);
  };
}

export default function HomePage() {
  // use next-intl for i18n
  const t = useTranslations("DashboardCredits");
  const tGlobal = useTranslations("global");

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isModalRedeemModalOpen, setRedeemModalOpen] = useState(false);
  const [limit, setLimit] = useState<string>("10");
  const [search, setSearch] = useState<string>("");

  const { data: profile, isLoading } = useGetQuery(
    "/auth/profile",
    ["profile"],
    {
      onError() {
        // toast.error("Could not load your profile");
      },
    }
  );

  const { data: paymentData, isLoading: loadingPaymentData } = useGetQuery(
    "/api/payment/default",
    ["payment-default-methods"],
    {
      onError() {
        toast.error(t("toast.NotLoadExchangeRates"));
      },
    }
  );

  const {
    data: paymentHistory,
    isLoading: isLoadingHistory,
    refetch: refetchPaymentHistory,
  } = useGetQuery(
    `/api/payment/history?page=1&limit=${limit}&search=${search}`,
    ["payment-history", limit, search]
  );

  // wrap setSearch in a debounced function (300ms here)
  const debouncedSetSearch = useMemo(
    () => debounce((val: any) => setSearch(val), 300),
    [setSearch]
  );

  return (
    <>
      <div className="border rounded-[8px] bg-white p-4">
        <h1 className="text-[22px] font-[600]  mb-4">{t("title")}</h1>

        <hr className="my-7" />

        <section className="flex flex-wrap gap-5 flex-col-reverse md:flex-row items-start justify-between">
          <Card className="linear h-[153px] max-w-[373px] w-full bg-[#FAFAFA7A] border-none">
            <CardContent className="p-4 flex flex-col justify-between h-full">
              <div className="flex items-center justify-between">
                <Image
                  src="/box.svg"
                  alt="icon"
                  height={30}
                  width={30}
                  className=""
                  priority
                />
                {/* <div className="flex items-center cursor-pointer">
                  <Plus className="w-5 h-5" color="#1774FD" />
                  <span className="text-[#1774FD] text-[12px]">Top Up</span>
                </div> */}
                {loadingPaymentData ? (
                  <Loader size={15} className="animate-spin" />
                ) : (
                  <TopUp />
                )}
              </div>

              <div className="flex items-end justify-between">
                <div>
                  <p className="text-[#828282] text-[12px] font-[500]">
                    {t("CreditsLeft")}
                  </p>
                  {isLoading ? (
                    <Loader size={15} className="animate-spin" />
                  ) : (
                    <span className="text-[#121212] text-[24px] font-[600]">
                      {dollarCurrencyFormat(profile?.profile?.balance) || 0}{" "}
                      {t("Credits")}
                    </span>
                  )}
                </div>
                <div
                  className="text-[#828282] mb-[9px] flex items-center gap-1 font-[400] cursor-pointer text-sm"
                  onClick={() => setRedeemModalOpen(true)}
                >
                  {" "}
                  <Gift size={17} />
                  {t("RedeemPerks")}
                </div>
              </div>
            </CardContent>
          </Card>

          <Link
            className="text-blue-600 font-medium text-sm cursor-pointer hover:text-blue-700 focus:outline-none underline"
            href="/dashboard/home/<USER>/subscribe"
          >
            {t("PaymentMethod")}
          </Link>
        </section>

        <hr className="my-7" />

        <section className="space-y-4">
          <div className="flex flex-wrap gap-3 items-center justify-between my-7">
            <h2 className="font-[500] text-[20px] text-[#101828]">
              {t("TransactionHistory")}
            </h2>
            <div className="flex w-full max-w-[600px] gap-3 justify-between flex-wrap">
              <button
                className="text-blue-600 font-medium text-sm cursor-pointer hover:text-blue-700 focus:outline-none"
                onClick={() => setIsModalOpen(true)}
              >
                {t("BillStatement")}
              </button>
              <div className="block  max-w-[470px] w-full">
                <Input
                  iconLeft="/search.svg"
                  className=" max-w-"
                  onChange={(e) => debouncedSetSearch(e.target.value)}
                />
              </div>
            </div>
          </div>
          <TransactionTable
            limit={limit}
            search={search}
            paymentHistory={paymentHistory}
            isLoadingHistory={isLoadingHistory}
            refetchPaymentHistory={refetchPaymentHistory}
          />
        </section>
      </div>
      <InvoiceModal isOpen={isModalOpen} onOpenChange={setIsModalOpen} />
      <RedeemParkModal
        isOpen={isModalRedeemModalOpen}
        onClose={() => {
          setRedeemModalOpen(false);
        }}
        refetchPaymentHistory={refetchPaymentHistory}
      />
    </>
  );
}
