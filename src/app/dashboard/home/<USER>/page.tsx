"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import { useState } from "react";
import { useTranslations } from 'next-intl';
import Account from "./Account";
import Organization from "./Organization";
import Security from "./Security";
import APIKeys from "./APIKeys";
import Image from "next/image";

const Settings = () => {

  // use next-intl for i18n
  const t = useTranslations("DashboardSettings");
  const tTabs = useTranslations("DashboardSettings.tabs");

  const tabs = [
    {
      value: "account",
      label: tTabs("Account.title"),
      unlock: true,
    },
    {
      value: "organization",
      label: tTabs("Organization.title"),
      unlock: false,
    },
    {
      value: "security",
      label: tTabs("Security.title"),
      unlock: false,
    },
    {
      value: "api-keys",
      label: tTabs("API.title"),
      unlock: false,
    },
  ];


  const [activeTab, setActiveTab] = useState("account");

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  return (
    <div className="border rounded-[8px] bg-white p-4 px-6 min-h-screen pb-10">
      <h1 className="text-[22px] font-[600]  mb-4">{t("title")}</h1>
      <Tabs
        defaultValue="account"
        value={activeTab}
        onValueChange={handleTabChange}
        className=" container mx-auto max-w-[1300px]"
      >
        <TabsList className="grid grid-cols-2 md:grid-cols-3  lg:flex lg:border-b rounded-none bg-white justify-start mb-20 lg:mb-10">
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.value}
              value={tab.value}
              disabled={tab.unlock === false}
              className={`lg:h-10 ${
                tab.unlock === false ? "disabled:cursor-not-allowed" : ""
              } flex gap-2 cursor-pointer font-[500] rounded-none data-[state=active]:shadow-none px-5 text-[#A0A7B4] data-[state=active]:text-black data-[state=active]:border-b-2 data-[state=active]:border-b-black `}
            >
              {tab.label}
              {!tab.unlock && (
                <Image
                  src="/padlock-gray.svg"
                  alt="lock icon"
                  height={15}
                  width={15}
                />
              )}
            </TabsTrigger>
          ))}
        </TabsList>

        {/* Partnership Tab Content */}
        <TabsContent value="account" className="mt-6">
          <Account />
        </TabsContent>

        {/* Custom Models Tab Content */}
        <TabsContent value="organization" className="mt-6">
          <Organization />
        </TabsContent>

        {/* Investment Tab Content */}
        <TabsContent value="security" className="mt-6">
          <Security />
        </TabsContent>

        <TabsContent value="api-keys" className="mt-6">
          <APIKeys />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;
