"use client";

import { useState } from "react";
import { useTranslations } from 'next-intl';
import { useGetQuery, useSubmitQuery } from "@/services/api_hooks";
import { toast } from "sonner";
import { DeleteAccountModal } from "@/components/modals/DeleteModal";
import { <PERSON><PERSON>, Eye, EyeOff, Trash2 } from "lucide-react";
import { SetDefualtModal } from "@/components/modals/SetDefualtModal";

const PaymentCards = () => {

  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.Subscribe");

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedMethod, setselectedMethod] = useState(null);
  const [isVisible, setIsVisible] = useState(false);
  const [copied, setCopied] = useState(false);

  // Get payment method data
  const {
    data: paymentData,
    isLoading,
    refetch,
    isRefetching,
  } = useGetQuery("/api/payment/payment_method", ["get-payment-methods"], {
    onError() {
      toast.error(t("toast.notLoadPayment"));
    },
  });

  console.log(paymentData);

  const walletAddress = "**********************************"; // Example Bitcoin address
  const maskedAddress = "**** **** 1234";

  const handleCopyAddress = async () => {
    try {
      await navigator.clipboard.writeText(walletAddress);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  const handleToggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  // Cancel subscription mutation
  const { mutateAsync: cancelSubscription, isPending: isCancelling } =
    useSubmitQuery("/api/payment/default", "POST", {
      onSuccess() {
        toast.success(t("toast.PaymentSetAsDefault"), {
          position: "top-right",
          className: "p-4",
        });
        setIsModalOpen(false);
        setselectedMethod(null);
        refetch(); // Refresh the payment data
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || t("toast.FailedToSet"), {
          position: "top-right",
          className: "p-4",
        });
      },
    });

  const handleCancelClick = (method: any) => {
    setselectedMethod(method);
    setIsModalOpen(true);
  };

  const handleConfirmCancel = async () => {
    if (!selectedMethod) return;

    try {
      // Assuming you have a subscription ID - you might need to add this to your API response
      await cancelSubscription({
        payment_method: selectedMethod,
      });
    } catch (error) {
      console.error("Error cancelling subscription:", error);
    }
  };

  function getActiveStatus(paymentMethod: any) {
    switch (paymentMethod) {
      case "stripe":
        return "Credit/Debit Card";
      case "bank_wire":
        return "Bank Transfer";
      case "crypto":
        return "Crypto Wallet";
      case "yedpay":
        return "Wechat/Unionpay";
      default:
        return "usd"; // fallback
    }
  }

  function getSubText(paymentMethod: any) {
    switch (paymentMethod) {
      case "stripe":
        return t("SubText.stripe");
      case "bank_wire":
        return t("SubText.bankWire");
      case "crypto":
        return t("SubText.crypto");
      case "yedpay":
        return t("SubText.yedpay");
      default:
        return t("SubText.default"); // fallback
    }
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-3 gap-6 p-4">
        <div className="border border-gray-200 rounded-xl w-[400px] py-3 px-6 animate-pulse">
          <div className="h-4 bg-gray-300 rounded w-1/2 mb-2"></div>
          <div className="h-3 bg-gray-300 rounded w-3/4 mb-3"></div>
          <div className="h-6 bg-gray-300 rounded w-1/3 mb-3"></div>
          <div className="h-3 bg-gray-300 rounded w-full mb-1"></div>
          <div className="h-3 bg-gray-300 rounded w-full"></div>
        </div>
      </div>
    );
  }

  const payment = paymentData || [];

  if (payment.length === 0) {
    return (
      <div className="grid grid-cols-3 gap-6 p-4">
        <div className="col-span-3 text-center py-8 text-gray-500">
          {t("NoActivePaymentMethodFound")}
        </div>
      </div>
    );
  }

  return (
    <div>
      <h2 className="pl-2 font-[500] text-[16px] mb-2">{t("PaymentMethod")}</h2>

      <div className="flex flex-wrap items-center gap-4 ">
        {payment.map((payment: any, index: number) => (
          <div
            key={index}
            className="w-full max-w-[400px] px-4 py-3 space-y-5 min-h-30 bg-white rounded-xl border border-gray-200 "
          >
            {/* Header */}
            <div className="flex items-center justify-between ">
              <div>
                <h2 className="text-[16px] font-semibold text-gray-900">
                  {getActiveStatus(payment?.payment_method)}
                </h2>
                <p className="text-xs text-[#7F7F81] mt-1">
                  {getSubText(payment?.payment_method)}
                </p>
              </div>
              {payment.is_default && (
                <span className="px-3 grid place-content-center py-1 bg-green-500 text-white text-xs font-medium rounded-full">
                  {t("Default")}
                </span>
              )}
            </div>

            {/* Wallet Address Section */}
            {/* <div className="">
            
              <div className="flex items-center justify-between">
                <span className="text-[#7F7F81] font-mono text-[14px] tracking-wider">
                  {isVisible ? walletAddress : maskedAddress}
                </span>
                <button
                  onClick={handleToggleVisibility}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  aria-label={isVisible ? "Hide address" : "Show address"}
                >
                  {isVisible ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>

              <button
                onClick={handleCopyAddress}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                <Copy size={16} />
                <span className="text-[12px]">
                  {copied ? "Copied!" : "Copy address"}
                </span>
              </button>
            </div> */}

            {/* Delete Button */}
            <div className=" flex justify-between gap-2 items-center  border-gray-100">
              {!payment.is_default && (
                <span
                  className="text-xs text-blue-500 cursor-pointer"
                  onClick={() => handleCancelClick(payment?.payment_method)}
                >
                  {t("SetAsDefault")}
                </span>
              )}
              {/* <button
                // onClick={handleDelete}
                className="flex items-center text-xs gap-1 text-red-500 hover:text-red-600 transition-colors"
              >
                <Trash2 size={14} />
                <span className=" font-medium mt-1">Delete</span>
              </button> */}
            </div>
          </div>
        ))}

        <SetDefualtModal
          isOpen={isModalOpen}
          onOpenChange={setIsModalOpen}
          onConfirm={handleConfirmCancel}
          loading={isCancelling}
          title={t("SetDefault.title")}
          description={t("SetDefault.description")}
          cancelText={t("SetDefault.cancelText")}
          confirmText={t("SetDefault.confirmText")}
          loadingText={t("SetDefault.loadingText")}
        />
      </div>
    </div>
  );
};

export default PaymentCards;
