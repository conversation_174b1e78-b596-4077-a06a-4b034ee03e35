// @ts-nocheck

"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { useGetQuery, useSubmitQuery } from "@/services/api_hooks";
import { toast } from "sonner";
import { DeleteAccountModal } from "@/components/modals/DeleteModal";

const SubscriptionCards = () => {
  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.Subscribe");
  const tForm = useTranslations("Form");

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState(null);

  // Get subscriptions data
  const {
    data: subscriptionsData,
    isLoading,
    refetch,
    isRefetching,
  } = useGetQuery("/api/subscribe/subscription", ["get-subscriptions"], {
    onError() {
      toast.error(t("toast.notLoadSubscriptions"));
    },
  });

  // Cancel subscription mutation
  const { mutateAsync: cancelSubscription, isPending: isCancelling } =
    useSubmitQuery("/api/subscribe/cancel-subscription", "POST", {
      onSuccess() {
        toast.success(t("toast.SubscriptionCancelled"), {
          position: "top-right",
          className: "p-4",
        });
        setIsModalOpen(false);
        setSelectedSubscription(null);
        refetch(); // Refresh the subscriptions data
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || t("toast.FailedToCancel"), {
          position: "top-right",
          className: "p-4",
        });
      },
    });

  const handleCancelClick = (subscription) => {
    setSelectedSubscription(subscription);
    setIsModalOpen(true);
  };

  const handleConfirmCancel = async () => {
    if (!selectedSubscription) return;

    try {
      // Assuming you have a subscription ID - you might need to add this to your API response
      await cancelSubscription({
        subscriptionId: selectedSubscription.id,
      });
    } catch (error) {
      console.error("Error cancelling subscription:", error);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      day: "numeric",
      month: "long",
      year: "numeric",
    });
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-3 gap-6 p-4">
        <div className="border border-gray-200 rounded-xl py-3 px-6 animate-pulse">
          <div className="h-4 bg-gray-300 rounded w-1/2 mb-2"></div>
          <div className="h-3 bg-gray-300 rounded w-3/4 mb-3"></div>
          <div className="h-6 bg-gray-300 rounded w-1/3 mb-3"></div>
          <div className="h-3 bg-gray-300 rounded w-full mb-1"></div>
          <div className="h-3 bg-gray-300 rounded w-full"></div>
        </div>
      </div>
    );
  }

  const subscriptions = subscriptionsData?.subscriptions || [];

  if (subscriptions.length === 0) {
    return (
      <div className="grid grid-cols-3 gap-6 p-4">
        <div className="col-span-3 text-center py-8 text-gray-500">
          {t("NoActiveSubscriptionsFound")}
        </div>
      </div>
    );
  }

  return (
    <div>
      <h2 className="pl-2 font-[500] text-[16px] mb-2">{t("Subscriptions")}</h2>

      <div className="flex flex-wrap items-center gap-4 ">
        {subscriptions.map((subscription, index) => {
          // Do not render if model is Yumi Sandbox and subscription is not active
          if (
            subscription.modelName === "Yumi Sandbox" &&
            subscription.active === false
          ) {
            return null;
          }
          return (
            <div
              key={index}
              className="border border-gray-200 rounded-xl py-2 px-4 flex flex-col w-full max-w-[400px]"
            >
              <h2 className="text-[16px] font-medium">
                {subscription.modelName}
              </h2>
              <p className="text-gray-500 text-xs font-[400]">
                {subscription.plan} {t("subscription")}
              </p>

              <div className="mt-3 flex items-baseline">
                <span className="text-2xl font-medium">
                  ${Math.abs(subscription.amount)}
                </span>
                <span className="text-[#828282] text-sm font-[400] ml-1">
                  / per {subscription.plan}
                </span>
              </div>

              <div className="mt-3 text-xs font-[400] text-[#828282]">
                {subscription.modelName === "Yumi Sandbox" ? (
                  <>
                    <p>
                      {t("BillingStarted")} {formatDate(subscription.startDate)}
                    </p>
                    <p className="mt-1">
                      {t("NextBillingCycle")} {formatDate(subscription.endDate)}
                    </p>
                  </>
                ) : (
                  <>
                    <p>
                      {t("BillingStarted")} {formatDate(subscription.startDate)}
                    </p>
                    <p className="mt-1">
                      {t("NextBillingCycle")} {formatDate(subscription.endDate)}
                    </p>
                  </>
                )}
                <p className="mt-1">
                  {t("RemainingDays")} {subscription.remainingDays}
                </p>
              </div>

              <div className="mt-auto pt-4 flex justify-end">
                {subscription.active ? (
                  <button
                    className="text-red-600 hover:text-red-700 text-xs font-normal cursor-pointer disabled:opacity-50"
                    onClick={() => handleCancelClick(subscription)}
                    disabled={isCancelling}
                  >
                    {isCancelling
                      ? tForm("Button.Cancelling")
                      : tForm("Button.CancelSubscription")}
                  </button>
                ) : (
                  <span className="text-gray-400 text-xs">
                    {t("Cancelled")}
                  </span>
                )}
              </div>
            </div>
          );
        })}

        <DeleteAccountModal
          isOpen={isModalOpen}
          onOpenChange={setIsModalOpen}
          onConfirm={handleConfirmCancel}
          loading={isCancelling}
          title={t("CancelSubscription.title")}
          description={t("CancelSubscription.description")}
          cancelText={t("CancelSubscription.cancelText")}
          confirmText={t("CancelSubscription.confirmText")}
          loadingText={t("CancelSubscription.loadingText")}
        />
      </div>
    </div>
  );
};

export default SubscriptionCards;
