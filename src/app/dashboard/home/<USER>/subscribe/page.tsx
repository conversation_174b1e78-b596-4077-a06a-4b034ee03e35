import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import React from "react";
import { useTranslations } from 'next-intl';
import SubscriptionCards from "./_components/SubscriptionCards";
import PaymentCards from "./_components/PaymentCards";

const Subscribe = () => {

  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.Subscribe");

  return (
    <div className="border rounded-[8px] bg-white p-4 min-h-screen pb-10">
      <Link
        href="/dashboard/home/<USER>"
        className="text-[16px] font-[500]  mb-4 flex items-center gap-1"
      >
        <ArrowLeft /> {t("title")}
      </Link>
      <hr className="my-10" />
      <div className="space-y-10">
        <PaymentCards />
        <SubscriptionCards />
      </div>
    </div>
  );
};

export default Subscribe;
