// @ts-nocheck

"use client";

import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";
import { Check, ChevronsUpDown, Plus } from "lucide-react";
import { Input } from "@/components/ui/input";
import TransactionTable from "@/components/table/CreditsTable";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { COUNTRIES } from "@/lib/countries";
import { cn } from "@/lib/utils";
import { Switch } from "@/components/ui/switch";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { PhoneCode } from "@/components/phone-input";
import { useGetQuery, useSubmitQuery } from "@/services/api_hooks";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

const schema = yup
  .object({
    firstName: yup.string().required("First Name is required"),
    lastName: yup.string().required("Last Name is required"),
    email: yup
      .string()
      .email("Invalid email format")
      .required("Email is required")
      .matches(
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,

        "Invalid email format"
      ),
    // phoneNumber: yup.string().required("Phone number is required"),
    // countryCode: yup.string().required("Country code is required"),
    // country: yup.object().required("Country is required"),
    // password: yup
    //   .string()
    //   .required("This field is required")
    //   .min(8, "Password must be at least 8 characters")
    //   .matches(/[0-9]/, "Password must contain a number")
    //   .matches(/[a-z]/, "Password must contain a lowercase letter")
    //   .matches(/[A-Z]/, "Password must contain an uppercase letter")
    //   .matches(
    //     /[!@#$%^&*(),.?":{}|<>]/,
    //     "Password must contain a special character"
    //   ),
  })
  .required();

export default function SettingsPage() {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [error, setError] = useState("");
  const [selectedCountry, setSelectedCountry] = useState("CN");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogStep, setDialogStep] = useState<"topUp" | "payment">("topUp");
  const {
    register,
    handleSubmit,
    control,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      // phoneNumber: "",
      // countryCode: "UK",
      // country: COUNTRIES.find((country) => country.value === "CN"),
      // password: "",
    },
  });

  const queryClient = useQueryClient();

  const handleTopUpClick = () => {
    setIsDialogOpen(true);
    setDialogStep("topUp");
  };

  const handleProceed = () => {
    if (dialogStep === "topUp") {
      setDialogStep("payment");
    } else {
      // Handle the payment completion logic here
      setIsDialogOpen(false);
      setDialogStep("topUp");
    }
  };

  const handleCancel = () => {
    if (dialogStep === "payment") {
      setDialogStep("topUp");
    } else {
      setIsDialogOpen(false);
    }
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setDialogStep("topUp");
  };

  const filteredCountries = useMemo(() => {
    if (!searchQuery) return COUNTRIES;

    const lowerQuery = searchQuery.toLowerCase();
    return COUNTRIES.filter((country) =>
      country.title.toLowerCase().includes(lowerQuery)
    );
  }, [searchQuery]);

  // useEffect(() => {
  //   const detectCountry = async () => {
  //     try {
  //       const res = await fetch("https://ipapi.co/jsoon/");
  //       const data = await res.json();
  //       const ipCountryCode = data.country_code?.toUpperCase();

  //       const country = COUNTRIES.find((c) => c.value === ipCountryCode);
  //       if (country) {
  //         setValue("country", country);
  //         return;
  //       }
  //     } catch (err) {
  //       console.warn("IP detection failed, using fallback (CN)");
  //     }
  //   };

  //   detectCountry();
  // }, [setValue]);

  // // Use useCallback for event handlers
  const handleSelect = useCallback(
    (country) => {
      console.log(country?.value, "couu");
      setSelectedCountry(country?.value);
      setValue("country", country);
      setOpen(false); // close the dropdown
      setSearchQuery(""); // reset search
    },
    [setValue]
  );

  // 1️⃣ Fetch profile on mount
  // 1️⃣ fetch once on mount, "profile" === response.data
  const { data: profile, isLoading } = useGetQuery(
    "/auth/profile",
    ["profile"],
    {
      onError() {
        toast.error("Could not load your profile");
      },
    }
  );

  // 2️⃣ when "profile" arrives, write into form
  useEffect(() => {
    if (!profile) return;
    // adjust these keys to whatever your API actually returns
    setValue("firstName", profile?.profile?.firstname);
    setValue("lastName", profile?.profile?.lastname);
    setValue("email", profile?.profile?.email);
  }, [profile, setValue]);

  console.log(profile);

  // 2️⃣ Prepare the mutation for updating
  const { mutateAsync: updateProfile, isPending: updating } = useSubmitQuery(
    "/auth/profile",
    "PUT",
    {
      onSuccess(response) {
        toast.success("Profile updated!", {
          position: "top-right",
          className: "p-4",
        });

        queryClient.invalidateQueries({
          queryKey: ["profile"],
        });
      },
      onError(err: any) {
        toast.error(err.response?.data?.message || "Update failed", {
          position: "top-right",
          className: "p-4",
        });
      },
    }
  );

  // 3️⃣ Form submission handler
  const onSubmit = async (vals: FormValues) => {
    const formData = new FormData();
    formData.append("firstname", vals.firstName);
    formData.append("lastname", vals.lastName);
    formData.append("email", vals.email);
    if (vals.password) {
      formData.append("password", vals.password);
    }
    await updateProfile(formData);
  };

  if (isLoading) {
    return <div className="text-center">Loading…</div>;
  }

  return (
    <>
      <div className="border rounded-[8px] bg-white p-4">
        <h1 className="text-[22px] font-[600]  mb-4">Settings</h1>

        <hr className="my-6" />

        <div className=" pb-10">
          <form className="space-y-3" onSubmit={handleSubmit(onSubmit)}>
            <div className="flex items-center justify-between mb-10">
              <div className="">
                <p className="text-[16px] font-[500]">Your Details</p>
                <p className="text-[#7F7F81] text-[14px] font-[500]">
                  Update your account settings. Set your preferred language and
                  timezone.
                </p>
              </div>
              <Button
                className="w-fit bg-[#121212] text-sm hover:bg-primary text-white py-2 px-3  h-[32px] cursor-pointer"
                type="submit"
                disabled={updating}
              >
                {updating ? "Updating…" : "Update Details"}
              </Button>
            </div>

            <div className="max-w-[626px] space-y-7">
              <div className="grid grid-cols-2 gap-4 ">
                <div>
                  <label
                    htmlFor="firstName"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    First Name
                  </label>
                  <Input
                    id="firstName"
                    type="text"
                    className={`w-full h-[34px] ${
                      errors.firstName ? "border-red-500" : ""
                    }`}
                    {...register("firstName")}
                  />
                  {errors.firstName && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.firstName.message}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="lastName"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Last Name
                  </label>
                  <Input
                    id="lastName"
                    type="text"
                    className={`w-full h-[34px] ${
                      errors.lastName ? "border-red-500" : ""
                    }`}
                    {...register("lastName")}
                  />
                  {errors.lastName && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.lastName.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="">
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Email
                </label>
                <Input
                  id="email"
                  type="email"
                  className={`w-full h-[34px] ${
                    errors.email ? "border-red-500" : ""
                  }`}
                  {...register("email")}
                />
                {errors.email && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.email.message}
                  </p>
                )}
              </div>

              {/* <div className="">
              <label
                htmlFor="country"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Country
              </label>
              <Controller
                name="country"
                control={control}
                render={({ field }) => (
                  <Popover open={open} onOpenChange={setOpen}>
                    <PopoverTrigger asChild className="h-[34px]">
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={open}
                        className={`w-full justify-between ${
                          errors.country ? "border-red-500" : ""
                        }`}
                        type="button"
                      >
                        {field.value ? (
                          <div className="flex items-center gap-2">
                            <img
                              alt={field.value.value}
                              src={`https://purecatamphetamine.github.io/country-flag-icons/3x2/${field.value.value}.svg`}
                              className="h-4 w-6 rounded-sm object-cover"
                            />
                            <span>{field.value.title}</span>
                          </div>
                        ) : (
                          "Select country"
                        )}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 z-50">
                      <Command className="max-h-[300px]">
                        <CommandInput
                          placeholder="Search country..."
                          className="h-9"
                          value={searchQuery}
                          onValueChange={setSearchQuery}
                        />
                        <CommandEmpty>No country found.</CommandEmpty>
                        <CommandGroup className="overflow-y-auto pointer-events-auto">
                          {filteredCountries.map((country) => (
                            <CommandItem
                              key={country.value}
                              value={country.title}
                              className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 active:bg-slate-200 dark:active:bg-slate-700 pointer-events-auto"
                              onSelect={() => {
                                handleSelect(country);
                                field.onChange(country);
                              }}
                            >
                              <div className="flex items-center gap-2">
                                <img
                                  alt={country.value}
                                  src={`https://purecatamphetamine.github.io/country-flag-icons/3x2/${country.value}.svg`}
                                  className="h-4 w-6 rounded-sm object-cover"
                                  loading="lazy"
                                />
                                <span className="text-black dark:text-white">
                                  {country.title}
                                </span>
                              </div>
                              <Check
                                className={cn(
                                  "ml-auto h-4 w-4",
                                  field.value?.value === country.value
                                    ? "opacity-100"
                                    : "opacity-0"
                                )}
                              />
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </Command>
                    </PopoverContent>
                  </Popover>
                )}
              />
              {errors.country && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.country.message}
                </p>
              )}
            </div> */}

              {/* <div className="">
              <label
                htmlFor="country"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Phone
              </label>
              <PhoneCode defaultCountry={selectedCountry} />
            </div> */}

              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Password
                </label>
                <div className="flex items-center justify-between border rounded-md h-[34px] px-2">
                  {/* <Input
                  id="password"
                  //   type={showPassword ? "text" : "password"}
                  //   className={`w-full h-[34px] ${
                  //     errors.password ? "border-red-500" : ""
                  //   }`}
                  //   {...register("password")}
                  placeholder="********"
                /> */}
                  <div className="text-gray-400 mt-1">********</div>
                  <div
                    className=" bg-transparent text-black hover:bg-transparent  cursor-pointer text-sm"
                    //   type="submit"
                    //   disabled={loading}
                    onClick={(e) => {
                      handleTopUpClick();
                      e.preventDefault();
                    }}
                  >
                    Change password
                  </div>
                </div>
                {/* {errors.password && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.password.message}
                </p>
              )} */}
              </div>
            </div>
          </form>

          <hr className="my-6" />

          <section className="flex flex-row items-center justify-between ">
            <div>
              <p className="text-[16px] font-[500]">2FA Authentication</p>
              <p className="text-[#7F7F81] text-[12px] font-[500]">
                Set up another layer of authentication for extra protection of
                your account.
              </p>
            </div>
            <Switch id="airplane-mode" />
          </section>
        </div>

        <Dialog open={isDialogOpen} onOpenChange={handleDialogClose}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader className="flex flex-row items-center justify-between">
              <DialogTitle className="text-[18px] font-[500]">
                {dialogStep === "topUp" ? (
                  "Change Password"
                ) : (
                  <p className="flex flex-col space-y-2">
                    Set Up 2FA{" "}
                    <span className="text-[14px] font-[400] text-[#7F7F81]">
                      Scan the QR Code below with your authenticator app.
                    </span>
                  </p>
                )}
              </DialogTitle>
            </DialogHeader>

            {dialogStep === "topUp" ? (
              /* Top Up Step */
              <div className="space-y-6">
                <div className="">
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Old Password
                  </label>
                  <div className="relative">
                    <Input
                      id="password"
                      //   type={showPassword ? "text" : "password"}
                      //   className={`w-full h-[34px] ${
                      //     errors.password ? "border-red-500" : ""
                      //   }`}
                      //   {...register("password")}
                      placeholder="********"
                      type="password"
                    />
                  </div>
                  {/* {errors.password && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.password.message}
                </p>
              )} */}
                </div>

                <div className="">
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    New Password
                  </label>
                  <div className="relative">
                    <Input
                      id="password"
                      //   type={showPassword ? "text" : "password"}
                      //   className={`w-full h-[34px] ${
                      //     errors.password ? "border-red-500" : ""
                      //   }`}
                      //   {...register("password")}
                      placeholder="********"
                      type="password"
                    />
                  </div>
                  {/* {errors.password && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.password.message}
                </p>
              )} */}
                </div>

                <div className="">
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Confirm New Password
                  </label>
                  <div className="relative">
                    <Input
                      id="password"
                      //   type={showPassword ? "text" : "password"}
                      //   className={`w-full h-[34px] ${
                      //     errors.password ? "border-red-500" : ""
                      //   }`}
                      //   {...register("password")}
                      placeholder="********"
                      type="password"
                    />
                  </div>
                  {/* {errors.password && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.password.message}
                </p>
              )} */}
                </div>

                <div className="flex justify-between pt-4">
                  <Button variant="outline" onClick={handleCancel}>
                    Cancel
                  </Button>
                  <Button
                    className="bg-[#0F172A] hover:bg-[#1E293B] text-white px-6 cursor-pointer"
                    onClick={handleProceed}
                  >
                    Proceed
                  </Button>
                </div>
              </div>
            ) : (
              /* Payment Method Step */
              <div className="space-y-6">
                <div>
                  <Image src="/qrCode.svg" alt="qr" height={220} width={420} />
                </div>
                <div>
                  <p className="text-[12px] font-[500] text-[#101828]">
                    Enter OTP Code from your Authenticator
                  </p>
                  <InputOTP maxLength={6}>
                    <InputOTPGroup className="gap-2">
                      <InputOTPSlot
                        index={0}
                        className="border w-[60px] h-[60px] rounded-[8px]"
                      />
                      <InputOTPSlot
                        index={1}
                        className="border w-[60px] h-[60px] rounded-[8px]"
                      />
                      <InputOTPSlot
                        index={2}
                        className="border w-[60px] h-[60px] rounded-[8px]"
                      />
                      <InputOTPSlot
                        index={3}
                        className="border w-[60px] h-[60px] rounded-[8px]"
                      />
                      <InputOTPSlot
                        index={4}
                        className="border w-[60px] h-[60px] rounded-[8px]"
                      />
                      <InputOTPSlot
                        index={5}
                        className="border w-[60px] h-[60px] rounded-[8px]"
                      />
                    </InputOTPGroup>
                  </InputOTP>
                </div>

                <div className="flex justify-between pt-4">
                  <Button variant="outline" onClick={handleCancel}>
                    Cancel
                  </Button>
                  <Button
                    className="bg-[#0F172A] hover:bg-[#1E293B] text-white px-6"
                    onClick={handleProceed}
                  >
                    Proceed
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
}
