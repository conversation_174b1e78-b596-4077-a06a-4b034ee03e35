import React from "react";
import WelcomeModal from "@/components/carousel";
import ProfileModal from "@/components/modals/profileModal";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout = ({ children }: DashboardLayoutProps) => (
  <div className="min-h-screen">
    <main>{children}</main>
    {/* <WelcomeModal /> */}
    <ProfileModal />
  </div>
);

export default DashboardLayout;
