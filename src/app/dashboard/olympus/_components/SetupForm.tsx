// components/ReportForm.tsx

"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { CalendarIcon, X } from "lucide-react";
import Image from "next/image";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@radix-ui/react-popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useForm, Controller } from "react-hook-form";
import { motion } from "framer-motion";
import {
  addDays,
  isAfter,
  isBefore,
  isSameDay,
  subYears,
  isFuture,
  differenceInCalendarDays,
} from "date-fns";

const SetupForm = () => {
  const router = useRouter();
  const today = new Date();
  const oneYearAgo = subYears(today, 1);
  const oneDayBefore = new Date(today);
  oneDayBefore.setDate(today.getDate() - 1);

  // React Hook Form setup
  const { control, handleSubmit, setValue, watch } = useForm<{
    ticker: string;
    dateRange: {
      from: Date;
      to: Date;
    };
  }>({
    defaultValues: {
      ticker: "",
      dateRange: {
        from: oneDayBefore,
        to: today,
      },
    },
  });

  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [isAnimatingOut, setIsAnimatingOut] = useState(false);

  // Form submit handler
  const onSubmit = (data: {
    ticker: string;
    dateRange: {
      from: Date;
      to: Date;
    };
  }) => {
    if (!data.ticker || !data.dateRange.from || !data.dateRange.to) {
      toast.error("Please fill all fields");
      return;
    }

    // Set specific times for start and end dates in Eastern Time
    const startDate = new Date(data.dateRange.from);
    const endDate = new Date(data.dateRange.to);

    // Set start date to 00:00:00.000 ET
    startDate.setHours(0, 0, 0, 0);

    // Set end date to 23:59:59.999 ET
    endDate.setHours(23, 59, 59, 999);

    // Convert to UTC (ET is UTC-4 or UTC-5 depending on DST)
    // For simplicity, we'll use UTC-5 (EST) - you can adjust this based on your needs
    const utcOffset = -5; // EST (UTC-5)

    const startDateUTC = new Date(
      startDate.getTime() - utcOffset * 60 * 60 * 1000
    );
    const endDateUTC = new Date(endDate.getTime() - utcOffset * 60 * 60 * 1000);

    // Start reverse animation
    setIsAnimatingOut(true);

    setTimeout(() => {
      router.push(
        `/dashboard/olympus/simulations?ticker=${
          data.ticker
        }&start_date=${startDateUTC.toISOString()}&end_date=${endDateUTC.toISOString()}`
      );
    }, 300);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -30 }}
      animate={{
        opacity: isAnimatingOut ? 0 : 1,
        y: isAnimatingOut ? 30 : 0,
        scale: isAnimatingOut ? 0.95 : 1,
      }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className="max-w-[458px] mx-auto rounded-lg shadow-sm py-2 space-y-5"
    >
      <Image
        src="/olympus-banner.svg"
        alt="Orion"
        width={1000}
        height={400}
        className="object-cover"
      />
      <form className="px-4" onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-2 text-center">
          <h2 className="text-[22px] font-[300] text-[#1E1E1E]">
            Welcome to Olympus Simulation
          </h2>
          <p className="text-[16px] font-[300] text-[#A7A7A7]">
            Let’s set up your market environment
          </p>
        </div>

        <div className="space-y-10 mb-8">
          {/* Ticker Field */}
          <div>
            <label htmlFor="ticker" className="block text-sm font-light mb-1">
              Ticker
            </label>
            <Controller
              name="ticker"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  id="ticker"
                  type="text"
                  placeholder="Enter a ticker to simulate"
                  className="w-full h-[34px]"
                />
              )}
            />
          </div>

          {/* Date Range Picker */}
          <div>
            <label className="block text-sm font-light mb-1">Time Period</label>
            <Controller
              name="dateRange"
              control={control}
              render={({ field }) => (
                <>
                  <Popover
                    open={isCalendarOpen}
                    onOpenChange={setIsCalendarOpen}
                  >
                    <PopoverTrigger asChild>
                      <div className="relative w-full">
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-between text-left font-normal bg-white border-[#e2e8f0]",
                            !field.value?.from && "text-[#98a2b3]"
                          )}
                          type="button"
                          onClick={() => setIsCalendarOpen(true)}
                        >
                          {field.value?.from ? (
                            field.value?.to ? (
                              <>
                                {format(field.value.from, "LLL dd, y")} -{" "}
                                {format(field.value.to, "LLL dd, y")}
                              </>
                            ) : (
                              format(field.value.from, "LLL dd, y")
                            )
                          ) : (
                            <span className="text-[#98a2b3]">
                              Select a time range
                            </span>
                          )}
                          <CalendarIcon className="mr-2 h-4 w-4 text-[#737384]" />
                        </Button>
                        {(field.value?.from || field.value?.to) && (
                          <button
                            type="button"
                            className="absolute right-10 top-1/2 -translate-y-1/2 p-1 rounded hover:bg-gray-100"
                            onClick={(e) => {
                              e.stopPropagation();
                              field.onChange({
                                from: undefined,
                                to: undefined,
                              });
                              //setIsCalendarOpen(true);
                            }}
                            tabIndex={-1}
                          >
                            <X className="w-4 h-4 text-gray-400" />
                          </button>
                        )}
                      </div>
                    </PopoverTrigger>
                    <PopoverContent
                      className="w-auto p-0 bg-white"
                      align="start"
                    >
                      <Calendar
                        mode="range"
                        defaultMonth={field.value?.from}
                        selected={field.value}
                        onSelect={(range) => {
                          if (!range?.from) {
                            field.onChange({ from: undefined, to: undefined });
                            return;
                          }
                          // Prevent selecting future dates
                          if (range.to && isAfter(range.to, today)) {
                            range.to = today;
                          }
                          // Prevent selecting before one year ago
                          if (isBefore(range.from, oneYearAgo)) {
                            range.from = oneYearAgo;
                          }
                          // Limit range to 10 days
                          if (
                            range.from &&
                            range.to &&
                            differenceInCalendarDays(range.to, range.from) > 13
                          ) {
                            range.to = addDays(range.from, 13);
                          }
                          field.onChange(range);
                        }}
                        numberOfMonths={2}
                        className="text-[#03061d]"
                        disabled={(date) =>
                          isAfter(date, today) || isBefore(date, oneYearAgo)
                        }
                      />
                    </PopoverContent>
                  </Popover>
                </>
              )}
            />
          </div>
        </div>

        <Button
          type="submit"
          className="w-full bg-gray-900 hover:bg-gray-800 text-white cursor-pointer"
        >
          Set Up Environment
        </Button>
      </form>
    </motion.div>
  );
};

export default SetupForm;
