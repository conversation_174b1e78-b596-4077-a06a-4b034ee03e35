// components/HeaderOrion.tsx
"use client";

import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { FC, useState, useEffect } from "react";
import { LogOut, Send, Menu, X } from "lucide-react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetT<PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";

import TelegramLinkButton from "@/components/button/TelegramLinkButton";
import {
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Menu items array

const menuItems = [
  { name: "Home", href: "/dashboard/olympus" },
  { name: "Simulations", href: "/dashboard/olympus/simulations/history" },
];

const HeaderOlympus: FC = () => {
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const mainMenuitem = menuItems;

  const handleClick = (e: any, href: string) => {
    if (pathname === href) {
      e.preventDefault(); // Prevent default navigation
      window.location.href = href;
    }
  };

  // Close mobile menu when clicking on a link
  const handleMobileMenuClick = (e: any, href: string) => {
    setMobileMenuOpen(false);
    handleClick(e, href);
  };

  // Close mobile menu when pathname changes
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [pathname]);

  return (
    <div className="px-[5%] py-3 bg-white shadow-sm sticky top-0 z-40">
      <nav className="flex items-center justify-between mx-auto max-w-[1400px]">
        {/* Left section - Logo */}
        <div className="flex items-center space-x-4">
          <button className="flex items-center space-x-2 focus:outline-none cursor-pointer">
            <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200">
              <Image
                src="/olympus_thumbnail.png"
                alt="Orion"
                width={32}
                height={32}
                className="object-cover"
              />
            </div>
            <span className="font-medium text-gray-800">Olympus</span>
          </button>

          {/* Desktop Menu items */}
          <div className="hidden lg:flex items-center space-x-8 ml-6">
            {mainMenuitem.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                onClick={(e) => handleClick(e, item.href)}
                className={`text-sm font-medium transition-colors duration-200 ${
                  pathname === item.href
                    ? "text-black "
                    : "text-gray-500 hover:text-gray-900"
                }`}
              >
                {item.name}
              </Link>
            ))}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Link
                  href="/dashboard/home"
                  className="hidden md:flex items-center gap-2 justify-between text-sm p-[4.5px] rounded-full bg-gray-500 text-white border border-gray-500 hover:bg-gray-700 transition-colors duration-200"
                >
                  <LogOut size={14} />
                </Link>
              </TooltipTrigger>
              <TooltipContent>Return to ai-wk</TooltipContent>
            </Tooltip>

            {/* Help button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Link
                  href="/dashboard/support"
                  className="hidden md:flex items-center justify-center text-sm text-[#0F172A] hover:text-gray-900"
                >
                  <Image
                    src="/help-icon-2.svg"
                    alt="help icon"
                    height={26}
                    width={26}
                  />
                </Link>
              </TooltipTrigger>
              <TooltipPortal>
                <TooltipContent>Need Help?</TooltipContent>
              </TooltipPortal>
            </Tooltip>
          </TooltipProvider>
        </div>

        {/* Mobile menu using shadcn Sheet */}
        <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
          <SheetTrigger asChild>
            <button
              className="md:hidden p-2 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-gray-500"
              aria-label="Toggle menu"
            >
              <Menu className="h-6 w-6" />
            </button>
          </SheetTrigger>

          <SheetContent side="right" className="w-full max-w-sm">
            <SheetHeader>
              <SheetTitle className="flex items-center space-x-2 text-left">
                <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200">
                  <Image
                    src="/orion_thumbnail.svg"
                    alt="Orion"
                    width={32}
                    height={32}
                    className="object-cover"
                  />
                </div>
                <span className="font-medium text-gray-800">Olympus</span>
              </SheetTitle>
            </SheetHeader>

            <div className="flex flex-col justify-between h-full pt-6">
              {/* Navigation Links */}
              <div className=" space-y-4">
                {menuItems.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    onClick={(e) => handleMobileMenuClick(e, item.href)}
                    className={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                      pathname === item.href
                        ? "text-black bg-gray-100"
                        : "text-gray-700 hover:text-gray-900 hover:bg-gray-50"
                    }`}
                  >
                    {item.name}
                  </Link>
                ))}

                {/* Return to ai-wk - Now in mobile menu */}
                <Link
                  href="/dashboard/home"
                  className="flex items-center gap-2 justify-center text-sm py-2 px-4 rounded-md bg-black text-white w-full mt-6"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Return to ai-wk
                  <LogOut size={14} />
                </Link>
              </div>

              {/* Bottom Actions */}
              <div className="border-t border-gray-200   ">
                {/* Help button */}
                <Link
                  href="/dashboard/support"
                  className="flex items-center justify-center text-sm text-gray-500 hover:text-gray-900 gap-2  py-2"
                >
                  <Image
                    src="/help-icon.svg"
                    alt="help icon"
                    height={20}
                    width={20}
                  />
                  Need Help?
                </Link>
              </div>
            </div>
          </SheetContent>
        </Sheet>
      </nav>
    </div>
  );
};

export default HeaderOlympus;
