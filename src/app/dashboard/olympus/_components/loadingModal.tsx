// @ts-nocheck
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import FadeLoader from "react-spinners/FadeLoader";

type LoadingModalProps = {
  isOpen: boolean;
  status: "generating" | "downloading" | "idle";
};

const LoadingModal = ({ isOpen, status }: LoadingModalProps) => {
  const statusText = {
    generating: "Generating",
    downloading: "Downloading Report",
    idle: "",
  };

  return (
    <Dialog open={isOpen}>
      <DialogContent className="sm:max-w-md p-0 bg-white shadow-lg rounded-md [&>button]:hidden"
        onPointerDownOutside={(e) => e.preventDefault()} >
        <DialogTitle className="sr-only"> loading dialog</DialogTitle>
        <div className="flex flex-col items-center justify-center py-8 px-6">
          <div className="mb-4">
            <FadeLoader color="black" size={17} />
          </div>
          <p className="text-gray-700 text-center">{statusText[status]}</p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default LoadingModal;
