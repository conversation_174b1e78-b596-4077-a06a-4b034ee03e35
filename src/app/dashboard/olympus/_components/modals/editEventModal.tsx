// @ts-nocheck
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, X } from "lucide-react";
import { useState, useEffect } from "react";
import { format, parse } from "date-fns";
import { toast } from "sonner";

type EditEventModalProps = {
  isOpen: boolean;
  onClose?: () => void;
  onUpdateEvent?: (updatedEvent: any, originalIndex: number) => void;
  event?: any;
  eventIndex?: number;
  startDate?: string;
  endDate?: string;
};

const EditEventModal = ({
  isOpen,
  onClose,
  onUpdateEvent,
  event,
  eventIndex,
  startDate,
  endDate,
}: EditEventModalProps) => {
  const [timestamp, setTimestamp] = useState<Date | undefined>(undefined);
  const [selectedTime, setSelectedTime] = useState<string>("09:30");
  const [eventType, setEventType] = useState<string>("");
  const [description, setDescription] = useState<string>("");

  // Populate form when event changes
  useEffect(() => {
    if (event && isOpen) {
      // Parse the date and time from the event
      try {
        const eventDate = new Date(event.time);

        setTimestamp(eventDate);

        // Extract time in HH:MM format
        const hours = eventDate.getHours().toString().padStart(2, "0");
        const minutes = eventDate.getMinutes().toString().padStart(2, "0");
        setSelectedTime(`${hours}:${minutes}`);
      } catch (error) {
        console.warn("Could not parse event date:", event.time);
        setTimestamp(undefined);
        setSelectedTime("09:30");
      }

      setEventType(event.event || "");
      setDescription(event.details || "");
    }
  }, [event, isOpen]);

  const handleSubmit = () => {
    // Validation
    if (!timestamp) {
      toast.error("Please select a date");
      return;
    }
    if (!selectedTime) {
      toast.error("Please select a time");
      return;
    }
    if (!eventType) {
      toast.error("Please select an event type");
      return;
    }
    if (!description.trim()) {
      toast.error("Please enter a description");
      return;
    }

    // Combine date and time
    const [hours, minutes] = selectedTime.split(":").map(Number);
    const combinedDateTime = new Date(timestamp);
    combinedDateTime.setHours(hours, minutes, 0, 0);

    // Create updated event object
    const updatedEvent = {
      ...event, // Keep original properties
      time: format(combinedDateTime, "yyyy-MM-dd'T'HH:mm:ss"), // Store raw datetime
      event: eventType,
      details: description.trim(),
      // Mark as edited if it was an API event
      isEdited: !event.isCustomEvent,
    };

    // Call the callback to update the event
    if (onUpdateEvent && eventIndex !== undefined) {
      onUpdateEvent(updatedEvent, eventIndex);
    }

    // Show success message
    toast.success("Event updated successfully!");

    // Close modal
    handleClose();
  };

  const handleClose = () => {
    // Reset form when closing
    setTimestamp(undefined);
    setEventType("");
    setDescription("");

    if (onClose) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className="p-8 bg-white shadow-lg rounded-md [&>button]:hidden"
        onPointerDownOutside={(e) => e.preventDefault()}
        style={{
          maxHeight: "90vh",
          marginTop: "-10vh",
        }}
      >
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-light">Edit Event</h2>
            {event?.isCustomEvent && (
              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                Custom Event
              </span>
            )}
            {event && !event.isCustomEvent && (
              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                API Event
              </span>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            {/* Date Picker */}
            <div>
              <Label
                htmlFor="edit-date-picker"
                className="text-sm font-medium mb-1"
              >
                Date
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={`w-full justify-start text-left font-normal bg-white ${
                      !timestamp ? "text-gray-400" : ""
                    }`}
                    id="edit-date-picker"
                    type="button"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {timestamp
                      ? format(timestamp, "MMM dd, yyyy")
                      : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={timestamp}
                    onSelect={setTimestamp}
                    initialFocus
                    disabled={(date) => {
                      if (!startDate || !endDate) return false;

                      const start = new Date(startDate);
                      const end = new Date(endDate);

                      // Disable dates outside the selected range
                      return date < start || date > end;
                    }}
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Time Picker */}
            <div>
              <Label
                htmlFor="edit-time-picker"
                className="text-sm font-medium mb-1"
              >
                Time (ET)
              </Label>
              <Input
                id="edit-time-picker"
                type="time"
                value={selectedTime}
                onChange={(e) => setSelectedTime(e.target.value)}
                className="w-full bg-white"
                step="300" // 5-minute intervals
              />
            </div>
          </div>

          <div>
            <Label
              htmlFor="edit-event-type"
              className="text-sm font-medium mb-1"
            >
              Event Type
            </Label>
            <Select value={eventType} onValueChange={setEventType}>
              <SelectTrigger className="w-full" id="edit-event-type">
                <SelectValue
                  className="text-[12px]"
                  placeholder="Select an event type"
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="EARNINGS">Earnings</SelectItem>
                <SelectItem value="MERGER">Merger</SelectItem>
                <SelectItem value="REGULATORY">Regulatory</SelectItem>
                <SelectItem value="ANALYST_RATING">Analyst Rating</SelectItem>
                <SelectItem value="COMPANY_NEWS">Company News</SelectItem>
                <SelectItem value="MARKET_SENTIMENT">
                  Market Sentiment
                </SelectItem>
                <SelectItem value="ECONOMIC">Economic</SelectItem>
                <SelectItem value="FED_ANNOUNCEMENT">
                  Fed Announcement
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <div>
              <Label className="block text-sm font-light mb-1">
                Description
              </Label>
              <Textarea
                id="edit-description"
                placeholder="Write a description for this event"
                className="w-full h-[200px]"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
              />
            </div>
          </div>

          <div className="w-full flex justify-between space-x-4">
            <Button onClick={handleClose} variant="outline">
              Cancel
            </Button>

            <Button onClick={handleSubmit} variant="default">
              Update Event
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditEventModal;
