// @ts-nocheck
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { yupResolver } from "@hookform/resolvers/yup";
import { X, Info } from "lucide-react";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { toast } from "sonner";

const schema = yup
  .object({
    name: yup.string().required("Name is required"),
    agent_type: yup.string().required("Type is required"),
    description: yup.string().required("Description is required"),
    risk_tolerance: yup.string().required("Risk tolerance is required"),
    reaction_speed_percentile: yup
      .number()
      .required("Reaction speed is required"),
    liquidity_constraint: yup
      .number()
      .required("Liquidity constraint is required"),
    behavioral_biases: yup.string().required("Behavioral biases is required"),
    objective: yup.string().required("Objective is required"),
    initial_capital: yup.number().required("Initial capital is required"),
    initial_positions: yup.string().required("Initial position is required"),
  })
  .required();

type EditParticipantModalProps = {
  isOpen: boolean;
  onClose?: () => void;
  onUpdateParticipant?: (updatedParticipant: any, index: number) => void;
  participant?: any;
  participantIndex?: number;
};

const EditParticipantModal = ({
  isOpen,
  onClose,
  onUpdateParticipant,
  participant,
  participantIndex,
}: EditParticipantModalProps) => {
  const [agentType, setAgentType] = useState<string>("");
  const [riskTolerance, setRiskTolerance] = useState<string>("");
  const [liquidityConstraint, setLiquidityConstraint] = useState<number>(0);
  const [behavioralBiases, setBehavioralBiases] = useState<string>("");

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm({ resolver: yupResolver(schema) });

  // Populate form when participant data changes
  useEffect(() => {
    if (participant && isOpen) {
      setValue("name", participant.name || "");
      setValue("description", participant.description || "");
      setValue(
        "reaction_speed_percentile",
        participant.reaction_speed_percentile || 0
      );
      setValue("objective", participant.objective || "");
      setValue("initial_capital", participant.initial_capital || 0);
      setValue("initial_positions", participant.initial_positions || "");

      // Set dropdown values and trigger validation
      const agentTypeValue = participant.agent_type || "";
      const riskToleranceValue = participant.risk_tolerance || "";
      const liquidityConstraintValue = participant.liquidity_constraint || 0;
      const behavioralBiasesValue = participant.behavioral_biases || "";

      setAgentType(agentTypeValue);
      setRiskTolerance(riskToleranceValue);
      setLiquidityConstraint(liquidityConstraintValue);
      setBehavioralBiases(behavioralBiasesValue);

      // Set form values and trigger validation for dropdown fields
      setValue("agent_type", agentTypeValue, { shouldValidate: true });
      setValue("risk_tolerance", riskToleranceValue, { shouldValidate: true });
      setValue("liquidity_constraint", liquidityConstraintValue, {
        shouldValidate: true,
      });
      setValue("behavioral_biases", behavioralBiasesValue, {
        shouldValidate: true,
      });
    }
  }, [participant, isOpen, setValue]);

  const onSubmit = (data: any) => {
    try {
      const updatedParticipant = {
        ...participant, // Keep original data
        name: data.name,
        agent_type: agentType,
        description: data.description,
        risk_tolerance: riskTolerance,
        reaction_speed_percentile: data.reaction_speed_percentile,
        liquidity_constraint: Number(liquidityConstraint),
        behavioral_biases: behavioralBiases,
        objective: data.objective,
        initial_capital: data.initial_capital,
        initial_positions: data.initial_positions,
        data_access: participant.data_access || "market_data",
        uses: participant.uses || "Custom Agent",
      };

      if (onUpdateParticipant && participantIndex !== undefined) {
        onUpdateParticipant(updatedParticipant, participantIndex);
      }

      toast.success("Participant updated successfully!");

      if (onClose) {
        onClose();
      }
    } catch (error) {
      toast.error("Failed to update participant");
    }
  };

  const handleClose = () => {
    reset();
    setAgentType("");
    setRiskTolerance("");
    setLiquidityConstraint(0);
    setBehavioralBiases("");
    if (onClose) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className="p-0 max-w-[600px] bg-white shadow-lg rounded-md [&>button]:hidden max-h-[95vh] flex flex-col"
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        {/* Fixed Header */}
        <div className="px-6 pt-6 flex-shrink-0">
          <h2 className="text-lg font-light">Edit Participant</h2>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto px-6 pb-4">
          <div className="space-y-4">
            <div>
              <div className="flex items-center gap-2 mb-1">
                <Label className="block text-sm font-medium">Agent Name</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="w-4 h-4 text-gray-400 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs">
                      <p>
                        A unique, descriptive name for this agent configuration.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="name"
                type="text"
                {...register("name")}
                className={`w-full h-[34px] ${
                  errors.name ? "border-red-500" : ""
                }`}
              />
              {errors.name && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.name?.message}
                </p>
              )}
            </div>

            <div>
              <div className="flex items-center gap-2 mb-1">
                <Label className="text-sm font-medium">Agent Type</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="w-4 h-4 text-gray-400 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs">
                      <p>
                        The category or role of the agent (e.g., &quot;Trend
                        Follower&quot;, &quot;Arbitrageur&quot;, &quot;Market
                        Maker&quot;).
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <Input
                id="agent_type"
                type="text"
                {...register("agent_type")}
                className={`w-full h-[34px] ${
                  errors.agent_type ? "border-red-500" : ""
                }`}
              />
              {errors.agent_type && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.agent_type?.message}
                </p>
              )}
            </div>

            <div>
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Label className="block text-sm font-light">
                    Strategy Description
                  </Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="w-4 h-4 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>
                          A detailed narrative of how the agent makes decisions
                          and what its focus or horizon is.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Textarea
                  id="description"
                  placeholder="Write a description for this participant"
                  className={`w-full h-[200px] ${
                    errors.description ? "border-red-500" : ""
                  }`}
                  {...register("description")}
                />
                {errors.description && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.description?.message}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Label className="text-sm font-medium">Risk Tolerance</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="w-4 h-4 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>
                          The agent&apos;s willingness to accept volatility
                          (Low, Medium, or High).
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Select
                  value={riskTolerance}
                  onValueChange={(value) => {
                    setRiskTolerance(value);
                    setValue("risk_tolerance", value);
                  }}
                >
                  <SelectTrigger className="w-full" id="risk-tolerance">
                    <SelectValue
                      className="text-[12px] "
                      placeholder="Select risk tolerance"
                    />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
                {errors.risk_tolerance && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.risk_tolerance?.message}
                  </p>
                )}
              </div>
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Label className="text-sm font-medium">
                    Latency Percentile (%)
                  </Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="w-4 h-4 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>
                          How quickly the agent reacts to market moves,
                          expressed as a percentile (0 – 100). Lower value means
                          shorter latency and faster reaction. Suggested value
                          for high frequency trader is less than 10, for
                          institutional investors is 20, for retail investors 50
                          or higher.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="reaction_speed_percentile"
                  type="number"
                  min="0"
                  max="100"
                  placeholder="0-100"
                  {...register("reaction_speed_percentile")}
                  className={`w-full h-[34px] ${
                    errors.reaction_speed_percentile ? "border-red-500" : ""
                  }`}
                />
                {errors.reaction_speed_percentile && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.reaction_speed_percentile?.message}
                  </p>
                )}
              </div>
            </div>
            <div>
              <div className="flex items-center gap-2 mb-1">
                <Label className="text-sm font-medium">
                  Liquidity Constraint
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="w-4 h-4 text-gray-400 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs">
                      <p>
                        Limit of an investor&apos;s order size relative to the
                        market 5-min trading volume. 0.1 means an investor can
                        place and execute an order up to 10% of the total market
                        volume.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="liquidity_constraint"
                type="number"
                min="0"
                step="0.01"
                placeholder="e.g., 0.5"
                {...register("liquidity_constraint")}
                className={`w-full h-[34px] ${
                  errors.liquidity_constraint ? "border-red-500" : ""
                }`}
                onChange={(e) => {
                  const value = parseFloat(e.target.value) || 0;
                  setLiquidityConstraint(value);
                  setValue("liquidity_constraint", value);
                }}
              />
              {errors.liquidity_constraint && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.liquidity_constraint?.message}
                </p>
              )}
            </div>
            <div>
              <div className="flex items-center gap-2 mb-1">
                <Label className="text-sm font-medium">Behavioral Biases</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="w-4 h-4 text-gray-400 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs">
                      <p>
                        Cognitive or emotional biases that influence the
                        agent&apos;s trading (e.g., herding, FOMO,
                        confirmation).
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Textarea
                {...register("behavioral_biases")}
                placeholder="Enter behavioral biases (e.g., overconfidence, loss aversion, herding behavior, anchoring bias, confirmation bias, etc.)"
                className="w-full min-h-[180px]"
                value={behavioralBiases}
                onChange={(e) => {
                  setBehavioralBiases(e.target.value);
                  setValue("behavioral_biases", e.target.value);
                }}
              />
              {errors.behavioral_biases && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.behavioral_biases?.message}
                </p>
              )}
            </div>
            <div>
              <div className="flex items-center gap-2 mb-1">
                <Label className="block text-sm font-medium">
                  Primary Objective
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="w-4 h-4 text-gray-400 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs">
                      <p>
                        The main goal driving the agent (e.g., &quot;long-term
                        appreciation&quot;, &quot;arbitrage profits&quot;).
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="objective"
                type="text"
                placeholder="e.g., Maximize returns, Minimize risk"
                {...register("objective")}
                className={`w-full h-[34px] ${
                  errors.objective ? "border-red-500" : ""
                }`}
              />
              {errors.objective && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.objective?.message}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Label className="block text-sm font-medium">
                    Allocated Cash
                  </Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="w-4 h-4 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>
                          Starting cash allocated to the trading of this stock.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="initial_capital"
                  type="number"
                  placeholder="e.g., 100000"
                  {...register("initial_capital")}
                  className={`w-full h-[34px] ${
                    errors.initial_capital ? "border-red-500" : ""
                  }`}
                />
                {errors.initial_capital && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.initial_capital?.message}
                  </p>
                )}
              </div>

              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Label className="block text-sm font-medium">
                    Initial Position
                  </Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="w-4 h-4 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>
                          Number of shares the agent holds at the start of the
                          simulation.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="initial_positions"
                  type="text"
                  placeholder="e.g., 100 shares"
                  {...register("initial_positions")}
                  className={`w-full h-[34px] ${
                    errors.initial_positions ? "border-red-500" : ""
                  }`}
                />
                {errors.initial_positions && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.initial_positions?.message}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Fixed Footer */}
        <div className="px-6 pb-4 flex-shrink-0">
          <div className="w-full flex justify-between space-x-4">
            <Button onClick={handleClose} variant="outline">
              Cancel
            </Button>

            <Button onClick={handleSubmit(onSubmit)} variant="default">
              Update Participant
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditParticipantModal;
