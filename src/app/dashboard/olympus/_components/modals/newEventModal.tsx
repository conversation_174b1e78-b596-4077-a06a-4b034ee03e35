// @ts-nocheck
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>alog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, X } from "lucide-react";
import { useState } from "react";
import { format } from "date-fns";
import { toast } from "sonner";

type NewEventModalProps = {
  isOpen: boolean;
  onClose?: () => void;
  onAddEvent?: (event: any) => void;
  startDate?: string;
  endDate?: string;
};

const NewEventModal = ({
  isOpen,
  onClose,
  onAddEvent,
  startDate,
  endDate,
}: NewEventModalProps) => {
  const [timestamp, setTimestamp] = useState<Date | undefined>(undefined);
  const [selectedTime, setSelectedTime] = useState<string>("09:30"); // Default to market open
  const [eventType, setEventType] = useState<string>("");
  const [description, setDescription] = useState<string>("");

  const handleSubmit = () => {
    // Validation
    if (!timestamp) {
      toast.error("Please select a date");
      return;
    }
    if (!selectedTime) {
      toast.error("Please select a time");
      return;
    }
    if (!eventType) {
      toast.error("Please select an event type");
      return;
    }
    if (!description.trim()) {
      toast.error("Please enter a description");
      return;
    }

    // Combine date and time
    const [hours, minutes] = selectedTime.split(":").map(Number);
    const combinedDateTime = new Date(timestamp);
    combinedDateTime.setHours(hours, minutes, 0, 0);

    // Create new event object
    const newEvent = {
      time: format(combinedDateTime, "yyyy-MM-dd'T'HH:mm:ss"), // Store raw datetime
      event: eventType,
      details: description.trim(),
      isCustomEvent: true, // Flag to identify custom events
    };

    // Call the callback to add the event
    if (onAddEvent) {
      onAddEvent(newEvent);
    }

    // Reset form
    setTimestamp(undefined);
    setSelectedTime("09:30");
    setEventType("");
    setDescription("");

    // Show success message
    toast.success("Event added successfully!");

    // Close modal
    if (onClose) {
      onClose();
    }
  };

  const handleClose = () => {
    // Reset form when closing
    setTimestamp(undefined);
    setEventType("");
    setDescription("");

    if (onClose) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className="p-8 bg-white shadow-lg rounded-md [&>button]:hidden"
        onPointerDownOutside={(e) => e.preventDefault()}
        style={{
          maxHeight: "90vh",
          marginTop: "-10vh",
        }}
      >
        <div className="space-y-4">
          <h2 className="text-lg font-light">New Events</h2>

          <div className="grid grid-cols-2 gap-4">
            {/* Date Picker */}
            <div>
              <Label htmlFor="date-picker" className="text-sm font-medium mb-1">
                Date
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={`w-full justify-start text-left font-normal bg-white ${
                      !timestamp ? "text-gray-400" : ""
                    }`}
                    id="date-picker"
                    type="button"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {timestamp
                      ? format(timestamp, "MMM dd, yyyy")
                      : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={timestamp}
                    onSelect={setTimestamp}
                    month={
                      timestamp || (startDate ? new Date(startDate) : undefined)
                    }
                    initialFocus
                    disabled={(date) => {
                      if (!startDate || !endDate) return false;

                      const start = new Date(startDate);
                      const end = new Date(endDate);

                      // Disable dates outside the selected range
                      return date < start || date > end;
                    }}
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Time Picker */}
            <div>
              <Label htmlFor="time-picker" className="text-sm font-medium mb-1">
                Time (ET)
              </Label>
              <Input
                id="time-picker"
                type="time"
                value={selectedTime}
                onChange={(e) => setSelectedTime(e.target.value)}
                className="w-full bg-white"
                step="300" // 5-minute intervals
              />
            </div>
          </div>

          <div>
            <Label htmlFor="event-type" className="text-sm font-medium mb-1">
              Event Type
            </Label>
            <Select value={eventType} onValueChange={setEventType}>
              <SelectTrigger className="w-full" id="event-type">
                <SelectValue
                  className="text-[12px] "
                  placeholder="Select an event type"
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="EARNINGS">Earnings</SelectItem>
                <SelectItem value="MERGER">Merger</SelectItem>
                <SelectItem value="REGULATORY">Regulatory</SelectItem>
                <SelectItem value="ANALYST_RATING">Analyst Rating</SelectItem>
                <SelectItem value="COMPANY_NEWS">Company News</SelectItem>
                <SelectItem value="MARKET_SENTIMENT">
                  Market Sentiment
                </SelectItem>
                <SelectItem value="ECONOMIC">Economic</SelectItem>
                <SelectItem value="FED_ANNOUNCEMENT">
                  Fed Announcement
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <div>
              <Label className="block text-sm font-light mb-1">
                Description
              </Label>
              <Textarea
                id="description"
                placeholder="Write a description for this event"
                className="w-full h-[200px]"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
              />
            </div>
          </div>
          <div className="w-full flex justify-between space-x-4">
            <Button onClick={handleClose} variant="outline">
              Cancel
            </Button>

            <Button onClick={handleSubmit} variant="default">
              Add Event
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default NewEventModal;
