// @ts-nocheck
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { X } from "lucide-react";
import { useState } from "react";

type SelectPresetParticipationModalProps = {
  isOpen: boolean;
  onClose?: () => void;
};

const SelectPresetParticipationModal = ({
  isOpen,
  onClose,
}: SelectPresetParticipationModalProps) => {
  const [downloadOption, setDownloadOption] = useState<string>("");

  const handleDownloadOptionChange = (value: string) => {
    setDownloadOption(value);
  };
  return (
    <Dialog open={isOpen} onOpenChange={() => onClose && onClose()}>
      <DialogContent
        className="p-8 bg-white shadow-lg rounded-md [&>button]:hidden"
        onPointerDownOutside={(e) => e.preventDefault()}
        style={{
          maxHeight: "90vh",
          marginTop: "-24vh",
        }}
      >
        <div className="space-y-4">
          <h2 className="text-lg font-light">Preset Participant</h2>

          <div>
            <Label
              htmlFor="download-option"
              className="text-sm font-medium mb-1"
            >
              Choose Participant
            </Label>
            <Select
              value={downloadOption}
              onValueChange={handleDownloadOptionChange}
            >
              <SelectTrigger className="w-full" id="download-option">
                <SelectValue
                  className="text-[12px] "
                  placeholder="Select a method to receive your report"
                />
              </SelectTrigger>
              <SelectContent>
                {/* <SelectItem value="download">Direct Download</SelectItem> */}
              </SelectContent>
            </Select>
          </div>

          <div className="w-full flex justify-between space-x-4">
            <Button onClick={() => onClose && onClose()} variant="outline">
              Cancel
            </Button>

            <Button onClick={onClose} variant="default">
              Use Participant
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SelectPresetParticipationModal;
