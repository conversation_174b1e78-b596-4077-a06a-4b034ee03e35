"use client";

import { useSearch<PERSON>ara<PERSON>, use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useState, useEffect, useRef, useMemo, useCallback } from "react";
import {
  OLYMPUS_URL,
  useGetQueryOlympusBackend,
  useGetQueryOlympusBackend2,
} from "@/services/api_hooks";
import axios from "axios";
import { toast } from "sonner";

// Custom hooks
import { useReplayControls } from "./hooks/useReplayControls";
import { useParticipantColors } from "./hooks/useParticipantColors";
import { useChartData } from "./hooks/useChartData";
import { useTimelineEvents } from "./hooks/useTimelineEvents";

// Components
import EventSimulationHeader from "./components/EventSimulationHeader";
import ParticipantsSection from "./components/ParticipantsSection";
import ChartSection from "./components/ChartSection";
import ReplayControls from "./components/ReplayControls";
import ActivitySidebar from "./components/ActivitySidebar";
import LoadingOverlay from "./components/LoadingOverlay";
import ErrorOverlay from "./components/ErrorOverlay";

export default function EventSimulation() {
  const router = useRouter();
  const params = useSearchParams();
  const param = useParams();

  // URL parameters
  const ticker = params.get("ticker");
  const start = params.get("start_date");
  const end = params.get("end_date");
  const history = params.get("history");
  const simulation_id = param?.id;

  // API data fetching
  const {
    data: key,
    isLoading: keyIsLoading,
    error: keyError,
    refetch: refetchKey,
  } = useGetQueryOlympusBackend2(`/report/olympus-key`, ["apiKey"]);

  const {
    data: prices,
    isLoading: isLoadingPrices,
    error: pricesError,
    refetch: refetchPrices,
  } = useGetQueryOlympusBackend(
    `/api/simulation/${simulation_id}/market_data`,
    ["price"]
  );

  const {
    data: assets,
    isLoading: isLoadingAssets,
    error: assetsError,
    refetch: refetchAssets,
  } = useGetQueryOlympusBackend(
    `/api/simulation/${simulation_id}/agent_assets`,
    ["assets"]
  );

  const {
    data: events,
    isLoading: isLoadingEvent,
    error: eventsError,
    refetch: refetchEvents,
  } = useGetQueryOlympusBackend(`/api/simulation/${simulation_id}/status`, [
    "events",
  ]);

  // Local state
  const [simulationData, setSimulationData] = useState<any>();
  const [isLoadingSimulate, setIsLoadingSimulate] = useState<boolean>(false);
  const [agentsList, setAgents] = useState<any>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [volume, setVolume] = useState(0.5);
  const [autoPlay, setAutoPlay] = useState(false);
  const [syncCharts, setSyncCharts] = useState(true);
  const [chartTimeRange, setChartTimeRange] = useState<{
    from: number;
    to: number;
  } | null>(null);
  const [isRefetching, setIsRefetching] = useState(false);
  const [refetchAttempts, setRefetchAttempts] = useState(0);

  // Refs
  const progressBarRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastSyncSourceRef = useRef<string | null>(null);

  // Custom hooks
  const { participantColors, getParticipantColor } = useParticipantColors(
    simulation_id as string
  );
  const { chartDataSource, transformedPriceData, transformedAssetData } =
    useChartData(
      prices,
      assets,
      isLoadingPrices,
      isLoadingAssets,
      pricesError,
      assetsError
    );

  const {
    replayIndex,
    isPlaying,
    playbackSpeed,
    isLooping,
    hasUserRestarted,
    handlePlayPause,
    handleRestart,
    handleStepBack,
    handleStepForward,
    handleJumpToStart,
    handleJumpToEnd,
    handleStop,
    handleSpeedChange,
    handleProgressClick,
    handleDrag,
    handleHandleMouseDown,
    handleHandleTouchStart,
  } = useReplayControls({ chartDataSource });

  const {
    timelineEventsWithProgress,
    handleTimelineEventClick: timelineEventClick,
    handleTimelineScrub: timelineScrub,
  } = useTimelineEvents(
    events,
    chartDataSource,
    replayIndex,
    isLoadingEvent,
    eventsError
  );

  // Data validation helpers
  const isValidPricesData =
    prices && Array.isArray(prices) && prices.length > 0;
  const isValidAssetsData =
    assets && Array.isArray(assets) && assets.length > 0;
  const isValidEventsData =
    events && events.event_timeline && Array.isArray(events.event_timeline);
  const isValidKeyData = key && key.api_key;

  // Loading and error states
  const isAnyLoading =
    keyIsLoading ||
    isLoadingPrices ||
    isLoadingAssets ||
    isLoadingEvent ||
    isLoadingSimulate;
  const hasErrors = keyError || pricesError || assetsError || eventsError;

  // Check if data is empty (simulation still processing)
  const isDataEmpty = useMemo(() => {
    return (
      !isAnyLoading &&
      !hasErrors &&
      (!isValidPricesData || !isValidAssetsData || !isValidEventsData)
    );
  }, [
    isAnyLoading,
    hasErrors,
    isValidPricesData,
    isValidAssetsData,
    isValidEventsData,
  ]);

  // Load agents from sessionStorage
  useEffect(() => {
    const agents = sessionStorage.getItem("agents");
    if (agents) {
      try {
        setAgents(JSON.parse(agents));
      } catch (e) {
        console.error("Failed to parse agents from sessionStorage", e);
      }
    }
  }, []);

  // Participants data
  const participants = useMemo(() => {
    if (isValidAssetsData && assets && Array.isArray(assets)) {
      return assets.map((assetAgent: any, index: number) => {
        const matchingAgent = agentsList?.find(
          (agent: any) =>
            agent.name === assetAgent.agent_name ||
            agent.agent_id === assetAgent.agent_id
        );

        const participantId =
          assetAgent.agent_id || matchingAgent?.agent_id || `agent_${index}`;
        const participantName =
          assetAgent.agent_name || matchingAgent?.name || `Agent ${index + 1}`;

        if (matchingAgent) {
          return {
            id: index,
            agent_id: participantId,
            name: participantName,
            status: index === 0 ? "active" : "inactive",
            color:
              matchingAgent.color ||
              getParticipantColor(participantId, participantName),
            agentType: matchingAgent.agent_type || "Unknown",
            riskTolerance: matchingAgent.risk_tolerance || "moderate",
            initialCapital: matchingAgent.initial_capital || 10000,
            originalAgent: matchingAgent,
            assetAgent: assetAgent,
          };
        } else {
          return {
            id: index,
            agent_id: participantId,
            name: participantName,
            status: index === 0 ? "active" : "inactive",
            color: getParticipantColor(participantId, participantName),
            agentType: "Unknown",
            riskTolerance: "moderate",
            initialCapital: 10000,
            originalAgent: {
              name: participantName,
              agent_type: "Unknown",
              description: "Agent from assets data",
              risk_tolerance: "moderate",
              reaction_speed_percentile: 50,
              liquidity_constraint: 1000,
              behavioral_biases: "None",
              objective: "Maximize returns",
              initial_capital: 10000,
              initial_positions: 0,
            },
            assetAgent: assetAgent,
          };
        }
      });
    }

    if (!agentsList || !Array.isArray(agentsList)) {
      return Array.from({ length: 3 }, (_, i) => {
        const participantId = `demo_agent_${i}`;
        const participantName = `Participant ${i + 1}`;
        return {
          id: i,
          name: participantName,
          status: i === 0 ? "active" : "inactive",
          color: getParticipantColor(participantId, participantName),
          agentType: "Demo Agent",
          riskTolerance: "moderate",
          initialCapital: 10000,
          agent_id: participantId,
          originalAgent: {
            name: participantName,
            agent_type: "Demo Agent",
            description: "Demo participant for testing purposes",
            risk_tolerance: "moderate",
            reaction_speed_percentile: 50,
            liquidity_constraint: 1000,
            behavioral_biases: "None",
            objective: "Maximize returns",
            initial_capital: 10000,
            initial_positions: 0,
          },
        };
      });
    }

    return agentsList.map((agent: any, index: number) => {
      const participantId = agent.agent_id || `agent_${index}`;
      const participantName = agent.name || `Agent ${index + 1}`;
      return {
        id: index,
        agent_id: participantId,
        name: participantName,
        status: index === 0 ? "active" : "inactive",
        color:
          agent.color || getParticipantColor(participantId, participantName),
        agentType: agent.agent_type,
        riskTolerance: agent.risk_tolerance,
        initialCapital: agent.initial_capital,
        originalAgent: agent,
      };
    });
  }, [agentsList, assets, isValidAssetsData, getParticipantColor]);

  // Detect participant actions from asset changes
  const allParticipantActions = useMemo(() => {
    if (!assets || !Array.isArray(assets) || assets.length === 0) {
      return [];
    }

    const actions: any[] = [];

    assets.forEach((agent: any, agentIndex: number) => {
      const participant = participants[agentIndex];
      if (!agent.assets_over_time || !participant) return;

      // Analyze consecutive asset data points to detect significant changes
      for (let i = 1; i < agent.assets_over_time.length; i++) {
        const current = agent.assets_over_time[i];
        const previous = agent.assets_over_time[i - 1];

        if (!current || !previous) continue;

        // Calculate position change (significant stock position changes indicate trades)
        const positionChange = current.stock_position - previous.stock_position;
        const cashChange = current.cash - previous.cash;

        // Threshold for significant trades (adjust as needed)
        const significantTradeThreshold =
          Math.abs(positionChange) > 100 || Math.abs(cashChange) > 1000;

        if (significantTradeThreshold) {
          const actionType = positionChange > 0 ? "buy" : "sell";
          const timestamp = Math.floor(new Date(current.time).getTime() / 1000);

          const participantId = participant.agent_id || `agent_${agentIndex}`;
          const participantName = participant.name || `Agent ${agentIndex + 1}`;
          actions.push({
            time: timestamp,
            type: actionType,
            participantName: participantName,
            participantColor:
              participant.color ||
              getParticipantColor(participantId, participantName),
            agentIndex: agentIndex,
            positionChange: Math.abs(positionChange),
            cashChange: Math.abs(cashChange),
            details: `${actionType === "buy" ? "Bought" : "Sold"} ${Math.abs(
              positionChange
            )} shares`,
          });
        }
      }
    });

    // Sort actions by time
    const sortedActions = actions.sort((a, b) => a.time - b.time);

    console.log("All participant actions detected:", {
      totalActions: sortedActions.length,
      actionsByParticipant: sortedActions.reduce((acc: any, action) => {
        acc[action.participantName] = (acc[action.participantName] || 0) + 1;
        return acc;
      }, {}),
      sampleActions: sortedActions.slice(0, 5),
    });

    return sortedActions;
  }, [assets, participants, getParticipantColor]);

  // Filter actions to show only up to current replay time
  const participantActions = useMemo(() => {
    if (!allParticipantActions.length || !chartDataSource.length) return [];

    const currentReplayTime = chartDataSource[replayIndex - 1]?.time || 0;
    const filteredActions = allParticipantActions.filter(
      (action) => action.time <= currentReplayTime
    );

    console.log("Filtered participant actions for replay:", {
      currentReplayTime,
      totalActions: allParticipantActions.length,
      visibleActions: filteredActions.length,
      replayIndex,
    });

    return filteredActions;
  }, [allParticipantActions, chartDataSource, replayIndex]);

  // Chart synchronization callbacks
  const handleChartTimeRangeChange = useCallback(
    (range: { from: number; to: number }, sourceChart: string) => {
      if (!syncCharts) return;

      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }

      if (lastSyncSourceRef.current !== sourceChart) {
        lastSyncSourceRef.current = sourceChart;

        syncTimeoutRef.current = setTimeout(() => {
          setChartTimeRange(range);
          setTimeout(() => {
            lastSyncSourceRef.current = null;
          }, 100);
        }, 50);
      }
    },
    [syncCharts]
  );

  const toggleChartSync = useCallback(() => {
    setSyncCharts((prev) => !prev);
    if (!syncCharts) {
      setChartTimeRange(null);
      lastSyncSourceRef.current = null;
    }
  }, [syncCharts]);

  // Enhanced timeline event handlers
  const handleTimelineEventClick = useCallback(
    (event: any) => {
      console.log("Timeline event clicked:", {
        eventTitle: event.title,
        priceChartIndex: event.priceChartIndex,
        eventTimestamp: event.eventTimestamp,
      });

      // Jump to the event's corresponding chart index
      if (
        event.priceChartIndex !== -1 &&
        event.priceChartIndex < chartDataSource.length
      ) {
        // This will be handled by the replay controls hook
        console.log("Jumping to event:", event.priceChartIndex + 1);
      }
    },
    [chartDataSource]
  );

  const handleTimelineScrub = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      const rect = e.currentTarget.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const percentage = clickX / rect.width;

      // Calculate the target replay index based on click position
      const targetIndex = Math.floor(percentage * chartDataSource.length);
      const clampedIndex = Math.max(
        1,
        Math.min(targetIndex, chartDataSource.length)
      );

      console.log("Timeline scrubbed:", {
        clickPercentage: percentage,
        targetIndex: clampedIndex,
        totalLength: chartDataSource.length,
      });
    },
    [chartDataSource]
  );

  // Cleanup
  useEffect(() => {
    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }
    };
  }, []);

  // Refetch function to check for new simulation data
  const handleRefetch = useCallback(async () => {
    setIsRefetching(true);
    setRefetchAttempts((prev) => prev + 1);

    try {
      // Refetch all simulation data
      await Promise.all([refetchPrices(), refetchAssets(), refetchEvents()]);

      toast.success("Data refreshed successfully!");
    } catch (error) {
      console.error("Error refetching data:", error);
      toast.error("Failed to refresh data. Please try again.");
    } finally {
      setIsRefetching(false);
    }
  }, [refetchPrices, refetchAssets, refetchEvents]);

  // Show loading overlay if any critical API is loading
  if (isAnyLoading && !simulationData) {
    return (
      <LoadingOverlay
        keyIsLoading={keyIsLoading}
        isLoadingPrices={isLoadingPrices}
        isLoadingAssets={isLoadingAssets}
        isLoadingEvent={isLoadingEvent}
        isLoadingSimulate={isLoadingSimulate}
      />
    );
  }

  // Show error state if there are critical errors
  if (hasErrors && !isAnyLoading) {
    return (
      <ErrorOverlay
        keyError={keyError}
        pricesError={pricesError}
        assetsError={assetsError}
        eventsError={eventsError}
      />
    );
  }

  return (
    <div className="min-h-screen bg-[#fafafa] mx-auto w-[1400px]">
      <main>
        <EventSimulationHeader
          start={start}
          end={end}
          history={history}
          ticker={ticker}
          simulation_id={simulation_id as string}
          onRefetch={handleRefetch}
          isRefetching={isRefetching}
          isDataEmpty={isDataEmpty}
        />

        {/* Refetch Button and Empty Data State */}
        {isDataEmpty && (
          <div className="mb-6 bg-white rounded-lg border border-gray-200 p-6">
            <div className="text-center">
              <div className="mb-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-8 h-8 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Simulation Still Processing
                </h3>
                <p className="text-gray-600 mb-4 max-w-md mx-auto">
                  Your simulation is still being processed on the backend. This
                  usually takes a few minutes. Click the button below to check
                  for new data.
                </p>
                {refetchAttempts > 0 && (
                  <p className="text-sm text-gray-500 mb-4">
                    Refetch attempts: {refetchAttempts}
                  </p>
                )}
                <button
                  onClick={handleRefetch}
                  disabled={isRefetching}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isRefetching ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Checking for Data...
                    </>
                  ) : (
                    <>
                      <svg
                        className="w-4 h-4 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                        />
                      </svg>
                      Refresh Data
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="flex space-x-4">
          {/* Main Content */}
          <div className="w-[100%]">
            <ParticipantsSection
              participants={participants}
              simulation_id={simulation_id as string}
              ticker={ticker}
              start={start}
              end={end}
            />

            <ChartSection
              replayIndex={replayIndex}
              chartDataSource={chartDataSource}
              transformedPriceData={transformedPriceData}
              transformedAssetData={transformedAssetData}
              participants={participants}
              participantActions={participantActions}
              getParticipantColor={getParticipantColor}
              syncCharts={syncCharts}
              chartTimeRange={chartTimeRange}
              handleChartTimeRangeChange={handleChartTimeRangeChange}
              timelineEventsWithProgress={timelineEventsWithProgress}
              handleTimelineEventClick={handleTimelineEventClick}
              handleTimelineScrub={handleTimelineScrub}
              isLoadingPrices={isLoadingPrices}
            />

            {/*   <ReplayControls
              replayIndex={replayIndex}
              chartDataSource={chartDataSource}
              isPlaying={isPlaying}
              playbackSpeed={playbackSpeed}
              isLooping={isLooping}
              showSettings={showSettings}
              volume={volume}
              autoPlay={autoPlay}
              syncCharts={syncCharts}
              progressBarRef={progressBarRef}
              isDragging={isDragging}
              handlePlayPause={handlePlayPause}
              handleRestart={handleRestart}
              handleStepBack={handleStepBack}
              handleStepForward={handleStepForward}
              handleJumpToStart={handleJumpToStart}
              handleJumpToEnd={handleJumpToEnd}
              handleStop={handleStop}
              handleSpeedChange={handleSpeedChange}
              handleProgressClick={handleProgressClick}
              handleHandleMouseDown={handleHandleMouseDown}
              handleHandleTouchStart={handleHandleTouchStart}
              setShowSettings={setShowSettings}
              setVolume={setVolume}
              setAutoPlay={setAutoPlay}
              toggleChartSync={toggleChartSync}
            /> */}
          </div>

          {/* Activity Sidebar */}
          {/*   <ActivitySidebar
            timelineEventsWithProgress={timelineEventsWithProgress}
            handleTimelineEventClick={handleTimelineEventClick}
            isLoadingEvent={isLoadingEvent}
          /> */}
        </div>
      </main>
    </div>
  );
}
