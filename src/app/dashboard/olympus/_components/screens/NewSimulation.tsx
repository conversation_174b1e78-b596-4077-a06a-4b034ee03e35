"use client";

import {
  <PERSON><PERSON>ef<PERSON>,
  ChevronRight,
  Plus,
  <PERSON><PERSON>,
  Trash2,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import NewEventModal from "../modals/newEventModal";
import EditEventModal from "../modals/editEventModal";
import { useEffect, useState, useMemo, useCallback } from "react";
import {
  useGetQueryOlympusBackend,
  useSubmitQueryOlympusBackend,
} from "@/services/api_hooks";
import CandlestickChart from "@/components/CandlestickChart";
import { motion } from "framer-motion";
import moment from "moment";
import { format, parseISO } from "date-fns";
import { formatInTimeZone } from "date-fns-tz";

// Utility function to format timestamp in Eastern Time
const formatTimestampET = (timestamp: string) => {
  try {
    // If timestamp already contains "ET", return as is
    if (timestamp && timestamp.includes("ET")) {
      return timestamp;
    }

    // Parse the timestamp and format it in Eastern Time
    const date = new Date(timestamp);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.warn("Invalid timestamp:", timestamp);
      return timestamp;
    }

    return formatInTimeZone(
      date,
      "America/New_York",
      "MMM dd, yyyy h:mm a 'ET'"
    );
  } catch (error) {
    console.warn("Could not format timestamp:", timestamp);
    return timestamp; // Fallback to original timestamp
  }
};

export default function OlympusNewSimulations() {
  const router = useRouter();
  const params = useSearchParams();
  const [showAddEventModal, setShowAddEventModal] = useState<boolean>(false);
  const [showEditEventModal, setShowEditEventModal] = useState<boolean>(false);
  const [editingEvent, setEditingEvent] = useState<any>(null);
  const [editingEventIndex, setEditingEventIndex] = useState<number>(-1);

  // State to manage custom events added via modal
  const [customEvents, setCustomEvents] = useState<any[]>([]);

  // Handler to add new event from modal
  const handleAddEvent = (newEvent: any) => {
    console.log("Adding new event:", newEvent);
    const eventWithId = {
      ...newEvent,
      _id: `custom_${Date.now()}_${Math.random()}`,
    };
    setCustomEvents((prev) => [...prev, eventWithId]);
  };

  // Handler to delete event
  const handleDeleteEvent = (event: any, index: number) => {
    console.log("Deleting event:", event, "at index:", index);

    if (event.isCustomEvent) {
      // Delete custom event directly
      setCustomEvents((prev) =>
        prev.filter((customEvent) => customEvent._id !== event._id)
      );
    } else {
      // For API events, add them to a "deleted" list to hide them
      const deletedEvent = {
        _id: `deleted_${Date.now()}_${Math.random()}`,
        isDeletedApiEvent: true,
        originalApiIndex: event._originalApiIndex,
      };
      setCustomEvents((prev) => [...prev, deletedEvent]);
    }
  };

  // Handler to open edit modal
  const handleEditEvent = (event: any, index: number) => {
    console.log("Editing event:", event, "at index:", index);

    // Add metadata to track the event's origin
    const eventWithMetadata = {
      ...event,
      _originalIndex: index,
      _isFromAllEventsArray: true,
    };

    setEditingEvent(eventWithMetadata);
    setEditingEventIndex(index);
    setShowEditEventModal(true);
  };

  // Handler to update event from edit modal
  const handleUpdateEvent = (updatedEvent: any, originalIndex: number) => {
    console.log("Updating event:", updatedEvent, "at index:", originalIndex);

    const currentAllEvents = allEvents;
    const eventBeingEdited = currentAllEvents[originalIndex];

    if (!eventBeingEdited) {
      console.error("Event not found at index:", originalIndex);
      return;
    }

    if (eventBeingEdited.isCustomEvent) {
      // This is a custom event - update it directly using the ID
      setCustomEvents((prev) =>
        prev.map((event) =>
          event._id === eventBeingEdited._id
            ? {
                ...updatedEvent,
                _id: eventBeingEdited._id,
                isCustomEvent: true,
              }
            : event
        )
      );
    } else {
      // This is an API event - create a replacement custom event
      const newCustomEvent = {
        ...updatedEvent,
        _id: `custom_${Date.now()}_${Math.random()}`,
        isCustomEvent: true,
        replacesApiEventIndex: eventBeingEdited._originalApiIndex,
      };
      setCustomEvents((prev) => [...prev, newCustomEvent]);
    }
  };

  const ticker = params.get("ticker");
  const start = params.get("start_date");
  const end = params.get("end_date");

  // Persistence keys for localStorage
  const EVENT_STORAGE_KEYS = useMemo(
    () => ({
      customEvents: `olympus_custom_events_${ticker}_${start}_${end}`,
    }),
    [ticker, start, end]
  );

  // State to track current simulation parameters
  const [currentSimulationKey, setCurrentSimulationKey] = useState<string>("");

  // Load persisted events on component mount
  useEffect(() => {
    try {
      const savedCustomEvents = localStorage.getItem(
        EVENT_STORAGE_KEYS.customEvents
      );
      if (savedCustomEvents) {
        const parsedCustomEvents = JSON.parse(savedCustomEvents);
        console.log("Loading persisted custom events:", parsedCustomEvents);
        setCustomEvents(parsedCustomEvents);
      }
    } catch (error) {
      console.error("Error loading persisted event data:", error);
    }
  }, [EVENT_STORAGE_KEYS]);

  // Clear persisted events when simulation parameters change
  useEffect(() => {
    const simulationKey = `${ticker}_${start}_${end}`;

    // If this is a new simulation (different parameters), clear old events
    if (simulationKey && simulationKey !== currentSimulationKey) {
      try {
        // Clear events from previous simulation
        localStorage.removeItem(EVENT_STORAGE_KEYS.customEvents);
        setCustomEvents([]);
        setCurrentSimulationKey(simulationKey);
        console.log("Cleared old events for new simulation:", simulationKey);
      } catch (error) {
        console.error("Error clearing old event data:", error);
      }
    }
  }, [ticker, start, end, currentSimulationKey, EVENT_STORAGE_KEYS]);

  // Persist custom events whenever they change
  useEffect(() => {
    try {
      localStorage.setItem(
        EVENT_STORAGE_KEYS.customEvents,
        JSON.stringify(customEvents)
      );
      console.log("Persisted custom events:", customEvents);
    } catch (error) {
      console.error("Error persisting custom events:", error);
    }
  }, [customEvents, EVENT_STORAGE_KEYS.customEvents]);

  // Function to clear all persisted event data (useful for debugging or reset)
  const clearPersistedEventData = useCallback(() => {
    try {
      localStorage.removeItem(EVENT_STORAGE_KEYS.customEvents);
      setCustomEvents([]);
      console.log("Cleared all persisted event data");
    } catch (error) {
      console.error("Error clearing persisted event data:", error);
    }
  }, [EVENT_STORAGE_KEYS]);

  // Add clearPersistedEventData to window for debugging (development only)
  useEffect(() => {
    if (
      typeof window !== "undefined" &&
      process.env.NODE_ENV === "development"
    ) {
      (window as any).clearOlympusEventData = clearPersistedEventData;
      console.log(
        "Debug: Use window.clearOlympusEventData() to clear persisted event data"
      );
    }
  }, [clearPersistedEventData]);

  const { data, isLoading, isFetching } = useGetQueryOlympusBackend(
    `/api/market/historical/${ticker}?start_date=${start}&end_date=${end}&interval=1h`,
    ["events"]
  );

  const {
    mutate,
    isPending,
    data: events,
  } = useSubmitQueryOlympusBackend("/api/market/events/generate", "POST", {
    onSuccess: (data: any) => {
      router.push(
        `/dashboard/olympus/simulations?ticker=${ticker}&start_date=${start}&end_date=${end}`
      );
      console.log(data?.data, "events generated");
    },
  });

  // Only trigger event generation when we have valid parameters and no existing events
  useEffect(() => {
    const simulationKey = `${ticker}_${start}_${end}`;

    if (
      ticker &&
      start &&
      end &&
      simulationKey === currentSimulationKey &&
      !events
    ) {
      console.log("Triggering event generation for simulation:", simulationKey);
      (
        mutate as unknown as (data: {
          ticker: string;
          start_date: string;
          end_date: string;
        }) => void
      )({
        ticker: ticker as string,
        start_date: start as string,
        end_date: end as string,
      });
    }
  }, [ticker, start, end, currentSimulationKey, events, mutate]);

  // Manual trigger for event generation
  const regenerateEvents = () => {
    if (ticker && start && end) {
      console.log("Manually regenerating events for:", { ticker, start, end });
      (
        mutate as unknown as (data: {
          ticker: string;
          start_date: string;
          end_date: string;
        }) => void
      )({
        ticker: ticker as string,
        start_date: start as string,
        end_date: end as string,
      });
    }
  };

  // Combine API events with custom events, handling replacements and deletions
  const allEvents = useMemo(() => {
    const apiEvents = (events as any)?.data || [];

    // Get indices of replaced API events
    const replacedIndices = new Set(
      customEvents
        .filter((event) => event.replacesApiEventIndex !== undefined)
        .map((event) => event.replacesApiEventIndex)
    );

    // Get indices of deleted API events
    const deletedIndices = new Set(
      customEvents
        .filter(
          (event) =>
            event.isDeletedApiEvent && event.originalApiIndex !== undefined
        )
        .map((event) => event.originalApiIndex)
    );

    // Filter out replaced and deleted API events
    const filteredApiEvents = apiEvents
      .filter(
        (_: any, index: number) =>
          !replacedIndices.has(index) && !deletedIndices.has(index)
      )
      .map((event: any, index: number) => ({
        ...event,
        _id: `api_${index}`,
        _originalApiIndex: index,
        isCustomEvent: false,
      }));

    // Filter out deleted markers and add IDs to remaining custom events
    const customEventsWithIds = customEvents
      .filter((event: any) => !event.isDeletedApiEvent)
      .map((event: any, index: number) => ({
        ...event,
        _id: event._id || `custom_${index}`,
        isCustomEvent: true,
      }));

    const allEventsUnsorted = [...filteredApiEvents, ...customEventsWithIds];

    // Sort events chronologically by timestamp
    const sortedEvents = allEventsUnsorted.sort((a, b) => {
      try {
        const dateA = new Date(a.time);
        const dateB = new Date(b.time);

        // Handle invalid dates by putting them at the end
        if (isNaN(dateA.getTime()) && isNaN(dateB.getTime())) return 0;
        if (isNaN(dateA.getTime())) return 1;
        if (isNaN(dateB.getTime())) return -1;

        return dateA.getTime() - dateB.getTime();
      } catch (error) {
        console.warn("Error sorting events:", error);
        return 0;
      }
    });

    console.log("Events sorted chronologically:", {
      totalEvents: sortedEvents.length,
      firstEvent: sortedEvents[0]?.time,
      lastEvent: sortedEvents[sortedEvents.length - 1]?.time,
    });

    return sortedEvents;
  }, [events, customEvents]);

  // Transform API data to hourly format for the chart
  const hourlyChartData = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }

    return data
      .map((item: any) => {
        // Convert timestamp to Unix timestamp for hourly precision
        const timestamp = item.timestamp || item.time;
        const time = timestamp
          ? Math.floor(new Date(timestamp).getTime() / 1000)
          : Math.floor(Date.now() / 1000);

        return {
          time: time, // Unix timestamp for hourly data
          open: item.open,
          high: item.high,
          low: item.low,
          close: item.close,
          volume: item.volume,
          // Keep original timestamp for debugging
          originalTimestamp: timestamp,
        };
      })
      .sort((a, b) => a.time - b.time); // Ensure chronological order
  }, [data]);

  console.log("Original data:", data);
  console.log("Transformed hourly data:", hourlyChartData);

  return (
    <motion.div
      initial={{ opacity: 0, y: -30 }}
      animate={{
        opacity: 1,
        transform: "translateY(0%)",
        type: "spring",
      }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className="min-h-screen bg-[#fafafa] mx-auto w-[1400px]"
    >
      <NewEventModal
        isOpen={showAddEventModal}
        onClose={() => setShowAddEventModal(false)}
        onAddEvent={handleAddEvent}
        startDate={start || undefined}
        endDate={end || undefined}
      />

      <EditEventModal
        isOpen={showEditEventModal}
        onClose={() => {
          setShowEditEventModal(false);
          setEditingEvent(null);
          setEditingEventIndex(-1);
        }}
        onUpdateEvent={handleUpdateEvent}
        event={editingEvent}
        eventIndex={editingEventIndex}
        startDate={start || undefined}
        endDate={end || undefined}
      />
      <main>
        <div className="flex items-center gap-2 mb-8">
          <div
            onClick={() => router.back()}
            className="cursor-pointer inline-flex items-center justify-center p-1 hover:bg-[#f7f7f7] rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-[#0359d8] focus:ring-offset-2"
          >
            <ArrowLeft className="w-4 h-4" />
          </div>
          <div className="w-[1px] bg-[#98a2b3] h-3" />

          <span className="text-[#98a2b3] text-sm">New Simulation</span>
          <span className="text-[#98a2b3] text-sm">
            <ChevronRight className="w-4 h-4" />
          </span>
          <span className="text-[#1d1d1d] text-sm font-medium">{ticker}</span>
        </div>

        <div className="mb-12 flex justify-between items-end">
          <div>
            <h1 className="text-[#1d1d1d] text-xl font-light mb-2">
              Ticker Based Events
            </h1>
            <p className="text-[#98a2b3] text-sm">
              The Events are sourced by Olympus, you can edit the events to your
              preference.
            </p>
          </div>
          <div className="flex gap-2">
            {/*  {data?.length && (
              <Button
                onClick={regenerateEvents}
                variant="outline"
                disabled={isPending}
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                {isPending ? "Regenerating..." : "Regenerate Events"}
              </Button>
            )} */}
            {data?.length && allEvents?.length && (
              <Button
                onClick={() => {
                  router.push(
                    `/dashboard/olympus/simulations/participants?ticker=${ticker}&start_date=${start}&end_date=${end}`
                  );
                  sessionStorage.setItem("events", JSON.stringify(allEvents));
                }}
                className="bg-black text-white hover:bg-gray-800"
              >
                Continue to Participants
              </Button>
            )}
          </div>
        </div>

        <div className="flex md:flex-row flex-col gap-6 bg-gray-50 min-h-screen items-stretch">
          <div className="flex-1 bg-white rounded-lg border border-gray-200 p-6 flex flex-col w-full min-h-[calc(100vh-200px)] h-[900px]">
            <h1 className="mb-2 text-lg">
              {ticker}
              {"  "}
              <span className="text-gray-600 text-base">
                {moment(start).format("DD MMM, YYYY")} -{" "}
                {moment(end).format("DD MMM, YYYY")}
              </span>
            </h1>
            {hourlyChartData && hourlyChartData.length > 0 ? (
              <CandlestickChart data={hourlyChartData} />
            ) : isLoading || isFetching ? (
              <div className="flex flex-col items-center justify-center h-80">
                <div className="relative">
                  <div className="w-12 h-12 border-4 border-gray-200 border-t-gray-600 rounded-full animate-spin"></div>
                  <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-t-gray-500 rounded-full animate-ping"></div>
                </div>
                <p className="mt-4 text-gray-600 font-medium">
                  Getting Price Changes
                </p>
                <p className="text-sm text-gray-400 mt-1">
                  Loading historical data...
                </p>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-80">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <Sparkles className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No Price Data
                </h3>
                <p className="text-gray-500 text-center max-w-md">
                  No historical price data available for the selected ticker and
                  date range.
                </p>
              </div>
            )}
          </div>

          <div className="flex-1 bg-white rounded-lg border border-gray-200 p-6 w-full min-h-[calc(100vh-200px)] h-[900px] flex flex-col">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-light">Events</h2>
              <Button variant="link" size="sm" className="text-blue-600 p-0">
                <Sparkles /> Identified by Olympus
              </Button>
            </div>

            <div className="mb-6">
              <Button
                onClick={() => setShowAddEventModal(true)}
                variant="outline"
                className="w-full border-dashed border-gray-300 text-gray-600 hover:bg-gray-50 text-center flex-col py-10"
              >
                <Plus className="w-4 h-4 mr-2" />
                <div>Click to add an event</div>
              </Button>
            </div>

            <div className="flex-1 overflow-y-auto scrollbar-hide">
              <div className="space-y-4 pr-2">
                {allEvents?.length ? (
                  allEvents.map(
                    (
                      event: {
                        details: string;
                        time: string;
                        event: string;
                        isCustomEvent?: boolean;
                        _id?: string;
                        _originalApiIndex?: number;
                      },
                      idx: number
                    ) => (
                      <Card
                        key={idx}
                        className={`p-4 border-none shadow-none ${
                          event.isCustomEvent
                            ? "bg-blue-50 border-l-4 border-l-blue-500"
                            : "bg-[#F9F9F9]"
                        }`}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-600">
                              {formatTimestampET(event?.time)}
                            </span>
                            {event.isCustomEvent && (
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                Custom
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-1">
                            {/* Edit button - available for all events */}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-blue-500 hover:text-blue-700 p-1"
                              onClick={() => handleEditEvent(event, idx)}
                              title="Edit event"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>

                            {/* Delete button - available for all events */}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-500 hover:text-red-700 p-1"
                              onClick={() => handleDeleteEvent(event, idx)}
                              title="Delete event"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>

                        <p className="text-sm text-gray-800 mb-2 leading-relaxed">
                          {event?.details}
                        </p>

                        <div className={`text-xs`}>
                          {event.event.replace(/_/g, " ")}
                        </div>
                      </Card>
                    )
                  )
                ) : isPending ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <div className="relative mb-4">
                      <div className="w-10 h-10 border-3 border-gray-200 border-t-gray-600 rounded-full animate-spin"></div>
                      <div className="absolute inset-0 w-10 h-10 border-3 border-transparent border-t-gray-500 rounded-full animate-ping opacity-75"></div>
                    </div>
                    <p className="text-gray-600 font-medium">
                      Generating Events
                    </p>
                    <p className="text-sm text-gray-400 mt-1">
                      AI is analyzing market data...
                    </p>
                    <div className="flex space-x-1 mt-3">
                      <div className="w-2 h-2 bg-black rounded-full animate-bounce"></div>
                      <div
                        className="w-2 h-2 bg-black rounded-full animate-bounce"
                        style={{ animationDelay: "0.1s" }}
                      ></div>
                      <div
                        className="w-2 h-2 bg-black rounded-full animate-bounce"
                        style={{ animationDelay: "0.2s" }}
                      ></div>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                      <Sparkles className="w-8 h-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No Events
                    </h3>
                    <p className="text-gray-500 text-center max-w-md">
                      No events have been generated for this simulation yet.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </motion.div>
  );
}
