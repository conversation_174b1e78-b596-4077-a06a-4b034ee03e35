"use client";

import { useGetQueryOlympusBackend } from "@/services/api_hooks";
import { <PERSON><PERSON>ef<PERSON>, ChevronRight } from "lucide-react";
import { useSearchParams, useParams, useRouter } from "next/navigation";
import { useMemo, useEffect } from "react";

// Custom hooks
import { useChartData } from "./hooks/useChartData";

// Components
import AgentDecision<PERSON>hart from "@/components/AgentDecisionChart";

import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  ReferenceLine,
  BarChart,
  Bar,
} from "recharts";

const CustomLegend = () => (
  <div className="flex items-center justify-end gap-6 mb-4 text-sm">
    <div className="flex items-center gap-2">
      <div className="w-4 h-0.5 bg-[#0d5eff]"></div>
      <span className="text-[#474747]">Total Assets</span>
    </div>
    <div className="flex items-center gap-2">
      <div className="w-4 h-0.5 border-t-2 border-dashed border-[#8ac48a]"></div>
      <span className="text-[#474747]">Cash</span>
    </div>
    <div className="flex items-center gap-2">
      <div className="w-4 h-0.5 border-t-2 border-dotted border-[#ff9595]"></div>
      <span className="text-[#474747]">Stock Value</span>
    </div>
  </div>
);

const TriangleDot = (props: any) => {
  const { cx, cy, fill } = props;
  if (cx === undefined || cy === undefined) {
    return <circle cx={0} cy={0} r={0} fill="transparent" />;
  }

  return (
    <polygon
      points={`${cx},${cy - 6} ${cx - 5},${cy + 4} ${cx + 5},${cy + 4}`}
      fill={fill}
      stroke={fill}
      strokeWidth={1}
    />
  );
};

// Custom inverted triangle dot component for stop loss
const InvertedTriangleDot = (props: any) => {
  const { cx, cy, fill } = props;
  if (cx === undefined || cy === undefined) {
    return <circle cx={0} cy={0} r={0} fill="transparent" />;
  }

  return (
    <polygon
      points={`${cx},${cy + 6} ${cx - 5},${cy - 4} ${cx + 5},${cy - 4}`}
      fill={fill}
      stroke={fill}
      strokeWidth={1}
    />
  );
};

// Enhanced Custom tooltip component matching the design
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    // Get the data point for additional information
    const dataPoint = payload[0]?.payload;

    // Determine order type based on data (you can customize this logic)
    const orderType =
      dataPoint?.entryPrices > dataPoint?.actualPrice
        ? "Short Order"
        : "Long Order";

    return (
      <div className="bg-white border border-gray-200 rounded-lg shadow-xl p-4 min-w-[280px] max-w-[400px]">
        {/* Header with order type */}
        <div className="flex items-center gap-2 mb-3">
          <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-b-[8px] border-l-transparent border-r-transparent border-b-green-500"></div>
          <h3 className="font-semibold text-gray-900 text-sm">Limit Order</h3>
        </div>

        {/* Order details in structured format */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-gray-500 text-sm">Position</span>
            <span className="font-medium text-gray-900 text-sm">
              {orderType}
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-500 text-sm">Entry Price</span>
            <span className="font-medium text-gray-900 text-sm">
              {dataPoint?.entryPrices || 0}
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-500 text-sm">Take Profit</span>
            <span className="font-medium text-gray-900 text-sm">
              {dataPoint?.takeProfit || 0}
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-500 text-sm">Stop Loss</span>
            <span className="font-medium text-gray-900 text-sm">
              {dataPoint?.stopLoss || 0}
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-500 text-sm">Confidence</span>
            <span className="font-medium text-gray-900 text-sm">
              {dataPoint?.confidence || 1}
            </span>
          </div>
        </div>

        {/* Reasoning section */}
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="text-gray-500 text-sm mb-1">Reasoning</div>
          <div className="text-gray-900 text-sm leading-relaxed">
            {dataPoint?.reasoning ||
              `Market analysis suggests ${orderType.toLowerCase()} position based on technical indicators and price action at ${label}.`}
          </div>
        </div>
      </div>
    );
  }
  return null;
};

const CustomTooltipBar = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const sellAmount =
      payload.find((p: any) => p.dataKey === "sellAmount")?.value || 0;
    const buyAmount =
      payload.find((p: any) => p.dataKey === "buyAmount")?.value || 0;
    const dataPoint = payload[0]?.payload;

    return (
      <div className="bg-white border border-[#e2e8f0] rounded-lg shadow-lg p-3 min-w-[280px]">
        <p className="text-sm font-medium text-[#1d1d1d] mb-2">{label}</p>

        {sellAmount > 0 && (
          <div className="flex items-center gap-2 mb-1">
            <div className="w-2 h-2 bg-[#8ac48a] rounded-full"></div>
            <span className="text-sm text-[#474747]">
              Sell Amount: {sellAmount}
            </span>
          </div>
        )}

        {buyAmount > 0 && (
          <div className="flex items-center gap-2 mb-1">
            <div className="w-2 h-2 bg-[#ff9595] rounded-full"></div>
            <span className="text-sm text-[#474747]">
              Buy Amount: {buyAmount}
            </span>
          </div>
        )}

        {/* Additional trade details */}
        {dataPoint && (
          <div className="mt-2 pt-2 border-t border-gray-100 space-y-1">
            {dataPoint.order_type && (
              <div className="flex justify-between text-xs">
                <span className="text-gray-500">Order Type:</span>
                <span className="text-gray-900 font-medium capitalize">
                  {dataPoint.order_type.replace("_", " ")}
                </span>
              </div>
            )}

            {dataPoint.trading_price && (
              <div className="flex justify-between text-xs">
                <span className="text-gray-500">Price:</span>
                <span className="text-gray-900 font-medium">
                  ${dataPoint.trading_price.toFixed(2)}
                </span>
              </div>
            )}

            {dataPoint.updated_position !== undefined && (
              <div className="flex justify-between text-xs">
                <span className="text-gray-500">Position:</span>
                <span className="text-gray-900 font-medium">
                  {dataPoint.updated_position.toLocaleString()} shares
                </span>
              </div>
            )}

            {dataPoint.updated_cash !== undefined && (
              <div className="flex justify-between text-xs">
                <span className="text-gray-500">Cash:</span>
                <span className="text-gray-900 font-medium">
                  ${dataPoint.updated_cash.toLocaleString()}
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    );
  }
  return null;
};

// Enhanced Custom tooltip component for Total Assets chart
const CustomTooltipTotalAssets = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const dataPoint = payload[0]?.payload;

    return (
      <div className="bg-white border border-gray-200 rounded-lg shadow-xl p-4 min-w-[280px] max-w-[400px]">
        {/* Header */}
        <div className="flex items-center gap-2 mb-3">
          <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-b-[8px] border-l-transparent border-r-transparent border-b-blue-500"></div>
          <h3 className="font-semibold text-gray-900 text-sm">
            Asset Overview
          </h3>
        </div>

        {/* Time */}
        <div className="text-gray-500 text-sm mb-3">{label}</div>

        {/* Asset details */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-gray-500 text-sm">
              Total Assets (Rebased)
            </span>
            <span className="font-medium text-gray-900 text-sm">
              {dataPoint?.totalAssetsRebase?.toFixed(2) || "N/A"}
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-500 text-sm">Cash</span>
            <span className="font-medium text-gray-900 text-sm">
              ${dataPoint?.cash?.toLocaleString() || "N/A"}
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-500 text-sm">Stock Value</span>
            <span className="font-medium text-gray-900 text-sm">
              ${dataPoint?.stockValue?.toLocaleString() || "N/A"}
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-500 text-sm">Stock Position</span>
            <span className="font-medium text-gray-900 text-sm">
              {dataPoint?.stock_position?.toLocaleString() || "N/A"} shares
            </span>
          </div>

          {dataPoint?.return !== undefined && (
            <div className="flex justify-between items-center">
              <span className="text-gray-500 text-sm">Return</span>
              <span
                className={`font-medium text-sm ${
                  dataPoint.return >= 0 ? "text-green-600" : "text-red-600"
                }`}
              >
                {dataPoint.return >= 0 ? "+" : ""}
                {dataPoint.return.toFixed(4)}%
              </span>
            </div>
          )}

          {dataPoint?.position_change !== undefined &&
            dataPoint.position_change !== 0 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-500 text-sm">Position Change</span>
                <span
                  className={`font-medium text-sm ${
                    dataPoint.position_change > 0
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  {dataPoint.position_change > 0 ? "+" : ""}
                  {dataPoint.position_change.toLocaleString()} shares
                </span>
              </div>
            )}
        </div>
      </div>
    );
  }
  return null;
};

export default function Participant() {
  const router = useRouter();
  const params = useSearchParams();
  const param = useParams();

  const simulation_id = params.get("simulation_id");
  const ticker = params.get("ticker");
  const name = params.get("name");

  const agent_id = param?.id;

  const { data: agent_details, isLoading } = useGetQueryOlympusBackend(
    `/api/simulation/${simulation_id}/agent/${agent_id}`,
    ["agent_details"]
  );

  // Fetch price data from the same endpoint as EventSimulation
  const {
    data: prices,
    isLoading: isLoadingPrices,
    error: pricesError,
  } = useGetQueryOlympusBackend(
    `/api/simulation/${simulation_id}/market_data`,
    ["price"]
  );

  const agent_decisions = agent_details?.agent_decisions;
  const agent_history = agent_details?.agent_config?.trade_history;
  const assets_over_time = agent_details?.assets_over_time;

  // Transform agent_decisions to chart data format
  const chartData = useMemo(() => {
    if (!agent_decisions || !Array.isArray(agent_decisions)) {
      // Fallback to dummy data if no decisions available
      return [
        {
          month: "January",
          actualPrice: 200,
          entryPrices: 50,
          takeProfit: 100,
          stopLoss: 620,
          confidence: 0.85,
          reasoning:
            "Strong bullish momentum with RSI oversold conditions. Technical analysis indicates potential reversal at current support levels.",
        },
        {
          month: "February",
          actualPrice: 200,
          entryPrices: 300,
          takeProfit: 180,
          stopLoss: 550,
          confidence: 0.72,
          reasoning:
            "Market consolidation phase with mixed signals. Volume analysis suggests accumulation by institutional investors.",
        },
        {
          month: "March",
          actualPrice: 200,
          entryPrices: 650,
          takeProfit: 500,
          stopLoss: 200,
          confidence: 0.91,
          reasoning:
            "Breakout above key resistance level confirmed. Strong fundamentals support continued upward movement.",
        },
        {
          month: "April",
          actualPrice: 200,
          entryPrices: 600,
          takeProfit: 430,
          stopLoss: 260,
          confidence: 0.68,
          reasoning:
            "Approaching overbought territory. Risk management suggests taking partial profits at current levels.",
        },
        {
          month: "May",
          actualPrice: 200,
          entryPrices: 80,
          takeProfit: 680,
          stopLoss: 50,
          confidence: 0.79,
          reasoning:
            "Earnings beat expectations driving momentum. However, market volatility requires careful position sizing.",
        },
        {
          month: "June",
          actualPrice: 200,
          entryPrices: 180,
          takeProfit: 250,
          stopLoss: 450,
          confidence: 0.83,
          reasoning:
            "Correction provides good entry opportunity. Support levels holding well with increased buying interest.",
        },
        {
          month: "July",
          actualPrice: 200,
          entryPrices: 160,
          takeProfit: 150,
          stopLoss: 550,
        },
      ];
    }

    // Transform API data to chart format
    return agent_decisions.map((decision: any, index: number) => {
      // Convert timestamp to readable month/time format
      const timestamp = new Date(decision.timestamp);
      const month = timestamp.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });

      return {
        month: month,
        actualPrice: decision.current_price || 0,
        entryPrices: decision.entry_price || 0,
        takeProfit: decision.take_profit || 0,
        stopLoss: decision.stop_loss || 0,
        confidence: decision.confidence || 0,
        reasoning: decision.reasoning || "No reasoning provided",
        // Additional fields from API for reference
        time_step: decision.time_step,
        position: decision.position,
        current_volume: decision.current_volume,
        pre_cash: decision.pre_cash,
        post_cash: decision.post_cash,
        pre_nosh: decision.pre_nosh,
        post_nosh: decision.post_nosh,
        pre_stock_value: decision.pre_stock_value,
        post_stock_value: decision.post_stock_value,
        bnh_return: decision.bnh_return,
        strategy_return: decision.strategy_return,
        decision_id: decision.decision_id,
      };
    });
  }, [agent_decisions]);

  // Use the same chart data transformation as EventSimulation for price chart
  const { chartDataSource, transformedPriceData } = useChartData(
    prices,
    null, // No assets data needed for this chart
    isLoadingPrices,
    false,
    pricesError,
    null
  );

  // Debug price data fetching
  useEffect(() => {
    console.log("Participant - Price data debug:", {
      pricesRaw: prices,
      isLoadingPrices,
      pricesError,
      transformedPriceDataLength: transformedPriceData.length,
      sampleTransformedPrice: transformedPriceData.slice(0, 3),
    });
  }, [prices, isLoadingPrices, pricesError, transformedPriceData]);

  // Transform agent decisions to overlay format for price chart
  const agentDecisionOverlays = useMemo(() => {
    if (!agent_decisions || !Array.isArray(agent_decisions)) {
      return [];
    }

    // Create a map of trading decisions by timestamp for quick lookup
    const tradingDecisionsMap = new Map();
    if (agent_history && Array.isArray(agent_history)) {
      agent_history.forEach((trade: any) => {
        const timestamp = new Date(trade.timestamp).getTime();
        tradingDecisionsMap.set(timestamp, {
          trading_amount: trade.trading_amount || 0,
          trading_price: trade.trading_price || 0,
          order_type: trade.order_type,
          position_change: trade.position_change || 0,
        });
      });
    }

    // Transform agent decisions to overlay format
    return agent_decisions.map((decision: any) => {
      const timestamp = new Date(decision.timestamp);
      const time = Math.floor(timestamp.getTime() / 1000); // Convert to Unix timestamp

      // Check if there's a trading decision at this timestamp
      const tradingDecision = tradingDecisionsMap.get(timestamp.getTime());

      return {
        time: time,
        currentPrice: decision.current_price || 0,
        entryPrice: decision.entry_price || 0,
        takeProfit: decision.take_profit || 0,
        stopLoss: decision.stop_loss || 0,
        confidence: decision.confidence || 0,
        position: decision.position || "hold",
        reasoning: decision.reasoning || "",
        // Trading information if available
        tradingAmount: tradingDecision?.trading_amount || 0,
        tradingPrice: tradingDecision?.trading_price || 0,
        orderType: tradingDecision?.order_type || "",
        positionChange: tradingDecision?.position_change || 0,
        // Additional metadata
        timeStep: decision.time_step,
        decisionId: decision.decision_id,
        originalTimestamp: decision.timestamp,
      };
    });
  }, [agent_decisions, agent_history]);

  // Transform agent_history to trading amount chart data
  const dataBar = useMemo(() => {
    if (!agent_history || !Array.isArray(agent_history)) {
      // Fallback to dummy data if no trade history available
      return [
        { month: "January", sellAmount: 700, buyAmount: 0 },
        { month: "February", sellAmount: 0, buyAmount: 0 },
        { month: "March", sellAmount: 0, buyAmount: 0 },
        { month: "April", sellAmount: 0, buyAmount: 0 },
        { month: "May", sellAmount: 0, buyAmount: 700 },
        { month: "June", sellAmount: 0, buyAmount: 0 },
        { month: "July", sellAmount: 0, buyAmount: 0 },
      ];
    }

    return agent_history.map((trade: any) => {
      // Convert timestamp to readable format
      const timestamp = new Date(trade.timestamp);
      const month = timestamp.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });

      // Determine buy/sell based on order_type and position change
      let sellAmount = 0;
      let buyAmount = 0;

      if (
        trade.order_type === "limit_entry" ||
        trade.order_type === "market_entry"
      ) {
        // Entry orders are buys
        buyAmount = trade.trading_amount || 0;
      } else if (
        trade.order_type === "limit_exit" ||
        trade.order_type === "market_exit"
      ) {
        // Exit orders are sells
        sellAmount = trade.trading_amount || 0;
      } else {
        // For other order types, determine based on position change
        if (trade.updated_position > 0) {
          buyAmount = trade.trading_amount || 0;
        } else {
          sellAmount = trade.trading_amount || 0;
        }
      }

      return {
        month: month,
        sellAmount: sellAmount,
        buyAmount: buyAmount,
        // Additional context from trade history
        order_type: trade.order_type,
        trading_price: trade.trading_price,
        updated_cash: trade.updated_cash,
        updated_position: trade.updated_position,
        time_step: trade.time_step,
        timestamp: trade.timestamp,
      };
    });
  }, [agent_history]);

  // Transform assets_over_time to Total Assets chart data
  const totalAssetsData = useMemo(() => {
    if (!assets_over_time || !Array.isArray(assets_over_time)) {
      // Fallback to dummy data if no assets data available
      return [
        {
          month: "January",
          totalAssets: 40000,
          cash: 18000,
          stockValue: 20000,
        },
        {
          month: "February",
          totalAssets: 40000,
          cash: 35000,
          stockValue: 19000,
        },
        { month: "March", totalAssets: 40000, cash: 100000, stockValue: 20000 },
        { month: "April", totalAssets: 40000, cash: 88000, stockValue: 22000 },
        { month: "May", totalAssets: 40000, cash: 135000, stockValue: 12000 },
        { month: "June", totalAssets: 40000, cash: 55000, stockValue: 10000 },
        { month: "July", totalAssets: 40000, cash: 32000, stockValue: 20000 },
      ];
    }

    return assets_over_time.map((asset: any) => {
      // Convert timestamp to readable format
      const timestamp = new Date(asset.time);
      const month = timestamp.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });

      return {
        month: month,
        totalAssets: asset.total_assets || 0,
        totalAssetsRebase: asset.total_assets_rebase || 100, // Use rebased value for better visualization
        cash: asset.cash || 0,
        stockValue: asset.stock_value || 0,
        // Additional context from API
        stock_position: asset.stock_position,
        return: asset.return,
        position_change: asset.position_change,
        timestamp: asset.time,
      };
    });
  }, [assets_over_time]);

  // Debug: Log the transformed data to verify mapping
  useEffect(() => {
    if (agent_decisions && Array.isArray(agent_decisions)) {
      console.log("Agent Decisions API Data:", {
        totalDecisions: agent_decisions.length,
        sampleDecision: agent_decisions[0],
        chartDataSample: chartData.slice(0, 3),
      });
    }

    if (agentDecisionOverlays.length > 0) {
      console.log("Agent Decision Overlays for Price Chart:", {
        totalOverlays: agentDecisionOverlays.length,
        sampleOverlay: agentDecisionOverlays[0],
        priceDataLength: transformedPriceData.length,
        samplePriceData: transformedPriceData.slice(0, 3),
      });
    }

    if (agent_history && Array.isArray(agent_history)) {
      console.log("Agent Trade History API Data:", {
        totalTrades: agent_history.length,
        sampleTrade: agent_history[0],
        dataBarSample: dataBar.slice(0, 3),
        tradeTypes: agent_history.reduce((acc: any, trade: any) => {
          acc[trade.order_type] = (acc[trade.order_type] || 0) + 1;
          return acc;
        }, {}),
      });
    }

    if (assets_over_time && Array.isArray(assets_over_time)) {
      console.log("Assets Over Time API Data:", {
        totalAssets: assets_over_time.length,
        sampleAsset: assets_over_time[0],
        totalAssetsDataSample: totalAssetsData.slice(0, 3),
        fieldMapping: {
          time: "timestamp",
          cash: "cash",
          stock_position: "stock_position",
          stock_value: "stockValue",
          total_assets: "totalAssets",
          total_assets_rebase: "totalAssetsRebase",
          return: "return",
          position_change: "position_change",
        },
      });
    }
  }, [
    agent_decisions,
    agent_history,
    assets_over_time,
    chartData,
    dataBar,
    totalAssetsData,
    agentDecisionOverlays,
    transformedPriceData,
  ]);

  return (
    <div className="min-h-screen bg-[#fafafa] mx-auto w-[1400px]">
      <main>
        <div className="flex items-center gap-2 mb-8">
          <div
            onClick={() => router.back()}
            className="cursor-pointer inline-flex items-center justify-center p-1 hover:bg-[#f7f7f7] rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-[#0359d8] focus:ring-offset-2"
          >
            <ArrowLeft className="w-4 h-4" />
          </div>
          <div className="w-[1px] bg-[#98a2b3] h-3" />

          <span
            onClick={() => router.back()}
            className="text-[#98a2b3] text-sm font-medium cursor-pointer hover:text-[#1d1d1d] transition-colors"
          >
            {ticker} Simulation
          </span>
          <span className="text-[#98a2b3] text-sm">
            <ChevronRight className="w-4 h-4" />
          </span>

          {/* Current page - not clickable */}
          <span className="text-[#1d1d1d] text-sm font-medium">{name}</span>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h2 className="text-lg font-medium text-gray-900 mb-2">
                Loading Agent Details
              </h2>
              <p className="text-sm text-gray-600">
                Fetching decision history and performance data...
              </p>
            </div>
          </div>
        )}

        {/* Main Content - only show when not loading */}
        {!isLoading && (
          <>
            {/* Participants */}
            <div className="mb-8 bg-white p-2 px-4 rounded-md">
              <h2 className="text-[#1d1d1d] font-medium text-xl">{name}</h2>
              <div className="text-[#0D5EFF] text-sm mb-2">Access to News</div>
              {agent_decisions && (
                <div className="text-sm text-gray-600">
                  Total Decisions: {agent_decisions.length}
                </div>
              )}
            </div>

            <div className="bg-[#ffffff] rounded-lg p-6 mb-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-medium text-[#1d1d1d]">
                  Agent Decisions
                </h2>
                <div className="flex items-center gap-6 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-[#0d5eff]"></div>
                    <span className="text-[#474747]">Actual Price</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-[#9a89ff]"></div>
                    <span className="text-[#474747]">Entry Prices</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-[#8ac48a]"></div>
                    <span className="text-[#474747]">Take Profit</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-[#ff9595]"></div>
                    <span className="text-[#474747]">Stop Loss</span>
                  </div>
                </div>
              </div>

              <div className="h-[400px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={chartData}
                    margin={{ top: 10, right: 10, left: 10, bottom: 10 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                    <XAxis
                      dataKey="month"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: "#a0a7b4", fontSize: 12 }}
                    />
                    <YAxis
                      domain={["dataMin - 5", "dataMax + 5"]}
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: "#a0a7b4", fontSize: 12 }}
                    />
                    <Tooltip content={<CustomTooltip />} />

                    {/* Actual Price Line - Blue, thick, no dots */}
                    <Line
                      type="monotone"
                      dataKey="actualPrice"
                      stroke="#0d5eff"
                      strokeWidth={3}
                      dot={false}
                      activeDot={{
                        r: 6,
                        fill: "#0d5eff",
                        stroke: "#ffffff",
                        strokeWidth: 2,
                      }}
                    />

                    {/* Entry Prices Line - Purple, no dots */}
                    <Line
                      type="monotone"
                      dataKey="entryPrices"
                      stroke="#9a89ff"
                      strokeWidth={2}
                      dot={false}
                      activeDot={{
                        r: 6,
                        fill: "#9a89ff",
                        stroke: "#ffffff",
                        strokeWidth: 2,
                      }}
                    />

                    {/* Take Profit Line - Green with upward triangles */}
                    <Line
                      type="monotone"
                      dataKey="takeProfit"
                      stroke="#8ac48a"
                      strokeWidth={2}
                      dot={(props) => {
                        // Show triangles for all data points that represent actual decisions
                        const dataPoint = props.payload;
                        if (
                          dataPoint &&
                          dataPoint.confidence &&
                          dataPoint.confidence > 0
                        ) {
                          return <TriangleDot {...props} fill="#007f00" />;
                        }
                        // Return an invisible circle instead of null to satisfy TypeScript
                        return (
                          <circle
                            cx={props.cx}
                            cy={props.cy}
                            r={0}
                            fill="transparent"
                          />
                        );
                      }}
                      activeDot={{
                        r: 6,
                        fill: "#8ac48a",
                        stroke: "#ffffff",
                        strokeWidth: 2,
                      }}
                    />

                    {/* Stop Loss Line - Red with downward triangles */}
                    <Line
                      type="monotone"
                      dataKey="stopLoss"
                      stroke="#ff9595"
                      strokeWidth={2}
                      dot={(props) => {
                        // Show triangles for all data points that represent actual decisions
                        const dataPoint = props.payload;
                        if (
                          dataPoint &&
                          dataPoint.confidence &&
                          dataPoint.confidence > 0
                        ) {
                          return (
                            <InvertedTriangleDot {...props} fill="#cc0000" />
                          );
                        }
                        // Return an invisible circle instead of null to satisfy TypeScript
                        return (
                          <circle
                            cx={props.cx}
                            cy={props.cy}
                            r={0}
                            fill="transparent"
                          />
                        );
                      }}
                      activeDot={{
                        r: 6,
                        fill: "#ff9595",
                        stroke: "#ffffff",
                        strokeWidth: 2,
                      }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Agent Decision Chart - Price Chart with Decision Overlays */}
            <div className="bg-[#ffffff] rounded-lg p-6 mb-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-medium text-[#1d1d1d]">
                  Agent Decisions on Price Chart
                </h2>
                <div className="text-sm text-gray-600">
                  {agentDecisionOverlays.length} decisions plotted
                </div>
              </div>

              {isLoadingPrices ? (
                <div className="flex items-center justify-center h-[500px] bg-gray-50 rounded-lg">
                  <div className="text-gray-500">Loading price data...</div>
                </div>
              ) : (
                <AgentDecisionChart
                  priceData={transformedPriceData}
                  agentDecisions={agentDecisionOverlays}
                  height={500}
                  isLoading={isLoadingPrices}
                />
              )}
            </div>

            <div className="bg-[#ffffff] rounded-lg p-6 mb-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-medium text-[#1d1d1d]">
                  Trading Amount
                </h2>
                <div className="flex items-center gap-6 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-[#8ac48a]"></div>
                    <span className="text-[#474747]">Selling Amount</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-[#ff9595]"></div>
                    <span className="text-[#474747]">Buy Amount</span>
                  </div>
                </div>
              </div>

              <div className="h-[700px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={dataBar}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 20,
                    }}
                    barCategoryGap="20%"
                  >
                    <CartesianGrid
                      strokeDasharray="0"
                      stroke="#e2e8f0"
                      strokeWidth={1}
                      horizontal={true}
                      vertical={true}
                    />
                    <XAxis
                      dataKey="month"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: "#98a2b3" }}
                      dy={10}
                    />
                    <YAxis
                      domain={[0, 700]}
                      ticks={[50, 100, 200, 300, 400, 500, 600, 700]}
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: "#98a2b3" }}
                      dx={-10}
                    />
                    <Tooltip
                      content={<CustomTooltipBar />}
                      cursor={{ fill: "rgba(0, 0, 0, 0.05)" }}
                    />
                    <ReferenceLine
                      y={200}
                      stroke="#0d5eff"
                      strokeWidth={2}
                      strokeDasharray="0"
                    />
                    <Bar
                      dataKey="sellAmount"
                      fill="#8ac48a"
                      radius={[0, 0, 0, 0]}
                      maxBarSize={20}
                    />
                    <Bar
                      dataKey="buyAmount"
                      fill="#ff9595"
                      radius={[0, 0, 0, 0]}
                      maxBarSize={20}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            <div className="bg-[#ffffff] rounded-lg p-6 mb-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-medium text-[#1d1d1d]">
                  Total Assets
                </h2>
                <div className="flex items-center gap-6 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-[#0d5eff]"></div>
                    <span className="text-[#474747]">
                      Total Assets (Rebased)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-[#8ac48a]"></div>
                    <span className="text-[#474747]">Cash</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-[#ff9595]"></div>
                    <span className="text-[#474747]">Stock Value</span>
                  </div>
                </div>
              </div>

              <div className="h-[700px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={totalAssetsData}
                    margin={{
                      top: 10,
                      right: 10,
                      left: 10,
                      bottom: 10,
                    }}
                  >
                    <CartesianGrid
                      strokeDasharray="none"
                      stroke="#e2e8f0"
                      strokeWidth={1}
                    />
                    <XAxis
                      dataKey="month"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: "#858585", fontSize: 12 }}
                      dy={10}
                    />
                    <YAxis
                      domain={["dataMin - 5", "dataMax + 5"]}
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: "#858585", fontSize: 12 }}
                      dx={-10}
                    />
                    <Tooltip content={<CustomTooltipTotalAssets />} />
                    <Line
                      type="linear"
                      dataKey="totalAssetsRebase"
                      stroke="#0d5eff"
                      strokeWidth={3}
                      dot={false}
                      strokeDasharray="none"
                    />
                    <Line
                      type="linear"
                      dataKey="cash"
                      stroke="#8ac48a"
                      strokeWidth={2}
                      dot={false}
                      strokeDasharray="8 4"
                    />
                    <Line
                      type="linear"
                      dataKey="stockValue"
                      stroke="#ff9595"
                      strokeWidth={2}
                      dot={false}
                      strokeDasharray="2 3"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </>
        )}
      </main>
    </div>
  );
}
