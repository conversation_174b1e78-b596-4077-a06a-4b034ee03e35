"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ronR<PERSON>,
  Plus,
  <PERSON><PERSON>,
  Trash2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Edit,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import NewEventModal from "../modals/newEventModal";
import { useEffect, useState, useMemo, useCallback } from "react";
import { HexColorPicker } from "react-colorful";
import AddPresetParticipationModal from "../modals/addPresetParticipationModal";
import SelectPresetParticipationModal from "../modals/selectPresetParticipationModal";
import EditParticipantModal from "../modals/editParticipantModal";
import ParticipantCardSkeleton from "../skeletons/ParticipantCardSkeleton";
import {
  OLYMPUS_URL,
  useGetQueryOlympusBackend,
  useGetQueryOlympusBackend2,
  useSubmitQueryOlympusBackend,
} from "@/services/api_hooks";
import { toast } from "sonner";
import axios from "axios";
import { MoonLoader } from "react-spinners";

// Utility function to generate random colors
const generateRandomColor = () => {
  const colors = [
    "#FF0000", // Red
    "#0000FF", // Blue
    "#00FF00", // Green
    "#FFFF00", // Yellow
    "#FF8000", // Orange
    "#000000", // Black
    "#808080", // Gray
    "#800080", // Purple
    "#006400", // Very Dark Green
    "#FFB6C1", // Very Light Red
  ];
  return colors[Math.floor(Math.random() * colors.length)];
};

// Get available colors (not currently used) - FIXED VERSION
const getAvailableColors = (usedColors: string[]) => {
  const allColors = [
    "#FF0000", // Red
    "#0000FF", // Blue
    "#00FF00", // Green
    "#FFFF00", // Yellow
    "#FF8000", // Orange
    "#000000", // Black
    "#808080", // Gray
    "#800080", // Purple
    "#006400", // Very Dark Green
    "#FFB6C1", // Very Light Red
  ];

  // Filter out colors that are currently in use
  const available = allColors.filter((color) => !usedColors.includes(color));
  return available;
};

// Get next available color (ensures no duplicates)
const getNextAvailableColor = (usedColors: string[]) => {
  const allColors = [
    "#FF0000", // Red
    "#0000FF", // Blue
    "#00FF00", // Green
    "#FFFF00", // Yellow
    "#FF8000", // Orange
    "#000000", // Black
    "#808080", // Gray
    "#800080", // Purple
    "#006400", // Very Dark Green
    "#FFB6C1", // Very Light Red
  ];

  // Find first unused color
  const availableColor = allColors.find((color) => !usedColors.includes(color));
  return availableColor || "#FF0000"; // Fallback to red if all colors used
};

// Get available colors excluding a specific participant's color
const getAvailableColorsExcluding = (
  usedColors: string[],
  excludeColor: string
) => {
  const allColors = [
    "#FF0000", // Red
    "#0000FF", // Blue
    "#00FF00", // Green
    "#FFFF00", // Yellow
    "#FF8000", // Orange
    "#000000", // Black
    "#808080", // Gray
    "#800080", // Purple
    "#006400", // Very Dark Green
    "#FFB6C1", // Very Light Red
  ];

  // Filter out colors that are currently in use, and also exclude the specified color
  const available = allColors.filter(
    (color) => !usedColors.includes(color) && color !== excludeColor
  );
  return available;
};

export default function Participants() {
  const router = useRouter();
  const params = useSearchParams();

  const ticker = params.get("ticker");
  const start = params.get("start_date");
  const end = params.get("end_date");
  const { data, isLoading, isFetching } = useGetQueryOlympusBackend(
    `/api/agents/examples`,
    ["agentsOlympus"]
  );
  const { data: key, isLoading: keyIsLoading } = useGetQueryOlympusBackend2(
    `/report/olympus-key`,
    ["apiKey"]
  );

  /*   const {
    mutate,
    isPending,
    data: events,
  } = useSubmitQueryOlympusBackend(
    "/api/simulation/create",
    "POST",
    {
      onSuccess: (data: any) => {
        router.push("/dashboard/olympus/simulations/participants/simulation");
        console.log(data, "data");
      },
    },

  ); */

  const [simulationData, setSimulationData] = useState<any>();
  const [isLoadingSimulate, setIsLoadingSimulate] = useState<boolean>();

  const startSimulation = (payload: any) => {
    setIsLoadingSimulate(true);
    axios
      .post(`${OLYMPUS_URL}/api/simulation/create`, payload, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${key?.api_key}`,
        },
      })
      .then((response) => {
        setSimulationData(response?.data);
        setIsLoadingSimulate(false);
        toast.success("Simulation Started");
        console.log(response?.data, "response");
        router.push(
          `/dashboard/olympus/simulations/${response?.data?.simulation_id}?ticker=${ticker}&start_date=${start}&end_date=${end}`
        );
      })
      .catch((err) => {
        toast.error("Simulation Not Created");
        setIsLoadingSimulate(false);
      });
  };

  const [showAddParticipantModal, setShowAddParticipantModal] =
    useState<boolean>(false);
  const [showSelectParticipantModal, setShowSelectParticipantModal] =
    useState<boolean>(false);
  const [showEditParticipantModal, setShowEditParticipantModal] =
    useState<boolean>(false);

  // State to manage custom participants added via modal
  const [customParticipants, setCustomParticipants] = useState<any[]>([]);

  // State for editing
  const [editingParticipant, setEditingParticipant] = useState<any>(null);
  const [editingIndex, setEditingIndex] = useState<number>(-1);

  // State for color management
  const [participantColors, setParticipantColors] = useState<{
    [key: string]: string;
  }>({});

  // State for managing displayed participants
  const [displayedParticipants, setDisplayedParticipants] = useState<string[]>(
    []
  );
  const [showDropdown, setShowDropdown] = useState(false);

  // State for color picker
  const [showColorPicker, setShowColorPicker] = useState<string | null>(null);
  const [tempColor, setTempColor] = useState<string>("");

  // Persistence keys for localStorage
  const STORAGE_KEYS = useMemo(
    () => ({
      customParticipants: `olympus_custom_participants_${ticker}_${start}_${end}`,
      participantColors: `olympus_participant_colors_${ticker}_${start}_${end}`,
      displayedParticipants: `olympus_displayed_participants_${ticker}_${start}_${end}`,
    }),
    [ticker, start, end]
  );

  // Load persisted data on component mount
  useEffect(() => {
    try {
      // Load custom participants
      const savedCustomParticipants = localStorage.getItem(
        STORAGE_KEYS.customParticipants
      );
      if (savedCustomParticipants) {
        const parsedCustomParticipants = JSON.parse(savedCustomParticipants);
        setCustomParticipants(parsedCustomParticipants);
      }

      // Load participant colors
      const savedParticipantColors = localStorage.getItem(
        STORAGE_KEYS.participantColors
      );
      if (savedParticipantColors) {
        const parsedParticipantColors = JSON.parse(savedParticipantColors);
        setParticipantColors(parsedParticipantColors);
      }

      // Load displayed participants
      const savedDisplayedParticipants = localStorage.getItem(
        STORAGE_KEYS.displayedParticipants
      );
      if (savedDisplayedParticipants) {
        const parsedDisplayedParticipants = JSON.parse(
          savedDisplayedParticipants
        );
        setDisplayedParticipants(parsedDisplayedParticipants);
      }
    } catch (error) {
      console.error("Error loading persisted participant data:", error);
    }
  }, [STORAGE_KEYS]);

  // Persist custom participants whenever they change
  useEffect(() => {
    try {
      localStorage.setItem(
        STORAGE_KEYS.customParticipants,
        JSON.stringify(customParticipants)
      );
    } catch (error) {
      console.error("Error persisting custom participants:", error);
    }
  }, [customParticipants, STORAGE_KEYS.customParticipants]);

  // Persist participant colors whenever they change
  useEffect(() => {
    try {
      localStorage.setItem(
        STORAGE_KEYS.participantColors,
        JSON.stringify(participantColors)
      );
    } catch (error) {
      console.error("Error persisting participant colors:", error);
    }
  }, [participantColors, STORAGE_KEYS.participantColors]);

  // Persist displayed participants whenever they change
  useEffect(() => {
    try {
      localStorage.setItem(
        STORAGE_KEYS.displayedParticipants,
        JSON.stringify(displayedParticipants)
      );
    } catch (error) {
      console.error("Error persisting displayed participants:", error);
    }
  }, [displayedParticipants, STORAGE_KEYS.displayedParticipants]);

  // Function to clear all persisted data (useful for debugging or reset)
  const clearPersistedData = useCallback(() => {
    try {
      localStorage.removeItem(STORAGE_KEYS.customParticipants);
      localStorage.removeItem(STORAGE_KEYS.participantColors);
      localStorage.removeItem(STORAGE_KEYS.displayedParticipants);
      setCustomParticipants([]);
      setParticipantColors({});
      setDisplayedParticipants([]);
    } catch (error) {
      console.error("Error clearing persisted data:", error);
    }
  }, [STORAGE_KEYS]);

  // Add clearPersistedData to window for debugging (development only)
  useEffect(() => {
    if (
      typeof window !== "undefined" &&
      process.env.NODE_ENV === "development"
    ) {
      (window as any).clearOlympusParticipantData = clearPersistedData;
      console.log(
        "Debug: Use window.clearOlympusParticipantData() to clear persisted data"
      );
    }
  }, [clearPersistedData]);

  // Handler to add new participant from modal
  const handleAddParticipant = (newParticipant: any) => {
    console.log("Adding new participant:", newParticipant);
    const participantWithId = {
      ...newParticipant,
      _id: `custom_${Date.now()}_${Math.random()}`,
    };

    // Assign color to new participant using deterministic assignment
    if (!participantColors[participantWithId._id]) {
      const usedColors = Object.values(participantColors);
      const newColor = getNextAvailableColor(usedColors);
      setParticipantColors((prev) => ({
        ...prev,
        [participantWithId._id]: newColor,
      }));
    }

    setCustomParticipants((prev) => {
      const updated = [...prev, participantWithId];
      console.log("Updated custom participants:", updated);
      return updated;
    });
  };

  // Handler to change participant color
  const handleColorChange = (participantId: string, color: string) => {
    setParticipantColors((prev) => ({
      ...prev,
      [participantId]: color,
    }));
  };

  // Initialize displayed participants with first 3 API participants
  useEffect(() => {
    if (data && data.length > 0 && displayedParticipants.length === 0) {
      const initialParticipants = data
        .slice(0, 3)
        .map((_: any, index: number) => `api_${index}`);
      setDisplayedParticipants(initialParticipants);
    }
  }, [data, displayedParticipants.length]);

  // Get participant color with fallback
  const getParticipantColor = (participantId: string) => {
    // If color already exists, return it
    if (participantColors[participantId]) {
      return participantColors[participantId];
    }

    // For API participants, assign colors based on index
    if (participantId.startsWith("api_")) {
      const index = parseInt(participantId.replace("api_", ""));
      const defaultColors = ["#FF0000", "#0000FF", "#00FF00"]; // Red, Blue, Green

      if (index < 3) {
        return defaultColors[index];
      } else {
        // For participants beyond the first 3, use a deterministic color assignment
        const allColors = [
          "#FFFF00", // Yellow
          "#FF8000", // Orange
          "#000000", // Black
          "#808080", // Gray
          "#800080", // Purple
          "#006400", // Very Dark Green
          "#FFB6C1", // Very Light Red
        ];
        return allColors[(index - 3) % allColors.length];
      }
    }

    // For custom participants, use a fallback color
    return "#FF0000"; // Fallback to red
  };

  // Add participant to displayed list
  const addParticipantToDisplay = (participantId: string) => {
    setDisplayedParticipants((prev) => [...prev, participantId]);
    setShowDropdown(false);
  };

  // Remove participant from displayed list
  const removeParticipantFromDisplay = (participantId: string) => {
    setDisplayedParticipants((prev) =>
      prev.filter((id: string) => id !== participantId)
    );
  };

  // Get available participants for dropdown (not currently displayed)
  const getAvailableParticipants = () => {
    const allApiParticipants = data || [];
    return allApiParticipants
      .map((_: any, index: number) => `api_${index}`)
      .filter((id: string) => !displayedParticipants.includes(id));
  };

  // Color picker functions
  const toggleColorPicker = (participantId: string, currentColor: string) => {
    if (showColorPicker === participantId) {
      setShowColorPicker(null);
      setTempColor("");
    } else {
      setShowColorPicker(participantId);
      setTempColor(currentColor);
    }
  };

  const applyColorChange = (participantId: string) => {
    if (tempColor && tempColor !== participantColors[participantId]) {
      setParticipantColors((prev) => ({
        ...prev,
        [participantId]: tempColor,
      }));
    }
    setShowColorPicker(null);
    setTempColor("");
  };

  const cancelColorChange = () => {
    setShowColorPicker(null);
    setTempColor("");
  };

  // Handler to delete participant
  const handleDeleteParticipant = (participant: any, index: number) => {
    console.log("Deleting participant:", {
      participant,
      index,
      isCustom: participant.isCustomParticipant,
      originalApiIndex: participant._originalApiIndex,
      participantId: participant._id,
    });

    if (participant.isCustomParticipant) {
      // Delete custom participant directly
      setCustomParticipants((prev) => {
        const filtered = prev.filter(
          (customParticipant) => customParticipant._id !== participant._id
        );
        console.log("Custom participants after deletion:", filtered);
        return filtered;
      });
    } else {
      // For API participants, add them to a "deleted" list to hide them
      const deletedParticipant = {
        _id: `deleted_${Date.now()}_${Math.random()}`,
        isDeletedApiParticipant: true,
        originalApiIndex: participant._originalApiIndex,
      };
      console.log(
        "Adding deleted marker for API participant:",
        deletedParticipant
      );
      setCustomParticipants((prev) => [...prev, deletedParticipant]);
    }
  };

  // Handler to edit participant
  const handleEditParticipant = (participant: any, index: number) => {
    console.log("Editing participant:", participant, "at index:", index);
    setEditingParticipant(participant);
    setEditingIndex(index);
    setShowEditParticipantModal(true);
  };

  // Handler to update participant after editing
  const handleUpdateParticipant = (updatedParticipant: any, index: number) => {
    console.log(
      "Updating participant:",
      updatedParticipant,
      "at index:",
      index
    );

    const currentAllParticipants = allParticipants;
    const participantBeingEdited = currentAllParticipants[index];

    if (!participantBeingEdited) {
      console.error("Participant not found at index:", index);
      return;
    }

    if (participantBeingEdited.isCustomParticipant) {
      // This is a custom participant - update it directly using the ID
      setCustomParticipants((prev) =>
        prev.map((participant) =>
          participant._id === participantBeingEdited._id
            ? {
                ...updatedParticipant,
                _id: participantBeingEdited._id,
                isCustomParticipant: true,
              }
            : participant
        )
      );
    } else {
      // This is an API participant - create a replacement custom participant
      const newCustomParticipant = {
        ...updatedParticipant,
        _id: `custom_${Date.now()}_${Math.random()}`,
        isCustomParticipant: true,
        isEditedApiParticipant: true,
        originalIndex: participantBeingEdited._originalApiIndex,
      };

      // Preserve the original color for edited API participants
      const originalColor = getParticipantColor(participantBeingEdited._id);
      setParticipantColors((prev) => ({
        ...prev,
        [newCustomParticipant._id]: originalColor,
      }));

      setCustomParticipants((prev) => [...prev, newCustomParticipant]);
    }
  };

  // Combine API participants with custom participants, filtering out edited and deleted API ones
  const allParticipants = useMemo(() => {
    const apiParticipants = data || [];

    // Get indices of edited API participants
    const editedIndices = new Set(
      customParticipants
        .filter(
          (cp) => cp.isEditedApiParticipant && cp.originalIndex !== undefined
        )
        .map((cp) => cp.originalIndex)
    );

    // Get indices of deleted API participants
    const deletedIndices = new Set(
      customParticipants
        .filter(
          (cp) =>
            cp.isDeletedApiParticipant && cp.originalApiIndex !== undefined
        )
        .map((cp) => cp.originalApiIndex)
    );

    // Filter out edited and deleted API participants and add IDs
    const filteredApiParticipants = apiParticipants
      .map((participant: any, originalIndex: number) => ({
        ...participant,
        _id: `api_${originalIndex}`,
        _originalApiIndex: originalIndex,
        isCustomParticipant: false,
      }))
      .filter(
        (participant: any) =>
          !editedIndices.has(participant._originalApiIndex) &&
          !deletedIndices.has(participant._originalApiIndex) &&
          displayedParticipants.includes(participant._id) // Only show displayed participants
      );

    // Filter out deleted markers and add IDs to remaining custom participants
    const customParticipantsWithIds = customParticipants
      .filter((participant: any) => !participant.isDeletedApiParticipant)
      .map((participant: any, index: number) => ({
        ...participant,
        _id: participant._id || `custom_${index}`,
        isCustomParticipant: true,
      }));

    const result = [...filteredApiParticipants, ...customParticipantsWithIds];

    console.log("AllParticipants computation:", {
      apiParticipantsCount: apiParticipants.length,
      editedIndices: Array.from(editedIndices),
      deletedIndices: Array.from(deletedIndices),
      displayedParticipants,
      filteredApiCount: filteredApiParticipants.length,
      customParticipantsCount: customParticipantsWithIds.length,
      totalResult: result.length,
      resultIds: result.map((p) => ({
        id: p._id,
        isCustom: p.isCustomParticipant,
        originalIndex: p._originalApiIndex,
      })),
    });

    return result;
  }, [data, customParticipants, displayedParticipants]);

  // Initialize colors for API participants
  useEffect(() => {
    if (data && data.length > 0) {
      const newColors: { [key: string]: string } = {};
      const defaultColors = ["#FF0000", "#0000FF", "#00FF00"]; // Red, Blue, Green

      data.forEach((participant: any, index: number) => {
        const participantId = `api_${index}`;
        if (!participantColors[participantId]) {
          // Assign specific colors to first 3 participants, then use available colors
          if (index < 3) {
            newColors[participantId] = defaultColors[index];
          } else {
            // Get all currently used colors to avoid duplicates
            const usedColors = Object.values({
              ...participantColors,
              ...newColors,
            });
            newColors[participantId] = getNextAvailableColor(usedColors);
          }
        }
      });

      if (Object.keys(newColors).length > 0) {
        setParticipantColors((prev) => ({
          ...prev,
          ...newColors,
        }));
      }
    }
  }, [data]); // Removed participantColors from dependencies to prevent infinite re-renders

  // Retrieve 'events' from sessionStorage on mount
  const [eventsData, setEventsData] = useState<any>(null);
  useEffect(() => {
    const eventsJson = sessionStorage.getItem("events");
    if (eventsJson) {
      try {
        setEventsData(JSON.parse(eventsJson));
        console.log(
          "Loaded events from sessionStorage:",
          JSON.parse(eventsJson)
        );
      } catch (e) {
        console.error("Failed to parse events from sessionStorage", e);
      }
    }
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showDropdown && !target.closest(".dropdown-container")) {
        setShowDropdown(false);
      }
      if (showColorPicker && !target.closest(".color-picker-container")) {
        setShowColorPicker(null);
        setTempColor("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showDropdown, showColorPicker]);

  return (
    <div className="min-h-screen bg-[#fafafa] mx-auto w-[1400px]">
      <AddPresetParticipationModal
        isOpen={showAddParticipantModal}
        onClose={() => setShowAddParticipantModal(false)}
        onAddParticipant={handleAddParticipant}
      />
      <EditParticipantModal
        isOpen={showEditParticipantModal}
        onClose={() => setShowEditParticipantModal(false)}
        onUpdateParticipant={handleUpdateParticipant}
        participant={editingParticipant}
        participantIndex={editingIndex}
      />

      <main>
        <div className="flex items-center gap-2 mb-8">
          <div
            onClick={() => router.back()}
            className="cursor-pointer inline-flex items-center justify-center p-1 hover:bg-[#f7f7f7] rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-[#0359d8] focus:ring-offset-2"
          >
            <ArrowLeft className="w-4 h-4" />
          </div>
          <div className="w-[1px] bg-[#98a2b3] h-3" />

          {/* New Simulation - Navigate to events page */}
          <span
            onClick={() =>
              router.push(
                `/dashboard/olympus/simulations?ticker=${ticker}&start_date=${start}&end_date=${end}`
              )
            }
            className="text-[#98a2b3] text-sm cursor-pointer hover:text-[#1d1d1d] transition-colors"
          >
            New Simulation
          </span>
          <span className="text-[#98a2b3] text-sm">
            <ChevronRight className="w-4 h-4" />
          </span>

          {/* Ticker - Navigate to events page */}
          <span
            onClick={() =>
              router.push(
                `/dashboard/olympus/simulations?ticker=${ticker}&start_date=${start}&end_date=${end}`
              )
            }
            className="text-[#98a2b3] text-sm font-medium cursor-pointer hover:text-[#1d1d1d] transition-colors"
          >
            {ticker}
          </span>
          <span className="text-[#98a2b3] text-sm">
            <ChevronRight className="w-4 h-4" />
          </span>

          {/* Current page - not clickable */}
          <span className="text-[#1d1d1d] text-sm font-medium">
            Market Participants
          </span>
        </div>

        <div className="mb-12 flex justify-between items-end">
          <div>
            <h1 className="text-[#1d1d1d] text-xl font-light mb-2">
              Market Participants
              {isLoading && (
                <span className="text-sm text-gray-500 ml-2">Loading...</span>
              )}
            </h1>
            <p className="text-[#98a2b3] text-sm">
              The Events are sourced by Olympus, you can edit the events to your
              preference.
            </p>
          </div>
          <div className="flex gap-2">
            {/* Add Agent Dropdown */}
            {getAvailableParticipants().length > 0 && (
              <div className="relative dropdown-container">
                <Button
                  variant="outline"
                  onClick={() => setShowDropdown(!showDropdown)}
                  className="border-gray-300 text-gray-700 hover:bg-gray-50"
                >
                  Add Agent ({getAvailableParticipants().length})
                </Button>
                {showDropdown && (
                  <div className="absolute top-full right-0 mt-1 w-64 bg-white border border-gray-300 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
                    {getAvailableParticipants().map((participantId: string) => {
                      const index = parseInt(participantId.replace("api_", ""));
                      const participant = data?.[index];
                      return (
                        <button
                          key={participantId}
                          onClick={() => addParticipantToDisplay(participantId)}
                          className="w-full px-4 py-2 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 flex items-center gap-3"
                        >
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{
                              backgroundColor:
                                getParticipantColor(participantId),
                            }}
                          />
                          <div>
                            <div className="font-medium text-sm text-gray-900">
                              {participant?.name}
                            </div>
                            <div className="text-xs text-gray-500">
                              {participant?.agent_type}
                            </div>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>
            )}
            <Button
              onClick={() => {
                const agents = allParticipants?.map((item: any) => {
                  return {
                    name: item?.name,
                    agent_type: item?.agent_type,
                    description: item?.description,
                    risk_tolerance: item?.risk_tolerance,
                    reaction_speed_percentile: item?.reaction_speed_percentile,
                    liquidity_constraint: item?.liquidity_constraint,
                    behavioral_biases: item?.behavioral_biases,
                    objective: item?.objective,
                    initial_capital: item?.initial_capital,
                    initial_positions: Number(item?.initial_positions),
                    color: getParticipantColor(item?._id),
                    data_access: item?.data_access || "market_data", // Default for custom participants
                  };
                });

                if (key?.api_key) {
                  sessionStorage.setItem("agents", JSON.stringify(agents));
                  startSimulation({
                    mode: "replay",
                    market_setup: {
                      ticker: ticker,
                      start_date: start,
                      end_date: end,
                      event_timeline: eventsData,
                    },
                    agents,
                  });
                } else {
                  toast.error("API Key Not Present");
                }
              }}
              disabled={keyIsLoading || isLoading}
              className="bg-black text-white hover:bg-gray-800"
            >
              Start Simulation{" "}
              {isLoadingSimulate ? (
                <MoonLoader size={17} color="white" />
              ) : null}
            </Button>
          </div>
        </div>

        <div className="bg-[#ffffff] mx-auto w-full rounded-lg border border-[#e2e8f0] p-8 space-y-6">
          <Button
            onClick={() => {
              setShowAddParticipantModal(true);
            }}
            disabled={isLoading}
          >
            Add New Participant
          </Button>

          <div className="grid grid-cols-1  sm:grid-cols-2  md:grid-cols-4 gap-6">
            {isLoading
              ? // Show skeleton cards while loading
                Array.from({ length: 8 }).map((_, idx) => (
                  <ParticipantCardSkeleton key={idx} index={idx} />
                ))
              : allParticipants?.map((item: any, idx: number) => {
                  return (
                    <Card
                      key={idx}
                      className="border-none shadow-none bg-[#F9F9F9]"
                    >
                      <CardContent className="p-6 flex flex-col justify-between items-start h-full space-y-4">
                        <div className="flex items-start justify-between h-[40px]">
                          <div className="flex items-start gap-3 flex-1 min-w-0">
                            {/* Color indicator - clickable for color picker */}
                            <div className="relative">
                              <div
                                className="w-6 h-6 rounded-full border-2 border-gray-300 flex-shrink-0 mt-0.5 cursor-pointer hover:scale-110 transition-transform"
                                style={{
                                  backgroundColor: getParticipantColor(
                                    item._id
                                  ),
                                }}
                                /*    onClick={() =>
                                  toggleColorPicker(
                                    item._id,
                                    getParticipantColor(item._id)
                                  )
                                } */
                                title="Click to change color"
                              />

                              {/* Color picker dropdown */}
                              {showColorPicker === item._id && (
                                <div className="absolute top-8 left-0 z-50 bg-white border border-gray-300 rounded-lg shadow-lg p-3 min-w-[200px] color-picker-container">
                                  <div className="text-sm font-medium text-gray-900 mb-2">
                                    Choose Color
                                  </div>
                                  <div className="grid grid-cols-5 gap-2 mb-3">
                                    {/* Show current participant's color first */}
                                    <button
                                      className={`w-8 h-8 rounded-full border-2 transition-all ${
                                        tempColor ===
                                        getParticipantColor(item._id)
                                          ? "border-gray-800 scale-110"
                                          : "border-gray-300 hover:border-gray-500"
                                      }`}
                                      style={{
                                        backgroundColor: getParticipantColor(
                                          item._id
                                        ),
                                      }}
                                      onClick={() =>
                                        setTempColor(
                                          getParticipantColor(item._id)
                                        )
                                      }
                                      title={`Current color: ${getParticipantColor(
                                        item._id
                                      )}`}
                                    />
                                    {/* Show available colors (not currently used by other participants) */}
                                    {getAvailableColorsExcluding(
                                      Object.values(participantColors),
                                      getParticipantColor(item._id)
                                    ).map((color) => (
                                      <button
                                        key={color}
                                        className={`w-8 h-8 rounded-full border-2 transition-all ${
                                          tempColor === color
                                            ? "border-gray-800 scale-110"
                                            : "border-gray-300 hover:border-gray-500"
                                        }`}
                                        style={{ backgroundColor: color }}
                                        onClick={() => setTempColor(color)}
                                        title={`Select ${color}`}
                                      />
                                    ))}
                                  </div>
                                  {getAvailableColorsExcluding(
                                    Object.values(participantColors),
                                    getParticipantColor(item._id)
                                  ).length === 0 && (
                                    <div className="text-sm text-gray-500 mb-3">
                                      No other colors available
                                    </div>
                                  )}
                                  <div className="flex gap-2">
                                    <Button
                                      size="sm"
                                      onClick={() => applyColorChange(item._id)}
                                      disabled={!tempColor}
                                      className="flex-1"
                                    >
                                      Apply
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={cancelColorChange}
                                      className="flex-1"
                                    >
                                      Cancel
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </div>
                            {/* Name container with 2-line limit */}
                            <TooltipProvider delayDuration={300}>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <h3 className="text-base font-semibold text-[#1d1d1d] leading-tight line-clamp-2 flex-1 min-w-0 cursor-help">
                                    {item?.name}
                                  </h3>
                                </TooltipTrigger>
                                <TooltipContent
                                  className="max-w-[300px] p-3 bg-white text-black border border-gray-200 shadow-lg"
                                  side="bottom"
                                  align="start"
                                >
                                  <div className="space-y-2">
                                    <div className="font-semibold text-sm text-gray-900">
                                      {item?.name}
                                    </div>
                                    {item?.description && (
                                      <div className="text-sm text-gray-700 leading-relaxed">
                                        {item.description}
                                      </div>
                                    )}
                                    {!item?.description && (
                                      <div className="text-sm text-gray-500 italic">
                                        No description available
                                      </div>
                                    )}
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>

                          <div className="flex items-center gap-2">
                            {idx >= (data?.length || 0) &&
                              !item.isEditedApiParticipant && (
                                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                  Custom
                                </span>
                              )}
                            {item.isEditedApiParticipant && (
                              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                                Edited
                              </span>
                            )}
                            {/* Remove button for displayed API participants */}
                            {/*  {!item.isCustomParticipant &&
                              displayedParticipants.includes(item._id) && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="p-1 h-6 w-6 text-gray-400 hover:text-red-500"
                                  onClick={() =>
                                    removeParticipantFromDisplay(item._id)
                                  }
                                  title="Remove from display"
                                >
                                  ×
                                </Button>
                              )} */}
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="text-sm">
                            <span className="font-medium text-[#1d1d1d]">
                              Details:
                            </span>
                          </div>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-[#737384]">Risk: </span>
                              <span className="text-[#a0a7b4]">
                                {" "}
                                {item?.risk_tolerance}
                              </span>
                            </div>
                            <div>
                              <span className="text-[#737384]">Latency: </span>
                              <span className="text-[#a0a7b4]">
                                {" "}
                                {item?.reaction_speed_percentile}
                              </span>
                            </div>
                          </div>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-[#737384]">
                                Initial Capital:{" "}
                              </span>
                              <span className="text-[#a0a7b4]">
                                {" "}
                                {item?.initial_capital}
                              </span>
                            </div>
                            <div>
                              <span className="text-[#737384]">
                                Initial Position:{" "}
                              </span>
                              <span className="text-[#a0a7b4]">
                                {" "}
                                {item?.initial_positions}
                              </span>
                            </div>
                          </div>
                          <div className="text-sm">
                            <span className="text-[#737384]">
                              Data access:{" "}
                            </span>
                            <span className="text-[#a0a7b4]">
                              {" "}
                              {item.data_access}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditParticipant(item, idx)}
                            title="Edit participant"
                          >
                            <Edit className="w-4 h-4 text-[#737384]" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Copy className="w-4 h-4 text-[#737384]" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteParticipant(item, idx)}
                            title="Delete participant"
                          >
                            <Trash2 className="w-4 h-4 text-[#dc2626]" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
          </div>
        </div>
      </main>
    </div>
  );
}
