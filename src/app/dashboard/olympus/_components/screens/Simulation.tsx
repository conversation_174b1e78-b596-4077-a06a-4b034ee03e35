"use client";

import { <PERSON><PERSON><PERSON><PERSON>, ChevronRight, Badge } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";

import { Progress } from "@/components/ui/progress";

export default function Simulation() {
  const router = useRouter();

  const params = useSearchParams();

  const ticker = params.get("ticker");
  const start = params.get("start_date");
  const end = params.get("end_date");

  return (
    <div className="min-h-screen bg-[#fafafa] mx-auto w-[1400px]">
      <main>
        <div className="flex items-center gap-2 mb-8">
          <div
            onClick={() => router.back()}
            className="cursor-pointer inline-flex items-center justify-center p-1 hover:bg-[#f7f7f7] rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-[#0359d8] focus:ring-offset-2"
          >
            <ArrowLeft className="w-4 h-4" />
          </div>
          <div className="w-[1px] bg-[#98a2b3] h-3" />

          <span className="text-[#98a2b3] text-sm">New Simulation</span>
          <span className="text-[#98a2b3] text-sm">
            <ChevronRight className="w-4 h-4" />
          </span>
          <span className="text-[#98a2b3] text-sm font-medium">{ticker}</span>
          <span className="text-[#98a2b3] text-sm">
            <ChevronRight className="w-4 h-4" />
          </span>
          <span className="text-[#98a2b3] text-sm font-medium">Events</span>
          <span className="text-[#98a2b3] text-sm">
            <ChevronRight className="w-4 h-4" />
          </span>
          <span className="text-[#98a2b3] text-sm font-medium">
            {" "}
            Market Participants
          </span>
          <span className="text-[#98a2b3] text-sm">
            <ChevronRight className="w-4 h-4" />
          </span>
          <span className="text-[#1d1d1d] text-sm font-medium">
            {" "}
            APPL Simulation
          </span>
        </div>

        <div className="flex justify-center px-6">
          <div className="w-full max-w-[550px] bg-white rounded-lg border border-[#e2e8f0] p-8">
            <div className="text-left mb-8">
              <h1 className="text-2xl font-light text-[#1e1e1e] mb-1">
                Simulation Ongoing
              </h1>
              <p className="text-[#737384] font-[300] text-base">
                You can leave this page and return anytime. Upon completion a
                telegram message will be sent to you.
              </p>
            </div>

            <div className="mb-8">
              <div className="flex justify-between items-center mb-3">
                <span className="text-[#1e1e1e] font-medium">Day 2</span>
                <span className="text-[#1e1e1e] font-medium">34% Complete</span>
              </div>
              <Progress value={34} className="h-2" />
            </div>

            <div className="mb-6 p-3 bg-[#FAFAFA]">
              <div className="bg-[#34c759]  hover:bg-[#34c759] text-sm text-white mb-4 w-fit px-2 rounded-full">
                Winners
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-sm">
                  <div className="text-[#1e1e1e] font-medium">1. HFL</div>
                  <div className="text-[#737384]">(5%)</div>
                </div>
                <div className="text-sm">
                  <div className="text-[#1e1e1e] font-medium">1. HFL</div>
                  <div className="text-[#737384]">(5%)</div>
                </div>
                <div className="text-sm">
                  <div className="text-[#1e1e1e] font-medium">1. HFL</div>
                  <div className="text-[#737384]">(5%)</div>
                </div>
              </div>
            </div>

            <div className="mb-8 p-3 bg-[#FAFAFA]">
              <div className="bg-[#dc2626] hover:bg-[#dc2626] text-sm text-white mb-4 w-fit px-2 rounded-full">
                Losers
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-sm">
                  <div className="text-[#1e1e1e] font-medium">1. HFL</div>
                  <div className="text-[#737384]">(5%)</div>
                </div>
                <div className="text-sm">
                  <div className="text-[#1e1e1e] font-medium">1. HFL</div>
                  <div className="text-[#737384]">(5%)</div>
                </div>
                <div className="text-sm">
                  <div className="text-[#1e1e1e] font-medium">1. HFL</div>
                  <div className="text-[#737384]">(5%)</div>
                </div>
              </div>
            </div>

            <Button className=" bg-[#1e1e1e] hover:bg-[#03061d] text-white">
              Return to Home
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
}
