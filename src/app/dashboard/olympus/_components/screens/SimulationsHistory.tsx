"use client";
import { But<PERSON> } from "@/components/ui/button";
import { useGetQueryOlympusBackend2 } from "@/services/api_hooks";
import {
  Play,
  Download,
  AlertCircle,
  Clock,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { useState, useMemo } from "react";
import { useRouter } from "next/navigation";

export default function SimulationsHistory() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageLimit] = useState(10);

  const {
    data: historyData,
    isLoading: isLoadingHistory,
    error: historyError,
  } = useGetQueryOlympusBackend2(
    `/report/simulation-history?page=${currentPage}&limit=${pageLimit}`,
    ["simulation-history", currentPage, pageLimit]
  );

  // Data validation and transformation
  const { simulations, pagination } = useMemo(() => {
    if (!historyData || !historyData.history) {
      return { simulations: [], pagination: null };
    }

    const transformedSimulations = historyData.history.map((sim: any) => ({
      id: sim.simulation_id,
      name: `${sim.ticker} Simulation`,
      ticker: sim.ticker,
      date: new Date(sim.started_at).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      }),
      status: sim.status,
      mode: sim.mode,
      progress: parseFloat(sim.progress_percentage || "0"),
      numAgents: sim.num_agents,
      startDate: sim.start_date,
      endDate: sim.end_date,
      //errorMessage: sim.error_message,
      hasReport: !!sim.s3_report_url,
      reportUrl: sim.s3_report_url,
      eventCount: sim.event_timeline?.length || 0,
      createdAt: sim.created_at,
      completedAt: sim.completed_at,
      currentSimTime: sim.current_sim_time,
    }));

    return {
      simulations: transformedSimulations,
      pagination: historyData.pagination,
    };
  }, [historyData]);

  // Status configuration
  const getStatusConfig = (status: string) => {
    switch (status) {
      case "completed":
        return {
          icon: CheckCircle,
          color: "text-green-600",
          bgColor: "bg-green-100",
          label: "Completed",
        };
      case "processing":
      case "running":
        return {
          icon: Clock,
          color: "text-blue-600",
          bgColor: "bg-blue-100",
          label: "Processing",
        };
      case "failed":
        return {
          icon: XCircle,
          color: "text-red-600",
          bgColor: "bg-red-100",
          label: "Failed",
        };
      default:
        return {
          icon: AlertCircle,
          color: "text-gray-600",
          bgColor: "bg-gray-100",
          label: status,
        };
    }
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Handle simulation actions
  const handlePlaySimulation = (simulation: any) => {
    router.push(
      `/dashboard/olympus/simulations//${simulation.id}?ticker=${simulation.ticker}&start_date=${simulation.startDate}&end_date=${simulation.endDate}&history=true`
    );
  };

  const handleDownloadReport = (simulation: any) => {
    if (simulation.reportUrl) {
      window.open(simulation.reportUrl, "_blank");
    }
  };

  // Determine which data to display
  const displaySimulations = simulations.length > 0 ? simulations : [];
  const displayPagination = pagination || {
    total: 0,
    totalPages: 0,
    currentPage: 1,
    hasNextPage: false,
    hasPrevPage: false,
  };

  // Loading state
  if (isLoadingHistory) {
    return (
      <div className="min-h-screen bg-[#fafafa] mx-auto w-[1400px]">
        <main className="max-w-xl mx-auto mt-12 p-6 bg-[#ffffff] rounded-lg">
          <div className="mb-8">
            <h1 className="text-2xl font-semibold text-[#03061d] mb-2">
              Simulation History
            </h1>
            <p className="text-[#98a2b3] text-sm">
              Loading your simulation history...
            </p>
          </div>

          <div className="space-y-4">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="py-4 animate-pulse">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <div className="w-8 h-8 bg-gray-200 rounded"></div>
                    <div className="w-8 h-8 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </main>
      </div>
    );
  }

  // Error state
  if (historyError) {
    return (
      <div className="min-h-screen bg-[#fafafa] mx-auto w-[1400px]">
        <main className="max-w-xl mx-auto mt-12 p-6 bg-[#ffffff] rounded-lg">
          <div className="mb-8">
            <h1 className="text-2xl font-semibold text-[#03061d] mb-2">
              Simulation History
            </h1>
            <div className="text-red-600 text-sm flex items-center gap-2">
              <XCircle className="w-4 h-4" />
              Error loading simulation history. Please try again.
            </div>
          </div>

          <div className="text-center py-8">
            <Button
              onClick={() => window.location.reload()}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Retry
            </Button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#fafafa] mx-auto w-[1400px]">
      <main className="max-w-xl mx-auto mt-12 p-6 bg-[#ffffff] rounded-lg">
        <div className="mb-8">
          <h1 className="text-2xl font-semibold text-[#03061d] mb-2">
            Simulation History
          </h1>
          <p className="text-[#98a2b3] text-sm">
            You can leave this page without interrupting the processing.
            <br />
            Simulation will be delivered to you when ready
          </p>
        </div>

        {/* Empty State */}
        {displaySimulations.length === 0 && !isLoadingHistory && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-4xl mb-4">📊</div>
            <h3 className="text-lg font-medium text-[#03061d] mb-2">
              No simulations yet
            </h3>
            <p className="text-[#98a2b3] text-sm mb-4">
              Start your first simulation to see it here
            </p>
            <Button
              onClick={() => router.push("/dashboard/olympus/simulations")}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Create Simulation
            </Button>
          </div>
        )}

        {/* Simulation List */}
        <div className="space-y-1">
          {displaySimulations.map((simulation: any, index: number) => {
            const statusConfig = getStatusConfig(simulation.status);
            const StatusIcon = statusConfig.icon;

            return (
              <div
                key={simulation.id || index}
                className="py-4 border-b border-gray-100 last:border-b-0"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium text-[#03061d]">
                        {simulation.name}
                      </h3>
                      <div
                        className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs ${statusConfig.bgColor} ${statusConfig.color}`}
                      >
                        <StatusIcon className="w-3 h-3" />
                        {statusConfig.label}
                      </div>
                    </div>

                    <div className="text-sm text-[#98a2b3] space-y-1">
                      <p>{simulation.date}</p>
                      <div className="flex items-center gap-4 text-xs">
                        <span>Mode: {simulation.mode}</span>
                        <span>Agents: {simulation.numAgents}</span>
                        {simulation.eventCount > 0 && (
                          <span>Events: {simulation.eventCount}</span>
                        )}
                      </div>
                    </div>

                    {/* Progress bar for processing simulations */}
                    {(simulation.status === "processing" ||
                      simulation.status === "running") && (
                      <div className="mt-2">
                        <div className="w-full bg-[#e2e8f0] rounded-full h-1">
                          <div
                            className="bg-[#00c81b] h-1 rounded-full transition-all duration-300"
                            style={{ width: `${simulation.progress}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-[#98a2b3] mt-1">
                          {simulation.progress.toFixed(1)}% complete
                        </p>
                      </div>
                    )}

                    {/* Error message for failed simulations */}
                    {/* {simulation.status === "failed" &&
                      simulation.errorMessage && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                          <p className="font-medium">Error:</p>
                          <p
                            className="truncate"
                            title={simulation.errorMessage}
                          >
                            {simulation.errorMessage}
                          </p>
                        </div>
                      )} */}
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="w-8 h-8 hover:bg-[#f2f2f2]"
                      onClick={() => handlePlaySimulation(simulation)}
                      disabled={simulation.status === "failed"}
                      title="View simulation"
                    >
                      <Play className="w-4 h-4 text-[#7f7f81]" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="w-8 h-8 hover:bg-[#f2f2f2]"
                      onClick={() => handleDownloadReport(simulation)}
                      disabled={!simulation.hasReport}
                      title={
                        simulation.hasReport
                          ? "Download report"
                          : "Report not available"
                      }
                    >
                      <Download className="w-4 h-4 text-[#7f7f81]" />
                    </Button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Pagination */}
        {displaySimulations.length > 0 && (
          <div className="flex items-center justify-between mt-8">
            <p className="text-sm text-[#98a2b3]">
              Showing {displaySimulations.length} of {displayPagination.total}{" "}
              simulations
            </p>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                className="text-[#98a2b3] hover:text-[#03061d] disabled:opacity-50"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={!displayPagination.hasPrevPage || isLoadingHistory}
              >
                Previous
              </Button>

              <div className="flex items-center gap-1">
                {/* Page numbers */}
                {Array.from(
                  { length: Math.min(displayPagination.totalPages, 5) },
                  (_, i) => {
                    const pageNum = i + 1;
                    const isCurrentPage =
                      pageNum === displayPagination.currentPage;

                    return (
                      <Button
                        key={pageNum}
                        variant={isCurrentPage ? "default" : "ghost"}
                        size="sm"
                        className={`w-8 h-8 p-0 ${
                          isCurrentPage
                            ? "bg-blue-600 text-white hover:bg-blue-700"
                            : "text-[#98a2b3] hover:text-[#03061d]"
                        }`}
                        onClick={() => handlePageChange(pageNum)}
                        disabled={isLoadingHistory}
                      >
                        {pageNum}
                      </Button>
                    );
                  }
                )}
              </div>

              <Button
                variant="ghost"
                className="text-[#98a2b3] hover:text-[#03061d] disabled:opacity-50"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={!displayPagination.hasNextPage || isLoadingHistory}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
