"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Loader2,
} from "lucide-react";
import { useSearchParams, useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  OLYMPUS_URL,
  useGetQueryOlympusBackend,
  useSubmitQueryOlympusBackend,
} from "@/services/api_hooks";
import AddQuestionModal from "./components/AddQuestionModal";
import EditQuestionModal from "./components/EditQuestionModal";
import GenerateReportModal from "./components/GenerateReportModal";
import { toast } from "sonner";

interface Question {
  order: number;
  sn: number;
  include: boolean;
  insightQuestion: string;
}

export default function SummaryReport() {
  const router = useRouter();
  const params = useSearchParams();
  const param = useParams();

  const ticker = params.get("ticker");
  const simulation_id = param?.id;

  // Modal states
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isGenerateModalOpen, setIsGenerateModalOpen] = useState(false);
  const [editingQuestionIndex, setEditingQuestionIndex] = useState<
    number | null
  >(null);

  // Report generation states
  const [reportData, setReportData] = useState<any>(null);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);

  const initialQuestions: Question[] = [
    {
      order: 1,
      sn: 1,
      include: true,
      insightQuestion: "Why did the top-performing agent(s) win?",
    },
    {
      order: 2,
      sn: 2,
      include: true,
      insightQuestion: "Why did the worst-performing agent(s) lose money?",
    },
    {
      order: 3,
      sn: 3,
      include: true,
      insightQuestion:
        "What were the key differences between the top and bottom agents' strategies?",
    },
    {
      order: 4,
      sn: 4,
      include: true,
      insightQuestion:
        "What trades or decisions were most responsible for each agent's performance?",
    },
    {
      order: 5,
      sn: 5,
      include: true,
      insightQuestion:
        "How did agents react differently to major price shocks or news events?",
    },
    {
      order: 6,
      sn: 6,
      include: true,
      insightQuestion:
        "Does the initial position of an agent affect their trading decisions?",
    },
    {
      order: 7,
      sn: 7,
      include: true,
      insightQuestion:
        "Did any agents consistently front-run or lag others during trade opportunities?",
    },
    {
      order: 8,
      sn: 8,
      include: true,
      insightQuestion:
        "Rank the most important factors that affect the relative performance of agents.",
    },
    {
      order: 9,
      sn: 9,
      include: true,
      insightQuestion:
        "What are the top 5 lessons learned from the simulation?",
    },
    {
      order: 10,
      sn: 10,
      include: true,
      insightQuestion:
        "Which agents made the fewest mistakes or avoided critical losses?",
    },
  ];

  const [questions, setQuestions] = useState<Question[]>(initialQuestions);
  const [draggedItem, setDraggedItem] = useState<number | null>(null);

  // Function to handle drag start
  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedItem(index);
    e.dataTransfer.effectAllowed = "move";
  };

  // Function to handle drag over
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  // Function to handle drop
  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();

    if (draggedItem === null || draggedItem === dropIndex) {
      setDraggedItem(null);
      return;
    }

    const newQuestions = [...questions];
    const draggedQuestion = newQuestions[draggedItem];

    // Remove the dragged item
    newQuestions.splice(draggedItem, 1);

    // Insert at new position
    newQuestions.splice(dropIndex, 0, draggedQuestion);

    // Update order numbers
    const updatedQuestions = newQuestions.map((question, index) => ({
      ...question,
      order: index + 1,
    }));

    setQuestions(updatedQuestions);
    setDraggedItem(null);
  };

  // Function to toggle include checkbox
  const toggleInclude = (index: number) => {
    const newQuestions = [...questions];
    newQuestions[index] = {
      ...newQuestions[index],
      include: !newQuestions[index].include,
    };
    setQuestions(newQuestions);
  };

  // Function to add new question
  const handleAddQuestion = (newQuestion: string) => {
    const newQuestionObj: Question = {
      order: questions.length + 1,
      sn: questions.length + 1,
      include: true,
      insightQuestion: newQuestion,
    };
    setQuestions([...questions, newQuestionObj]);
  };

  // Function to edit question
  const handleEditQuestion = (editedQuestion: string) => {
    if (editingQuestionIndex !== null) {
      const newQuestions = [...questions];
      newQuestions[editingQuestionIndex] = {
        ...newQuestions[editingQuestionIndex],
        insightQuestion: editedQuestion,
      };
      setQuestions(newQuestions);
      setEditingQuestionIndex(null);
    }
  };

  // Function to delete question
  const handleDeleteQuestion = (index: number) => {
    const newQuestions = questions.filter((_, i) => i !== index);
    // Update order numbers
    const updatedQuestions = newQuestions.map((question, i) => ({
      ...question,
      order: i + 1,
      sn: i + 1,
    }));
    setQuestions(updatedQuestions);
  };

  // Function to reset to default questions
  const handleResetToDefault = () => {
    setQuestions(initialQuestions);
  };

  // Function to open edit modal
  const openEditModal = (index: number) => {
    setEditingQuestionIndex(index);
    setIsEditModalOpen(true);
  };

  // Function to open generate report confirmation modal
  const handleGenerateReportClick = () => {
    // Filter questions that are included
    const includedQuestions = questions.filter((q) => q.include);

    if (includedQuestions.length === 0) {
      toast.error(
        "Please include at least one question before generating the report."
      );
      return;
    }

    setIsGenerateModalOpen(true);
  };

  // Function to confirm and generate report
  const handleConfirmGenerateReport = async () => {
    try {
      setIsGeneratingReport(true);
      // Filter questions that are included
      const includedQuestions = questions.filter((q) => q.include);

      const payload = {
        questions: includedQuestions.map((q) => q.insightQuestion),
      };

      const response = await fetch(
        `${OLYMPUS_URL}/api/simulation/${simulation_id}/report`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to generate report");
      }

      const data = await response.json();
      setReportData(data);
      setIsGenerateModalOpen(false);
      toast.success("Report generated successfully!");
    } catch (error) {
      console.error("Error generating report:", error);
      toast.error("Failed to generate report. Please try again.");
    } finally {
      setIsGeneratingReport(false);
    }
  };

  // Handle successful report generation
  useEffect(() => {
    if (reportData) {
      console.log("Generated report:", reportData);
    }
  }, [reportData]);

  return (
    <div className="min-h-screen bg-[#fafafa] mx-auto w-[1400px]">
      <main>
        <div className="flex items-center gap-2 mb-8">
          <div
            onClick={() => router.back()}
            className="cursor-pointer inline-flex items-center justify-center p-1 hover:bg-[#f7f7f7] rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-[#0359d8] focus:ring-offset-2"
          >
            <ArrowLeft className="w-4 h-4" />
          </div>
          <div className="w-[1px] bg-[#98a2b3] h-3" />

          <span
            onClick={() => router.back()}
            className="text-[#98a2b3] text-sm cursor-pointer hover:text-[#1d1d1d] transition-colors"
          >
            Simulation
          </span>
          <span className="text-[#98a2b3] text-sm">
            <ChevronRight className="w-4 h-4" />
          </span>
          <span className="text-[#98a2b3] text-sm font-medium cursor-pointer hover:text-[#1d1d1d] transition-colors">
            {ticker}
          </span>
        </div>

        <main className="flex-1 p-6">
          <div className="bg-[#ffffff] rounded-lg shadow-sm p-8 border border-[#e5e5e5]">
            <h1 className="text-3xl font-bold text-[#03061d] mb-2">
              Summary report
            </h1>
            <p className="text-[#7f7f81] mb-6">
              Define the questions you want Olympus to answer in the summary
              report. You can edit, reorder, or toggle them off. Olympus will
              use your final list to generate a detailed post-simulation insight
              report.
            </p>

            <div className="flex justify-between items-center mb-8">
              <Button
                variant="outline"
                className="border-[#d9d9d9] text-[#03061d] hover:bg-[#f7f7f7] bg-transparent"
                onClick={() => setIsAddModalOpen(true)}
              >
                Add Question
              </Button>
              <Button
                className="bg-[#03061d] text-[#ffffff] hover:bg-[#121212]"
                onClick={handleGenerateReportClick}
                disabled={isGeneratingReport}
              >
                Generate Report{" "}
                {isGeneratingReport && (
                  <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                )}
              </Button>
            </div>

            <h2 className="text-xl font-semibold text-[#03061d] mb-4">
              Questions
            </h2>

            <div className="border border-[#e5e5e5] rounded-lg overflow-hidden">
              <Table>
                <TableHeader className="bg-[#f7f7f7]">
                  <TableRow className="border-b border-[#e5e5e5]">
                    <TableHead className="w-[50px] text-[#7f7f81] font-medium">
                      Order
                    </TableHead>
                    <TableHead className="w-[50px] text-[#7f7f81] font-medium">
                      S/N
                    </TableHead>
                    <TableHead className="w-[70px] text-[#7f7f81] font-medium">
                      Include
                    </TableHead>
                    <TableHead className="text-[#7f7f81] font-medium">
                      Insight Question
                    </TableHead>
                    <TableHead className="w-[100px] text-[#7f7f81] font-medium text-right">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {questions.map((question, index) => (
                    <TableRow
                      key={question.sn}
                      className={`border-b border-[#e5e5e5] last:border-b-0 ${
                        draggedItem === index ? "opacity-50" : ""
                      }`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, index)}
                      onDragOver={handleDragOver}
                      onDrop={(e) => handleDrop(e, index)}
                    >
                      <TableCell className="py-3">
                        <Menu className="w-4 h-4 text-[#a7a7a7] cursor-grab" />
                      </TableCell>
                      <TableCell className="font-medium text-[#121212] py-3">
                        {question.order}
                      </TableCell>
                      <TableCell className="py-3">
                        <Checkbox
                          checked={question.include}
                          onCheckedChange={() => toggleInclude(index)}
                          className="border-[#a7a7a7] data-[state=checked]:bg-[#0359d8] data-[state=checked]:text-[#ffffff]"
                        />
                      </TableCell>
                      <TableCell className="text-[#121212] py-3">
                        {question.insightQuestion}
                      </TableCell>
                      <TableCell className="text-right py-3">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 px-3 border-[#d9d9d9] hover:bg-[#e2e8f0] bg-transparent"
                            onClick={() => openEditModal(index)}
                          >
                            Edit <Pencil className="w-3 h-3 ml-1" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 px-3 border-[#d9d9d9] text-[#af202d] hover:bg-[#ffe5e5] hover:text-[#af202d] bg-transparent"
                            onClick={() => handleDeleteQuestion(index)}
                          >
                            Delete <Trash2 className="w-3 h-3 ml-1" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 px-3 border-[#d9d9d9] hover:bg-[#e2e8f0] bg-transparent"
                            onClick={handleResetToDefault}
                          >
                            Reset to default{" "}
                            <RefreshCcw className="w-3 h-3 ml-1" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </main>

        {/* Add Question Modal */}
        <AddQuestionModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          onSave={handleAddQuestion}
        />

        {/* Edit Question Modal */}
        <EditQuestionModal
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setEditingQuestionIndex(null);
          }}
          onSave={handleEditQuestion}
          currentQuestion={
            editingQuestionIndex !== null
              ? questions[editingQuestionIndex]?.insightQuestion || ""
              : ""
          }
        />

        {/* Generate Report Confirmation Modal */}
        <GenerateReportModal
          isOpen={isGenerateModalOpen}
          onClose={() => setIsGenerateModalOpen(false)}
          onConfirm={handleConfirmGenerateReport}
          isGenerating={isGeneratingReport}
        />
      </main>
    </div>
  );
}
