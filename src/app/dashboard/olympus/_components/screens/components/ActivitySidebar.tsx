interface ActivitySidebarProps {
  timelineEventsWithProgress: any[];
  handleTimelineEventClick: (event: any) => void;
  isLoadingEvent: boolean;
}

export default function ActivitySidebar({
  timelineEventsWithProgress,
  handleTimelineEventClick,
  isLoadingEvent,
}: ActivitySidebarProps) {
  return (
    <div className="w-[25%] border-l border-[#f7f7f7] bg-white p-2 px-4 rounded-md">
      <h3 className="text-[#1d1d1d] font-medium text-lg mb-4">
        Activity
        {isLoadingEvent && (
          <span className="text-sm text-gray-500 ml-2">Loading...</span>
        )}
      </h3>
      <div className="space-y-4">
        {timelineEventsWithProgress.map((item: any, idx: number) => (
          <div key={idx} className="relative">
            <div
              className={`flex items-start gap-3 p-4 transition-all duration-300 cursor-pointer hover:bg-gray-100 ${
                item?.isCurrent
                  ? "bg-blue-50 border-l-4 border-blue-500"
                  : item?.isPassed
                  ? "bg-gray-50 hover:bg-gray-200"
                  : "hover:bg-gray-50"
              }`}
              onClick={() => handleTimelineEventClick(item)}
              title={`Click to jump to: ${item?.title}`}
            >
              {item?.isCurrent ? (
                <svg
                  width="12"
                  height="87"
                  viewBox="0 0 12 87"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="animate-pulse"
                >
                  <circle cx="6" cy="9" r="6" fill="#0D5EFF" />
                  <rect x="5.5" y="19" width="1" height="80" fill="#0D5EFF" />
                </svg>
              ) : item?.isPassed ? (
                <svg
                  width="12"
                  height="78"
                  viewBox="0 0 12 78"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="6" cy="9" r="6" fill="#6B7280" />
                  <rect x="5.5" y="19" width="1" height="56" fill="#6B7280" />
                </svg>
              ) : (
                <svg
                  width="12"
                  height="78"
                  viewBox="0 0 12 78"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="6" cy="9" r="6" fill="#D9D9D9" />
                  <rect x="5.5" y="19" width="1" height="56" fill="#D9D9D9" />
                </svg>
              )}
              <div className="flex-1">
                <h4
                  className={`font-medium text-sm mb-1 ${
                    item?.isCurrent
                      ? "text-blue-900"
                      : item?.isPassed
                      ? "text-gray-600"
                      : "text-[#1d1d1d]"
                  }`}
                >
                  {item?.title}
                  {item?.isCurrent && (
                    <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                      Current
                    </span>
                  )}
                  {item?.isPassed && (
                    <span className="ml-2 text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                      Completed
                    </span>
                  )}
                </h4>
                <p
                  className={`text-xs mb-2 ${
                    item?.isCurrent
                      ? "text-blue-700"
                      : item?.isPassed
                      ? "text-gray-500"
                      : "text-[#7f7f81]"
                  }`}
                >
                  {item?.description}
                </p>
                <span
                  className={`text-xs ${
                    item?.isCurrent
                      ? "text-blue-600"
                      : item?.isPassed
                      ? "text-gray-400"
                      : "text-[#a0a7b4]"
                  }`}
                >
                  {item?.time}
                  {item?.eventTimestamp && (
                    <span className="ml-2">
                      •{" "}
                      {new Date(item.eventTimestamp * 1000)
                        .toLocaleString("en-US", {
                          timeZone: "America/New_York",
                          month: "2-digit",
                          day: "2-digit",
                          hour: "2-digit",
                          minute: "2-digit",
                          hour12: false,
                        })
                        .replace(",", "")}
                    </span>
                  )}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
