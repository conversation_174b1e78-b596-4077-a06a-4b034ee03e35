import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface AddQuestionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (question: string) => void;
}

export default function AddQuestionModal({
  isOpen,
  onClose,
  onSave,
}: AddQuestionModalProps) {
  const [question, setQuestion] = useState("");

  const handleSave = () => {
    if (question.trim()) {
      onSave(question.trim());
      setQuestion("");
      onClose();
    }
  };

  const handleClose = () => {
    setQuestion("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-[#03061d]">
            Add Question
          </DialogTitle>
          <p className="text-sm text-[#7f7f81] mt-2">
            Add a Question to your preference.
          </p>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label
              htmlFor="question"
              className="text-sm font-medium text-[#03061d]"
            >
              Insight Question
            </Label>
            <Textarea
              id="question"
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              placeholder="Enter your insight question here..."
              className="mt-2 min-h-[120px] resize-none border-[#d9d9d9] focus:border-[#0359d8] focus:ring-[#0359d8]"
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="outline"
              onClick={handleClose}
              className="border-[#d9d9d9] text-[#03061d] hover:bg-[#f7f7f7] bg-transparent"
            >
              Back
            </Button>
            <Button
              onClick={handleSave}
              disabled={!question.trim()}
              className="bg-[#03061d] text-[#ffffff] hover:bg-[#121212] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Save
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
