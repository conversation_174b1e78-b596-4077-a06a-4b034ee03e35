import CandlestickReplayChart from "@/components/CandlestickReplayChart";
import AssetValueChart from "@/components/AssetValueChart";
import { cn } from "@/lib/utils";

interface ChartSectionProps {
  replayIndex: number;
  chartDataSource: any[];
  transformedPriceData: any[];
  transformedAssetData: any[];
  participants: any[];
  participantActions: any[];
  getParticipantColor: (
    participantId: string,
    participantName: string
  ) => string;
  syncCharts: boolean;
  chartTimeRange: { from: number; to: number } | null;
  handleChartTimeRangeChange: (
    range: { from: number; to: number },
    sourceChart: string
  ) => void;
  timelineEventsWithProgress: any[];
  handleTimelineEventClick: (event: any) => void;
  handleTimelineScrub: (e: React.MouseEvent<HTMLDivElement>) => void;
  isLoadingPrices: boolean;
}

export default function ChartSection({
  replayIndex,
  chartDataSource,
  transformedPriceData,
  transformedAssetData,
  participants,
  participantActions,
  getParticipantColor,
  syncCharts,
  chartTimeRange,
  handleChartTimeRangeChange,
  timelineEventsWithProgress,
  handleTimelineEventClick,
  handleTimelineScrub,
  isLoadingPrices,
}: ChartSectionProps) {
  // Create replay data slice for the price chart (candlestick format)
  const replayData = chartDataSource.slice(0, replayIndex).map((item: any) => {
    // Validate and sanitize values to prevent chart errors
    const sanitizeValue = (value: any, fallback: number = 0): number => {
      if (value === null || value === undefined || !isFinite(value)) {
        return fallback;
      }
      return Number(value);
    };

    const baseValue = sanitizeValue(item.value, 100);

    return {
      time: item.time,
      open: sanitizeValue(item.open, baseValue),
      high: sanitizeValue(item.high, baseValue),
      low: sanitizeValue(item.low, baseValue),
      close: sanitizeValue(item.close, baseValue),
    };
  });

  // Use full asset data for the asset chart to show complete time range
  const assetReplayData = transformedAssetData;

  return (
    <div className="bg-white p-4 rounded-md">
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-[#1d1d1d] font-medium text-lg">
            Price Chart
            {isLoadingPrices && (
              <span className="text-sm text-gray-500 ml-2">Loading...</span>
            )}
          </h3>
        </div>

        <div
          className="w-full h-64 sm:h-72 md:h-80 lg:h-96 overflow-visible"
          style={{
            maxWidth: "100%",
            minWidth: 0,
            position: "relative",
          }}
        >
          <CandlestickReplayChart
            data={replayData}
            fullDataLength={transformedPriceData.length}
            replayIndex={replayIndex}
            participantActions={participantActions}
            eventMarkers={timelineEventsWithProgress}
            height={0}
            onTimeRangeChange={handleChartTimeRangeChange}
            syncedTimeRange={syncCharts ? chartTimeRange : null}
          />
        </div>
      </div>

      <div className="mb-8">
        <h3 className="text-[#1d1d1d] font-medium text-lg mb-4">Asset Value</h3>
        <AssetValueChart
          data={assetReplayData}
          height={400}
          hideTimeLabels={false}
          fullDataLength={transformedAssetData.length}
          replayIndex={replayIndex}
          agentNames={
            assetReplayData.length > 0 && assetReplayData[0].agentNames
              ? assetReplayData[0].agentNames
              : participants.map((participant, index) => {
                  const participantId =
                    participant.agent_id || `agent_${index}`;
                  const participantName =
                    participant.name || `Agent ${index + 1}`;
                  return {
                    key: `agent_${index}`,
                    name: participantName,
                    color:
                      participant.color ||
                      getParticipantColor(participantId, participantName),
                  };
                })
          }
          onTimeRangeChange={handleChartTimeRangeChange}
          syncedTimeRange={syncCharts ? chartTimeRange : null}
        />

        {/* Timeline Events */}
        {/*   <div className="mt-20">
          <div className="text-xs text-gray-500 mb-2 text-center">
            Click on events or timeline to jump to specific moments
          </div>
          <div className="relative h-8">
            <div
              className="absolute top-1/2 left-0 right-0 h-[1px] bg-gray-500 transform -translate-y-1/2 cursor-pointer hover:bg-gray-400 transition-colors rounded-full"
              onClick={handleTimelineScrub}
              title="Click to jump to any point in the timeline"
            >
              <div
                className="absolute top-1/2 left-0 h-[1px] bg-gray-500 transform -translate-y-1/2 pointer-events-none transition-all duration-200"
                style={{ width: "100%" }}
              ></div>
            </div>

            {timelineEventsWithProgress.map((event: any, index: number) => {
              let eventPosition = 0;

              if (event.priceChartIndex !== -1 && chartDataSource.length > 0) {
                eventPosition =
                  (event.priceChartIndex / chartDataSource.length) * 100;
              } else {
                const totalEvents = Math.max(
                  timelineEventsWithProgress.length,
                  1
                );
                eventPosition = (index / (totalEvents - 1)) * 100;
              }

              eventPosition = Math.min(eventPosition, 95);
              eventPosition = Math.max(eventPosition, 5);

              return (
                <div
                  key={event.id || index}
                  className="absolute top-1/2 transform -translate-x-1/2 -translate-y-1/2"
                  style={{ left: `${eventPosition}%` }}
                >
                  <div
                    className={cn(
                      "w-3 h-3 rounded-full transition-all duration-500 cursor-pointer relative group",
                      event.isCurrent
                        ? "bg-blue-600 shadow-lg ring-2 ring-blue-300"
                        : event.isPassed
                        ? "bg-gray-500 opacity-70 hover:opacity-90"
                        : "bg-gray-400 opacity-80 hover:opacity-100"
                    )}
                    title={`${event.title}: ${event.description} ${
                      event.isCurrent
                        ? "(Current)"
                        : event.isPassed
                        ? "(Passed)"
                        : "(Upcoming)"
                    } - Click to jump to this event`}
                    onClick={() => handleTimelineEventClick(event)}
                  >
                    <div
                      className={cn(
                        "absolute bottom-full left-1/2 transform -translate-x-1/2 mb-3 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-[9999] max-w-xs shadow-lg",
                        event.isCurrent
                          ? "opacity-100 shadow-xl ring-2 ring-blue-300"
                          : "opacity-0 group-hover:opacity-100"
                      )}
                    >
                      <div className="font-medium text-blue-200">
                        {event.title}
                      </div>
                      <div className="text-gray-300 mt-1">
                        {event.description}
                      </div>
                      {event.eventTimestamp && (
                        <div className="text-gray-400 mt-1 text-xs">
                          {new Date(
                            event.eventTimestamp * 1000
                          ).toLocaleString()}
                        </div>
                      )}
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 z-[9999]"></div>
                    </div>

                    {event.isCurrent && (
                      <div className="absolute inset-0 bg-blue-400 rounded-full animate-pulse opacity-40"></div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div> */}
      </div>
    </div>
  );
}
