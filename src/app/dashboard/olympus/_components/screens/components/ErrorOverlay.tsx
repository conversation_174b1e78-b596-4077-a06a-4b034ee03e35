interface ErrorOverlayProps {
  keyError: any;
  pricesError: any;
  assetsError: any;
  eventsError: any;
}

export default function ErrorOverlay({
  keyError,
  pricesError,
  assetsError,
  eventsError,
}: ErrorOverlayProps) {
  return (
    <div className="min-h-screen bg-[#fafafa] flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-sm text-center max-w-md">
        <div className="text-red-500 text-4xl mb-4">⚠️</div>
        <h2 className="text-lg font-medium text-gray-900 mb-2">
          Error Loading Data
        </h2>
        <div className="space-y-2 text-sm text-gray-600 mb-4">
          {keyError && <div className="text-red-600">• API key error</div>}
          {pricesError && (
            <div className="text-red-600">• Price data error</div>
          )}
          {assetsError && (
            <div className="text-red-600">• Asset data error</div>
          )}
          {eventsError && <div className="text-red-600">• Events error</div>}
        </div>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    </div>
  );
}
