import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, Refresh<PERSON>w } from "lucide-react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";

interface EventSimulationHeaderProps {
  history: string | null;
  ticker: string | null;
  start: string | null;
  end: string | null;
  simulation_id: string;
  onRefetch?: () => void;
  isRefetching?: boolean;
  isDataEmpty?: boolean;
}

export default function EventSimulationHeader({
  history,
  ticker,
  start,
  end,
  simulation_id,
  onRefetch,
  isRefetching,
  isDataEmpty,
}: EventSimulationHeaderProps) {
  const router = useRouter();

  return (
    <div className="flex items-center gap-2 mb-8">
      {history ? (
        <div className="flex items-center gap-2">
          <div
            onClick={() => router.back()}
            className="cursor-pointer inline-flex items-center justify-center p-1 hover:bg-[#f7f7f7] rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-[#0359d8] focus:ring-offset-2"
          >
            <ArrowLeft className="w-4 h-4" />
          </div>
          <div className="w-[1px] bg-[#98a2b3] h-3" />

          <span
            onClick={() => router.back()}
            className="text-[#98a2b3] text-sm font-medium cursor-pointer hover:text-[#1d1d1d] transition-colors"
          >
            Simulation History
          </span>
          <span className="text-[#98a2b3] text-sm">
            <ChevronRight className="w-4 h-4" />
          </span>

          <span className="text-[#1d1d1d] text-sm font-medium">
            {ticker} Simulation
          </span>
        </div>
      ) : (
        <div className="flex items-center gap-2">
          <div
            onClick={() => router.back()}
            className="cursor-pointer inline-flex items-center justify-center p-1 hover:bg-[#f7f7f7] rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-[#0359d8] focus:ring-offset-2"
          >
            <ArrowLeft className="w-4 h-4" />
          </div>
          <div className="w-[1px] bg-[#98a2b3] h-3" />

          <span
            onClick={() => router.push(`/dashboard/olympus`)}
            className="text-[#98a2b3] text-sm cursor-pointer hover:text-[#1d1d1d] transition-colors"
          >
            New Simulation
          </span>
          <span className="text-[#98a2b3] text-sm">
            <ChevronRight className="w-4 h-4" />
          </span>
          <span className="text-[#1d1d1d] text-sm font-medium">
            {ticker} Simulation
          </span>
        </div>
      )}

      {/* Data Status Indicators */}

      <div className="ml-auto flex items-center gap-2 text-xs">
        {/* Data Status Indicator */}

        {onRefetch && (
          <Button
            onClick={onRefetch}
            disabled={isRefetching}
            variant="outline"
            size="sm"
            className="text-blue-600 border-blue-200 hover:bg-blue-50"
          >
            {isRefetching ? (
              <>
                <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="w-3 h-3 mr-1" />
                Refresh
              </>
            )}
          </Button>
        )}
        {/* <Button
          onClick={() => {
            router.push(
              `/dashboard/olympus/simulations/history/${simulation_id}?ticker=${ticker}&start_date=${start}&end_date=${end}`
            );
          }}
          variant="link"
          size="sm"
          className="text-blue-600 p-0"
        >
          <Sparkles /> Summary Report
        </Button> */}
        {/*  <Button
          onClick={() => {
            router.push(
              `/dashboard/olympus/simulations/history/${simulation_id}?ticker=${ticker}&start_date=${start}&end_date=${end}`
            );
          }}
        >
          Summary Report
        </Button> */}
      </div>
    </div>
  );
}
