import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";

interface GenerateReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isGenerating: boolean;
}

export default function GenerateReportModal({
  isOpen,
  onClose,
  onConfirm,
  isGenerating,
}: GenerateReportModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-[#03061d]">
            Generate Report
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-sm text-[#7f7f81]">
            Are you sure you want to generate the simulation report based on
            these insight questions?
          </p>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isGenerating}
              className="border-[#d9d9d9] text-[#03061d] hover:bg-[#f7f7f7] bg-transparent"
            >
              Cancel
            </Button>
            <Button
              onClick={onConfirm}
              disabled={isGenerating}
              className="bg-[#03061d] text-[#ffffff] hover:bg-[#121212] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isGenerating ? "Generating..." : "Generate Report"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
