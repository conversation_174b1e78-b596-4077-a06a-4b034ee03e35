interface LoadingOverlayProps {
  keyIsLoading: boolean;
  isLoadingPrices: boolean;
  isLoadingAssets: boolean;
  isLoadingEvent: boolean;
  isLoadingSimulate: boolean;
}

export default function LoadingOverlay({
  keyIsLoading,
  isLoadingPrices,
  isLoadingAssets,
  isLoadingEvent,
  isLoadingSimulate,
}: LoadingOverlayProps) {
  return (
    <div className="min-h-screen bg-[#fafafa] flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-sm text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-lg font-medium text-gray-900 mb-2">
          Loading Simulation Data
        </h2>
        <div className="space-y-1 text-sm text-gray-600">
          {keyIsLoading && <div>• Loading API key...</div>}
          {isLoadingPrices && <div>• Loading price data...</div>}
          {isLoadingAssets && <div>• Loading asset data...</div>}
          {isLoadingEvent && <div>• Loading events...</div>}
          {isLoadingSimulate && <div>• Starting simulation...</div>}
        </div>
      </div>
    </div>
  );
}
