import { useRouter } from "next/navigation";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { Sparkles } from "lucide-react";

interface ParticipantsSectionProps {
  participants: any[];
  simulation_id: string;
  ticker: string | null;
  start: string | null;
  end: string | null;
}

export default function ParticipantsSection({
  participants,
  simulation_id,
  ticker,
  start,
  end,
}: ParticipantsSectionProps) {
  const router = useRouter();

  // Helper function to abbreviate participant names
  const abbreviateName = (name: string): string => {
    if (!name) return "P";

    const words = name.split(" ").filter((word) => word.length > 0);
    if (words.length === 1) {
      return words[0].substring(0, 3).toUpperCase();
    } else {
      return words
        .slice(0, 3)
        .map((word) => word[0].toUpperCase())
        .join("");
    }
  };

  return (
    <div className="mb-8 bg-white p-2 px-4 rounded-md">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-[#1d1d1d] font-medium text-xl">Participants</h2>
          <div className="text-[#7F7F81] text-sm">
            Click on a participant to see their individual trade
          </div>
        </div>

        <Button
          onClick={() => {
            router.push(
              `/dashboard/olympus/simulations/history/${simulation_id}?ticker=${ticker}&start_date=${start}&end_date=${end}`
            );
          }}
          variant="link"
          size="sm"
          className="text-blue-600 p-0"
        >
          <Sparkles /> Summary Report
        </Button>
      </div>
      <div className="flex gap-3">
        <TooltipProvider delayDuration={300}>
          {participants.map((participant, index) => (
            <Tooltip key={participant.id}>
              <TooltipTrigger asChild>
                <button
                  onClick={() => {
                    router.push(
                      `/dashboard/olympus/simulations/participants/${participant.agent_id}?name=${participant.name}&simulation_id=${simulation_id}&ticker=${ticker}&start_date=${start}&end_date=${end}`
                    );
                  }}
                  className="flex items-center gap-2 py-2 px-3 rounded-lg whitespace-nowrap transition-colors hover:bg-gray-50 hover:shadow-md"
                >
                  <span
                    className="text-xs font-bold text-white rounded-full w-5 h-5 flex items-center justify-center"
                    style={{ backgroundColor: participant.color }}
                  >
                    {index + 1}
                  </span>
                  <span className="text-sm font-medium">
                    {abbreviateName(participant.name)}
                  </span>
                </button>
              </TooltipTrigger>
              <TooltipContent
                className="max-w-[320px] p-4 bg-white text-black border border-gray-200 shadow-lg"
                side="bottom"
                align="center"
              >
                <div className="space-y-3">
                  <div className="flex items-center gap-2 pb-2 border-b border-gray-100">
                    <span
                      className="text-xs font-bold text-white rounded-full w-6 h-6 flex items-center justify-center"
                      style={{ backgroundColor: participant.color }}
                    >
                      {index + 1}
                    </span>
                    <div>
                      <div className="font-semibold text-sm text-gray-900">
                        {participant.name}
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <span>
                          {participant.originalAgent?.agent_type || "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3 text-xs">
                    <div>
                      <div className="text-gray-500 font-medium">
                        Risk Tolerance
                      </div>
                      <div className="text-gray-900 capitalize">
                        {participant.originalAgent?.risk_tolerance || "N/A"}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500 font-medium">
                        Allocated Cash
                      </div>
                      <div className="text-gray-900">
                        $
                        {participant.originalAgent?.initial_capital?.toLocaleString() ||
                          "N/A"}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500 font-medium">Latency</div>
                      <div className="text-gray-900">
                        {participant.originalAgent?.reaction_speed_percentile
                          ? `${participant.originalAgent.reaction_speed_percentile}%`
                          : "N/A"}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500 font-medium">Liquidity</div>
                      <div className="text-gray-900">
                        {participant.originalAgent?.liquidity_constraint ||
                          "N/A"}
                      </div>
                    </div>
                  </div>

                  {participant.originalAgent?.description && (
                    <div className="pt-2 border-t border-gray-100">
                      <div className="text-gray-500 font-medium text-xs mb-1">
                        Description
                      </div>
                      <div className="text-gray-700 text-xs leading-relaxed">
                        {participant.originalAgent.description}
                      </div>
                    </div>
                  )}

                  {participant.originalAgent?.behavioral_biases && (
                    <div>
                      <div className="text-gray-500 font-medium text-xs mb-1">
                        Behavioral Biases
                      </div>
                      <div className="text-gray-700 text-xs">
                        {participant.originalAgent.behavioral_biases}
                      </div>
                    </div>
                  )}

                  {participant.originalAgent?.objective && (
                    <div>
                      <div className="text-gray-500 font-medium text-xs mb-1">
                        Objective
                      </div>
                      <div className="text-gray-700 text-xs">
                        {participant.originalAgent.objective}
                      </div>
                    </div>
                  )}
                </div>
              </TooltipContent>
            </Tooltip>
          ))}
        </TooltipProvider>
      </div>
    </div>
  );
}
