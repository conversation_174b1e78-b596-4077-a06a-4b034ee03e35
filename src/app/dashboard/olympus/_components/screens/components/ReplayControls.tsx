import {
  Rotate<PERSON>c<PERSON>,
  <PERSON>,
  Skip<PERSON><PERSON>,
  Ski<PERSON><PERSON><PERSON><PERSON>,
  Pa<PERSON>,
  FastFor<PERSON>,
  Rewind,
  Square,
  Volume2,
  Settings,
} from "lucide-react";

interface ReplayControlsProps {
  replayIndex: number;
  chartDataSource: any[];
  isPlaying: boolean;
  playbackSpeed: number;
  isLooping: boolean;
  showSettings: boolean;
  volume: number;
  autoPlay: boolean;
  syncCharts: boolean;
  progressBarRef: React.RefObject<HTMLDivElement | null>;
  isDragging: boolean;
  handlePlayPause: () => void;
  handleRestart: () => void;
  handleStepBack: () => void;
  handleStepForward: () => void;
  handleJumpToStart: () => void;
  handleJumpToEnd: () => void;
  handleStop: () => void;
  handleSpeedChange: (speed: number) => void;
  handleProgressClick: (event: React.MouseEvent<HTMLDivElement>) => void;
  handleHandleMouseDown: (e: React.MouseEvent<HTMLDivElement>) => void;
  handleHandleTouchStart: (e: React.TouchEvent<HTMLDivElement>) => void;
  setShowSettings: (show: boolean) => void;
  setVolume: (volume: number) => void;
  setAutoPlay: (autoPlay: boolean) => void;
  toggleChartSync: () => void;
}

export default function ReplayControls({
  replayIndex,
  chartDataSource,
  isPlaying,
  playbackSpeed,
  isLooping,
  showSettings,
  volume,
  autoPlay,
  syncCharts,
  progressBarRef,
  isDragging,
  handlePlayPause,
  handleRestart,
  handleStepBack,
  handleStepForward,
  handleJumpToStart,
  handleJumpToEnd,
  handleStop,
  handleSpeedChange,
  handleProgressClick,
  handleHandleMouseDown,
  handleHandleTouchStart,
  setShowSettings,
  setVolume,
  setAutoPlay,
  toggleChartSync,
}: ReplayControlsProps) {
  const speedOptions = [0.25, 0.5, 1, 1.5, 2, 3, 5];

  // Calculate progress percentage
  const progressPercentage =
    chartDataSource.length > 0
      ? (replayIndex / chartDataSource.length) * 100
      : 0;

  // Get current time display
  const getCurrentTimeDisplay = () => {
    if (chartDataSource.length === 0) return "00:00";
    const currentData = chartDataSource[replayIndex - 1];
    if (currentData && currentData.time) {
      return new Date(currentData.time * 1000)
        .toLocaleString("en-US", {
          timeZone: "America/New_York",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        })
        .replace(",", "");
    }
    return `${replayIndex}/${chartDataSource.length}`;
  };

  return (
    <div className="space-y-4 py-6">
      {/* Progress Bar */}
      <div className="px-8">
        <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
          <span>{getCurrentTimeDisplay()}</span>
          <span>
            {replayIndex}/{chartDataSource.length}
          </span>
        </div>
        <div
          ref={progressBarRef}
          className="w-full h-2 bg-gray-200 rounded-full cursor-pointer relative"
          onClick={handleProgressClick}
        >
          <div
            className="h-full bg-blue-500 rounded-full transition-all duration-200"
            style={{ width: `${progressPercentage}%` }}
          />
          <div
            className="absolute top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-600 rounded-full shadow-md cursor-pointer"
            style={{ left: `calc(${progressPercentage}% - 8px)` }}
            onMouseDown={handleHandleMouseDown}
            onTouchStart={handleHandleTouchStart}
          />
        </div>
      </div>

      {/* Main Controls */}
      <div className="flex items-center justify-center gap-4">
        <button
          onClick={handleRestart}
          className="flex flex-col items-center gap-1 text-[#a0a7b4] hover:text-[#1d1d1d] transition-colors"
          title="Restart"
        >
          <RotateCcw className="w-5 h-5" />
          <span className="text-xs">Restart</span>
        </button>

        <button
          onClick={handleJumpToStart}
          className="flex flex-col items-center gap-1 text-[#a0a7b4] hover:text-[#1d1d1d] transition-colors"
          title="Jump to Start"
        >
          <Rewind className="w-5 h-5" />
          <span className="text-xs">Start</span>
        </button>

        <button
          onClick={handleStepBack}
          className="flex flex-col items-center gap-1 text-[#a0a7b4] hover:text-[#1d1d1d] transition-colors"
          title="Previous Step"
        >
          <SkipBack className="w-5 h-5" />
          <span className="text-xs">Previous</span>
        </button>

        <button
          onClick={handlePlayPause}
          className="flex flex-col items-center gap-1 text-[#1d1d1d]"
          title={isPlaying ? "Pause" : "Play"}
        >
          <div className="w-12 h-12 bg-[#f7f7f7] rounded-full flex items-center justify-center hover:bg-[#e5e7eb] transition-colors">
            {isPlaying ? (
              <Pause className="w-6 h-6" />
            ) : (
              <Play className="w-6 h-6 ml-1" />
            )}
          </div>
          <span className="text-xs font-medium">
            {isPlaying ? "Pause" : "Play"}
          </span>
        </button>

        <button
          onClick={handleStepForward}
          className="flex flex-col items-center gap-1 text-[#a0a7b4] hover:text-[#1d1d1d] transition-colors"
          title="Next Step"
        >
          <SkipForward className="w-5 h-5" />
          <span className="text-xs">Next</span>
        </button>

        <button
          onClick={handleJumpToEnd}
          className="flex flex-col items-center gap-1 text-[#a0a7b4] hover:text-[#1d1d1d] transition-colors"
          title="Jump to End"
        >
          <FastForward className="w-5 h-5" />
          <span className="text-xs">End</span>
        </button>

        <button
          onClick={handleStop}
          className="flex flex-col items-center gap-1 text-[#a0a7b4] hover:text-[#1d1d1d] transition-colors"
          title="Stop"
        >
          <Square className="w-5 h-5" />
          <span className="text-xs">Stop</span>
        </button>
      </div>

      {/* Speed and Settings Controls */}
      <div className="flex items-center justify-center gap-6 pt-2">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Speed:</span>
          <select
            value={playbackSpeed}
            onChange={(e) => handleSpeedChange(Number(e.target.value))}
            className="text-sm border border-gray-300 rounded px-2 py-1 bg-white"
          >
            {speedOptions.map((speed) => (
              <option key={speed} value={speed}>
                {speed}x
              </option>
            ))}
          </select>
        </div>

        <label className="flex items-center gap-2 cursor-pointer">
          <input
            type="checkbox"
            checked={isLooping}
            onChange={(e) => setAutoPlay(e.target.checked)}
            className="rounded"
          />
          <span className="text-sm text-gray-600">Loop</span>
        </label>

        <button
          onClick={() => setShowSettings(!showSettings)}
          className="flex items-center gap-1 text-[#a0a7b4] hover:text-[#1d1d1d] transition-colors"
          title="Settings"
        >
          <Settings className="w-4 h-4" />
          <span className="text-sm">Settings</span>
        </button>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="mx-8 p-4 bg-gray-50 rounded-lg border">
          <h4 className="text-sm font-medium mb-3">Replay Settings</h4>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Volume2 className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600 w-16">Volume:</span>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={(e) => setVolume(Number(e.target.value))}
                className="flex-1"
              />
              <span className="text-sm text-gray-500 w-8">
                {Math.round(volume * 100)}%
              </span>
            </div>

            <label className="flex items-center gap-3 cursor-pointer">
              <input
                type="checkbox"
                checked={autoPlay}
                onChange={(e) => setAutoPlay(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-gray-600">Auto-play on load</span>
            </label>

            <label className="flex items-center gap-3 cursor-pointer">
              <input
                type="checkbox"
                checked={syncCharts}
                onChange={toggleChartSync}
                className="rounded"
              />
              <span className="text-sm text-gray-600">
                Synchronize chart zoom/pan
              </span>
            </label>

            <div className="pt-3 border-t border-gray-200">
              <h5 className="text-xs font-medium text-gray-700 mb-2">
                Keyboard Shortcuts:
              </h5>
              <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                <div>
                  <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">
                    Space
                  </kbd>{" "}
                  Play/Pause
                </div>
                <div>
                  <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">
                    ←/→
                  </kbd>{" "}
                  Step
                </div>
                <div>
                  <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">
                    Home/End
                  </kbd>{" "}
                  Jump
                </div>
                <div>
                  <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">
                    Ctrl+R
                  </kbd>{" "}
                  Restart
                </div>
                <div>
                  <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">
                    Ctrl+S
                  </kbd>{" "}
                  Stop
                </div>
                <div>
                  <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">
                    Ctrl+L
                  </kbd>{" "}
                  Loop
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
