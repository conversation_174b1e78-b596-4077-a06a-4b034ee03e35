import { useMemo } from "react";
import { DUMMY_PRICE_CHART_DATA } from "../constants/chartData";

export const useChartData = (
  prices: any,
  assets: any,
  isLoadingPrices: boolean,
  isLoadingAssets: boolean,
  pricesError: any,
  assetsError: any
) => {
  // Transform API price data to chart format
  const transformedPriceData = useMemo(() => {
    const isValidPricesData =
      prices && Array.isArray(prices) && prices.length > 0;

    if (!isValidPricesData) {
      if (isLoadingPrices) {
        return [];
      }
      return [];
    }

    const transformed =
      prices?.map((item: any) => {
        const date = new Date(item.timestamp);
        const time = Math.floor(date.getTime() / 1000);

        // Validate and sanitize numeric values to prevent -Infinity errors
        const sanitizeValue = (value: any): number => {
          if (value === null || value === undefined || !isFinite(value)) {
            return 0;
          }
          return Number(value);
        };

        return {
          time: isNaN(time) ? Math.floor(Date.now() / 1000) : time,
          value: sanitizeValue(item.close),
          open: sanitizeValue(item.open),
          high: sanitizeValue(item.high),
          low: sanitizeValue(item.low),
          volume: sanitizeValue(item.volume),
          originalTimestamp: item.timestamp,
        };
      }) || [];

    // Remove duplicates by keeping the latest entry for each timestamp
    const uniqueData = transformed.reduce((acc: any[], current: any) => {
      const existingIndex = acc.findIndex((item) => item.time === current.time);
      if (existingIndex >= 0) {
        if (
          new Date(current.originalTimestamp) >
          new Date(acc[existingIndex].originalTimestamp)
        ) {
          acc[existingIndex] = current;
        }
      } else {
        acc.push(current);
      }
      return acc;
    }, []);

    return uniqueData.sort((a: any, b: any) => a.time - b.time);
  }, [prices, isLoadingPrices]);

  // Transform API asset data to chart format
  const transformedAssetData = useMemo(() => {
    const isValidAssetsData =
      assets && Array.isArray(assets) && assets.length > 0;

    if (!isValidAssetsData) {
      if (isLoadingAssets) {
        return [];
      }

      return [
        { time: "2024-06-01", agent_0: 100, agent_1: 100 },
        { time: "2024-06-02", agent_0: 102, agent_1: 98 },
        { time: "2024-06-03", agent_0: 105, agent_1: 97 },
        { time: "2024-06-04", agent_0: 103, agent_1: 99 },
        { time: "2024-06-05", agent_0: 108, agent_1: 102 },
      ];
    }

    // Create a map of all unique timestamps across all agents
    const allTimestamps = new Set<string>();
    assets.forEach((agent: any) => {
      agent.assets_over_time?.forEach((asset: any) => {
        if (asset.time) {
          const timestamp = Math.floor(new Date(asset.time).getTime() / 1000);
          allTimestamps.add(timestamp.toString());
        }
      });
    });

    // Sort timestamps
    const sortedTimestamps = Array.from(allTimestamps).sort(
      (a, b) => parseInt(a) - parseInt(b)
    );

    // Transform data to chart format using pre-calculated rebased values
    const chartData = sortedTimestamps.map((timestamp) => {
      const dataPoint: any = { time: parseInt(timestamp) };

      assets.forEach((agent: any, index: number) => {
        const assetAtTime = agent.assets_over_time?.find((asset: any) => {
          const assetTimestamp = Math.floor(
            new Date(asset.time).getTime() / 1000
          );
          return assetTimestamp.toString() === timestamp;
        });

        const agentKey = `agent_${index}`;

        if (assetAtTime) {
          // Validate and sanitize asset values to prevent -Infinity errors
          const sanitizeAssetValue = (value: any): number | null => {
            if (value === null || value === undefined) {
              return null;
            }
            const numValue = Number(value);
            if (!isFinite(numValue)) {
              return null;
            }
            return numValue;
          };

          // Use the pre-calculated rebased value from the backend
          dataPoint[agentKey] = sanitizeAssetValue(
            assetAtTime.total_assets_rebase
          );
          dataPoint[`${agentKey}_original`] = sanitizeAssetValue(
            assetAtTime.total_assets
          );
        } else {
          dataPoint[agentKey] = null;
        }
      });

      return dataPoint;
    });

    return chartData;
  }, [assets, isLoadingAssets]);

  // Use transformed API data if available, otherwise fallback to dummy data
  const chartDataSource = useMemo(() => {
    const result =
      transformedPriceData.length > 0
        ? transformedPriceData
        : DUMMY_PRICE_CHART_DATA.map((d) => ({
            time: `2024-06-${String(d.day.match(/\d+/)?.[0] || 1).padStart(
              2,
              "0"
            )}`,
            value: d.price1,
          }));

    return result;
  }, [transformedPriceData]);

  return {
    chartDataSource,
    transformedPriceData,
    transformedAssetData,
  };
};
