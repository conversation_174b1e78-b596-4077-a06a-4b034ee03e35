import { useState, useEffect } from "react";

const COLORS = [
  "#FF0000", // Red
  "#0000FF", // Blue
  "#00FF00", // Green
  "#FFFF00", // Yellow
  "#FF8000", // Orange
  "#000000", // Black
  "#808080", // Gray
  "#800080", // Purple
  "#006400", // Very Dark Green
  "#FFB6C1", // Very Light Red
];

export const useParticipantColors = (simulationId: string) => {
  const [participantColors, setParticipantColors] = useState<{
    [key: string]: string;
  }>({});

  const generateRandomColor = () => {
    return COLORS[Math.floor(Math.random() * COLORS.length)];
  };

  const getParticipantColor = (
    participantId: string,
    participantName: string
  ) => {
    const storageKey = `olympus_participant_color_${simulationId}_${participantId}`;

    // Check if color exists in state
    if (participantColors[participantId]) {
      return participantColors[participantId];
    }

    // Check if color exists in localStorage
    const savedColor = localStorage.getItem(storageKey);
    if (savedColor) {
      setParticipantColors((prev) => ({
        ...prev,
        [participantId]: savedColor,
      }));
      return savedColor;
    }

    // Generate new color
    const newColor = generateRandomColor();
    localStorage.setItem(storageKey, newColor);
    setParticipantColors((prev) => ({ ...prev, [participantId]: newColor }));
    return newColor;
  };

  return {
    participantColors,
    getParticipantColor,
  };
};
