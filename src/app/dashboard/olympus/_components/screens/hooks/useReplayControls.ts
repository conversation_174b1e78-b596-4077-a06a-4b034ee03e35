import { useState, useEffect, useRef, useCallback } from "react";

interface UseReplayControlsProps {
  chartDataSource: any[];
}

export const useReplayControls = ({
  chartDataSource,
}: UseReplayControlsProps) => {
  const [replayIndex, setReplayIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [isLooping, setIsLooping] = useState(false);
  const [hasUserRestarted, setHasUserRestarted] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Calculate base interval (700ms at 1x speed)
  const playbackInterval = 700 / playbackSpeed;

  // Initialize replay when data changes - start at end to show fully played state
  useEffect(() => {
    if (chartDataSource.length > 0) {
      if (!isPlaying && !hasUserRestarted) {
        // Set to end of data to show fully played state on load
        setReplayIndex(chartDataSource.length);
      } else if (replayIndex > chartDataSource.length) {
        setReplayIndex(chartDataSource.length);
        setIsPlaying(false);
      }
    }
  }, [chartDataSource.length, hasUserRestarted, isPlaying, replayIndex]);

  // Additional effect to ensure we're at the end when all data is loaded
  useEffect(() => {
    if (
      chartDataSource.length > 0 &&
      !isPlaying &&
      !hasUserRestarted &&
      replayIndex === 0
    ) {
      // Set to end of data to show fully played state on load
      setReplayIndex(chartDataSource.length);
    }
  }, [chartDataSource.length, isPlaying, replayIndex, hasUserRestarted]);

  // Auto-play functionality
  useEffect(() => {
    if (chartDataSource.length > 0 && !isPlaying && replayIndex === 1) {
      // Auto-play logic can be added here if needed
    }
  }, [chartDataSource.length, isPlaying, replayIndex]);

  // Replay interval management
  useEffect(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    if (isPlaying && chartDataSource.length > 0) {
      intervalRef.current = setInterval(() => {
        setReplayIndex((idx) => {
          if (idx < chartDataSource.length) {
            return idx + 1;
          } else {
            if (isLooping) {
              return 1;
            } else {
              setIsPlaying(false);
              return idx;
            }
          }
        });
      }, playbackInterval);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isPlaying, chartDataSource.length, playbackInterval, isLooping]);

  // Control functions
  const handleStepBack = useCallback(
    () => setReplayIndex((idx) => Math.max(1, idx - 1)),
    []
  );

  const handleStepForward = useCallback(
    () => setReplayIndex((idx) => Math.min(chartDataSource.length, idx + 1)),
    [chartDataSource.length]
  );

  const handlePlayPause = useCallback(() => {
    setIsPlaying((p) => !p);
  }, []);

  const handleRestart = useCallback(() => {
    setReplayIndex(1);
    setIsPlaying(false);
    setHasUserRestarted(true);
  }, []);

  const handleStop = useCallback(() => {
    setIsPlaying(false);
    setReplayIndex(1);
    setHasUserRestarted(true);
  }, []);

  const handleSpeedChange = useCallback((speed: number) => {
    setPlaybackSpeed(speed);
  }, []);

  const handleJumpToStart = useCallback(() => {
    setReplayIndex(1);
    setHasUserRestarted(true);
  }, []);

  const handleJumpToEnd = useCallback(() => {
    setReplayIndex(chartDataSource.length);
    setIsPlaying(false);
    setHasUserRestarted(false);
  }, [chartDataSource.length]);

  const handleProgressClick = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      const rect = event.currentTarget.getBoundingClientRect();
      const clickX = event.clientX - rect.left;
      const percentage = clickX / rect.width;
      const newIndex = Math.max(
        1,
        Math.min(
          chartDataSource.length,
          Math.floor(percentage * chartDataSource.length)
        )
      );
      setReplayIndex(newIndex);
    },
    [chartDataSource.length]
  );

  const handleDrag = useCallback(
    (
      clientX: number,
      progressBarRef: React.RefObject<HTMLDivElement | null>
    ) => {
      if (!progressBarRef.current) return;
      const rect = progressBarRef.current.getBoundingClientRect();
      const x = clientX - rect.left;
      const percentage = x / rect.width;
      const newIndex = Math.max(
        1,
        Math.min(
          chartDataSource.length,
          Math.floor(percentage * chartDataSource.length)
        )
      );
      setReplayIndex(newIndex);
    },
    [chartDataSource.length]
  );

  const handleHandleMouseDown = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      const progressBarRef = e.currentTarget.parentElement?.parentElement;
      if (!progressBarRef) return;

      const handleMouseMove = (e: MouseEvent) =>
        handleDrag(e.clientX, { current: progressBarRef as HTMLDivElement });
      const handleMouseUp = () => {
        window.removeEventListener("mousemove", handleMouseMove);
        window.removeEventListener("mouseup", handleMouseUp);
      };

      handleDrag(e.clientX, { current: progressBarRef as HTMLDivElement });
      window.addEventListener("mousemove", handleMouseMove);
      window.addEventListener("mouseup", handleMouseUp);
    },
    [handleDrag]
  );

  const handleHandleTouchStart = useCallback(
    (e: React.TouchEvent<HTMLDivElement>) => {
      const progressBarRef = e.currentTarget.parentElement?.parentElement;
      if (!progressBarRef) return;

      const handleTouchMove = (e: TouchEvent) =>
        handleDrag(e.touches[0].clientX, {
          current: progressBarRef as HTMLDivElement,
        });
      const handleTouchEnd = () => {
        window.removeEventListener("touchmove", handleTouchMove);
        window.removeEventListener("touchend", handleTouchEnd);
      };

      handleDrag(e.touches[0].clientX, {
        current: progressBarRef as HTMLDivElement,
      });
      window.addEventListener("touchmove", handleTouchMove);
      window.addEventListener("touchend", handleTouchEnd);
    },
    [handleDrag]
  );

  return {
    replayIndex,
    isPlaying,
    playbackSpeed,
    isLooping,
    hasUserRestarted,
    handlePlayPause,
    handleRestart,
    handleStepBack,
    handleStepForward,
    handleJumpToStart,
    handleJumpToEnd,
    handleStop,
    handleSpeedChange,
    handleProgressClick,
    handleDrag,
    handleHandleMouseDown,
    handleHandleTouchStart,
  };
};
