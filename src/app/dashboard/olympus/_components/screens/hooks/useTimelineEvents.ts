import { useMemo, useCallback } from "react";

export const useTimelineEvents = (
  events: any,
  chartDataSource: any[],
  replayIndex: number,
  isLoadingEvent: boolean,
  eventsError: any
) => {
  // Transform events data for activity section
  const activityItems = useMemo(() => {
    const isValidEventsData =
      events && events.event_timeline && Array.isArray(events.event_timeline);

    if (!isValidEventsData) {
      if (isLoadingEvent) {
        return [];
      }

      // Enhanced fallback dummy data with timestamps
      const now = Date.now();
      const oneHour = 60 * 60 * 1000;

      return [
        {
          id: 1,
          title: "Market Open",
          description: "Trading session begins",
          time: new Date(now - 4 * oneHour).toLocaleDateString(),
          isActive: false,
          originalEvent: {
            timestamp: new Date(now - 4 * oneHour).toISOString(),
            title: "Market Open",
            description: "Trading session begins",
          },
        },
        {
          id: 2,
          title: "High Volume Alert",
          description: "Unusual trading volume detected",
          time: new Date(now - 3 * oneHour).toLocaleDateString(),
          isActive: false,
          originalEvent: {
            timestamp: new Date(now - 3 * oneHour).toISOString(),
            title: "High Volume Alert",
            description: "Unusual trading volume detected",
          },
        },
        {
          id: 3,
          title: "Price Movement",
          description: "Significant price change observed",
          time: new Date(now - 2 * oneHour).toLocaleDateString(),
          isActive: false,
          originalEvent: {
            timestamp: new Date(now - 2 * oneHour).toISOString(),
            title: "Price Movement",
            description: "Significant price change observed",
          },
        },
        {
          id: 4,
          title: "Market Activity",
          description: "Normal trading activity observed",
          time: new Date(now - oneHour).toLocaleDateString(),
          isActive: false,
          originalEvent: {
            timestamp: new Date(now - oneHour).toISOString(),
            title: "Market Activity",
            description: "Normal trading activity observed",
          },
        },
        {
          id: 5,
          title: "Market Close",
          description: "Trading session ends",
          time: new Date(now).toLocaleDateString(),
          isActive: true,
          originalEvent: {
            timestamp: new Date(now).toISOString(),
            title: "Market Close",
            description: "Trading session ends",
          },
        },
      ];
    }

    // Transform API events data with additional validation
    return events.event_timeline.map((event: any, index: number) => ({
      id: index + 1,
      title: event.event || event.event_type || "Market Event",
      description: event.details || event.message || "Event details",
      time: event.timestamp
        ? new Date(event.timestamp).toLocaleDateString()
        : `Event ${index + 1}`,
      isActive: index === events.event_timeline.length - 1,
      originalEvent: event,
    }));
  }, [events, isLoadingEvent]);

  // Calculate which events have been reached based on current replay progress
  const timelineEventsWithProgress = useMemo(() => {
    if (!activityItems.length) {
      return [];
    }

    if (!chartDataSource.length) {
      return activityItems.map((event: any, index: number) => ({
        ...event,
        isReached: index <= replayIndex / 20,
        isPassed: index < replayIndex / 20 - 1,
        isCurrent: index === Math.floor(replayIndex / 20),
        progress: index <= replayIndex / 20 ? 100 : 0,
        priceChartIndex: index * 20,
      }));
    }

    return activityItems.map((event: any, index: number) => {
      // Convert event time to timestamp for comparison
      let eventTimestamp = 0;

      if (event.originalEvent?.timestamp) {
        eventTimestamp = Math.floor(
          new Date(event.originalEvent.timestamp).getTime() / 1000
        );
      } else if (event.originalEvent?.time) {
        eventTimestamp = Math.floor(
          new Date(event.originalEvent.time).getTime() / 1000
        );
      }

      // Find the closest price chart data point to this event's timestamp
      let closestIndex = -1;
      let smallestTimeDiff = Infinity;

      chartDataSource.forEach((pricePoint: any, priceIndex: number) => {
        const priceTimestamp = pricePoint.time;
        const timeDiff = Math.abs(priceTimestamp - eventTimestamp);

        if (timeDiff < smallestTimeDiff) {
          smallestTimeDiff = timeDiff;
          closestIndex = priceIndex;
        }
      });

      // Define event states based on replay progress
      const isReached = closestIndex !== -1 && replayIndex >= closestIndex;

      // Find the next event's closest index to determine when current event should end
      const nextEvent = activityItems[index + 1];
      let nextEventClosestIndex = -1;

      if (nextEvent) {
        let nextEventTimestamp = 0;
        if (nextEvent.originalEvent?.timestamp) {
          nextEventTimestamp = Math.floor(
            new Date(nextEvent.originalEvent.timestamp).getTime() / 1000
          );
        } else if (nextEvent.originalEvent?.time) {
          nextEventTimestamp = Math.floor(
            new Date(nextEvent.originalEvent.time).getTime() / 1000
          );
        }

        let nextEventSmallestTimeDiff = Infinity;
        chartDataSource.forEach((pricePoint: any, priceIndex: number) => {
          const priceTimestamp = pricePoint.time;
          const timeDiff = Math.abs(priceTimestamp - nextEventTimestamp);
          if (timeDiff < nextEventSmallestTimeDiff) {
            nextEventSmallestTimeDiff = timeDiff;
            nextEventClosestIndex = priceIndex;
          }
        });
      }

      // Event is "current" from when it's reached until the next event is reached
      const isCurrent =
        closestIndex !== -1 &&
        replayIndex >= closestIndex &&
        (nextEventClosestIndex === -1 || replayIndex < nextEventClosestIndex);

      // Event is "passed" after the next event is reached
      const isPassed =
        closestIndex !== -1 &&
        nextEventClosestIndex !== -1 &&
        replayIndex >= nextEventClosestIndex;

      const progress = isReached ? 100 : 0;

      return {
        ...event,
        isReached,
        isPassed,
        isCurrent,
        progress,
        eventTimestamp,
        priceChartIndex: closestIndex,
        timeDiffMinutes: smallestTimeDiff / 60,
      };
    });
  }, [activityItems, chartDataSource, replayIndex]);

  // Handle timeline event click to jump to event and sync charts
  const handleTimelineEventClick = useCallback((event: any) => {
    // This will be implemented in the parent component
    console.log("Timeline event clicked:", event);
  }, []);

  // Handle timeline scrubbing (clicking on the timeline line)
  const handleTimelineScrub = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      // This will be implemented in the parent component
      console.log("Timeline scrubbed:", e);
    },
    []
  );

  return {
    timelineEventsWithProgress,
    handleTimelineEventClick,
    handleTimelineScrub,
  };
};
