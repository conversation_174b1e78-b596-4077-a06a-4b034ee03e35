import { Card, CardContent } from "@/components/ui/card";

interface ParticipantCardSkeletonProps {
  index?: number;
}

const ParticipantCardSkeleton = ({
  index = 0,
}: ParticipantCardSkeletonProps) => {
  // Vary the skeleton widths slightly to make it look more natural
  const nameWidths = ["w-3/4", "w-2/3", "w-4/5", "w-3/5"];
  const nameWidth = nameWidths[index % nameWidths.length];

  // Add staggered animation delay
  const animationDelay = `${index * 100}ms`;

  return (
    <Card
      className="border-none shadow-none bg-[#F9F9F9] animate-fadeIn"
      style={{ animationDelay }}
    >
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          {/* Name skeleton */}
          <div
            className={`h-6 bg-gray-200 rounded ${nameWidth} animate-pulse`}
          ></div>
          {/* Badge skeleton */}
          <div className="h-5 bg-gray-200 rounded-full w-16 animate-pulse"></div>
        </div>

        <div className="space-y-3">
          {/* Details label skeleton */}
          <div className="text-sm">
            <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
          </div>

          {/* Risk and Type row */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="h-3 bg-gray-200 rounded w-8 mb-1 animate-pulse"></div>
              <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
            </div>
            <div>
              <div className="h-3 bg-gray-200 rounded w-8 mb-1 animate-pulse"></div>
              <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
            </div>
          </div>

          {/* Position row */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="h-3 bg-gray-200 rounded w-12 mb-1 animate-pulse"></div>
              <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
            </div>
            <div></div>
          </div>

          {/* Uses row */}
          <div className="text-sm">
            <div className="h-3 bg-gray-200 rounded w-10 mb-1 animate-pulse"></div>
            <div className="h-3 bg-gray-200 rounded w-20 animate-pulse"></div>
          </div>
        </div>

        {/* Action buttons skeleton */}
        <div className="flex items-center gap-2 mt-6 pt-4">
          <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ParticipantCardSkeleton;
