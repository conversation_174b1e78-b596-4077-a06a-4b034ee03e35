import React from "react";
import HeaderOlympus from "./_components/headerOlympus";

interface OlympusDashboardLayoutProps {
  children: React.ReactNode;
}

const OlympusDashboardLayout = ({ children }: OlympusDashboardLayoutProps) => {
  return (
    <div className=" ">
      <main className="min-h-screen">
        {/* <HeaderModels
          avatarUrl="/orion_thumbnail.svg"
          userName="Orion"
          menuItems={menuItems}
        /> */}
        <HeaderOlympus />
        <div className="h-full">{children}</div>
      </main>
    </div>
  );
};

export default OlympusDashboardLayout;
