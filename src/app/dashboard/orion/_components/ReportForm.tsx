// components/ReportForm.tsx

//@ts-nocheck
"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetFooter,
} from "@/components/ui/sheet";
import { Label } from "@/components/ui/label";
import LoadingModal from "./loadingModal";
import { CircleX, Info, Paperclip, Search } from "lucide-react";
import Image from "next/image";
// import { useSubmitQuery } from '@/lib/api';
import FadeLoader from "react-spinners/FadeLoader";
import <PERSON><PERSON>oader from "react-spinners/MoonLoader";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import UploadSheet from "./UploadSheet";
import {
  api,
  orionAPI,
  useGetQuery,
  useGetQueryOrionBackend,
  useSubmitQuery,
} from "@/services/api_hooks";
import useLocalStorage from "@/hooks/use-local-storage";
import UploadTickers from "./UploadTickers";
import axios from "axios";
import TelegramIDSaveModal from "./TelegramIDSaveModal";
import { useQueryClient } from "@tanstack/react-query";
import { orion_base_url } from "@/config/baseUrl";
import { useTranslations } from "next-intl";
import { useProfile } from "@/hooks/useProfile";

interface DocumentWithTickers {
  file: File;
  tickers: string[];
}

// API base URL
const API_BASE_URL = orion_base_url;

const ReportForm = () => {
  const t = useTranslations("DashboardOrion.Form");

  const router = useRouter();
  // Sheet and dialog states
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isTickerSheetOpen, setIsTickerSheetOpen] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [dontAskAgain, setDontAskAgain] = useState(false);

  // Ticker states
  const [searchValue, setSearchValue] = useState("");
  const [foundTickers, setFoundTickers] = useState<
    { symbol: string; name?: string; RIC: string }[]
  >([]);
  const [selectedTickers, setSelectedTickers] = useState<
    { symbol: string; name?: string; RIC: string }[]
  >([]);
  const [notFoundTickers, setNotFoundTickers] = useState<string[]>([]);
  const [showNotRecognized, setShowNotRecognized] = useState(false);
  const [hasParsedInput, setHasParsedInput] = useState(false);

  // Download options states
  const [downloadOption, setDownloadOption] = useState<string>("");
  const [userCredentials, setuserCredentials] = useLocalStorage("authCred", {});
  const [emailAddress, setEmailAddress] = useState<string>("");
  const [telegramId, setTelegramId] = useState<string>("");
  const { profile } = useProfile()

  const [tickerValidationError, setTickerValidationError] =
    useState<string>("");

  // Report generation states
  const [jobId, setJobId] = useState<string | null>(null);
  const [status, setStatus] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  const [loadingModalOpen, setLoadingModalOpen] = useState(false);
  const [loadingStatus, setLoadingStatus] = useState<
    "generating" | "downloading" | "idle"
  >("idle");
  const [isDownloading, setIsDownloading] = useState(false);

  const [apiResults, setApiResults] = useState(null);
  const [isSearching, setIsSearching] = useState(false);

  const [sendSecFiling, setSendSecFiling] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  const [tickerError, setTickerError] = useState<string | null>(null);
  const [downloadOptionError, setDownloadOptionError] = useState<string | null>(
    null
  );
  const [emailError, setEmailError] = useState<string | null>(null);
  const [telegramError, setTelegramError] = useState<string | null>(null);

  const [isTelegramModalOpen, setIsTelegramModalOpen] = useState(false);
  const [storedTelegramId, setStoredTelegramId] = useState("");

  const [emailSent, setEmailSent] = useState(false);
  const [tempUploadedFiles, setTempUploadedFiles] = useState<File[]>([]);
  const [pendingJobs, setPendingJobs] = useLocalStorage(
    "pendingJobs",
    JSON.stringify([])
  );

  const [isUploadSheetOpen, setIsUploadSheetOpen] = useState(false);
  const [uploadedDocuments, setUploadedDocuments] = useState<
    DocumentWithTickers[]
  >([]);
  const [secFilingLocalStorage, setSecFilingLocalStorage] = useLocalStorage(
    "secFiling",
    ""
  );
  const [emailToUse, setEmailToUse] = useLocalStorage("reportEmail", "");
  const [dontAskAgainPref, setDontAskAgainPref] = useLocalStorage(
    "dontAskAgainPref",
    false
  );

  const [uploadedEarningsDocuments, setUploadedEarningsDocuments] = useState<
    DocumentWithTickers[]
  >([]);
  const [uploadedOtherDocuments, setUploadedOtherDocuments] = useState<
    DocumentWithTickers[]
  >([]);

  const [isEarningsSheetOpen, setIsEarningsSheetOpen] = useState(false);
  const [isOtherSheetOpen, setIsOtherSheetOpen] = useState(false);

  const [tempEarningsFiles, setTempEarningsFiles] = useState<File[]>([]);
  const [tempOtherFiles, setTempOtherFiles] = useState<File[]>([]);

  const earningsFileInputRef = useRef<HTMLInputElement>(null);
  const otherFileInputRef = useRef<HTMLInputElement>(null);

  const queryClient = useQueryClient();

  const fileInputRef = useRef<HTMLInputElement>(null);

  const [userCred, setUserCred] = useState<any>(null);

  const { data: apikey, isLoading: isModelsLoading } = useGetQueryOrionBackend(
    "/report/orion-key",
    ["get-apikey"],
    {
      onError() {
        toast.error(t("couldNotLoadModels"));
      },
    }
  );

  const {
    data: telegramData,
    isLoading: isTelegramLoading,
    error: errorData,
  } = useGetQuery("/api/user/telegramId", ["get-telegram-id"], {
    refetchOnWindowFocus: true,
    onSuccess: (data) => {
      if (data?.telegram_id) {
        setStoredTelegramId(data.telegram_id);
        console.log(data, "dww");
        // Set the telegram input field with the existing ID if available
        setTelegramId(data.telegram_id);
      }
    },
    onError: () => {
      console.error(t("failedToFetchTelegramId"));
    },
  });

  const { mutateAsync: saveTelegramIdMutation, isPending: isSavingTelegramId } =
    useSubmitQuery("/api/user/save-telegram-id", "POST", {
      onSuccess(response: any) {
        console.log(response, t("telegramIdSaved"));

        // Update the stored ID
        // setStoredTelegramId(telegramId);
        queryClient.invalidateQueries({
          queryKey: ["get-telegram-id"],
        });
        // Close the modal and continue with report generation
        setIsTelegramModalOpen(false);
        startAnalysis();

        toast.success(response?.message || t("telegramIdSaved"), {
          position: "top-right",
          className: "p-4",
        });
      },
      onError(error: any) {
        toast.error(
          error.response?.data?.error || t("failedToSaveTelegramId"),
          {
            position: "top-right",
            className: "p-4",
          }
        );
        console.error(t("errorSavingTelegramId"), error);

        // Continue with report generation option remains commented out
        // setIsTelegramModalOpen(false);
      },
    });

  const saveTelegramId = async () => {
    try {
      await saveTelegramIdMutation({
        telegram_id: telegramId,
      });
    } catch (error) {
      // Error handling is done in the onError callback of useSubmitQuery
      console.error(
        t("errorInSaveTelegramIdFunction", { error: String(error) })
      );
    }
  };

  const shouldShowTelegramModal = () => {
    // Show when there's no stored Telegram ID but user entered one
    if (!telegramId) {
      return true;
    }

    // Show when the entered ID is different from the stored one
    if (telegramId !== telegramData?.telegram_id) {
      return true;
    }

    return false;
  };

  // handler for first click:
  const handleUploadEarningsClick = () => {
    if (!selectedTickers.length) {
      toast.error(t("pleaseSelectTickersFirst"), {
        position: "top-right",
        className: "p-4",
      });
      return;
    }
    uploadedEarningsDocuments.length
      ? setIsEarningsSheetOpen(true)
      : earningsFileInputRef.current?.click();
  };

  const handleUploadOtherClick = () => {
    if (!selectedTickers.length) {
      toast.error(t("pleaseSelectTickersFirst"), {
        position: "top-right",
        className: "p-4",
      });
      return;
    }
    uploadedOtherDocuments.length
      ? setIsOtherSheetOpen(true)
      : otherFileInputRef.current?.click();
  };

  const handleMainFormEarningsUpload = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = e.target.files && Array.from(e.target.files);
    if (!files) return;
    setTempEarningsFiles(files);
    setIsEarningsSheetOpen(true);
    earningsFileInputRef.current!.value = "";
  };

  const handleMainFormOtherUpload = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = e.target.files && Array.from(e.target.files);
    if (!files) return;
    setTempOtherFiles(files);
    setIsOtherSheetOpen(true);
    otherFileInputRef.current!.value = "";
  };

  const continueWithoutSaving = () => {
    setIsTelegramModalOpen(false);
    startAnalysis();
  };

  useEffect(() => {
    try {
      if (profile) {
        const email = profile.email || "";
        setEmailAddress(email);
      }

      // Load the "don't ask again" preference from local storage
      const storedDontAskAgain = localStorage.getItem("dontAskAgainPref");
      if (storedDontAskAgain) {
        setDontAskAgain(JSON.parse(storedDontAskAgain));
      }
    } catch (error) {
      console.error("Error loading data from local storage:", error);
    }
  }, []);

  const handleSecFilingChange = (check) => {
    setSendSecFiling(check);
    setSecFilingLocalStorage(check); // Save to local storage
  };

  const getCurrentDate = () => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  };

  // Status check interval reference
  const statusCheckInterval = useRef<NodeJS.Timeout | null>(null);

  // Parse input and identify found/not found tickers
  const parseInput = (input) => {
    if (!input.trim()) {
      setFoundTickers([]);
      setNotFoundTickers([]);
      setHasParsedInput(false);
      return;
    }

    // Split by comma or space and clean up
    const inputTickers = input
      .split(/[\s,]+/)
      .map((ticker) => ticker.trim())
      .filter((ticker) => ticker.length > 0)
      .map((ticker) => ticker.toUpperCase());

    setFoundTickers(found);
    setNotFoundTickers(notFound);
    setHasParsedInput(true);

    const cleanedInput = input.trim();
    setSearchValue(cleanedInput);
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchValue(value);

    // If the input is completely cleared, reset states
    if (!value.trim()) {
      setTickerValidationError("");
      return;
    }

    // Count tickers (split by common separators and filter empty values)
    const tickers = value
      .split(/[,\s\n\r]+/)
      .map((ticker) => ticker.trim())
      .filter((ticker) => ticker.length > 0);

    if (tickers.length > 10) {
      setTickerValidationError(t("tickerLimitExceeded", { limit: 10 }));
    } else {
      setTickerValidationError("");
    }
  };

  const handleUploadDocumentClick = () => {
    // Check if tickers are selected first
    if (selectedTickers.length === 0) {
      toast.error(t("pleaseSelectTickersFirst"), {
        position: "top-right",
        className: "p-4",
      });
      return;
    }

    // If user already has documents, open the sheet directly
    if (uploadedDocuments.length > 0) {
      setIsUploadSheetOpen(true);
    } else {
      // Otherwise, open file selector first
      fileInputRef.current?.click();
    }
  };

  const handleMainFormFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Double-check that tickers are selected
    if (selectedTickers.length === 0) {
      toast.error(t("pleaseSelectTickersFirst"), {
        position: "top-right",
        className: "p-4",
      });
      return;
    }

    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);

      // Instead of creating DocumentWithTickers here, just pass the files
      // and selected ticker symbols to the UploadSheet component

      // Open the upload sheet without setting documents - the UploadSheet will handle it
      setIsUploadSheetOpen(true);

      // Store the files temporarily to pass to UploadSheet
      setTempUploadedFiles(newFiles);
    }

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Handle saving documents from the UploadSheet component
  const handleSaveDocuments = (documents: DocumentWithTickers[]) => {
    setUploadedDocuments(documents);
  };

  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + " B";
    else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(0) + " KB";
    else return (bytes / (1024 * 1024)).toFixed(0) + " MB";
  };

  // Handle ticker removal from found list
  const removeTicker = (tickerToRemove) => {
    const newFoundTickers = foundTickers.filter(
      (t) => t.symbol !== tickerToRemove.symbol
    );
    setFoundTickers(newFoundTickers);

    // Remove from search value as well
    const terms = searchValue
      .split(/[\s,]+/)
      .filter(
        (term) =>
          term.trim().toUpperCase() !== tickerToRemove.symbol.toUpperCase()
      );
    setSearchValue(terms.join(", "));
  };

  // Handle selection completion
  const handleFinish = () => {
    setSelectedTickers(foundTickers);
    setTickerError(null); // Clear ticker error when tickers are selected
    setIsTickerSheetOpen(false);
  };

  // Handle removal from selected tickers
  const removeSelectedTicker = (tickerToRemove) => {
    setSelectedTickers((prev) =>
      prev.filter((t) => t.symbol !== tickerToRemove.symbol)
    );

    const newFoundTickers = foundTickers.filter(
      (t) => t.symbol !== tickerToRemove.symbol
    );
    setFoundTickers(newFoundTickers);

    // Remove from search value as well
    const terms = searchValue
      .split(/[\s,]+/)
      .filter(
        (term) =>
          term.trim().toUpperCase() !== tickerToRemove.symbol.toUpperCase()
      );
    setSearchValue(terms.join(", "));
  };

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    setUploadedFiles(files);
  };

  // Format not found message
  const formatNotFoundMessage = () => {
    if (notFoundTickers.length === 0) return "";

    if (notFoundTickers.length <= 3) {
      return `${notFoundTickers.join(", ")} not found`;
    }

    return `${notFoundTickers.slice(0, 3).join(", ")} and ${
      notFoundTickers.length - 3
    } others not found`;
  };

  // Handle download option selection
  const handleDownloadOptionChange = (value: string) => {
    setDownloadOption(value);
    setDownloadOptionError(null); // Clear download option error when option is selected

    // Reset fields when changing option
    setEmailAddress("");
    setTelegramId("");
    setEmailAddress(userCred?.user?.user_metadata?.email || "");
    setTelegramId(telegramData?.telegram_id);
  };

  const handleEmailChange = (e) => {
    const value = e.target.value;
    setEmailAddress(value);

    if (!value) {
      setEmailError(t("pleaseEnterEmail"));
    } else if (!/\S+@\S+\.\S+/.test(value)) {
      setEmailError(t("pleaseEnterValidEmail"));
    } else {
      setEmailError(null);
    }
  };

  const handleTelegramChange = (e) => {
    setTelegramId(e.target.value);
    if (e.target.value) {
      setTelegramError(null); // Clear telegram error when input has value
    }
  };

  // Search button handler

  const handleSearch = async () => {
    if (!searchValue.trim()) {
      setFoundTickers([]);
      setNotFoundTickers([]);
      setHasParsedInput(false);
      return;
    }

    setIsSearching(true);
    setError(null);

    try {
      const tickersInput = searchValue.trim();

      // Making POST request using axios
      const response = await orionAPI.post(`/validate/tickers`, {
        tickers: tickersInput,
      });

      // Handling the data from the response
      const data = response.data;

      // Merge new valid tickers with existing ones, avoiding duplicates
      const newFoundTickers = [...foundTickers];
      data.valid.forEach((newTicker) => {
        // Check if this ticker symbol already exists in our list
        const tickerExists = newFoundTickers.some(
          (existingTicker) => existingTicker.symbol === newTicker.symbol
        );

        // Only add if it doesn't already exist
        if (!tickerExists) {
          newFoundTickers.push(newTicker);
        }
      });

      setFoundTickers(newFoundTickers);

      // Handle invalid tickers - avoid duplicates
      const upperCaseInvalid = data.invalid.map((item) => item.toUpperCase());
      setNotFoundTickers((prev) => {
        const combined = [...prev];

        // Only add tickers that aren't already in the list
        upperCaseInvalid.forEach((invalidTicker) => {
          if (!combined.includes(invalidTicker)) {
            combined.push(invalidTicker);
          }
        });

        return combined;
      });

      setHasParsedInput(true);

      // Clear the input field after successful search
      setSearchValue("");
    } catch (error) {
      console.log(error);
      toast.error(t("searchError", { error: error?.response?.data?.error }), {
        position: "top-right",
        className: "p-4",
      });
      // console.error("Validation API error:", error);
      // setError(
      //   `Search error: ${error instanceof Error ? error.message : "Unknown error"}`
      // );
    } finally {
      setIsSearching(false);
    }
  };

  const processApiResults = (data) => {
    const found = [];
    const notFound = [];

    if (data && data.results) {
      data.results.forEach((item) => {
        if (item.results && item.results.length > 0) {
          // Extract the ticker symbol or RIC as needed
          item.results.forEach((result) => {
            found.push(result.RIC || result.symbol);
          });
        } else {
          // This query returned no results
          notFound.push(item.query.toUpperCase());
        }
      });
    }

    setFoundTickers(found);
    setNotFoundTickers(notFound);
    setHasParsedInput(true);
  };

  // Handle generate report button click
  const handleGenerateClick = () => {
    // Reset all field errors
    setTickerError(null);
    setDownloadOptionError(null);
    setEmailError(null);
    setTelegramError(null);

    // Validate form
    let hasError = false;

    if (selectedTickers.length === 0) {
      setTickerError(t("pleaseSelectAtLeastOneTicker"));
      hasError = true;
    }

    if (!downloadOption) {
      setDownloadOptionError(t("pleaseSelectDownloadOption"));
      hasError = true;
    }

    if (downloadOption === "email" && !emailAddress) {
      setEmailError(t("pleaseEnterEmail"));
      hasError = true;
    }

    if (downloadOption === "telegram" && !telegramId) {
      setTelegramError(t("pleaseEnterTelegramId"));
      hasError = true;
    }

    if (hasError) {
      return;
    }

    // If user has chosen to not ask again or it's not a Telegram option, generate report directly
    if (dontAskAgain) {
      startAnalysis();
    } else {
      // Show confirmation modal
      setIsConfirmModalOpen(true);
    }
  };

  const handleDontAskAgainChange = (checked) => {
    setDontAskAgain(checked);
    setDontAskAgainPref(checked); // This will also save to local storage via the hook
  };

  // Start analysis API call
  const startAnalysis = async () => {
    try {
      setIsGenerating(true);
      setError(null);
      setSuccess(null);

      //Extract RICs from selected tickers
      const rics = selectedTickers.map((ticker) => ticker.RIC);

      let response;
      let data;

      if (
        uploadedDocuments.length > 0 ||
        uploadedEarningsDocuments.length > 0 ||
        uploadedOtherDocuments.length > 0
      ) {
        // ─── Build FormData for /analyze/with-uploads ───
        const formData = new FormData();

        //Append tickers as JSON array
        const tickersArray = rics.slice(0, 4); // limit to first 4 if needed
        formData.append("tickers", JSON.stringify(tickersArray));

        //Append every file under the same 'files' key:
        // Create a single map for file_tickers metadata
        const fileTickersMap: Record<
          string,
          { type: string; tickers: string[] }
        > = {};

        // ─ Upload "uploadedDocuments" (type: reports)
        uploadedDocuments.forEach((doc) => {
          formData.append("files", doc.file);
          fileTickersMap[doc.file.name] = {
            type: "reports",
            tickers: doc.tickers,
          };
        });

        // ─ Upload "uploadedEarningsDocuments" (type: company_docs)
        uploadedEarningsDocuments.forEach((doc) => {
          formData.append("files", doc.file);
          fileTickersMap[doc.file.name] = {
            type: "company_docs",
            tickers: doc.tickers,
          };
        });

        // ─ Upload "uploadedOtherDocuments" (type: other_docs)
        uploadedOtherDocuments.forEach((doc) => {
          formData.append("files", doc.file);
          fileTickersMap[doc.file.name] = {
            type: "other_docs",
            tickers: doc.tickers,
          };
        });

        //Append the file_tickers JSON string
        formData.append("file_tickers", JSON.stringify(fileTickersMap));


        //Build delivery_info payload
        const deliveryInfo = {
          include_company_docs: sendSecFiling,
        };

        // Only add email if email delivery option is selected
        if (downloadOption === "email") {
          deliveryInfo.email = emailAddress;
        }

        // Only add telegram_chat_id if telegram delivery option is selected
        if (downloadOption === "telegram") {
          deliveryInfo.telegram_chat_id = telegramId;
        }

        formData.append("delivery_info", JSON.stringify(deliveryInfo));

        // 7️⃣ Send multipart request
        response = await axios.post(
          `${API_BASE_URL}/analyze/with-uploads`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${apikey.api_key}`,
            },
          }
        );

        data = response.data;
      } else {
        // ─── Fallback: JSON-only request to /analyze ───
        const requestBody = {
          tickers: rics,
          date: getCurrentDate(),
          include_company_docs: sendSecFiling,
        };

        // Only add email if email delivery option is selected
        if (downloadOption === "email") {
          requestBody.email = emailAddress;
        }

        // Only add telegram_chat_id if telegram delivery option is selected
        if (downloadOption === "telegram") {
          requestBody.telegram_chat_id = telegramId;
        }

        response = await axios.post(`${API_BASE_URL}/analyze`, requestBody, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${apikey.api_key}`,
          },
        });

        data = response.data;
      }

      if (!data.job_id) {
        throw new Error(t("noJobIdReturned"));
      }

      const newJobId = data.job_id;
      setJobId(newJobId);
      setStatus({
        status: "processing",
        progress: 0,
        started_at: new Date().toISOString(),
      });
      setIsConfirmModalOpen(false);

      // Save to pending jobs (localStorage)...
      try {
        const currentPendingJobs = JSON.parse(pendingJobs || "[]");
        if (!currentPendingJobs.includes(newJobId)) {
          currentPendingJobs.push(newJobId);
          setPendingJobs(JSON.stringify(currentPendingJobs));
        }
      } catch (e) {
        setPendingJobs(JSON.stringify([newJobId]));
      }
      localStorage.setItem("currentJobId", newJobId);

      if (downloadOption === "email" && emailAddress) {
        setEmailToUse(emailAddress);
      } else {
        setEmailToUse("");
      }

      // Redirect to reports history
      router.push("/dashboard/orion/reports");
    } catch (error: any) {
      setIsGenerating(false);
      toast.error(
        error.response?.data?.detail || error.message || t("unexpectedError"),
        {
          position: "top-right",
          className: "p-4",
        }
      );
    }
  };

  useEffect(() => {
    if (selectedTickers.length > 0) {
      setTickerError(null);
    }
  }, [selectedTickers]);

  useEffect(() => {
    if (downloadOption) {
      setDownloadOptionError(null);

      // Reset related field errors when changing option
      setEmailError(null);
      setTelegramError(null);
    }
  }, [downloadOption]);

  const handleUploadedTickers = (validTickers, invalidTickers) => {
    // Merge new valid tickers with existing ones, avoiding duplicates
    const newFoundTickers = [...foundTickers];
    validTickers.forEach((newTicker) => {
      // Check if this ticker symbol already exists in our list
      const tickerExists = newFoundTickers.some(
        (existingTicker) => existingTicker.symbol === newTicker.symbol
      );

      // Only add if it doesn't already exist
      if (!tickerExists) {
        newFoundTickers.push(newTicker);
      }
    });

    setFoundTickers(newFoundTickers);
    setSelectedTickers(newFoundTickers);
    setHasParsedInput(true);

    // Add any invalid tickers to the not found list
    if (invalidTickers && invalidTickers.length > 0) {
      const upperCaseInvalid = invalidTickers.map((item) => item.toUpperCase());
      setNotFoundTickers((prev) => {
        const combined = [...prev];

        // Only add tickers that aren't already in the list
        upperCaseInvalid.forEach((invalidTicker) => {
          if (!combined.includes(invalidTicker)) {
            combined.push(invalidTicker);
          }
        });

        return combined;
      });
      setShowNotRecognized(true);
    }
  };

  return (
    <div className="max-w-[458px] mx-auto rounded-lg shadow-sm py-2 px-4 space-y-5">
      <div className="space-y-2">
        <h2 className="text-[22px] font-[600] text-[#1E1E1E]">
          {t("generateReportTitle")}
        </h2>
        <p className="text-[16px] font-[500] text-[#A7A7A7]">
          {t("generateReportDescription")}
        </p>
      </div>

      <div className="space-y-1.5">
        <div className="flex justify-between items-center">
          <Label
            htmlFor="ticker"
            className="text-[14px] font-medium text-[#1E1E1E]"
          >
            {t("tickersLabel")}{" "}
            <span className="text-sm text-gray-500">
              {t("tickersSubLabel")}
            </span>
          </Label>
          <Button
            variant="link"
            className="text-[#0359D8] p-0 h-auto text-[12px] text-sm cursor-pointer"
            onClick={() => setIsDialogOpen(true)}
          >
            {t("uploadTickers")}
          </Button>
        </div>
        <div
          className="flex items-center justify-between w-full border rounded-md px-3 py-2 text-sm cursor-pointer hover:border-gray-400 transition-colors"
          onClick={() => setIsTickerSheetOpen(true)}
        >
          {selectedTickers.length > 0 ? (
            <div className="flex flex-wrap gap-1 pr-4">
              {selectedTickers.slice(0, 4).map((ticker, index) => (
                <div
                  key={index}
                  className="flex items-center bg-gray-100 rounded px-2 py-1 text-xs"
                >
                  {ticker.symbol}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeSelectedTicker(ticker);
                    }}
                    className="ml-1 text-gray-500 hover:text-gray-700 cursor-pointer"
                    aria-label={t("removeTicker")}
                  >
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 4L4 12M4 4L12 12"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </button>
                </div>
              ))}
              {selectedTickers.length > 4 && (
                <div className="bg-gray-100 rounded px-2 py-1 text-xs">
                  +{selectedTickers.length - 4} more
                </div>
              )}
            </div>
          ) : (
            <span className="text-gray-500">
              {t("selectTickersPlaceholder")}
            </span>
          )}
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-gray-400 flex-shrink-0"
          >
            <path
              d="M6 12L10 8L6 4"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
        {tickerError && (
          <p className="text-sm text-red-500 mt-1">
            {t("pleaseSelectAtLeastOneTicker")}
          </p>
        )}
      </div>

      <div className="space-y-1.5">
        <div className="flex justify-between items-center gap-3">
          <Label className="text-sm font-medium flex justify-between w-full items-center text-[#1E1E1E]">
            <div>
              {t("uploadResearchReportsLabel")}{" "}
              <span className="text-[#7F7F81] font-medium text-[14px]">
                {t("optional")}
              </span>
            </div>

            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger asChild className="">
                  <div className="flex items-center ml-1 cursor-pointer gap-1">
                    <div className="min-w-4">
                      <Info color="#CB680C" size={18} />
                    </div>
                  </div>
                </TooltipTrigger>
                <TooltipContent
                  className="max-w-[300px] gap-2 flex flex-col p-3 text-xs text-start font-[400] leading-5 bg-white text-black"
                  side="right"
                >
                  <span>{t("researchReportsTooltip")}</span>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </Label>
          {uploadedDocuments.length > 0 && (
            <Button
              variant="link"
              className="ml-2 text-[#0359D8] p-0 h-auto text-[12px] text-sm cursor-pointer"
              onClick={() => setIsUploadSheetOpen(true)}
            >
              {t("viewUploads")}
            </Button>
          )}
        </div>

        <div className="flex flex-col items-center justify-between">
          {uploadedDocuments.length === 0 && (
            <div
              className={`border-[2px] rounded-md p-4 w-full text-center cursor-pointer transition-colors flex-grow ${
                selectedTickers.length === 0
                  ? "border-gray-200 bg-gray-100 text-gray-400"
                  : "hover:bg-gray-50"
              }`}
              onClick={handleUploadDocumentClick}
            >
              <div className="flex flex-col items-center justify-center">
                <Paperclip
                  className={
                    selectedTickers.length === 0 ? "text-gray-400" : ""
                  }
                  size={10}
                />
                <p
                  className={`text-[12px] font-medium ${
                    selectedTickers.length === 0
                      ? "text-gray-400"
                      : "text-[#0F172A]"
                  }`}
                >
                  {t("uploadDocumentButton")}
                </p>
                <p className="text-[10px] font-[400] text-[#7F7F81] mt-1">
                  {t("uploadDocumentDescription")}
                </p>
              </div>
              <input
                ref={fileInputRef}
                id="research"
                type="file"
                accept=".pdf,.doc,.docx,.txt"
                className="hidden"
                multiple
                onChange={handleMainFormFileUpload}
              />
            </div>
          )}
        </div>

        {/* Display uploaded documents summary */}
        <div className="flex items-center gap-2 mt-2">
          {uploadedDocuments.slice(0, 3).map((doc, index) => (
            <div
              key={index}
              className="w-fit bg-white border rounded-md flex items-center justify-center "
            >
              <Image
                src="/doc-icon.svg"
                alt="doc icon"
                height={50}
                width={50}
              />
            </div>
          ))}
          {uploadedDocuments.length > 3 && (
            <div className="w-fit bg-white border rounded-md flex items-center justify-center p-2">
              <div className="text-center bg-[#FAFAFA] w-10 h-10 place-content-center">
                <p className="text-xs font-medium">
                  +{uploadedDocuments.length - 3}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* ── Latest Earnings Releases ── */}
      <div className="space-y-1.5">
        <div className="flex justify-between items-center gap-3">
          <Label className="text-sm font-medium flex justify-between w-full items-center text-[#1E1E1E]">
            <div>
              {t("uploadEarningsLabel")}{" "}
              <span className="text-[#7F7F81] font-medium text-[14px]">
                {t("optional")}
              </span>
            </div>

            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger asChild className="">
                  <div className="flex items-center ml-1 cursor-pointer gap-1">
                    <div className="min-w-4">
                      <Info color="#CB680C" size={18} />
                    </div>
                  </div>
                </TooltipTrigger>
                <TooltipContent
                  className="max-w-[300px] gap-2 flex flex-col p-3 text-xs text-start font-[400] leading-5 bg-white text-black"
                  side="right"
                >
                  <span>{t("earningsTooltip")}</span>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </Label>
          {uploadedEarningsDocuments.length > 0 && (
            <Button
              variant="link"
              className="ml-2 text-[#0359D8] p-0 h-auto text-[12px] text-sm cursor-pointer"
              onClick={() => setIsEarningsSheetOpen(true)}
            >
              {t("viewUploads")}
            </Button>
          )}
        </div>

        <div className="flex flex-col items-center justify-between">
          {uploadedEarningsDocuments.length === 0 && (
            <div
              className={`border-[2px] rounded-md p-4 w-full text-center cursor-pointer transition-colors flex-grow ${
                selectedTickers.length === 0
                  ? "border-gray-200 bg-gray-100 text-gray-400"
                  : "hover:bg-gray-50"
              }`}
              onClick={handleUploadEarningsClick}
            >
              <div className="flex flex-col items-center justify-center">
                <Paperclip
                  className={
                    selectedTickers.length === 0 ? "text-gray-400" : ""
                  }
                  size={10}
                />
                <p
                  className={`text-[12px] font-medium ${
                    selectedTickers.length === 0
                      ? "text-gray-400"
                      : "text-[#0F172A]"
                  }`}
                >
                  {t("uploadDocumentButton")}
                </p>
                <p className="text-[10px] font-[400] text-[#7F7F81] mt-1">
                  {t("uploadDocumentDescription")}
                </p>
              </div>
              <input
                ref={earningsFileInputRef}
                id="earnings"
                type="file"
                accept=".pdf,.doc,.docx,.txt"
                className="hidden"
                multiple
                onChange={handleMainFormEarningsUpload}
              />
            </div>
          )}
        </div>

        {/* Display uploaded documents summary */}
        <div className="flex items-center gap-2 mt-2">
          {uploadedEarningsDocuments.slice(0, 3).map((doc, index) => (
            <div
              key={index}
              className="w-fit bg-white border rounded-md flex items-center justify-center "
            >
              {/* <div className="text-center bg-[#FAFAFA] w-10 h-10 place-content-center">
                <p className="text-xs font-medium">PDF</p>
              </div> */}
              <Image
                src="/doc-icon.svg"
                alt="doc icon"
                height={50}
                width={50}
              />
            </div>
          ))}
          {uploadedEarningsDocuments.length > 3 && (
            <div className="w-fit bg-white border rounded-md flex items-center justify-center p-2">
              <div className="text-center bg-[#FAFAFA] w-10 h-10 place-content-center">
                <p className="text-xs font-medium">
                  +{uploadedEarningsDocuments.length - 3}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* ── Other Documents for Analysis ── */}
      <div className="space-y-1.5">
        <div className="flex justify-between items-center gap-3">
          <Label className="text-sm font-medium flex justify-between w-full items-center text-[#1E1E1E]">
            <div>
              {t("uploadOtherDocumentsLabel")}{" "}
              <span className="text-[#7F7F81] font-medium text-[14px]">
                {t("optional")}
              </span>
            </div>

            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger asChild className="">
                  <div className="flex items-center ml-1 cursor-pointer gap-1">
                    <div className="min-w-4">
                      <Info color="#CB680C" size={18} />
                    </div>
                  </div>
                </TooltipTrigger>
                <TooltipContent
                  className="max-w-[300px] gap-2 flex flex-col p-3 text-xs text-start font-[400] leading-5 bg-white text-black"
                  side="right"
                >
                  <span>{t("otherDocumentsTooltip")}</span>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </Label>
          {uploadedOtherDocuments.length > 0 && (
            <Button
              variant="link"
              className="ml-2 text-[#0359D8] p-0 h-auto text-[12px] text-sm cursor-pointer"
              onClick={() => setIsOtherSheetOpen(true)}
            >
              {t("viewUploads")}
            </Button>
          )}
        </div>

        <div className="flex flex-col items-center justify-between">
          {uploadedOtherDocuments.length === 0 && (
            <div
              className={`border-[2px] rounded-md p-4 w-full text-center cursor-pointer transition-colors flex-grow ${
                selectedTickers.length === 0
                  ? "border-gray-200 bg-gray-100 text-gray-400"
                  : "hover:bg-gray-50"
              }`}
              onClick={handleUploadOtherClick}
            >
              <div className="flex flex-col items-center justify-center">
                <Paperclip
                  className={
                    selectedTickers.length === 0 ? "text-gray-400" : ""
                  }
                  size={10}
                />
                <p
                  className={`text-[12px] font-medium ${
                    selectedTickers.length === 0
                      ? "text-gray-400"
                      : "text-[#0F172A]"
                  }`}
                >
                  {t("uploadDocumentButton")}
                </p>
                <p className="text-[10px] font-[400] text-[#7F7F81] mt-1">
                  {t("otherDocumentsDescription")}
                </p>
              </div>
              <input
                ref={otherFileInputRef}
                id="otherDocs"
                type="file"
                accept=".pdf,.doc,.docx,.txt"
                className="hidden"
                multiple
                onChange={handleMainFormOtherUpload}
              />
            </div>
          )}
        </div>

        {/* Display uploaded documents summary */}
        <div className="flex items-center gap-2 mt-2">
          {uploadedOtherDocuments.slice(0, 3).map((doc, index) => (
            <div
              key={index}
              className="w-fit bg-white border rounded-md flex items-center justify-center "
            >
              {/* <div className="text-center bg-[#FAFAFA] w-10 h-10 place-content-center">
               <p className="text-xs font-medium">PDF</p>
             </div> */}
              <Image
                src="/doc-icon.svg"
                alt="doc icon"
                height={50}
                width={50}
              />
            </div>
          ))}
          {uploadedOtherDocuments.length > 3 && (
            <div className="w-fit bg-white border rounded-md flex items-center justify-center p-2">
              <div className="text-center bg-[#FAFAFA] w-10 h-10 place-content-center">
                <p className="text-xs font-medium">
                  +{uploadedOtherDocuments.length - 3}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="space-y-1.5">
        <Label htmlFor="download-option" className="text-sm font-medium">
          {t("deliveryOptionsLabel")}
        </Label>
        <Select
          value={downloadOption}
          onValueChange={handleDownloadOptionChange}
        >
          <SelectTrigger className="w-full" id="download-option">
            <SelectValue
              className="text-[12px] "
              placeholder={t("selectDeliveryPlaceholder")}
            />
          </SelectTrigger>
          <SelectContent>
            {/* <SelectItem value="download">Direct Download</SelectItem> */}
            <SelectItem value="email">{t("emailOption")}</SelectItem>
            <SelectItem value="telegram">{t("telegramOption")}</SelectItem>
          </SelectContent>
        </Select>

        {downloadOptionError && (
          <p className="text-sm text-red-500 mt-1">
            {t("pleaseSelectDownloadOption")}
          </p>
        )}

        {downloadOption === "email" && (
          <div className="mt-3">
            <Label htmlFor="email-address" className="text-sm font-medium">
              {t("emailLabel")}
            </Label>
            <Input
              id="email-address"
              type="email"
              placeholder={t("emailPlaceholder")}
              className="mt-1"
              value={
                emailAddress
                  ? emailAddress
                  : userCred?.user?.user_metadata?.email || ""
              }
              onChange={handleEmailChange} // Use the new handler
              disabled={isGenerating} // Set default value from userCred
            />
            {emailError && (
              <p className="text-sm text-red-500 mt-1">
                {t("pleaseEnterValidEmail")}
              </p>
            )}
          </div>
        )}

        {downloadOption === "telegram" && (
          <div className="mt-1 pl-2">
            {isTelegramLoading ? (
              <p className="text-sm">{t("checkingStatus")}</p>
            ) : (
              <div className="">
                {errorData?.response?.data?.error ===
                  "No Telegram ID found for this user." && (
                  <div className="text-sm text-red-500">
                    {t("pleaseLinkTelegram")}
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        <p className="text-xs text-[#7F7F81] font-[400] mt-3">
          {t("generatedReportsNote")}
        </p>
      </div>

      <div className="flex items-center gap-1">
        <Checkbox
          id="sec-filing"
          checked={sendSecFiling}
          onCheckedChange={(checked) =>
            handleSecFilingChange(checked as boolean)
          }
        />
        <label
          htmlFor="sec-filing"
          className="text-[12px] font-normal text-[#0F172A]"
        >
          {t("secFilingCheckbox")}
        </label>
      </div>

      <Button
        className="w-full bg-gray-900 hover:bg-gray-800 text-white cursor-pointer"
        onClick={handleGenerateClick}
        disabled={isGenerating}
      >
        {isGenerating ? t("generatingReportButton") : t("generateReportButton")}
      </Button>

      {success && (
        <div className="bg-green-50 border text-center border-green-200 text-green-700 px-4 py-3 rounded relative">
          {t("reportDelivered", { method: downloadOption })}
        </div>
      )}

      {/* Upload Tickers Dialog */}
      <UploadTickers
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onUploadComplete={handleUploadedTickers}
      />

      {/* Ticker Selection Sheet */}
      <Sheet
        open={isTickerSheetOpen}
        onOpenChange={setIsTickerSheetOpen}
        side="right"
      >
        <SheetContent className="sm:max-w-md w-full h-[95%] my-auto flex flex-col p-0 [&>button]:hidden rounded-md mr-4">
          <SheetHeader className="px-6 py-4 ">
            <SheetTitle className="flex items-center justify-between">
              {t("tickersFound")} {/* Not found warning */}
            </SheetTitle>
          </SheetHeader>

          <div className="px-6 py-1 flex flex-col h-full overflow-hidden">
            {/* Search input with combined border */}
            <div className="flex items-center border rounded-lg px-2 overflow-hidden focus-within:ring-2 focus-within:ring-primary focus-within:border-gray-400">
              <div className="relative flex-1">
                <Input
                  type="text"
                  placeholder={t("searchPlaceholder")}
                  className="border-0 shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 rounded-none h-[38px] pr-8"
                  value={searchValue}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      // Only trigger search if validation passes
                      if (!tickerValidationError && searchValue.trim()) {
                        handleSearch();
                      }
                    }
                  }}
                />
                {searchValue && (
                  <button
                    onClick={() => handleSearchChange("")}
                    className="absolute right-2.5 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-500 cursor-pointer"
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 4L4 12M4 4L12 12"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </button>
                )}
              </div>
              <Button
                className={`rounded-sm max-w-[83px] h-[27px] px-3 bg-gray-900 ${
                  searchValue && !tickerValidationError
                    ? "opacity-[1] cursor-pointer"
                    : "opacity-[0.5] cursor-not-allowed"
                }`}
                onClick={handleSearch}
                disabled={
                  isSearching || !!tickerValidationError || !searchValue.trim()
                }
              >
                {isSearching ? (
                  <div className="flex w-5 h-5">
                    <MoonLoader color="white" size={15} />
                  </div>
                ) : (
                  t("searchButton")
                )}
                {!isSearching && <Search />}
              </Button>
            </div>

            {/* Add error message below the search input */}
            {tickerValidationError && (
              <p className="text-red-600 text-xs mt-2">
                {t("tickerLimitExceeded")}
              </p>
            )}

            <p className="text-xs text-[#7F7F81] font-normal mt-4">
              {t("enterTickersInstruction")}
            </p>

            {/* Found tickers */}
            {foundTickers.length > 0 && (
              <div className="mt-4 ">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium ">{t("tickersFound")}</h3>
                </div>

                <div className="flex flex-wrap gap-2 max-h-48 overflow-y-auto">
                  {foundTickers.map((ticker) => (
                    <div
                      key={ticker.symbol}
                      className="flex items-center bg-gray-100 rounded px-2 py-1"
                    >
                      <span className="text-sm">{ticker.symbol}</span>
                      <button
                        onClick={() => removeTicker(ticker)}
                        className="ml-1 text-gray-500 hover:text-gray-700"
                      >
                        <svg
                          width="14"
                          height="14"
                          viewBox="0 0 16 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M12 4L4 12M4 4L12 12"
                            stroke="currentColor"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Not recognized tickers */}
            {/* {showNotRecognized && notFoundTickers.length > 0 && ( */}
            {hasParsedInput && notFoundTickers.length > 0 && (
              <div className="mt-4">
                <h3 className="flex gap-1 items-center font-medium text-[12px] text-red-500  mb-2">
                  {t("tickersNotRecognized")}
                  <CircleX
                    size={15}
                    className="cursor-pointer"
                    onClick={() => setNotFoundTickers([])}
                  />
                </h3>
                <div className="flex flex-wrap gap-2 max-h-48 overflow-y-auto">
                  {notFoundTickers.map((ticker) => (
                    <div
                      key={ticker}
                      className="text-[12px] font-[400] bg-[#FFECEB] text-red-600 rounded px-2 py-1"
                    >
                      {ticker}
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="flex-grow"></div>
          </div>

          <div className="mt-auto  p-4 flex justify-between">
            <Button
              variant="outline"
              onClick={() => setIsTickerSheetOpen(false)}
            >
              {t("cancel")}
            </Button>
            <Button
              className="bg-gray-900"
              onClick={handleFinish}
              disabled={foundTickers.length === 0}
            >
              {t("finish")}
            </Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* Confirmation Modal */}
      <Dialog open={isConfirmModalOpen} onOpenChange={setIsConfirmModalOpen}>
        <DialogContent className="sm:max-w-[409px] min-h-[200px]">
          <DialogTitle className="text-[18px] font-[500]">
            {t("generateReportConfirmTitle")}
          </DialogTitle>
          <div className="py-">
            <p className="text-gray-500 text-sm">
              {t("generateReportConfirmDescription")}
            </p>

            <div className="flex items-center mt-2">
              <Checkbox
                id="dont-ask-again"
                checked={dontAskAgain}
                onCheckedChange={handleDontAskAgainChange}
              />
              <label
                htmlFor="dont-ask-again"
                className="ml-2 text-xs font-[400] text-gray-500"
              >
                {t("dontAskAgain")}
              </label>
            </div>

            <div className="flex justify-end space-x-3 mt-8">
              <Button
                variant="outline"
                onClick={() => setIsConfirmModalOpen(false)}
                disabled={isGenerating}
              >
                {t("cancel")}
              </Button>
              <Button
                className="bg-gray-900 text-white"
                onClick={startAnalysis}
                disabled={isGenerating}
              >
                {isGenerating
                  ? t("generatingReportButton")
                  : t("generateReportButton")}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* <LoadingModal isOpen={loadingModalOpen} status={loadingStatus} /> */}
      <UploadSheet
        isOpen={isUploadSheetOpen}
        onOpenChange={setIsUploadSheetOpen}
        initialDocuments={uploadedDocuments}
        initialFiles={tempUploadedFiles}
        initialTickers={selectedTickers.map((ticker) => ticker.symbol)}
        onSave={handleSaveDocuments}
      />

      <UploadSheet
        isOpen={isEarningsSheetOpen}
        onOpenChange={setIsEarningsSheetOpen}
        initialDocuments={uploadedEarningsDocuments}
        initialFiles={tempEarningsFiles}
        initialTickers={selectedTickers.map((t) => t.symbol)}
        onSave={setUploadedEarningsDocuments}
      />

      <UploadSheet
        isOpen={isOtherSheetOpen}
        onOpenChange={setIsOtherSheetOpen}
        initialDocuments={uploadedOtherDocuments}
        initialFiles={tempOtherFiles}
        initialTickers={selectedTickers.map((t) => t.symbol)}
        onSave={setUploadedOtherDocuments}
      />

      <TelegramIDSaveModal
        open={isTelegramModalOpen}
        onOpenChange={setIsTelegramModalOpen}
        telegramId={telegramId}
        onSave={saveTelegramId}
        onContinue={continueWithoutSaving}
        saving={isSavingTelegramId}
      />
    </div>
  );
};

export default ReportForm;
