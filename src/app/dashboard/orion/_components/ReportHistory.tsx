// @ts-nocheck

"use client";

import { FC, useEffect, useRef, useState } from "react";
import { Share, Download, Share2, Loader } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import {
  orionAPI,
  useGetQuery,
  useGetQueryOrionBackend,
} from "@/services/api_hooks";
import { toast } from "sonner";
import useLocalStorage from "@/hooks/use-local-storage";
import moment from "moment";
import axios from "axios";
import { orion_base_url, orionBackendUrl } from "@/config/baseUrl";
import { useTranslations } from "next-intl";

interface Report {
  id: string;
  title: string;
  date: string;
  time: string;
}

interface ReportsHistoryProps {
  reports?: Report[];
  pageSize?: number;
}

const ReportsHistory: FC<ReportsHistoryProps> = ({}) => {
  const t = useTranslations("DashboardOrion.ReportHistory");
  const [emailModalOpen, setEmailModalOpen] = useState(false);
  const [telegramModalOpen, setTelegramModalOpen] = useState(false);
  const [loadingSendEmail, setLoadingSendEmail] = useState(false);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [email, setEmail] = useState("");
  const [telegramId, setTelegramId] = useState("");
  const [downloadingReportId, setDownloadingReportId] = useState<string | null>(
    null
  );
  const [loadingSendTelegram, setLoadingSendTelegram] = useState(false);

  const [emailSent, setEmailSent] = useState<Record<string, boolean>>({});
  const [successToastShown, setSuccessToastShown] = useState<
    Record<string, boolean>
  >({});

  // Local storage hooks
  const [userCredentials, setName] = useLocalStorage("authCred", "");
  const [secFiling, setSecFiling] = useLocalStorage("secFiling", "");
  const [storedEmail, setStoredEmail] = useLocalStorage("reportEmail", "");
  // const [pendingJobs, setPendingJobs] = useLocalStorage("pendingJobs", "[]");
  const [pendingJobs, setPendingJobs] = useState<string[]>([]);

  // Reference for history polling interval
  const historyPollingRef = useRef<NodeJS.Timeout | null>(null);

  // Setup React Query for fetching history data
  const {
    data,
    isLoading: isHistoryLoading,
    refetch: refetchHistory,
  } = useGetQueryOrionBackend("/report/history", ["get-history"], {
    onError() {
      toast.error(t("loadingReports"), {
        position: "top-right",
        className: "p-4",
      });
    },
  });


  // Update the pendingJobs localStorage
  const updatePendingJobs = (newPendingJobs: string[]) => {
    setPendingJobs(JSON.stringify(newPendingJobs));
  };

  // Remove a job from pendingJobs
  const removeFromPendingJobs = (jobId: string) => {
    setPendingJobs((currentJobs) => {
      const updatedJobs = currentJobs.filter((id) => id !== jobId);
      console.log(
        `Removed job ${jobId} from pending jobs. Remaining:`,
        updatedJobs
      );

      // If no more pending jobs, clear the polling interval
      if (updatedJobs.length === 0) {
        stopPolling();
      }
      return updatedJobs;
    });
  };

  // Stop the polling interval
  const stopPolling = () => {
    if (historyPollingRef.current) {
      console.log("Stopping history polling - no more pending jobs");
      clearInterval(historyPollingRef.current);
      historyPollingRef.current = null;
    }
  };

  // Start or restart the polling interval
  const startPolling = () => {
    // Clear any existing interval first
    stopPolling();

    // Only start polling if we have pending jobs
    if (pendingJobs.length > 0) {
      console.log("Starting history polling for pending jobs");
      historyPollingRef.current = setInterval(() => {
        console.log("Polling: Refreshing history to update progress");
        refetchHistory();
      }, 5000); // Poll history every 5 seconds for more responsive progress updates
    }
  };

  const identifyPendingJobs = (historyData: any[]) => {
    if (!historyData) return;

    const pendingJobIds = historyData
      .filter(
        (report) =>
          report.progress < 1.0 &&
          report.status !== "completed" &&
          report.status !== "failed"
      )
      .map((report) => report.job_id);

    console.log("Identified pending jobs from API:", pendingJobIds);
    setPendingJobs(pendingJobIds);

    // Start or stop polling based on whether we have pending jobs
    if (pendingJobIds.length > 0) {
      startPolling();
    } else {
      stopPolling();
    }

    return pendingJobIds.length > 0; // Return true if we have pending jobs
  };

  // Handle email sharing
  const handleShareViaEmail = (report: Report) => {
    setSelectedReport(report);
    if (userCredentials?.user?.user_metadata?.email) {
      setEmail(userCredentials.user.user_metadata.email);
    }
    setEmailModalOpen(true);
  };

  // Handle telegram sharing
  const handleShareViaTelegram = (report: Report) => {
    setSelectedReport(report);
    if (userCredentials?.user?.user_metadata?.telegram_id) {
      setTelegramId(userCredentials.user.user_metadata.telegram_id);
    }
    setTelegramModalOpen(true);
  };

  // Handle email form submission
  const handleEmailSubmit = async () => {
    if (!selectedReport) return;

    if (!email) {
      toast.error(t("pleaseEnterEmail"), {
        position: "top-right",
        className: "p-4",
      });
      return;
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
      toast.error(t("pleaseEnterValidEmail"), {
        position: "top-right",
        className: "p-4",
      });
      return;
    }

    // Call the sendReportEmail function with the job_id and email
    const success = await sendReportEmail(selectedReport.job_id, email);

    if (success) {
      // Close the modal and reset email only on success
      setEmailModalOpen(false);
      setEmail("");
    }
  };

  // Handle telegram form submission
  const handleTelegramSubmit = async () => {
    if (!selectedReport) return;

    if (!telegramId) {
      toast.error(t("pleaseEnterTelegramId"), {
        position: "top-right",
        className: "p-4",
      });
      return;
    }

    setLoadingSendTelegram(true);
    try {
      const API_BASE_URL = orionBackendUrl;
      console.log(
        `Sending report ${selectedReport.job_id} via Telegram: ${telegramId}`
      );

      const response = await orionAPI.post(
        `/report/by-telegram`,
        {
          job_id: selectedReport.job_id,
          telegram_id: telegramId,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userCredentials?.token?.accessToken}`,
          },
        }
      );

      console.log("Telegram sent response:", response.data);
      setLoadingSendTelegram(false);

      toast.success("Report sent to Telegram successfully", {
        position: "top-right",
        className: "p-4",
      });

      setTelegramModalOpen(false);
      setTelegramId("");
    } catch (error) {
      setLoadingSendTelegram(false);
      console.error("Telegram error:", error);
      toast.error(error?.response?.data?.error, {
        position: "top-right",
        className: "p-4",
      });
    }
  };

  // Use registered email for form
  const useRegisteredEmail = () => {
    if (userCredentials?.user?.user_metadata?.email) {
      setEmail(userCredentials.user.user_metadata.email);
    } else {
      setEmail("<EMAIL>");
    }
  };

  // Send report via email
  const sendReportEmail = async (jobId: string, emailAddress: string) => {
    setLoadingSendEmail(true);
    if (!jobId) {
      toast.error(t("noJobIdForEmail"), {
        position: "top-right",
        className: "p-4",
      });
      return false;
    }

    if (!emailAddress) {
      toast.error(t("pleaseEnterEmail"), {
        position: "top-right",
        className: "p-4",
      });
      return false;
    }

    // If we've already sent an email for this job, don't send again
    if (emailSent[jobId]) {
      console.log(`Email already sent for job ${jobId}, skipping`);
      return true;
    }

    try {
      const API_BASE_URL = orion_base_url;
      console.log(`Sending email for job: ${jobId} to: ${emailAddress}`);

      // Check if secFiling option is set in localStorage
      const includeDocs = secFiling === "true" || secFiling === true;
      console.log(
        `Include company docs: ${includeDocs}, secFiling value: ${secFiling}`
      );

      const response = await orionAPI.post(
        `/report/by-email`,
        {
          job_id: jobId,
          email: emailAddress,
          include_company_docs: includeDocs,
        },
        {
          headers: {
            Authorization: `Bearer ${userCredentials?.token?.accessToken}`,
          },
        }
      );

      console.log("Email sent response:", response.data);
      setLoadingSendEmail(false);
      // Mark this report as having email sent
      setEmailSent((prev) => ({ ...prev, [jobId]: true }));

      toast.success(t("reportSentToEmail"), {
        position: "top-right",
        className: "p-4",
      });

      return true;
    } catch (error) {
      setLoadingSendEmail(false);
      console.error("Email error:", error);
      toast.error(error?.response?.data?.error, {
        position: "top-right",
        className: "p-4",
      });
      return false;
    }
  };

  const downloadReport = async (jobId: string) => {
    if (!jobId) {
      toast.error(t("noJobIdForDownload"), {
        position: "top-right",
        className: "p-4",
      });
      return;
    }

    try {
      setDownloadingReportId(jobId);

      // Determine whether to include docs
      const includeDocs = secFiling === "true" || secFiling === true;

      const API_BASE_URL = orion_base_url;
      const downloadEndpoint = `/report/download/${jobId}`;

      // Add query parameter for include_company_docs if needed
      const params = includeDocs ? { include_company_docs: true } : {};

      // Axios GET with responseType 'blob' to fetch binary PDF
      const response = await orionAPI.get(downloadEndpoint, {
        responseType: "blob",
        headers: {
          Authorization: `Bearer ${userCredentials?.token?.accessToken}`,
        },
        params: params,
      });

      // Create a blob URL for download
      const blob = new Blob([response.data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `analysis_${jobId}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast.success(t("reportDownloaded"), {
        position: "top-right",
        className: "p-4",
      });
    } catch (err) {
      console.error("Download error:", err);

      // Extract the API's error message if present
      const apiMessage = axios.isAxiosError(err)
        ? err.response?.data?.detail
        : undefined;

      const messageToShow =
        apiMessage ||
        (axios.isAxiosError(err) ? err.message : "Failed to download report");

      toast.error(messageToShow, {
        position: "top-right",
        className: "p-4",
      });
    } finally {
      setDownloadingReportId(null);
    }
  };

  // Check for job completions in history data
  const checkCompletionsInHistory = () => {
    if (!data?.history) return;

    if (pendingJobs.length === 0) {
      // No pending jobs, stop polling
      stopPolling();
      return;
    }

    // Check each pending job
    pendingJobs.forEach((jobId) => {
      const report = data.history.find((r) => r.job_id === jobId);

      if (!report) {
        return;
      }

      // Check if the report is complete
      const isComplete =
        report.status === "completed" &&
        Math.abs(report.progress - 1.0) < 0.001;

      // Check if the report has failed
      const hasFailed = report.status === "failed";

      // Handle completed reports
      if (isComplete && !successToastShown[jobId]) {
        // Mark as having shown toast
        setSuccessToastShown((prev) => ({ ...prev, [jobId]: true }));

        // Show success notification
        toast.success(t("reportGenerationComplete"), {
          position: "top-right",
          className: "p-4",
        });

        // Remove from pending jobs
        removeFromPendingJobs(jobId);
      }

      // Handle failed reports
      if (hasFailed && !successToastShown[jobId]) {
        // Mark as having shown toast to prevent future checks
        setSuccessToastShown((prev) => ({ ...prev, [jobId]: true }));

        // Show failure notification
        toast.error(t("reportGenerationFailed"), {
          position: "top-right",
          className: "p-4",
        });

        // Remove from pending jobs
        removeFromPendingJobs(jobId);
      }
    });
  };

  // Start/restart history polling when pendingJobs changes
  useEffect(() => {
    const pendingJobsArray = pendingJobs;

    if (pendingJobsArray.length > 0) {
      // We have pending jobs, make sure polling is active
      startPolling();
      // Also do an immediate fetch
      refetchHistory();
    } else {
      // No pending jobs, stop polling
      stopPolling();
    }

    return () => {
      // Clean up on unmount or when pendingJobs changes
      stopPolling();
    };
  }, [pendingJobs]);

  // Initial fetch on component mount
  useEffect(() => {
    // Fetch history once regardless of pending jobs
    refetchHistory();

    // Setup polling only if we have pending jobs
    if (pendingJobs.length > 0) {
      startPolling();
    }

    return () => {
      // Clean up on unmount
      stopPolling();
    };
  }, []);

  // Check for job completions whenever history data updates
  useEffect(() => {
    if (data?.history) {
      // First identify pending jobs in the history data
      const hasPendingJobs = identifyPendingJobs(data.history);

      // Then check for completions
      checkCompletionsInHistory();

      // If we have pending jobs, ensure polling is active
      if (hasPendingJobs) {
        startPolling();
      }
    }
  }, [data]);

  return (
    <div className="w-full max-w-[458px] bg-white rounded-lg shadow-sm p-6">
      <div className="mb-6">
        <h2 className="text-[22px] font-[600] text-gray-800">
          {t("reportsHistoryTitle")}
        </h2>
        <p className="text-[#A7A7A7] text-sm">
          {t("reportsHistoryDescription")}
        </p>
      </div>

      {isHistoryLoading && (
        <div className="py-4 text-center">
          <p>{t("loadingReports")}</p>
        </div>
      )}

      {!isHistoryLoading && data?.history?.length === 0 && (
        <div className="py-4">
          <p className="text-center mb-6">{t("noReportsFound")}</p>
          <p className="text-[#A7A7A7] text-sm">
            {t("wantToKnow")}{" "}
            <a
              href="/sample_report.pdf"
              download
              className="underline text-blue-600 cursor-pointer text-sm"
            >
              {t("reportSample")}
            </a>
          </p>
        </div>
      )}

      <div className="space-y-4">
        {data?.history?.map((report: any) => {
          // Check if this report is in the pending jobs list
          const isPending = pendingJobs.includes(report.job_id);

          return (
            <div key={report.id}>
              <div className="flex items-center justify-between py-2 border-b border-gray-100">
                <div className="space-y-2">
                  <h3 className="font-medium text-sm text-[#1E1E1E]">
                    {/* Using HTML title attribute for native tooltip */}
                    <span
                      title={
                        report.tickers && report.tickers.length > 0
                          ? ` ${report.tickers.join(", ")}`
                          : t("noTickers")
                      }
                    >
                      {report.tickers && report.tickers.length > 0 ? (
                        <>
                          {report.tickers.slice(0, 3).join(", ")}
                          {report.tickers.length > 3 && "..."}
                        </>
                      ) : (
                        t("noTickers")
                      )}
                    </span>
                  </h3>
                  <p className="text-xs text-[#7F7F81]">
                    {moment(report.timestamp).format("DD MMMM YYYY, h:mma")}
                  </p>

                  <p
                    className={`text-xs ${
                      report.status === "failed"
                        ? "text-red-600"
                        : "text-blue-600"
                    }`}
                  >
                    {report.status}
                  </p>
                </div>
                <div className={`flex space-x-2 `}>
                  <Popover>
                    <PopoverTrigger asChild>
                      <button
                        className={`text-gray-500 border cursor-pointer hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors ${
                          report.progress < 1.0
                            ? "opacity-[0.4]"
                            : "opacity-[1]"
                        }`}
                        aria-label="Share"
                        disabled={report.progress < 1.0}
                      >
                        <Share2 size={18} />
                      </button>
                    </PopoverTrigger>
                    <PopoverContent className="w-36 p-0">
                      <div className="py-1">
                        <button
                          className={`w-full text-left px-4 py-2 cursor-pointer text-sm hover:bg-gray-100 `}
                          onClick={() => handleShareViaEmail(report)}
                        >
                          Email
                        </button>
                        <button
                          className="w-full text-left px-4 cursor-pointer py-2 text-sm hover:bg-gray-100"
                          onClick={() => handleShareViaTelegram(report)}
                        >
                          Telegram ID
                        </button>
                      </div>
                    </PopoverContent>
                  </Popover>
                  <button
                    className={`text-gray-500 cursor-pointer border hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors ${
                      report.progress < 1.0 ? "opacity-[0.4]" : "opacity-[1]"
                    }`}
                    aria-label="Download"
                    disabled={
                      report.progress < 1.0 ||
                      downloadingReportId === report.job_id
                    }
                    onClick={() => downloadReport(report.job_id)}
                  >
                    {downloadingReportId === report.job_id ? (
                      <Loader size={18} className="animate-spin" />
                    ) : (
                      <Download size={18} />
                    )}
                  </button>
                </div>
              </div>

              <Progress
                value={report.progress * 100}
                className={`w-[100%] h-2 ${
                  report.status === "failed"
                    ? "[&>*]:bg-red-600"
                    : "[&>*]:bg-green-600"
                }`}
              />

              <div
                className={`text-xs mt-1 text-right ${
                  report.status === "failed" ? "text-red-500" : "text-gray-500"
                }`}
              >
                {report.status === "failed"
                  ? t("failed")
                  : t("progressPercent", {
                      percent: Math.round(report.progress * 100),
                    })}
              </div>
            </div>
          );
        })}
      </div>

      <div className="flex justify-between items-center mt-6">
        <div className="text-sm text-gray-500"> {t("showingRows")}</div>
        <div className="flex space-x-2">
          <Button variant="ghost" size="sm">
            {t("previous")}
          </Button>
          <Button variant="ghost" size="sm">
            {t("next")}
          </Button>
        </div>
      </div>

      {/* Email Modal */}
      <Dialog open={emailModalOpen} onOpenChange={setEmailModalOpen}>
        <DialogContent className="sm:max-w-[509px]">
          <DialogHeader>
            <DialogTitle className="text-[16px] text-[#1E1E1E] font-medium">
              {t("enterReceivingEmailTitle")}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <Input
              placeholder={t("enterEmailPlaceholder")}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            <div>
              <button
                className="text-[#0359D8] font-medium text-sm cursor-pointer"
                onClick={useRegisteredEmail}
              >
                {t("useRegisteredAddress")}
              </button>
            </div>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setEmailModalOpen(false)}
                className="cursor-pointer"
              >
                {t("cancel")}
              </Button>
              <Button
                onClick={handleEmailSubmit}
                className="bg-[#0F172A] hover:opacity-[1] cursor-pointer"
              >
                {t("send")}
                {loadingSendEmail && (
                  <Loader className="animate-spin" size={16} />
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Telegram Modal */}
      <Dialog open={telegramModalOpen} onOpenChange={setTelegramModalOpen}>
        <DialogContent className="sm:max-w-[509px]">
          <DialogHeader>
            <DialogTitle className="text-[16px] text-[#1E1E1E] font-medium">
              {t("enterReceivingTelegramTitle")}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <Input
              placeholder={t("enterTelegramPlaceholder")}
              value={telegramId}
              onChange={(e) => setTelegramId(e.target.value)}
            />
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setTelegramModalOpen(false)}
              >
                {t("cancel")}
              </Button>
              <Button
                onClick={handleTelegramSubmit}
                className="bg-[#0F172A] hover:opacity-[1] cursor-pointer"
              >
                {t("send")}
                {loadingSendTelegram && (
                  <Loader className="animate-spin" size={16} />
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ReportsHistory;
