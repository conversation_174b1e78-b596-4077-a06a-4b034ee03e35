// components/TelegramModal.tsx

import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

interface TelegramModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  telegramId: string;
  onSave: () => void;
  onContinue: () => void;
  saving: any;
}

const TelegramIDSaveModal = ({
  open,
  onOpenChange,
  telegramId,
  onSave,
  onContinue,
  saving,
}: TelegramModalProps) => {
  const t = useTranslations("DashboardOrion.TelegramModal");
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[409px] min-h-[200px]">
        <DialogTitle className="text-[18px] font-[500]">
          {t("saveTelegramIdTitle")}
        </DialogTitle>
        <div className="py-2">
          <p className="text-gray-500 text-sm">
            {t("saveTelegramIdDescription")}
          </p>

          <div className="my-4 py-3 text-center font-medium text-[18px]">
            {telegramId}
          </div>

          <div className="bg-[#FFFBEB] border border-[#FEF3C7] rounded-md p-3 mb-4">
            <div className="flex items-start gap-2">
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M10 17.5C14.1421 17.5 17.5 14.1421 17.5 10C17.5 5.85786 14.1421 2.5 10 2.5C5.85786 2.5 2.5 5.85786 2.5 10C2.5 14.1421 5.85786 17.5 10 17.5Z"
                  stroke="#F59E0B"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M10 6.66669V10"
                  stroke="#F59E0B"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M10 13.3333H10.0083"
                  stroke="#F59E0B"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <p className="text-xs text-[#B45309]">
                {t("replaceExistingIdWarning")}
              </p>
            </div>
          </div>

          <div className="flex justify-between space-x-3 mt-4">
            <Button variant="outline" onClick={onContinue} className="flex-1">
              {t("noDontSave")}
            </Button>
            <Button className="bg-gray-900 text-white flex-1" onClick={onSave}>
              {saving ? t("saving") : t("yesSaveId")}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TelegramIDSaveModal;
