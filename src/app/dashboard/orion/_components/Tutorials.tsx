"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChevronRight } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import EmbeddedVideoPlayer from "./embeddedVideoPlayer";
import { useState } from "react";
import { getOrionVideoLinks } from "@/utils/videoLinks";
import { useTranslations } from "next-intl";

export default function OrionTutorials() {
  const router = useRouter();
  const t = useTranslations("DashboardOrion.Tutorials");

  const [videoLink, setVideoLink] = useState<string>("");
  const [showModal, setShowModal] = useState<boolean>(false);

  const tLinks = useTranslations();
  const orionVideoLinks = getOrionVideoLinks(tLinks);

  return (
    <div className="min-h-screen bg-[#fafafa] mx-auto w-[1400px]">
      <EmbeddedVideoPlayer
        isOpen={showModal}
        videoUrl={videoLink}
        onClose={() => {
          setShowModal(false);
          setVideoLink("");
        }}
      />
      <main className="p-4 rounded-lg border border-[#E2E8F0]">
        <div className="flex items-center gap-2 mb-8">
          <div
            onClick={() => router.back()}
            className="cursor-pointer inline-flex items-center justify-center p-1 hover:bg-[#f7f7f7] rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-[#0359d8] focus:ring-offset-2"
          >
            <ArrowLeft className="w-4 h-4" />
          </div>
          <div className="w-[1px] bg-[#98a2b3] h-3" />

          <span className="text-[#98a2b3] text-sm">{t("breadcrumbOrion")}</span>
          <span className="text-[#98a2b3] text-sm">
            <ChevronRight className="w-4 h-4" />
          </span>
          <span className="text-[#1d1d1d] text-sm font-medium">
            {t("breadcrumbTutorials")}
          </span>
        </div>

        <div className="mb-12">
          <h1 className="text-[#1d1d1d] text-3xl font-semibold mb-2">
            {t("getTheMostOutOfModels")}
          </h1>
          <p className="text-[#98a2b3] text-lg">{t("watchHowToUse")}</p>
        </div>

        {/* Video Sections */}
        <div className="space-y-12">
          {orionVideoLinks.map((section, sectionIndex) => (
            <div key={sectionIndex}>
              <h2 className="text-[#98a2b3] text-base font-medium mb-6">
                {section.title}
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {section.videos.length === 0 && (
                  <div className="col-span-full flex flex-col items-center justify-center py-10 bg-gray-50 rounded-lg border border-dashed border-gray-200">
                    <div className="text-gray-400 mb-2">
                      <Play className="w-10 h-10 opacity-50" />
                    </div>
                    <p className="text-[#98a2b3] text-sm">
                      {t("noVideosAvailable")}
                    </p>
                  </div>
                )}
                {section.videos.map((video, videoIndex) => (
                  <div
                    key={videoIndex}
                    onClick={() => {
                      setShowModal(true);
                      setVideoLink(video.url);
                    }}
                    className="group cursor-pointer text-left focus:outline-none focus:ring-2 focus:ring-[#0359d8] focus:ring-offset-2 rounded-lg"
                  >
                    <div className="relative aspect-video bg-[#1e1e1e] rounded-lg overflow-hidden mb-3">
                      <div className="relative w-full h-full">
                        <Image
                          src={video.thumbnail}
                          alt={video.title}
                          fill
                          className="object-cover"
                        />
                        {video.duration && (
                          <div className="absolute bottom-0 right-0 z-10 bg-[#1d1d1d] bg-opacity-80 text-[#ffffff] text-xs px-2 py-1 rounded">
                            {video.duration}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center justify-center w-full h-full absolute transform -translate-y-1/2 -translate-x-1/2 top-1/2 left-1/2 bg-black/50">
                        <Play className="w-6 h-6 text-[#ffffff] ml-1" />
                      </div>
                    </div>
                    <h3 className="text-[#1d1d1d] font-medium text-sm leading-tight">
                      {video.title}
                    </h3>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </main>
    </div>
  );
}
