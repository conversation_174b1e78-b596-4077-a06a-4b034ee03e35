// components/UploadSheet.tsx
// @ts-nocheck
import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/sheet";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Paperclip, Trash2, Plus, X, Search } from "lucide-react";
import MoonLoader from "react-spinners/MoonLoader";
import useLocalStorage from "@/hooks/use-local-storage";
import { orionAPI } from "@/services/api_hooks";
import Image from "next/image";
import { useTranslations } from "next-intl";

interface DocumentWithTickers {
  file: File;
  tickers: string[];
}

interface UploadSheetProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  initialDocuments?: DocumentWithTickers[];
  initialFiles?: File[];
  initialTickers?: string[];
  onSave: (documents: DocumentWithTickers[]) => void;
}

const UploadSheet = ({
  isOpen,
  onOpenChange,
  initialDocuments = [],
  initialFiles = [],
  initialTickers = [],
  onSave,
}: UploadSheetProps) => {
  const t = useTranslations("DashboardOrion.UploadSheet");
  const [documents, setDocuments] = useState<DocumentWithTickers[]>([]);
  const [searchValue, setSearchValue] = useState<string>("");
  const [foundTickers, setFoundTickers] = useState<
    { symbol: string; name?: string; RIC: string }[]
  >([]);
  const [notFoundTickers, setNotFoundTickers] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [activeDocumentIndex, setActiveDocumentIndex] = useState<number | null>(
    null
  );
  const [popoverOpen, setPopoverOpen] = useState<boolean>(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [apiResults, setApiResults] = useState<any>(null);
  const [pendingDocuments, setPendingDocuments] = useState<File[]>([]);

  const [popoverSearchValue, setPopoverSearchValue] = useState<string>("");
  const [filteredTickers, setFilteredTickers] = useState<string[]>([]);
  const [availableTickers, setAvailableTickers] = useState<string[]>([]);

  const [userCredentials, setName] = useLocalStorage("authCred", "");

  // Update documents when initialDocuments changes or when sheet opens
  useEffect(() => {
    if (isOpen) {
      // Handle initialDocuments
      if (initialDocuments && initialDocuments.length > 0) {
        setDocuments(initialDocuments);

        // Extract tickers for documents without tickers
        const docsWithoutTickers = initialDocuments.filter(
          (doc) => doc.tickers.length === 0
        );
        if (docsWithoutTickers.length > 0 && documents.length === 0) {
          const files = docsWithoutTickers.map((doc) => doc.file);
          extractTickersFromFiles(
            files,
            initialDocuments.flatMap((doc) => doc.tickers)
          );
        }
      }

      // Handle initialFiles (from initial upload)
      if (initialFiles && initialFiles.length > 0 && documents.length === 0) {
        // For new initialFiles, don't immediately create documents
        // Instead, extract tickers first via API
        extractTickersFromFiles(initialFiles, initialTickers);
      }
    }

    // Clean up function to reset state when sheet closes
    return () => {
      if (!isOpen) {
        setPendingDocuments([]);
      }
    };
  }, [isOpen, initialDocuments, initialFiles, initialTickers]);

  useEffect(() => {
    if (isOpen) {
      // Set available tickers from initialTickers or extract from documents
      if (initialTickers && initialTickers.length > 0) {
        setAvailableTickers(initialTickers);
      } else if (initialDocuments && initialDocuments.length > 0) {
        // Extract unique tickers from initialDocuments
        const uniqueTickers = Array.from(
          new Set(initialDocuments.flatMap((doc) => doc.tickers))
        );
        setAvailableTickers(uniqueTickers);
      }

      // Rest of the existing code...
    }
  }, [isOpen, initialDocuments, initialFiles, initialTickers]);

  // Focus search input when popover opens
  useEffect(() => {
    if (popoverOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [popoverOpen]);

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + " B";
    else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(0) + " KB";
    else return (bytes / (1024 * 1024)).toFixed(0) + " MB";
  };

  const handlePopoverSearch = (value: string) => {
    setPopoverSearchValue(value);

    if (!value.trim()) {
      // If search is empty, show all available tickers
      setFilteredTickers(availableTickers);
      return;
    }

    // Filter tickers that match the search query
    const filtered = availableTickers.filter((ticker) =>
      ticker.toLowerCase().includes(value.toLowerCase())
    );
    setFilteredTickers(filtered);
  };

  const handleTickerSelect = (documentIndex: number, ticker: string) => {
    setDocuments((prev) => {
      const updated = [...prev];

      // Check if ticker is already added to this document
      if (!updated[documentIndex].tickers.includes(ticker)) {
        updated[documentIndex] = {
          ...updated[documentIndex],
          tickers: [...updated[documentIndex].tickers, ticker],
        };
      }

      return updated;
    });
  };

  const extractTickersFromFiles = async (
    files: File[],
    existingTickers: string[] = []
  ) => {
    if (files.length === 0) return;

    setIsUploading(true);
    setPendingDocuments(files); // Store the pending files

    try {
      // Prepare payload for API
      const payload = {
        file: files.map((file) => ({
          name: file.name,
          size: formatFileSize(file.size),
        })),
        tickerList: existingTickers.length > 0 ? existingTickers : [],
      };

      // If tickerList is empty and we have initialDocuments, use their tickers
      if (payload.tickerList.length === 0 && initialDocuments) {
        payload.tickerList = initialDocuments.flatMap((doc) => doc.tickers);
      }

      console.log("Sending payload to ticker-extract API:", payload);

      const response = await orionAPI.post(
        "/ticker-extract",
        payload
        // {
        //   headers: {
        //     "Content-Type": "application/json",
        //     Authorization: `Bearer ${userCredentials?.token?.accessToken}`,
        //   },
        // }
      );

      const data = response.data;
      console.log("Received API response:", data);
      setApiResults(data);

      // Now update documents based on API response
      if (data.results && data.results.length > 0) {
        // Create new document entries from API results
        const newDocuments = data.results
          .map((result) => {
            // Find the matching file from our pendingDocuments
            const matchingFile = files.find(
              (file) => file.name === result.filename
            );

            if (!matchingFile) {
              console.warn(
                `Could not find matching file for ${result.filename}`
              );
              return null;
            }

            return {
              file: matchingFile,
              tickers: result.tickers || [],
            };
          })
          .filter(Boolean) as DocumentWithTickers[];

        // Update documents state with the new entries
        setDocuments((prevDocs) => {
          // Get filenames of existing documents to avoid duplicates
          const existingFilenames = prevDocs.map((doc) => doc.file.name);

          // Filter out new documents that already exist
          const uniqueNewDocs = newDocuments.filter(
            (newDoc) => !existingFilenames.includes(newDoc.file.name)
          );

          // For existing documents, update their tickers
          const updatedExistingDocs = prevDocs.map((doc) => {
            const matchingResult = data.results.find(
              (result) => result.filename === doc.file.name
            );
            if (matchingResult && matchingResult.tickers) {
              return { ...doc, tickers: matchingResult.tickers };
            }
            return doc;
          });

          return [...updatedExistingDocs, ...uniqueNewDocs];
        });
      }

      // Clear pending documents
      setPendingDocuments([]);
    } catch (error) {
      console.error("Error extracting tickers:", error);

      // Handle axios error responses
      let errorMessage = "Unknown error";
      if (axios.isAxiosError(error)) {
        errorMessage = error.response?.data?.message || error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast.error(`Error extracting tickers: ${errorMessage}`, {
        position: "top-right",
        className: "p-4",
      });

      // If API fails, still add the files but without tickers
      const newDocuments = files.map((file) => ({
        file,
        tickers: [],
      }));

      setDocuments((prev) => [...prev, ...newDocuments]);
      setPendingDocuments([]);
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);

      // Do NOT immediately add files to documents
      // Instead, start the extraction process and wait for API response
      extractTickersFromFiles(
        newFiles,
        documents.flatMap((doc) => doc.tickers) // Pass existing tickers
      );
    }

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const removeDocument = (index: number) => {
    setDocuments((prev) => prev.filter((_, i) => i !== index));
    if (activeDocumentIndex === index) {
      setActiveDocumentIndex(null);
      setPopoverOpen(false);
    }
  };

  const toggleTickerSelector = (index: number) => {
    if (activeDocumentIndex === index && popoverOpen) {
      setActiveDocumentIndex(null);
      setPopoverOpen(false);
      setPopoverSearchValue("");
      setFilteredTickers([]);
    } else {
      setActiveDocumentIndex(index);
      setPopoverOpen(true);
      setPopoverSearchValue("");
      // Initialize filtered tickers with all available tickers
      setFilteredTickers(availableTickers);
    }
  };

  // Search button handler - uses same API as ReportForm
  const handleTickerSearch = async () => {
    if (!searchValue.trim()) {
      setFoundTickers([]);
      setNotFoundTickers([]);
      return;
    }

    setIsSearching(true);

    try {
      const tickersInput = searchValue.trim();

      const response = await orionAPI.post("/validate/tickers", {
        tickers: tickersInput,
      });

      const data = response.data;

      // Store the full ticker objects from valid array
      setFoundTickers(data.valid);
      setNotFoundTickers(data.invalid.map((item) => item.toUpperCase()));

      // If document index is active and we have found tickers, add them to the document
      if (activeDocumentIndex !== null && data.valid.length > 0) {
        addTickersToDocument(activeDocumentIndex, data.valid);
      }
    } catch (error) {
      console.error(error.response?.data?.error);
      // toast.error(`Error extracting tickers: ${errorMessage}`, {
      //   position: "top-right",
      //   className: "p-4",
      // });
      // Optional: Handle specific error responses
      if (axios.isAxiosError(error)) {
        console.error("Response data:", error.response?.data);
        console.error("Response status:", error.response?.status);
      }
    } finally {
      setIsSearching(false);
    }
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchValue(value);

    // If the input is completely cleared, reset states
    if (!value.trim()) {
      setFoundTickers([]);
      setNotFoundTickers([]);
    }
  };

  const addTickersToDocument = (
    documentIndex: number,
    tickers: { symbol: string; name?: string; RIC: string }[]
  ) => {
    setDocuments((prev) => {
      const updated = [...prev];
      const currentTickers = updated[documentIndex].tickers;

      // Add new tickers that aren't already in the document
      const tickersToAdd = tickers
        .map((ticker) => ticker.symbol)
        .filter((symbol) => !currentTickers.includes(symbol));

      if (tickersToAdd.length > 0) {
        updated[documentIndex] = {
          ...updated[documentIndex],
          tickers: [...currentTickers, ...tickersToAdd],
        };
      }

      return updated;
    });

    // Clear search after adding
    setSearchValue("");
    setFoundTickers([]);
    setPopoverOpen(false);
  };

  const removeTickerFromDocument = (documentIndex: number, ticker: string) => {
    setDocuments((prev) => {
      const updated = [...prev];
      updated[documentIndex] = {
        ...updated[documentIndex],
        tickers: updated[documentIndex].tickers.filter((t) => t !== ticker),
      };
      return updated;
    });
  };

  const handleFinish = () => {
    onSave(documents);
    onOpenChange(false);
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-md w-full h-[95%]  my-auto flex flex-col p-0 [&>button]:hidden rounded-md mr-4">
        <SheetHeader className="px-6 py-4">
          <SheetTitle>{t("uploadResearchReportsTitle")}</SheetTitle>
        </SheetHeader>

        {/* Add loading indicator at the top */}
        {isUploading && (
          <div className="absolute inset-0 bg-white/70 backdrop-blur-sm z-50 flex flex-col items-center justify-center opacity-[0.7]">
            <MoonLoader color="black" size={30} />
            <p className="mt-2 text-black font-medium">{t("uploading")}</p>
          </div>
        )}

        <div className="px-6 py-1 flex flex-col h-full overflow-y-auto">
          {/* Upload area at the top */}
          <div
            className="border border-dashed rounded-md p-3 text-center cursor-pointer hover:bg-gray-50 transition-colors"
            onClick={() => fileInputRef.current?.click()}
          >
            <div className="flex flex-col items-center justify-center">
              <Paperclip className="" size={10} />
              <p className="text-[12px] text-[#0F172A] font-medium">
                {t("clickToUploadDocument")}
              </p>
              <p className="text-[10px] font-[400] text-[#7F7F81] mt-1">
                {t("uploadDocumentDescription")}
              </p>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept=".pdf,.doc,.docx,.txt"
              className="hidden"
              multiple
              onChange={handleFileUpload}
            />
          </div>

          {/* Uploads section - only show when there are documents and not uploading */}
          {documents.length > 0 && (
            <div className="mt-6">
              <h3 className="font-medium mb-4 text-[14px]">
                {t("uploadsSectionTitle")}
              </h3>
              <div className="space-y-4">
                {documents.map((doc, index) => (
                  <div key={index} className="">
                    <div className="flex items-center justify-between border rounded-md p-4">
                      <div className="flex items-center">
                        <div className="w-fit bg-white border rounded-md flex items-center justify-center ">
                          <Image
                            src="/doc-icon.svg"
                            alt="doc icon"
                            height={50}
                            width={50}
                          />
                        </div>
                        <div className="ml-3">
                          <p className="text-xs font-medium">{doc.file.name}</p>
                          <p className="text-xs text-[#7F7F81]">
                            {formatFileSize(doc.file.size)}
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={() => removeDocument(index)}
                        className="text-red-500 hover:text-red-700 cursor-pointer border-none outline-none"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>

                    {/* Tickers for this document */}
                    <div className="mt-4 flex flex-wrap items-center gap-2">
                      {doc.tickers.map((ticker, tickerIndex) => (
                        <div
                          key={tickerIndex}
                          className="flex items-center bg-gray-100 rounded px-2 py-1 text-xs"
                        >
                          {ticker}
                          <button
                            onClick={() =>
                              removeTickerFromDocument(index, ticker)
                            }
                            className="ml-1 text-gray-500 hover:text-gray-700"
                          >
                            <X size={12} />
                          </button>
                        </div>
                      ))}

                      {/* Add ticker button with Popover */}
                      <Popover
                        open={activeDocumentIndex === index && popoverOpen}
                        onOpenChange={(open) => {
                          if (open) {
                            setActiveDocumentIndex(index);
                            setPopoverOpen(true);
                          } else {
                            setPopoverOpen(false);
                          }
                        }}
                      >
                        <PopoverTrigger asChild>
                          <button
                            onClick={() => toggleTickerSelector(index)}
                            className="flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-xs w-5 h-5"
                          >
                            <Plus size={14} />
                          </button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-64 p-0"
                          sideOffset={5}
                          side="bottom"
                          align="start"
                        >
                          <div className="p-">
                            {/* Search input */}
                            <div className="relative">
                              <Input
                                ref={searchInputRef}
                                type="text"
                                placeholder={t("searchForTickersPlaceholder")}
                                className="pr-8 text-sm h-[36px] rounded-none focus:outline-none focus:ring-0 focus-visible:ring-0 outline-none ring-0 shadow-none focus:shadow-none"
                                value={popoverSearchValue}
                                onChange={(e) =>
                                  handlePopoverSearch(e.target.value)
                                }
                              />
                              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <Search size={14} className="text-gray-400" />
                              </div>
                            </div>

                            {/* Ticker list */}
                            <div className="mt-2 max-h-40 overflow-y-auto p-2">
                              {filteredTickers.length > 0 ? (
                                filteredTickers.map((ticker, index) => (
                                  <div
                                    key={index}
                                    className="px-2 py-1.5 hover:bg-gray-100 cursor-pointer rounded text-sm flex justify-between items-center"
                                    onClick={() => {
                                      handleTickerSelect(
                                        activeDocumentIndex,
                                        ticker
                                      );
                                      // Don't close popover after selection to allow multiple selections
                                    }}
                                  >
                                    <span>{ticker}</span>
                                    {/* Show check mark if ticker is already selected for this document */}
                                    {activeDocumentIndex !== null &&
                                      documents[
                                        activeDocumentIndex
                                      ]?.tickers.includes(ticker) && (
                                        <svg
                                          width="16"
                                          height="16"
                                          viewBox="0 0 16 16"
                                          fill="none"
                                          xmlns="http://www.w3.org/2000/svg"
                                          className="text-green-500"
                                        >
                                          <path
                                            d="M13.3334 4L6.00002 11.3333L2.66669 8"
                                            stroke="currentColor"
                                            strokeWidth="1.5"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                          />
                                        </svg>
                                      )}
                                  </div>
                                ))
                              ) : (
                                <div className="px-2 py-2 text-sm text-gray-500">
                                  {t("noMatchingTickersFound")}
                                </div>
                              )}
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <SheetFooter className="px-6 py-4  mt-auto">
          <div className="flex justify-between w-full">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              {t("cancel")}
            </Button>
            <Button onClick={handleFinish} className="bg-gray-900">
              {t("finish")}
            </Button>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default UploadSheet;
