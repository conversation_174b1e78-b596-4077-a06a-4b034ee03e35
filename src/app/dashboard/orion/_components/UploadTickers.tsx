import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { Paperclip, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { orionAPI } from "@/services/api_hooks";
import useLocalStorage from "@/hooks/use-local-storage";
import { toast } from "sonner";
import { useTranslations } from "next-intl";

interface TickerData {
  symbol: string;
  name?: string;
  RIC: string;
}

interface UploadTickersProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUploadComplete?: (
    validTickers: TickerData[],
    invalidTickers: string[]
  ) => void;
}

const UploadTickers = ({
  open,
  onOpenChange,
  onUploadComplete,
}: UploadTickersProps) => {
  const t = useTranslations("DashboardOrion.UploadTickers");

  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [tickerCount, setTickerCount] = useState<number>(0);
  const [isValidating, setIsValidating] = useState(false);
  const [validationError, setValidationError] = useState<string>("");

  const validateCSVFile = async (csvFile: File) => {
    setIsValidating(true);
    setValidationError("");

    try {
      const text = await csvFile.text();
      const lines = text.split("\n").filter((line) => line.trim() !== "");

      // Skip header row if present
      const hasHeader = lines[0]?.toLowerCase().includes("ticker");
      const tickerLines = hasHeader ? lines.slice(1) : lines;
      const count = tickerLines.length;

      setTickerCount(count);

      if (count > 10) {
        setValidationError(`${t("limitExceeded")}`);
        return false;
      }

      return true;
    } catch (error) {
      setValidationError(t("failedToReadCSV"));
      return false;
    } finally {
      setIsValidating(false);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];

    if (!selectedFile) return;

    // Validate file type
    if (selectedFile.type !== "text/csv") {
      toast.error(`${selectedFile.name} ${t("notCSVFile")}`, {
        position: "top-right",
        className: "p-4",
      });
      return;
    }

    setFile(selectedFile);
    await validateCSVFile(selectedFile);
  };

  const removeFile = () => {
    setFile(null);
    setTickerCount(0);
    setValidationError("");
  };

  const handleUpload = async () => {
    if (!file) {
      toast.error(t("noFileSelected"), {
        position: "top-right",
        className: "p-4",
      });
      return;
    }

    if (validationError) {
      toast.error(t("pleaseFixValidationErrors"), {
        position: "top-right",
        className: "p-4",
      });
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append("files", file);

      const response = await orionAPI.post("/ticker-extract-csv", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      const result = response.data;

      toast.success(t("uploadSuccessful"), {
        position: "top-right",
        className: "p-4",
      });

      // Pass the valid and invalid tickers to the parent component
      if (onUploadComplete && result.valid && Array.isArray(result.valid)) {
        onUploadComplete(result.valid, result.invalid || []);
      }

      // Clear file after successful upload
      setFile(null);
      setTickerCount(0);
      setValidationError("");

      // Close the dialog
      onOpenChange(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Upload failed";
      toast.error(errorMessage, {
        position: "top-right",
        className: "p-4",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const isUploadDisabled =
    isUploading || !file || !!validationError || isValidating;

  return (
    <div>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[409px] min-h-[193px]">
          <DialogHeader>
            <DialogTitle className="text-center text-[14px] font-[500]">
              {t("uploadTickersTitle")}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <label htmlFor="doc">
              <div className="border py-4 border-dashed rounded-md text-center cursor-pointer hover:bg-gray-50 transition-colors">
                <div className="flex flex-col items-center justify-center">
                  <Paperclip className="mb-2" size={16} />
                  <p className="text-sm text-gray-700 font-medium">
                    {t("clickToUploadDocument")}
                  </p>
                  <p className="text-xs text-[#7F7F81] mt-1">
                    {t("tickerLimit")} 10
                  </p>
                </div>
              </div>
            </label>
            <input
              type="file"
              id="doc"
              accept=".csv"
              className="hidden"
              onChange={handleFileChange}
            />

            {/* Display selected file */}
            {file && (
              <div className="mt-3 space-y-2">
                <p className="text-xs text-gray-600 font-medium">
                  {t("selectedFile")}
                </p>
                <div className="bg-gray-50 p-2 rounded-md text-xs">
                  <div className="flex items-center justify-between">
                    <span className="truncate max-w-[250px]">{file.name}</span>
                    <button
                      onClick={removeFile}
                      className="text-gray-500 hover:text-red-500 transition-colors"
                    >
                      <X size={14} />
                    </button>
                  </div>

                  {/* Ticker count and validation status */}
                  <div className="mt-2">
                    {isValidating ? (
                      <span className="text-blue-600">{t("validating")}</span>
                    ) : validationError ? (
                      <span className="text-red-600">{validationError}</span>
                    ) : (
                      <span className="text-green-600"></span>
                    )}
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-center mt-3">
              <Button
                className="bg-gray-900 hover:bg-gray-800 text-white disabled:bg-gray-400"
                onClick={handleUpload}
                disabled={isUploadDisabled}
              >
                {isUploading ? t("uploadingButton") : t("uploadButton")}
              </Button>
            </div>

            <div className="flex justify-center mt-3">
              <a
                href="/upload_tickers_template.csv"
                className="text-[#0359D8] text-[11px]"
                download
              >
                {t("useCSVTemplate")}
              </a>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UploadTickers;
