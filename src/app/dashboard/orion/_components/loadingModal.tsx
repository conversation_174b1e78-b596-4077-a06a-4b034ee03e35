// @ts-nocheck
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import FadeLoader from "react-spinners/FadeLoader";
import { useTranslations } from "next-intl";

type LoadingModalProps = {
  isOpen: boolean;
  status: "generating" | "downloading" | "idle";
};

const LoadingModal = ({ isOpen, status }: LoadingModalProps) => {
  const t = useTranslations("DashboardOrion.LoadingModal");
  const statusText = {
    generating: t("generating"),
    downloading: t("downloadingReport"),
    idle: "",
  };

  return (
    <Dialog open={isOpen}>
      <DialogContent
        className="sm:max-w-md p-0 bg-white shadow-lg rounded-md [&>button]:hidden"
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <DialogTitle className="sr-only">{t("loadingDialogTitle")}</DialogTitle>
        <div className="flex flex-col items-center justify-center py-8 px-6">
          <div className="mb-4">
            <FadeLoader color="black" size={17} />
          </div>
          <p className="text-gray-700 text-center">{statusText[status]}</p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default LoadingModal;
