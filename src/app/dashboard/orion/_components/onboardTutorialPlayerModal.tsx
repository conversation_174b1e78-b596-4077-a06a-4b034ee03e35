"use client";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { useState, useEffect } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { getYouTubeVideoId } from "../_utils/libs";
import { videoLinks } from "../_utils/videoLinks";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

type Video = {
  title: string;
  url: string;
};

type OnboardTutorialPlayerModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

const OnboardTutorialPlayerModal = ({
  isOpen,
  onClose,
}: OnboardTutorialPlayerModalProps) => {
  const [step, setStep] = useState(0);
  const t = useTranslations("DashboardOrion.Onboard");

  // Reset step when modal is closed
  useEffect(() => {
    if (!isOpen) setStep(0);
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className={cn(
          "sm:max-w-[450px] p-0 bg-white shadow-lg rounded-md [&>button]:hidden",
          "fixed top-[40%] left-1/2 -translate-x-1/2 -translate-y-1/2",
          "data-[state=open]:animate-contentShow"
        )}
        onPointerDownOutside={(e) => e.preventDefault()}
        style={{
          maxHeight: "90vh",
          marginTop: "-10vh",
        }}
      >
        <DialogTitle className="sr-only">{t("orionTutorialTitle")}</DialogTitle>
        {step === 1 ? (
          <VideoSection onClose={onClose} t={t} />
        ) : (
          <WelcomeSection onClose={onClose} setStep={setStep} t={t} />
        )}
      </DialogContent>
    </Dialog>
  );
};

const WelcomeSection = ({
  onClose,
  setStep,
  t,
}: {
  onClose: () => void;
  setStep: (step: number) => void;
  t: ReturnType<typeof useTranslations>;
}) => (
  <div className="w-full">
    <div className="w-full h-[220px]">
      <Image
        src="/tutorial-onboard.png"
        width={600}
        className="rounded-md w-full h-full object-cover"
        height={600}
        alt="tutorial-onboard"
      />
    </div>

    <div className="p-4 w-full">
      <div className="text-[#0F172A]">{t("welcomeToOrion")}</div>
      <div className="text-[#737384] text-sm">{t("welcomeDescription")}</div>
      <div className="w-full mt-10 grid grid-cols-2 justify-between space-x-4">
        <Button onClick={() => setStep(1)} variant="outline">
          {t("watchHowToVideo")}
        </Button>
        <Button onClick={onClose} variant="default">
          {t("continueToDashboard")}
        </Button>
      </div>
    </div>
  </div>
);

const VideoSection = ({
  onClose,
  t,
}: {
  onClose: () => void;
  t: ReturnType<typeof useTranslations>;
}) => {
  const videos = videoLinks?.[0]?.videos || [];
  const [activeVideo, setActiveVideo] = useState(0);
  const videoId = getYouTubeVideoId(videos?.[activeVideo]?.url);
  const isLastVideo = videos?.length === activeVideo + 1;

  return (
    <div className="w-full">
      <div className="w-full h-[220px]">
        <iframe
          className="rounded-md"
          width="100%"
          height="100%"
          src={`https://www.youtube.com/embed/${videoId}`}
          title="YouTube video player"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        ></iframe>
      </div>

      <div className="p-4 w-full">
        <div className="text-[#0F172A]">{videos?.[activeVideo]?.title}</div>
        <div className="text-[#737384] my-2 text-sm">
          {t("welcomeDescription")}
        </div>
        <div className="w-full flex justify-between space-x-4">
          <Button
            onClick={() => {
              isLastVideo ? setActiveVideo((prev) => prev - 1) : onClose();
            }}
            variant="outline"
          >
            {isLastVideo ? t("previousVideo") : t("watchLater")}
          </Button>

          {isLastVideo ? (
            <Button onClick={onClose} variant="default">
              {t("continueDashboard")}
            </Button>
          ) : (
            <Button
              onClick={() => setActiveVideo((prev) => prev + 1)}
              variant="outline"
            >
              {t("nextVideo", { title: videos?.[activeVideo + 1]?.title })}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default OnboardTutorialPlayerModal;
