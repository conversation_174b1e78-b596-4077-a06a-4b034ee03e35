"use client"
import React, { useEffect } from "react";
import HeaderOrion from "./_components/headerOrion";
import HeaderModels from "../_components/HeaderModels";
import { useQueryClient } from "@tanstack/react-query";
import { orionAPI } from "@/services/api_hooks";

interface OrionDashboardLayoutProps {
  children: React.ReactNode;
}

const menuItems = [
  { name: "Home", href: "/dashboard/orion" },
  { name: "Reports", href: "/dashboard/orion/reports" },
];

const OrionDashboardLayout = ({ children }: OrionDashboardLayoutProps) => {
  const queryClient = useQueryClient();
  useEffect(() => {
    queryClient.prefetchQuery({
          queryKey: ["get-history"],
          queryFn: async () => {
            const res = await orionAPI.get("/report/history");
            return res.data;
          },
          staleTime: 5 * 60 * 1000, // 5 minutes
          gcTime: 60 * 60 * 1000, // 1 hour
        });
  })
  return (
    <div className=" ">
      <main className="min-h-screen">
        <HeaderOrion/>
        <div className="h-full">{children}</div>
      </main>
    </div>
  );
};

export default OrionDashboardLayout;
