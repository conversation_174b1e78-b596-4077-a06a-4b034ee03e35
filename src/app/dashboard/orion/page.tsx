"use client";
import React, { Suspense } from "react";
import ReportForm from "./_components/ReportForm";
import Image from "next/image";
import Link from "next/link";
import OnboardTutorialPlayerModal from "./_components/onboardTutorialPlayerModal";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";

const LoadingFallback = () => (
  <div className="flex justify-center items-center h-full">
    <div className="animate-pulse">Loading...</div>
  </div>
);

const OrionDashboardContent = () => {
  const params = useSearchParams();
  const tutorialStatus = params.get("show_tutorial") === "true" ? true : false;
  const router = useRouter();
  const t = useTranslations("DashboardOrion.Tutorials");

  return (
    <div className="relative flex flex-col justify-center items-center h-full py-[30px] md:mb-0 mb-14">
      <OnboardTutorialPlayerModal
        isOpen={tutorialStatus}
        onClose={() => {
          router.push("/dashboard/orion");
        }}
      />
      <ReportForm />
      <Link className="md:hidden block" href="/dashboard/orion/tutorials">
        <button className="p-2 bg-white border rounded-full flex items-center space-x-2 absolute md:bottom-0 -bottom-10 md:right-20 right-1/2 transform translate-x-1/2 md:translate-x-0 cursor-pointer">
          <Image
            src="/cam-recorder.svg"
            alt={"cam-recorder"}
            width={30}
            height={30}
            className="object-cover"
          />
          <span className="text-base">{t('watchTutorials')}</span>
        </button>
      </Link>
    </div>
  );
};

const OrionDashboard = () => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <OrionDashboardContent />
    </Suspense>
  );
};

export default OrionDashboard;
