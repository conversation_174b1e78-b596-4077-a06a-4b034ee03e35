import { api } from "@/services/api_hooks";
import React from "react";

function YedpayButton() {
  const handlePay = async () => {
    try {
      const { data } = await api.post("/api/payment/yedpay/pay", {
        currency: "HKD",
        amount: 30,
      });

      if (data.url) {
        window.location.href = data.url; // Redirect to payment URL
      } else {
        alert("Payment URL not received.");
      }
    } catch (error) {
      console.error("Error:", error);
      // alert('Payment error: ' + error.message);
    }
  };

  return (
    <div className="flex flex-col  text-xs">
      Test button
      <button
        onClick={handlePay}
        className="p-2 bg-blue-600 text-white rounded cursor-pointer"
      >
        Pay HKD 30
      </button>
    </div>
  );
}

export default YedpayButton;
