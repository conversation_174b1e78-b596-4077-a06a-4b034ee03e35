// @ts-nocheck

import React, { useState, FormEvent, KeyboardEvent, useRef } from "react";
import { useTranslations } from 'next-intl';
import { Textarea } from "./textarea";
import { But<PERSON> } from "./button";
import { Paperclip } from "lucide-react";

interface ChatBoxProps {
  onSendMessage: (text: string, attachments?: File[]) => void;
  onAttachmentsChange: (attachments: File[]) => void;
  loading: boolean
}

export default function ChatBox({
  onSendMessage,
  onAttachmentsChange,
  loading
}: ChatBoxProps) {

  // use next-intl for i18n
  const t = useTranslations("DashboardSupport.TicketForm");
  const tGlobal = useTranslations("global");

  const [input, setInput] = useState("");
  const [attachments, setAttachments] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const send = () => {
    if (!input.trim() && attachments.length === 0) return;
    onSendMessage(input, attachments.length > 0 ? attachments : undefined);
    setInput("");
    // Clear attachments from chat after sending
    setAttachments([]);
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    send();
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      send();
    }
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      const newFiles = Array.from(files).filter((file) => {
        const allowedTypes = [
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "image/jpeg",
          "image/png",
          "image/gif",
          "image/webp",
        ];
        return allowedTypes.includes(file.type);
      });

      const updatedAttachments = [...attachments, ...newFiles];
      setAttachments(updatedAttachments);
      onAttachmentsChange(updatedAttachments);
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const removeAttachment = (index: number) => {
    const updatedAttachments = attachments.filter((_, i) => i !== index);
    setAttachments(updatedAttachments);
    onAttachmentsChange(updatedAttachments);
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="shadow-sm w-full sticky bottom-2 bg-white pb-2 min-h-40 flex flex-col justify-between rounded-xl"
    >
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.webp"
        onChange={handleFileChange}
        className="hidden"
      />

      <Textarea
        placeholder={t("Chat.text")}
        aria-label={t("Chat.text")}
        className="py-4 h-[100px] px-6 shadow-none  hover:border-none border-none ring-0"
        value={input}
        onChange={(e) => setInput(e.target.value)}
        onKeyDown={handleKeyDown}
      />

      {/* Show attached files */}
      {attachments.length > 0 && (
        <div className="px-6 py-1">
          <div className="flex flex-wrap sm:flex-nowrap w-full sm:max-w-[500px] overflow-x-auto gap-2">
            {attachments.map((file, index) => (
              <div
                key={`${file.name}-${index}`}
                className="flex items-center gap-2 bg-gray-100 border rounded px-2 py-1 min-w-0"
              >
                <span className="text-sm text-gray-700 truncate max-w-32">
                  {file.name}
                </span>
                <button
                  type="button"
                  onClick={() => removeAttachment(index)}
                  className="text-gray-400 hover:text-red-500 text-xs"
                  title="Remove file"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="flex justify-between pt-4 items-center ">
        <div className="flex pl-2">
          <Button
            type="button"
            variant="ghost"
            className="flex items-center space-x-1"
            onClick={handleFileSelect}
          >
            <Paperclip size={12} />
            <span>{t("AddAttachment")}</span>
          </Button>
        </div>
        <Button
          type="submit"
          variant="default"
          aria-label="Send message"
          className="mr-4 bg-black text-white"
          disabled={loading}
        >
          {tGlobal("Send")}
        </Button>
      </div>
    </form>
  );
}
