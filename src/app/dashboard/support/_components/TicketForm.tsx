// @ts-nocheck

"use client";

import { useProfile } from "@/hooks/useProfile";
import { Loader, Paperclip } from "lucide-react";
import React, {
  ChangeEvent,
  FormEvent,
  useEffect,
  useRef,
  useState,
} from "react";
import { useTranslations } from 'next-intl';

interface TicketFormProps {
  initialValues?: {
    fullName: string;
    email: string;
    issueType: string;
    priority: string;
    subject: string;
    body: string;
  };
  mode: "auto" | "manual";
  onSubmitPayload: (payload: any) => void;
  submitting: boolean;
  sharedAttachments?: File[];
}

const issueTypes = [
  "Business & Partnerships",
  "Billing & Payments",
  "Using our Models",
  "Customization Requests",
  "Technical Issues & Bugs",
  "Account Help",
  "Media & Brand Requests",
  "Careers and Opportunities",
  "General Questions or Feedback",
  "Other or Private Concern",
];

const priorityLevels = [
  { label: "1", value: 1 },
  { label: "2", value: 2 },
  { label: "3", value: 3 },
  { label: "4", value: 4 },
  { label: "5", value: 5 },
];

export default function TicketForm({
  initialValues = {
    fullName: "",
    email: "",
    issueType: "",
    priority: "",
    subject: "",
    body: "",
  },
  mode,
  onSubmitPayload,
  submitting,
  sharedAttachments = [],
}: TicketFormProps) {

  // use next-intl for i18n
  const t = useTranslations("DashboardSupport.TicketForm");
  const tForm = useTranslations("Form");
  const tGlobal = useTranslations("global");

  const IssueTypesJSON = {
    "Business & Partnerships": "BusinessAndPartnerships",
    "Billing & Payments": "illingAndPayments",
    "Using our Models": "UsingOurModels",
    "Customization Requests": "CustomizationRequests",
    "Technical Issues & Bugs": "TechnicalIssues",
    "Account Help": "AccountHelp",
    "Media & Brand Requests": "Media",
    "Careers and Opportunities": "Careers",
    "General Questions or Feedback": "GeneralQuestions",
    "Other or Private Concern": "Other",
  }

  const { profile: user, isLoading, error, refetch } = useProfile();
  const [form, setForm] = useState({
    ...initialValues,
    fullName:
      `${user?.firstname || ""} ${user?.lastname || ""}` ||
      initialValues.fullName ||
      "",
    email: user?.email || initialValues.email || "",
  });

  // State for file attachments - initialize with shared attachments
  const [attachments, setAttachments] = useState<File[]>(sharedAttachments);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setAttachments(sharedAttachments);
  }, [sharedAttachments]);

  useEffect(() => {
    const updatedForm = {
      ...initialValues,
      fullName:
        `${user?.firstname || ""} ${user?.lastname || ""}` ||
        initialValues.fullName ||
        "",
      email: user?.email || initialValues.email || "",
      issueType: issueTypes.includes(initialValues.issueType)
        ? initialValues.issueType
        : "Other or Private Concern",
    };

    setForm((prev) => {
      const isSame = JSON.stringify(prev) === JSON.stringify(updatedForm);
      return isSame ? prev : updatedForm;
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, JSON.stringify(initialValues)]);

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: name === "priority" ? Number(value) : value,
    }));
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      const newFiles = Array.from(files).filter((file) => {
        const allowedTypes = [
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "video/mp4",
          "image/jpeg",
          "image/png",
          "image/gif",
          "image/webp",
        ];
        return allowedTypes.includes(file.type);
      });

      setAttachments((prev) => [...prev, ...newFiles]);
    }
    // Reset the input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes("pdf")) return "📄";
    if (fileType.includes("word") || fileType.includes("document")) return "📝";
    if (fileType.includes("video")) return "🎥";
    if (fileType.includes("image")) return "🖼️";
    return "📎";
  };

  const handleCancel = () => {
    setForm({
      fullName: `${user?.firstname || ""} ${user?.lastname || ""}` || "",
      email: user?.email || "",
      issueType: "",
      priority: "",
      subject: "",
      body: "",
    });
    setAttachments([]);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (attachments.length > 0) {
      // Create FormData when there are file attachments
      const formData = new FormData();

      // Add form fields
      formData.append("summary", form.body);
      formData.append("contact_email", form.email);
      formData.append("subject", form.subject);
      formData.append("priority", String(form.priority));
      formData.append("name", form.fullName);
      formData.append("type", form.issueType);
      formData.append("raise_human", String(mode === "auto"));

      if (user?.user_id) {
        formData.append("user_id", user.user_id);
      }

      // Add each file with the 'files' key (this creates an array on the server)
      attachments.forEach((file) => {
        formData.append("files", file);
      });

      for (const [key, value] of formData.entries()) {
        console.log(key, value);
      }

      // Pass FormData directly to parent
      onSubmitPayload(formData);
    } else {
      // No files - send regular JSON payload
      const payload = {
        summary: form.body,
        contact_email: form.email,
        subject: form.subject,
        priority: Number(form.priority),
        name: form.fullName,
        type: form.issueType,
        user_id: user?.user_id,
        raise_human: mode === "auto",
        files: [],
      };

      onSubmitPayload(payload);
    }

    if (mode === "manual") handleCancel();
  };

  return (
    <form
      className="bg-white rounded-lg md:max-h-[650px] shadow-md p-8 w-[100%] lg:w-[50%]"
      onSubmit={handleSubmit}
    >
      <h2 className="text-xl font-semibold mb-6">{t("title")}</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium mb-1">{t("FullName.text")}</label>
          <input
            type="text"
            name="fullName"
            value={form.fullName}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
            placeholder={t("FullName.description")}
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">
            {tForm("email.text")}
          </label>
          <input
            type="email"
            name="email"
            value={form.email}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
            placeholder="<EMAIL>"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">{t("IssueType.text")}</label>
          <select
            name="issueType"
            value={form.issueType}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
            required
          >
            <option value="">{t("IssueType.description")}</option>
            {issueTypes.map((type) => {
              const key = IssueTypesJSON[type];
              return (
              <option key={type}>{t(`IssueTypes.${key}`)}</option>)
            })}
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">
            {t("PriorityLevel.text")}
          </label>
          <select
            name="priority"
            value={form.priority}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
            required
          >
            <option value="">{t("PriorityLevel.description")}</option>
            {priorityLevels.map((level) => (
              <option key={level.value} value={level.value}>
                {level.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">{t("Subject.text")}</label>
        <input
          type="text"
          name="subject"
          value={form.subject}
          onChange={handleChange}
          className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
          placeholder={t("Subject.description")}
          required
        />
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">{t("Attachments")}</label>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.mp4,.jpg,.jpeg,.png,.gif,.webp"
          onChange={handleFileChange}
          className="hidden"
        />

        {/* Attachment container */}
        <div className=" flex items-center  border rounded-lg py-2 px-3 gap-3 overflow-x-auto max-w-[621px]">
          {/* Add Attachment Button */}
          <button
            type="button"
            onClick={handleFileSelect}
            className="flex items-center gap-2 whitespace-nowrap text-sm text-gray-600 rounded hover:bg-gray-50 cursor-pointer transition-colors"
          >
            <Paperclip size={12} />
            {t("AddAttachment")}
          </button>

          {/* Uploaded Files List */}
          {attachments.length > 0 && (
            <div className="flex flex-wrap sm:flex-nowrap w-full sm:max-w-[500px] overflow-x-auto gap-2">
              {attachments.map((file, index) => (
                <div
                  key={`${file.name}-${index}`}
                  className="flex items-center gap-2 bg-gray-100 border rounded px-2 py-1 min-w-0"
                >
                  <div className="flex items-center gap-2 min-w-0">
                    {/* <span className="text-lg flex-shrink-0">
          {getFileIcon(file.type)}
        </span> */}
                    <div className="min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {file.name}
                      </p>
                      {/* <p className="text-xs text-gray-500">
            {formatFileSize(file.size)}
          </p> */}
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeAttachment(index)}
                    className="flex-shrink-0 w-4 h-4 flex items-center justify-center text-gray-400 hover:text-red-500 transition-colors"
                    title={t("RemoveFile")}
                  >
                    ✕
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Helper text */}
          {/* <p className="text-xs text-gray-500 mt-2">
            Supported formats: PDF, DOC, DOCX, MP4, JPG, PNG, GIF, WEBP
          </p> */}
        </div>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">{tForm("message.text")}</label>
        <textarea
          name="body"
          value={form.body}
          onChange={handleChange}
          className="w-full border border-gray-300 rounded px-3 py-2 text-sm min-h-[120px]"
          placeholder={t("DescribeYourIssue")}
          required
        />
      </div>

      {/* Attachments Section */}

      <div className="flex justify-between">
        <button
          type="button"
          onClick={handleCancel}
          className="px-4 py-2 cursor-pointer border border-gray-300 rounded text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {tGlobal("Reset")}
        </button>
        <button
          type="submit"
          disabled={submitting}
          className="px-4 py-2 flex items-center gap-2 cursor-pointer bg-black text-white rounded hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {mode === "auto" ? tForm("Button.ConfirmTicket") : tForm("Button.Submit")}
          {submitting && <Loader className="animate-spin" />}
        </button>
      </div>
    </form>
  );
}
