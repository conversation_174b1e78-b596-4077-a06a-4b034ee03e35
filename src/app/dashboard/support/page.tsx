// @ts-nocheck

"use client";

import React, { useEffect, useRef, useState, useMemo } from "react";
import { useTranslations } from 'next-intl';
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { v4 as uuidv4 } from "uuid";
import { useProfile } from "@/hooks/useProfile";
import TicketForm, { TicketFormProps } from "./_components/TicketForm";
import { ArrowLeft, Loader } from "lucide-react";
import ChatBox from "./_components/ChatBox";
import { queryChatBot, useGetQueryYumi, yumiAPI } from "@/services/api_hooks";
import axios from "axios";
import Typed from "react-typed";
import { TypedBotMessage } from "@/components/TypingEffect/typedBotMessage";
import Link from "next/link";

type Message = {
  sender: "user" | "bot";
  text: string;
};

type ChatBotResponse = {
  text: string;
  usedTools: Array<{
    tool: string;
    toolInput: Record<string, any>;
    toolOutput: any;
  }>;
  question: string;
  chatId: string;
  chatMessageId: string;
  isStreamValid: boolean;
  sessionId: string;
  memoryType: string;
};

export default function SupportPage() {

  // use next-intl for i18n
  const t = useTranslations("DashboardSupport");
  const tGlobal = useTranslations("global");

  const router = useRouter();
  const { profile: user, isLoading, error, refetch } = useProfile();
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [submittingTicket, setSubmittingTicket] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [sharedAttachments, setSharedAttachments] = useState<File[]>([]);
  const [autoTicketData, setAutoTicketData] = useState<null | {
    summary: string;
    contact_email: string;
    subject: string;
    priority: number;
    name: string;
    type: string;
  }>(null);

  useEffect(() => {
    setSessionId(uuidv4());
  }, []);

  // const {
  //   data: outstandingTickets,
  //   isLoading: loadingOutstandingTickets,
  //   isFetching: fetchingOutstandingTickets,
  //   refetch: refetchOutstandingTickets,
  // } = useGetQueryYumi(
  //   `/api/ticket/outstanding-tickets?user_id=${user?.user_id}`,
  //   ["get-outstanding-tickets"],
  //   {
  //     onError() {
  //       toast.error("Could not load alerts");
  //     },
  //   }
  // );

  useEffect(() => {
    if (!sessionId || !user?.user_id) return;

    const storeChatLog = async () => {
      const payload = {
        sessionId: sessionId, // Generate it right here
        user_id: user.user_id,
      };
      try {
        const response = await yumiAPI.post("/api/user/store-chatlog", payload);
        console.log("chat log sent");
      } catch (error) {
        console.error("Error storing chat log:", error);
      }
    };

    storeChatLog();
  }, [user?.user_id, sessionId]);

  const handleSendMessage = async (text: string, attachments?: File[]) => {
    // Create user message with text and attachment info
    const messageText = text.trim();
    const attachmentInfo =
      attachments && attachments.length > 0
        ? " " +
          attachments
            .map((file) => {
              const name = file.name;
              return name.length > 25 ? name.substring(0, 25) + "..." : name;
            })
            .join(", ")
        : "";

    setMessages((prev) => [
      ...prev,
      {
        sender: "user",
        text:
          messageText +
          " " +
          (attachmentInfo ? `Attachment: [${attachmentInfo}]` : ""),
      },
    ]);
    setIsTyping(true);

    try {
      // Prepare uploads array if attachments exist
      const uploads = attachments
        ? await Promise.all(
            attachments.map(async (file) => {
              const isImage = file.type.startsWith("image/");
              const isPdfOrDoc =
                file.type.includes("pdf") ||
                file.type.includes("word") ||
                file.type.includes("document");

              if (isPdfOrDoc) {
                // For PDF/DOC files, use the /attachments/ API
                try {
                  const formData = new FormData();
                  formData.append("files", file);

                  const chatflowId = "6e40a41c-3b54-4474-ac89-03a306d34fc4";
                  const chatId = sessionId;

                  const response = await axios.post(
                    `https://yumi.up.railway.app/api/v1/attachments/${chatflowId}/${chatId}`,
                    formData,
                    {
                      headers: {
                        "Content-Type": "multipart/form-data",
                      },
                    }
                  );

                  // Extract content from response
                  const attachmentData = response.data[0]; // Assuming single file response
                  const content = attachmentData.content;

                  // Convert content text to base64
                  const base64Content = btoa(
                    unescape(encodeURIComponent(content))
                  );
                  const base64Data = `data:text/plain;base64,${base64Content}`;

                  return {
                    data: base64Data,
                    type: "file:full",
                    name: file.name,
                    mime: file.type,
                  };
                } catch (error) {
                  console.error("Error processing PDF/DOC file:", error);
                  // Fallback to original method if API fails
                  const base64Data = await new Promise<string>((resolve) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result as string);
                    reader.readAsDataURL(file);
                  });

                  return {
                    data: base64Data,
                    type: "file:full",
                    name: file.name,
                    mime: file.type,
                  };
                }
              } else if (isImage) {
                // For images, use the original method
                const base64Data = await new Promise<string>((resolve) => {
                  const reader = new FileReader();
                  reader.onload = () => resolve(reader.result as string);
                  reader.readAsDataURL(file);
                });

                return {
                  data: base64Data,
                  type: "file",
                  name: file.name,
                  mime: file.type,
                };
              } else {
                // For other file types, use the original method
                const base64Data = await new Promise<string>((resolve) => {
                  const reader = new FileReader();
                  reader.onload = () => resolve(reader.result as string);
                  reader.readAsDataURL(file);
                });

                return {
                  data: base64Data,
                  type: "file",
                  name: file.name,
                  mime: file.type,
                };
              }
            })
          )
        : undefined;

      // Generate contextual prompt based on attachments when no text is provided
      let defaultPrompt = "see this";
      if (!messageText && attachments && attachments.length > 0) {
        const fileTypes = attachments.map((file) => {
          if (file.type.startsWith("image/")) return "image";
          if (file.type.includes("pdf")) return "document";
          if (file.type.includes("word") || file.type.includes("document"))
            return "document";
          return "file";
        });

        const hasImages = fileTypes.includes("image");
        const hasDocuments = fileTypes.includes("document");

        if (hasImages && hasDocuments) {
          defaultPrompt =
            "Please analyze these files and help me understand their content.";
        } else if (hasImages) {
          defaultPrompt =
            fileTypes.length > 1 ? "What do you see?" : "What do you see?";
        } else if (hasDocuments) {
          defaultPrompt =
            fileTypes.length > 1 ? "summarize these." : "summarize this.";
        } else {
          defaultPrompt =
            fileTypes.length > 1 ? "analyze these " : " analyze this";
        }
      }

      const result: ChatBotResponse = await queryChatBot({
        question: messageText || defaultPrompt,
        uploads: uploads,
        overrideConfig: {
          vars: { userid: user?.user_id || "-1" },
          sessionId: sessionId || "",
        },
      });

      setMessages((prev) => [...prev, { sender: "bot", text: result.text }]);

      const intervention = Array.isArray(result.usedTools)
        ? result.usedTools.find((t) => t.tool === "Escalation")
        : undefined;

      if (intervention) {
        let out = intervention.toolOutput;
        if (typeof out === "string") {
          try {
            out = JSON.parse(out);
          } catch (e) {
            console.error("Failed to parse toolOutput:", e);
            out = null;
          }
        }
        if (out) {
          setAutoTicketData({
            summary: out.summary,
            contact_email: out.contact_email,
            subject: out.subject,
            priority: out.priority,
            name: out.name,
            type: out.type,
          });
        }
      }
    } catch (err) {
      console.log("ChatBot error:", err);
      if (
        err?.response?.data?.message?.includes(
          "400 This model's maximum context length"
        )
      ) {
        setMessages((prev) => [
          ...prev,
          {
            sender: "bot",
            text: t("messages.InputTooLarge"),
          },
        ]);
      } else {
        setMessages((prev) => [
          ...prev,
          { sender: "bot", text: t("messages.SomethingWentWrong") },
        ]);
      }
    } finally {
      setIsTyping(false);
    }
  };

  const handleSubmitTicket = async (
    payload: Parameters<TicketFormProps["onSubmitPayload"]>[0]
  ) => {
    setSubmittingTicket(true);

    try {
      let result;

      // If payload is FormData (i.e., you’re uploading files), use axios.post directly
      const response = await yumiAPI.post(
        "/api/ticket/create",
        payload,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
        // NOTE: No headers needed—axios sets the multipart boundary automatically.
      );

      setSubmittingTicket(false);
      console.log("Ticket created:", result);

      setMessages((m) => [
        ...m,
        { sender: "bot", text: t("messages.TicketCreated") },
      ]);
      setAutoTicketData(null);
    } catch (err: any) {
      setSubmittingTicket(false);

      // If axios received a non-2xx status, err.response will be defined
      if (err.response) {
        console.error("API Error:", err.response.data);
        // You could inspect err.response.status or err.response.data.message here
        alert(
          "Failed to create ticket: " +
            (err.response.data?.message || err.response.status)
        );
      } else {
        console.error("Error submitting ticket:", err);
        alert(t("FailedToCreateTicket"));
      }
    }
  };

  const autoTicketInitialValues = useMemo(() => {
    if (!autoTicketData) return undefined;
    return {
      fullName: autoTicketData.name,
      email: autoTicketData.contact_email,
      issueType: autoTicketData.type,
      priority: autoTicketData.priority.toString(),
      subject: autoTicketData.subject,
      body: autoTicketData.summary,
    };
  }, [autoTicketData]);

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  }, [messages, isTyping]);

  return (
    <div className=" w-full overflow-auto h-screen px-[5%] ">
      <div className="h-20  flex justify-between items-center ">
        <div
          className=" text-lg flex items-center gap-2 w-fit cursor-pointer"
          onClick={() => router.back()}
        >
          <button className="text-gray-600 hover:text-gray-900 pt-1 cursor-pointer">
            <ArrowLeft width={20} height={20} />
          </button>
          <div className="flex items-center gap-2">
            <span>|</span>
            {/* <span className=" font-medium">Need Help</span> */}
            <span className=" font-medium">{tGlobal("ai-wk")}</span>
          </div>
        </div>
         <Link href="/dashboard/support/ticket-history" className="text-[#1051E3] text-sm font-[600] cursor-pointer">
          {t("TicketHistory.text")}
        </Link>
      </div>

      {/* <div className="flex justify-end">
        <p className="bg-gray-100 p-2 text-sm w-fit rounded-sm flex items-center gap-2">
          Outstanding tickets:  {loadingOutstandingTickets ? <Loader className="animate-spin" size={13}/> :outstandingTickets?.length}
        </p>
      </div> */}

      <div className="flex flex-col lg:flex-row gap-10 mx-auto h-full  max-w-[1600px]  mt-5">
        {/* Left Panel */}
        <div className="max-h-[650px] w-[100%] lg:w-[50%] rounded-lg shadow-md">
          <div className="md:max-h-[600px] h-full   flex flex-col justify-between  overflow-hidden">
            {messages.length === 0 && (
              <div className="flex items-center justify-center flex-col px-6 py-4 pt-32 mb-8">
                <Image
                  src="/yumi.png"
                  alt="Yumi"
                  width={40}
                  height={40}
                  className="rounded-full"
                />
                <h1 className="text-2xl md:text-3xl font-bold text-center mt-4">
                  {t("title.text1")} <br />
                  <span className="bg-gradient-to-b from-[#0F172A] to-[#9CC4FF] bg-clip-text text-transparent">
                    {t("title.text2")}
                  </span>
                </h1>
                <p className="text-gray-500 mt-2">
                  {t("description")}
                </p>
              </div>
            )}

            {messages.length > 0 && (
              <div
                ref={chatContainerRef}
                className="flex flex-col flex-1 overflow-y-auto min-h-0 px-6 py-4 space-y-4"
              >
                {messages.map((msg, i) => (
                  <div
                    key={i}
                    className={`flex items-start space-x-3 ${
                      msg.sender === "user"
                        ? "flex-row-reverse space-x-reverse"
                        : ""
                    }`}
                  >
                    {/* Profile Picture - only show for bot messages */}
                    {msg.sender === "bot" && (
                      <Image
                        src="/yumi.png"
                        alt="Yumi"
                        width={40}
                        height={40}
                        className="rounded-full flex-shrink-0 mt-1"
                      />
                    )}

                    {/* Message Bubble */}
                    <div
                      className={`
              max-w-[80%] p-3 rounded-lg break-words
              ${
                msg.sender === "user"
                  ? "bg-indigo-100 text-gray-900"
                  : "bg-gray-100 text-gray-900"
              }
            `}
                    >
                      {msg.sender === "bot" ? (
                        <div className="prose prose-sm max-w-none">
                          {/* <ReactMarkdown remarkPlugins={[remarkGfm]}>
                            {msg.text}
                          </ReactMarkdown> */}
                          <TypedBotMessage text={msg.text} messageIndex={i} />
                        </div>
                      ) : (
                        <span>{msg.text}</span>
                      )}
                    </div>
                  </div>
                ))}

                {isTyping && (
                  <div className="flex items-start space-x-3">
                    <Image
                      src="/yumi.png"
                      alt="Yumi"
                      width={40}
                      height={40}
                      className="rounded-full flex-shrink-0 mt-1"
                    />
                    <div className="bg-gray-100 p-3 rounded-lg max-w-[60%]">
                      <div className="flex space-x-1 items-center">
                        <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce delay-100"></div>
                        <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce delay-200"></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className=" px-6 pb-1 pt-4 bottom-0">
              <ChatBox
                onSendMessage={handleSendMessage}
                onAttachmentsChange={setSharedAttachments}
                loading={isTyping}
              />
            </div>
          </div>
        </div>

        {/* Right Panel */}

        {autoTicketData ? (
          <TicketForm
            initialValues={autoTicketInitialValues}
            onSubmitPayload={handleSubmitTicket}
            mode="auto"
            submitting={submittingTicket}
            sharedAttachments={sharedAttachments}
          />
        ) : (
          <TicketForm
            onSubmitPayload={handleSubmitTicket}
            submitting={submittingTicket}
            mode="manual"
            sharedAttachments={sharedAttachments}
          />
        )}
      </div>
    </div>
  );
}
