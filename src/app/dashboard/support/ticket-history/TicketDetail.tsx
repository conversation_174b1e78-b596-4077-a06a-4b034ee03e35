import React from "react";
import { useTranslations } from 'next-intl';
import { format } from "date-fns";
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON><PERSON><PERSON>,
  Sheet<PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Download, Eye, Paperclip, X } from "lucide-react";
import { Button } from "../_components/button";

export interface TicketDetail {
  created_at: string;
  summary: string;
  type: string;
  source: string;
  raise_human: boolean;
  is_closed: boolean;
  updated_at: string;
  user_id: string;
  contact_email: string;
  idx: string;
  subject: string;
  priority: string;
  name: string;
  stk: string;
  additional_info: string | null;
  sessionid: string | null;
  attachment: string[];
  attachment_email: string | null;
  attachment_helpdesk: string | null;
}

interface TicketDetailSheetProps {
  isOpen: boolean;
  onClose: () => void;
  ticketDetail: TicketDetail | null;
  isLoading: boolean;
}

export const TicketDetailSheet: React.FC<TicketDetailSheetProps> = ({
  isOpen,
  onClose,
  ticketDetail,
  isLoading,
}) => {

  // use next-intl for i18n
  const t = useTranslations("DashboardSupport.TicketHistory");
  const tDetail = useTranslations("DashboardSupport.TicketHistory.TicketDetail");
  const tGlobal = useTranslations("global");

  const getStatusBadge = (isClosed: boolean) => {
    return isClosed ? (
      <Badge className="bg-green-100 text-green-800 hover:bg-green-100">{t("Resolved")}</Badge>
    ) : (
      <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">{t("Pending")}</Badge>
    );
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="max-w-[700px] h-[97%] overflow-y-auto rounded-xl m-2">
        <SheetHeader>
          <div className="flex items-center justify-between">
            <SheetTitle>ID: {ticketDetail?.idx || "Loading..."}</SheetTitle>
          </div>
        </SheetHeader>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : ticketDetail ? (
          <div className="mt-6 space-y-4">
            {/* Full Name */}
            <div>
              <label className="text-sm font-medium text-gray-500">
                {tDetail("FullName")}
              </label>
              <p className="mt-1 text-sm">{ticketDetail.name}</p>
            </div>

            {/* Email */}
            <div>
              <label className="text-sm font-medium text-gray-500">{tDetail("Email")}</label>
              <p className="mt-1 text-sm ">
                {ticketDetail.contact_email}
              </p>
            </div>

            {/* Issue */}
            <div>
              <label className="text-sm font-medium text-gray-500">{tDetail("Issue")}</label>
              <p className="mt-1 text-sm">{ticketDetail.type}</p>
            </div>

            {/* Priority */}
            <div>
              <label className="text-sm font-medium text-gray-500">
                {tDetail("Priority")}
              </label>
              <div className="mt-1">
                {ticketDetail.priority}
              </div>
            </div>

            {/* Status */}
            <div>
              <label className="text-sm font-medium text-gray-500">
                {tDetail("Status")}
              </label>
              <div className="mt-1">
                {getStatusBadge(ticketDetail.is_closed)}
              </div>
            </div>

            {/* Subject */}
            <div>
              <label className="text-sm font-medium text-gray-500">
                {tDetail("Subject")}
              </label>
              <p className="mt-1 text-sm">{ticketDetail.subject}</p>
            </div>

            <Separator />

            {/* Description */}
            <div>
              <label className="text-sm font-medium text-gray-500">
                {tDetail("Description")}
              </label>
              <div className="mt-1 text-sm text-gray-700 leading-relaxed">
                <p>{ticketDetail.summary}</p>
              </div>
            </div>

            {/* Attachments */}
            <div>
              <label className="text-sm font-medium text-gray-500">
                {tDetail("Attachments")}
              </label>
              <div className="mt-2">
                {ticketDetail.attachment &&
                ticketDetail.attachment.length > 0 ? (
                  <div className="space-y-2">
                    {ticketDetail.attachment.map((url, index) => {
                      const fileName =
                        url.split("/").pop() || `Attachment ${index + 1}`;
                      const fileExtension = fileName
                        .split(".")
                        .pop()
                        ?.toLowerCase();

                      return (
                        <div
                          key={index}
                          className="flex items-center justify-between gap-2  p-1 bg-gray-50 rounded-lg border"
                        >
                          <div className="flex items-center gap-2">
                            <Paperclip className="h-4 w-4 text-gray-500" />
                            {fileExtension && (
                              <Badge variant="outline" className="text-xs">
                                {fileExtension.toUpperCase()}
                              </Badge>
                            )}
                            <span className="text-sm font-medium truncate max-w-[100px]">
                              {fileName}
                            </span>
                          </div>
                          <div className="flex gap-1">
                            {/* <Button
                              size="sm"
                              variant="ghost"
                              className="h-8 w-8 p-0"
                              onClick={() => {
                                const link = document.createElement('a');
                                link.href = url;
                                link.download = fileName;
                                link.click();
                              }}
                            >
                              <Download className="h-4 w-4" />
                            </Button> */}
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-8 w-8 p-0"
                              onClick={() => window.open(url, "_blank")}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 italic">No attachments</p>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-64">
            <p className="text-gray-500">No ticket details available</p>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
};
