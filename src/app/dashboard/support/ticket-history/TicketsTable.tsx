import React from "react";
import { useTranslations } from 'next-intl';
import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CalendarIcon, ChevronLeftIcon, ChevronRightIcon } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

export interface Ticket {
  created_at: string;
  summary: string;
  type: string;
  source: string;
  raise_human: boolean;
  is_closed: boolean;
  updated_at: string;
  user_id: string;
  contact_email: string;
  idx: string;
  subject: string;
  priority: string;
  name: string;
  stk: string;
  additional_info: string | null;
  sessionid: string | null;
  attachment: any[];
  attachment_email: string | null;
  attachment_helpdesk: string | null;
}

interface TicketsTableProps {
  tickets: Ticket[];
  isLoading: boolean;
  onTicketClick: (stk: string) => void;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export const TicketsTable: React.FC<TicketsTableProps> = ({
  tickets,
  isLoading,
  onTicketClick,
  pagination,
}) => {

  // use next-intl for i18n
  const t = useTranslations("DashboardSupport.TicketHistory");
  const tTable = useTranslations("Table");

  const getStatusBadge = (isClosed: boolean) => {
    return isClosed ? (
      <Badge className="bg-green-100 text-green-800 hover:bg-green-100">{t("Resolved")}</Badge>
    ) : (
      <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">{t("Pending")}</Badge>
    );
  };

  return (
    <div className="w-full mt-[30px]">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold">{t("title")}</h2>
        <div></div>
        {/* <div className="flex items-center gap-4">
          <Button variant="outline" size="sm">
            <CalendarIcon className="h-4 w-4 mr-2" />
            Choose date range
          </Button>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Status:</span>
            <select className="text-sm border rounded px-2 py-1">
              <option>All</option>
              <option>Pending</option>
              <option>Resolved</option>
            </select>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Priority:</span>
            <select className="text-sm border rounded px-2 py-1">
              <option>All</option>
              <option>High</option>
              <option>Medium</option>
              <option>Low</option>
            </select>
          </div>
        </div> */}
      </div>

      {/* Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("tableHeader.ID")}</TableHead>
              <TableHead>{t("tableHeader.Date")}</TableHead>
              <TableHead>{t("tableHeader.IssueType")}</TableHead>
              <TableHead>{t("tableHeader.Status")}</TableHead>
              <TableHead>{t("tableHeader.PriorityLevel")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <>
                {Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Skeleton className="h-4 w-12" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-24" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-32" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-6 w-20" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-6 w-16" />
                    </TableCell>
                  </TableRow>
                ))}
              </>
            ) : (
              <>
                {tickets.map((ticket) => (
                  <TableRow
                    key={ticket.stk}
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => onTicketClick(ticket.stk)}
                  >
                    <TableCell className="font-medium">{ticket.stk}</TableCell>
                    <TableCell>
                      {format(new Date(ticket.created_at), "yy/MM/dd, HH:mm")}
                    </TableCell>
                    <TableCell>{ticket.type}</TableCell>
                    <TableCell>{getStatusBadge(ticket.is_closed)}</TableCell>
                    <TableCell>{ticket.priority}</TableCell>
                  </TableRow>
                ))}
              </>
            )}
          </TableBody>
        </Table>
        {tickets?.length === 0 && !isLoading && <div className="text-gray-500 text-center flex justify-center h-20 items-center">{t("NoResultsFound")}</div>}
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between mt-4">
        <p className="text-sm text-gray-600">
          {tTable("Page")} {pagination?.page || 1} {tTable("of")} {pagination?.totalPages || 1}
        </p>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            disabled={!pagination || pagination.page <= 1}
          >
            <ChevronLeftIcon className="h-4 w-4" />
            {tTable("Previous")}
          </Button>
          <Button
            variant="outline"
            size="sm"
            disabled={!pagination || pagination.page >= pagination.totalPages}
          >
            {tTable("Next")}
            <ChevronRightIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};
