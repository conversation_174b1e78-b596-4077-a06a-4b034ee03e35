"use client";

import { useProfile } from "@/hooks/useProfile";
import { useGetQueryYumi } from "@/services/api_hooks";
import { useState } from "react";
import { useTranslations } from 'next-intl';
import { toast } from "sonner";
import { TicketsTable } from "./TicketsTable";
import { TicketDetailSheet } from "./TicketDetail";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface TicketsPageProps {
  user: {
    user_id: string;
  };
}

export default function TicketsPage() {

  // use next-intl for i18n
  const t = useTranslations("DashboardSupport.TicketHistory");
  const tGlobal = useTranslations("global");

  const [selectedTicketStk, setSelectedTicketStk] = useState<string | null>(
    null
  );
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const { profile: user, isLoading, error, refetch } = useProfile();
  const router = useRouter();

  // Fetch tickets list
  const {
    data: ticketsResponse,
    isLoading: loadingTickets,
    isFetching: fetchingTickets,
    refetch: refetchTickets,
  } = useGetQueryYumi(
    `/api/ticket/user-tickets?user_id=${user?.user_id}`,
    ["get-user-tickets"],
    {
      //   enabled: user ? true : false,
      onError() {
        toast.error(t("toast.notLoadTickets"));
      },
    }
  );

  // Fetch ticket details
  const {
    data: ticketDetailResponse,
    isLoading: loadingTicketDetail,
    isFetching: fetchingTicketDetail,
  } = useGetQueryYumi(
    `/api/ticket/ticket-details?stk=${selectedTicketStk}`,
    ["get-ticket-detail", selectedTicketStk],
    {
      enabled: !!selectedTicketStk,
      onError() {
        toast.error(t("toast.notLoadTicketDetails"));
      },
    }
  );

  const handleTicketClick = (stk: string) => {
    setSelectedTicketStk(stk);
    setIsSheetOpen(true);
  };

  const handleCloseSheet = () => {
    setIsSheetOpen(false);
    setSelectedTicketStk(null);
  };

  return (
    <div className="w-full overflow-auto  min-h-screen px-[5%] mx-auto max-w-[1600px]">
      <div className="h-20  mb-5 flex justify-between items-center ">
        <div
          className=" text-lg flex items-center gap-2 w-fit cursor-pointer"
          onClick={() => router.back()}
        >
          <button className="text-gray-600 hover:text-gray-900 pt-1 cursor-pointer">
            <ArrowLeft width={20} height={20} />
          </button>
          <div className="flex items-center gap-2">
            <span>|</span>
            {/* <span className=" font-medium">Need Help</span> */}
            <span className=" font-medium">{tGlobal("ai-wk")}</span>
          </div>
        </div>
      </div>
      <TicketsTable
        tickets={ticketsResponse?.tickets || []}
        isLoading={loadingTickets}
        onTicketClick={handleTicketClick}
        pagination={
          ticketsResponse
            ? {
                page: ticketsResponse.page,
                limit: ticketsResponse.limit,
                total: ticketsResponse.total,
                totalPages: ticketsResponse.totalPages,
              }
            : undefined
        }
      />

      <TicketDetailSheet
        isOpen={isSheetOpen}
        onClose={handleCloseSheet}
        ticketDetail={ticketDetailResponse?.[0] || null}
        isLoading={loadingTicketDetail}
      />
    </div>
  );
}
