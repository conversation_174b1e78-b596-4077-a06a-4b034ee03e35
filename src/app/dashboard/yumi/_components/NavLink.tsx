"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation"; // Use this for App Router
import { cn } from "@/lib/utils";

type NavLinkProps = {
  href: string;
  icon: React.ReactNode;
  name: string;
  collapsed: boolean;
  className?: string;
  disabled?: boolean; // Add this
};

export default function NavLink({
  href,
  icon,
  name,
  collapsed,
  className,
  disabled = false,
}: NavLinkProps) {
  const pathname = usePathname();

  // Fix: Only exact match for root <PERSON><PERSON>, nested for others
  const isRootYumi = href === "/dashboard/yumi";
  const isActive = isRootYumi
    ? pathname === href
    : pathname === href || pathname.startsWith(`${href}/`);

  return (
    <Link
      href={disabled ? "#" : href}
      tabIndex={disabled ? -1 : 0}
      aria-disabled={disabled}
      className={cn(
        "flex gap-2.5 items-center px-4 py-[7px] text-sm rounded-xl hover:bg-[#fafafa] text-gray-500 transition-colors",
        !collapsed ? " px-4" : "justify-center",
        isActive && "text-black bg-[#fafafa] font-medium",
        disabled && "opacity-50 pointer-events-none cursor-not-allowed",
        className
      )}
    >
      {icon}
      {!collapsed && name}
    </Link>
  );
}
