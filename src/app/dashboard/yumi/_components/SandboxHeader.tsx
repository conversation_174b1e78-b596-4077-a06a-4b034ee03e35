"use client";

import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { FC, useState, useEffect } from "react";
import { LogOut, Send, Menu, User, ChevronDown } from "lucide-react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  Sheet<PERSON>itle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useTestUserContext } from "../_context/TestUserContext";
import { useTranslations } from "next-intl";
import {
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import LocaleButtons from "@/components/localeButtons";

// Define prop types
interface MenuItem {
  name: string;
  href: string;
  icon: React.ReactNode;
  disabled: boolean;
}

interface HeaderProps {
  avatarUrl: string;
  userName: string;
  menuItems?: MenuItem[];
}

const SandboxHeader: FC<HeaderProps> = ({ avatarUrl, userName, menuItems }) => {
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const router = useRouter();
  const { testUsers, fakeUserId, setFakeUserId, selectedTestUser } =
    useTestUserContext();
  const t = useTranslations("DashboardYumiSandbox.SandboxHeader");

  const handleClick = (e: any, href: string) => {
    if (pathname === href) {
      e.preventDefault(); // Prevent default navigation
      window.location.href = href;
    }
  };

  // Close mobile menu when clicking on a link
  const handleMobileMenuClick = (e: any, href: string) => {
    setMobileMenuOpen(false);
    handleClick(e, href);
  };

  // Close mobile menu when pathname changes
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [pathname]);

  return (
    <div className="px-[5%] py-3 bg-white shadow-sm sticky top-0 z-40">
      <nav className="flex items-center justify-between mx-auto max-w-[1400px]">
        {/* Left section - User Info */}
        <div className="flex items-center space-x-4">
          {/* Mobile menu button on the left */}
          <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
            <SheetTrigger asChild>
              <button
                className="md:hidden p-2 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-gray-500"
                aria-label="Toggle menu"
              >
                <Menu className="h-6 w-6" />
              </button>
            </SheetTrigger>

            <SheetContent side="left" className="w-full max-w-sm">
              <SheetHeader className="flex flex-row items-center justify-between">
                <SheetTitle className="flex items-center space-x-2 text-left">
                  <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200">
                    <Image
                      src={avatarUrl}
                      alt={userName}
                      width={32}
                      height={32}
                      className="object-cover"
                    />
                  </div>
                  <span className="font-medium text-gray-800">{userName}</span>
                </SheetTitle>
                <LocaleButtons />
              </SheetHeader>

              <div className="flex flex-col h-full pt-6">
                {/* Navigation Links */}
                <div className="flex-1 space-y-4">
                  {menuItems?.map((item) => (
                    <Link
                      key={item.name}
                      href={item.disabled ? "#" : item.href}
                      onClick={(e) => {
                        if (item.disabled) {
                          e.preventDefault();
                          return;
                        }
                        handleMobileMenuClick(e, item.href);
                      }}
                      aria-disabled={item.disabled}
                      tabIndex={item.disabled ? -1 : 0}
                      className={`flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                        pathname === item.href
                          ? "text-black bg-gray-100"
                          : "text-gray-700 hover:text-gray-900 hover:bg-gray-50"
                      } ${
                        item.disabled
                          ? "opacity-50 pointer-events-none cursor-not-allowed"
                          : ""
                      }`}
                    >
                      {item.icon && <span className="mr-2">{item.icon}</span>}
                      {item.name}
                    </Link>
                  ))}

                  {/* Return to ai-wk - Now in mobile menu */}
                  <Link
                    href="/dashboard/home"
                    className="flex items-center gap-2 justify-center text-sm py-2 px-4 rounded-md bg-white text-[#0F172A] border border-[#0F172A] hover:bg-gray-50 transition-colors duration-200 w-full mt-6"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {t("returnToAiWk")}
                    <LogOut size={14} />
                  </Link>
                </div>

                {/* Bottom Actions */}
                <div className="border-t border-gray-200 pt-4 mt-6 space-y-4">
                  {/* Help button */}
                  <Link
                    href="/dashboard/support"
                    className="flex items-center justify-center text-sm text-gray-500 hover:text-gray-900 gap-2  py-2"
                  >
                    <Image
                      src="/help-icon.svg"
                      alt="help icon"
                      height={20}
                      width={20}
                    />
                    {t("needHelp")}
                  </Link>
                </div>
              </div>
            </SheetContent>
          </Sheet>

          {/* User avatar and name */}
          <div className="flex md:hidden items-center space-x-2">
            <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200">
              <Image
                src={avatarUrl}
                alt={userName}
                width={32}
                height={32}
                className="object-cover"
              />
            </div>
            <span className="font-medium text-gray-800">{userName}</span>
            <div className="">
              {pathname === "/dashboard/yumi" && (
                <Popover>
                  <PopoverTrigger asChild>
                    <button className="flex items-center gap-1 px-[6px] py-[4px] rounded-sm bg-[#4691FF1A] hover:bg-[#4691FF2A] text-[#0359D8] text-[14px] cursor-pointer">
                      {t("testUser")} {selectedTestUser?.firstname}
                      <ChevronDown size={14} />
                    </button>
                  </PopoverTrigger>
                  <PopoverContent align="end" className="w-full p-0">
                    <div className="flex flex-col gap-1 mb-2">
                      {testUsers.map((u) => (
                        <button
                          key={u.user_id}
                          type="button"
                          className="text-left px-4 py-2 rounded hover:bg-gray-100 text-sm"
                          onClick={() => setFakeUserId(u.user_id)}
                        >
                          {t("testUser")} {u.firstname}
                        </button>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>
              )}
            </div>
          </div>
          <div className="hidden md:flex">
            {pathname === "/dashboard/yumi" && (
              <Popover>
                <PopoverTrigger asChild>
                  <button className="flex items-center gap-1 px-[6px] py-[4px] rounded-sm bg-[#4691FF1A] hover:bg-[#4691FF2A] text-[#0359D8] text-[14px] cursor-pointer">
                    <User size={14} />
                    {t("testUser")} {selectedTestUser?.firstname}{" "}
                    {selectedTestUser?.lastname}
                    <ChevronDown size={14} />
                  </button>
                </PopoverTrigger>
                <PopoverContent align="end" className="w-full p-0">
                  <div className="flex flex-col gap-1 mb-2">
                    {testUsers.map((u) => (
                      <button
                        key={u.user_id}
                        type="button"
                        className="text-left px-4 py-2 rounded hover:bg-gray-100 text-sm"
                        onClick={() => setFakeUserId(u.user_id)}
                      >
                        {t("testUser")} {u.firstname} {u.lastname}
                      </button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>
            )}
          </div>
        </div>

        {/* Right section - Desktop */}
        <div className="hidden md:flex items-center space-x-2">
          <TooltipProvider delayDuration={0}>
            <LocaleButtons />
            <Tooltip>
              <TooltipTrigger asChild>
                <Link
                  href="/dashboard/home"
                  className="hidden md:flex items-center gap-2 justify-between text-sm p-[4.5px] rounded-full bg-gray-500 text-white border border-gray-500 hover:bg-gray-700 transition-colors duration-200"
                >
                  <LogOut size={14} />
                </Link>
              </TooltipTrigger>
              <TooltipContent>{t("returnToAiWk")}</TooltipContent>
            </Tooltip>

            {/* Help button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Link
                  href="/dashboard/support"
                  className="flex items-center justify-center text-sm text-[#0F172A] hover:text-gray-900"
                >
                  <Image
                    src="/help-icon.svg"
                    alt="help icon"
                    height={26}
                    width={26}
                  />
                </Link>
              </TooltipTrigger>
              <TooltipPortal>
                <TooltipContent>{t("needHelp")}</TooltipContent>
              </TooltipPortal>
            </Tooltip>
          </TooltipProvider>
        </div>
      </nav>
    </div>
  );
};

export default SandboxHeader;
