"use client";

import Image from "next/image";
import Link from "next/link";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader, LogOut } from "lucide-react";
import { useTranslations } from "next-intl";

export default function SandboxSubscribeModal({
  open,
  onClose,
  onSubscribe,
  loading,
  disclaimer,
  setDisclaimer,
}: {
  open: boolean;
  onClose: () => void;
  onSubscribe: () => void;
  loading?: boolean;
  disclaimer: boolean;
  setDisclaimer: (v: boolean) => void;
}) {
  const t = useTranslations("DashboardYumiSandbox.SandboxSubscribeModal");
  return (
    <Dialog open={open} onOpenChange={() => {}}>
      <DialogContent
        hideClose
        className="sm:max-w-[447px] p-0 overflow-hidden bg-white rounded-lg"
        onInteractOutside={(e) => e.preventDefault()} // Prevent closing on outside click
        onEscapeKeyDown={(e) => e.preventDefault()} // Prevent closing with Escape
      >
        <div className="w-full h-[212px] bg-gradient-to-t from-blue-300 to-[#FFFFFF] flex items-end justify-center">
          <Image
            src="/concierge.svg"
            alt="HermesX Logo"
            width={135}
            height={259}
            priority
            className="object-cover h-40 w-40"
          />
        </div>

        <div className="px-6 mb-1">
          <DialogHeader>
            <DialogTitle className="text-[16px] font-[500] ">
              {t("title")}
            </DialogTitle>
            <DialogDescription>
              <span className="text-start text-[#737384] text-xs leading-[20px] mt-2">
                {t("description")}
              </span>
            </DialogDescription>
          </DialogHeader>
        </div>

        <div className="flex items-center justify-between gap-4 px-6">
          <div
            className={`border rounded-md p-2 flex items-center w-full justify-between cursor-pointer`}
          >
            <div className="grid">
              <span className="text-[16px] font-[500] text-black">
                {t("thirtyCredits")}
              </span>
              <span className="text-xs font-[300] text-[#737384]">
                {t("forThreeDays")}
              </span>
            </div>
            <Image
              src="/dollar_icon.svg"
              alt="HermesX Logo"
              width={28}
              height={53}
              priority
              className="object-contain"
            />
          </div>
        </div>

        <div className="flex items-center space-x-2 px-6 text-[12px]">
          <Checkbox
            checked={disclaimer}
            onCheckedChange={(checked) => setDisclaimer(checked === true)}
          />
          <div>
            {t("agreementPrefix")}{" "}
            <Link
              href="/terms of use"
              target="_blank"
              className="text-blue-600 underline"
            >
              {t("termsOfUse")}
            </Link>{" "}
            {t("and")}{" "}
            <Link
              href="/disclaimer"
              target="_blank"
              className="text-blue-600 underline"
            >
              {t("disclaimer")}
            </Link>
          </div>
        </div>

        <DialogFooter className="px-6 pb-6 sm:pb-6">
          <div className="flex items-center justify-between w-full gap-4">
            <Button
              className="min-w-fit text-black rounded-md"
              onClick={onClose}
              variant="outline"
            >
              {t("returnToAiWk")} <LogOut />
            </Button>
            <Button
              className="bg-gray-900 hover:bg-gray-800 text-white rounded-md"
              onClick={onSubscribe}
              disabled={!disclaimer || loading}
            >
              {t("agreeAndContinue")}
              {loading && <Loader className="animate-spin" />}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
