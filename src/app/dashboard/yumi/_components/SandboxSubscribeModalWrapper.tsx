// Place this above your HomePage component or in a separate file
import { useState } from "react";
import { useSubmitQuery } from "@/services/api_hooks";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import SandboxSubscribeModal from "./SandboxSubscribeModal";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

function SandboxSubscribeModalWrapper({
  model,
  onClose,
}: {
  model: any;
  onClose?: () => void;
}) {
  const [open, setOpen] = useState(true);
  const [loading, setLoading] = useState(false);
  const [disclaimer, setDisclaimer] = useState(false);
  const router = useRouter();
  const queryClient = useQueryClient();
  const t = useTranslations(
    "DashboardYumiSandbox.SandboxSubscribeModalWrapper"
  );

  // Track if user has successfully subscribed
  const [subscribed, setSubscribed] = useState(false);

  const { mutateAsync: subscribe, isPending: loadingPending } = useSubmitQuery(
    "/api/subscribe/subscribe-to-yumi-sandbox",
    "POST",
    {
      onSuccess(response: any) {
        toast.success(t("success"), {
          position: "top-right",
          className: "p-4",
        });
        queryClient.invalidateQueries({
          queryKey: ["get-sandbox-subscription-status"],
        });
        setSubscribed(true);
        onClose?.();
      },
      onError(err: any) {
        toast.error(err?.response?.data?.error || t("subscriptionFailed"), {
          position: "top-right",
          className: "p-4",
        });
      },
    }
  );

  const handleSubscribe = async () => {
    setLoading(true);
    try {
      await subscribe({});
      setOpen(false);
    } catch (e) {
      // error handled in onError
    } finally {
      setLoading(false);
    }
  };

  // Redirect to /dashboard/home if closed without subscribing
  const handleClose = () => {
    setOpen(false);
    onClose?.();
    if (!subscribed) {
      router.push("/dashboard/home");
    }
  };

  return (
    <SandboxSubscribeModal
      open={open}
      onClose={handleClose}
      onSubscribe={handleSubscribe}
      loading={loading || loadingPending}
      disclaimer={disclaimer}
      setDisclaimer={setDisclaimer}
    />
  );
}

export default SandboxSubscribeModalWrapper;
