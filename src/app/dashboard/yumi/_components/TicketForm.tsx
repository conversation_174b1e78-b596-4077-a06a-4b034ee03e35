// @ts-nocheck

"use client";

import { Input } from "@/components/ui/input";
import { useProfile } from "@/hooks/useProfile";
import { Loader, Paperclip } from "lucide-react";
import React, {
  ChangeEvent,
  FormEvent,
  useEffect,
  useRef,
  useState,
} from "react";
import { useTranslations } from "next-intl";

interface TicketFormProps {
  initialValues?: {
    fullName: string;
    email: string;
    issueType: string;
    priority: string;
    subject: string;
    body: string;
    fakeUserId: string;
    sandboxId?: string;
  };
  mode: "auto" | "manual";
  onSubmitPayload: (payload: any) => void;
  submitting: boolean;
  sharedAttachments?: File[];
}

const issueTypes = (t: (key: string) => string) => [
  t("issueTypeList.GI"),
  t("issueTypeList.PSQ"),
  t("issueTypeList.OTI"),
  t("issueTypeList.BPQ"),
  t("issueTypeList.AAH"),
  t("issueTypeList.TSE"),
  t("issueTypeList.FS"),
  t("issueTypeList.PSC"),
];

const priorityLevels = [
  { label: "1", value: 1 },
  { label: "2", value: 2 },
  { label: "3", value: 3 },
  { label: "4", value: 4 },
  { label: "5", value: 5 },
];

export default function TicketForm({
  initialValues = {
    fullName: "",
    email: "",
    issueType: "",
    priority: "",
    subject: "",
    body: "",
  },
  mode,
  onSubmitPayload,
  submitting,
  sharedAttachments = [],
  fakeUserId,
  sandboxId,
}: TicketFormProps) {
  const { profile: user, isLoading, error, refetch } = useProfile();
  const [form, setForm] = useState({
    ...initialValues,
  });
  const t = useTranslations("DashboardYumiSandbox.TicketForm");

  // State for file attachments - initialize with shared attachments
  const [attachments, setAttachments] = useState<File[]>(sharedAttachments);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setAttachments(sharedAttachments);
  }, [sharedAttachments]);

  useEffect(() => {
    const updatedForm = {
      ...initialValues,
      issueType: issueTypes(t).includes(initialValues.issueType)
        ? initialValues.issueType
        : "Private or Sensitive Concerns",
    };

    setForm((prev) => {
      const isSame = JSON.stringify(prev) === JSON.stringify(updatedForm);
      return isSame ? prev : updatedForm;
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(initialValues)]);

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: name === "priority" ? Number(value) : value,
    }));
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      const newFiles = Array.from(files).filter((file) => {
        const allowedTypes = [
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "video/mp4",
          "image/jpeg",
          "image/png",
          "image/gif",
          "image/webp",
        ];
        return allowedTypes.includes(file.type);
      });

      setAttachments((prev) => [...prev, ...newFiles]);
    }
    // Reset the input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes("pdf")) return "📄";
    if (fileType.includes("word") || fileType.includes("document")) return "📝";
    if (fileType.includes("video")) return "🎥";
    if (fileType.includes("image")) return "🖼️";
    return "📎";
  };

  const handleCancel = () => {
    setForm({
      ...initialValues,
    });
    setAttachments([]);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (attachments.length > 0) {
      // Create FormData when there are file attachments
      const formData = new FormData();

      // Add form fields
      formData.append("summary", form.body);
      formData.append("contact_email", form.email);
      formData.append("subject", form.subject);
      formData.append("priority", String(form.priority));
      formData.append("name", form.fullName);
      formData.append("type", form.issueType);
      formData.append("raise_human", String(mode === "auto"));

      if (fakeUserId) {
        formData.append("fake_user_id", fakeUserId);
      }
      if (sandboxId) {
        formData.append("sandbox_id", sandboxId);
      }

      // Add each file with the 'files' key (this creates an array on the server)
      attachments.forEach((file) => {
        formData.append("attachments", file);
      });

      for (const [key, value] of formData.entries()) {
        console.log(key, value);
      }

      // Pass FormData directly to parent
      onSubmitPayload(formData);
    } else {
      const payload = {
        summary: form.body,
        contact_email: form.email,
        subject: form.subject,
        priority: Number(form.priority),
        name: form.fullName,
        type: form.issueType,
        fake_user_id: fakeUserId,
        sandbox_id: sandboxId,
        raise_human: mode === "auto",
        attachments: [],
      };

      onSubmitPayload(payload);
    }

    if (mode === "manual") handleCancel();
  };

  return (
    <form
      className="bg-white rounded-lg md:max-h-[650px] shadow-md p-8 w-[100%] lg:w-[50%]"
      onSubmit={handleSubmit}
    >
      <h2 className="text-xl font-semibold mb-6">{t("title")}</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium mb-1">
            {t("fullName")}
          </label>
          <Input
            type="text"
            name="fullName"
            value={form.fullName}
            disabled
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
            placeholder={t("yourName")}
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">
            {t("emailAddress")}
          </label>
          <Input
            type="email"
            name="email"
            value={form.email}
            disabled
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
            placeholder={t("emailPlaceholder")}
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">
            {t("issueType")}
          </label>
          <select
            name="issueType"
            value={form.issueType}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
            required
          >
            <option value="">{t("selectIssueType")}</option>
            {issueTypes(t).map((type) => (
              <option key={type}>{type}</option>
            ))}
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">
            {t("priorityLevel")}
          </label>
          <select
            name="priority"
            value={form.priority}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
            required
          >
            <option value="">{t("selectPriorityLevel")}</option>
            {priorityLevels.map((level) => (
              <option key={level.value} value={level.value}>
                {level.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">{t("subject")}</label>
        <input
          type="text"
          name="subject"
          value={form.subject}
          onChange={handleChange}
          className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
          placeholder={t("subjectPlaceholder")}
          required
        />
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">
          {t("attachments")}
        </label>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.mp4,.jpg,.jpeg,.png,.gif,.webp"
          onChange={handleFileChange}
          className="hidden"
        />

        {/* Attachment container */}
        <div className=" flex items-center  border rounded-lg py-2 px-3 gap-3 overflow-x-auto max-w-[621px]">
          {/* Add Attachment Button */}
          <button
            type="button"
            onClick={handleFileSelect}
            className="flex items-center gap-2 whitespace-nowrap text-sm text-gray-600 rounded hover:bg-gray-50 cursor-pointer transition-colors"
          >
            <Paperclip size={12} />
            {t("addAttachment")}
          </button>

          {/* Uploaded Files List */}
          {attachments.length > 0 && (
            <div className="flex flex-wrap sm:flex-nowrap w-full sm:max-w-[500px] overflow-x-auto gap-2">
              {attachments.map((file, index) => (
                <div
                  key={`${file.name}-${index}`}
                  className="flex items-center gap-2 bg-gray-100 border rounded px-2 py-1 min-w-0"
                >
                  <div className="flex items-center gap-2 min-w-0">
                    {/* <span className="text-lg flex-shrink-0">
          {getFileIcon(file.type)}
        </span> */}
                    <div className="min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {file.name}
                      </p>
                      {/* <p className="text-xs text-gray-500">
            {formatFileSize(file.size)}
          </p> */}
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeAttachment(index)}
                    className="flex-shrink-0 w-4 h-4 flex items-center justify-center text-gray-400 hover:text-red-500 transition-colors"
                    title={t("removeFile")}
                  >
                    ✕
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Helper text */}
          {/* <p className="text-xs text-gray-500 mt-2">
            Supported formats: PDF, DOC, DOCX, MP4, JPG, PNG, GIF, WEBP
          </p> */}
        </div>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">{t("message")}</label>
        <textarea
          name="body"
          value={form.body}
          onChange={handleChange}
          className="w-full border border-gray-300 rounded px-3 py-2 text-sm min-h-[120px]"
          placeholder={t("describeYourIssue")}
          required
        />
      </div>

      {/* Attachments Section */}

      <div className="flex justify-between">
        <button
          type="button"
          onClick={handleCancel}
          className="px-4 py-2 cursor-pointer border border-gray-300 rounded text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {t("reset")}
        </button>
        <button
          type="submit"
          disabled={submitting}
          className="px-4 py-2 flex items-center gap-2 cursor-pointer bg-black text-white rounded hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {mode === "auto" ? t("confirmAndSend") : t("submit")}
          {submitting && <Loader className="animate-spin" />}
        </button>
      </div>
    </form>
  );
}
