"use client";
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, File06, Grid01, Stars02 } from "@untitled-ui/icons-react";
import { useConcierge } from "../_context/ConciergeContext";
import { YumiSidebar } from "./YumiSidebar";
import SandboxHeader from "./SandboxHeader";
import { TestUserProvider } from "../_context/TestUserContext";
import { useTranslations } from "next-intl";
import { PhoneCall } from "lucide-react";

interface HomeLayoutProps {
  children: React.ReactNode;
}

const YumiLayoutInner = ({ children }: HomeLayoutProps) => {
  const { data, notFound } = useConcierge();
  const t = useTranslations("DashboardYumiSandbox.Layout");

  return (
    <div className="flex min-h-screen bg-[#FAFAFA] ">
      <YumiSidebar />
      <main className="flex-1 overflow-auto ">
        <TestUserProvider>
          <SandboxHeader
            avatarUrl="/yumi.png"
            userName={`Yumi-${t("sandbox")}`}
            menuItems={[
              {
                name: t("setup"),
                icon: <Grid01 height={16} width={16} />,
                href: "/dashboard/yumi/setup",
                disabled: false,
              },
              {
                name: data?.concierge
                  ? `${t("chatWithYumi")} ${data.concierge}`
                  : t("chatWithYumi"),
                icon: <Stars02 height={16} width={16} />,
                href: "/dashboard/yumi",
                disabled: !!notFound,
              },
              {
                name: t("callCenter"),
                icon: <PhoneCall height={16} width={16} />,
                href: "/dashboard/yumi/call-center",
                disabled: !!notFound,
              },
              {
                name: t("internalSupport"),
                icon: <File06 height={16} width={16} />,
                href: "/dashboard/yumi/support",
                disabled: !!notFound,
              },
              {
                name: t("testChecklist"),
                icon: <CheckCircle height={16} width={16} />,
                href: "/dashboard/yumi/test-checklist",
                disabled: !!notFound,
              },
            ]}
          />
          <div className="mx-auto max-w-[1400px] px-[2%]">{children}</div>
        </TestUserProvider>
      </main>
    </div>
  );
};

export default YumiLayoutInner;
