"use client";
import React, { useState } from "react";
import { cn } from "@/lib/utils";
import Image from "next/image";
import NavLink from "./NavLink";
import { getSidebarNavigation } from "./sidebar.data";
import { useTranslations } from "next-intl";
import { LayoutLeft } from "@untitled-ui/icons-react";
import { useConcierge } from "../_context/ConciergeContext";

export function YumiSidebar({ className }: { className?: string }) {
  const [collapsed, setCollapsed] = useState(false);
  const { data, notFound } = useConcierge();
  const t = useTranslations("DashboardYumiSandbox.Sidebar");

  const dynamicNavigations = getSidebarNavigation(t).map((nav) =>
    nav.href === "/dashboard/yumi"
      ? {
          ...nav,
          name: `${t("chatWithYumi")} ${
            data?.concierge ? `${data.concierge}` : ""
          }`.trim(),
        }
      : nav
  );

  return (
    <aside
      className={cn(
        "bg-white h-screen border-r border-[#f7f7f7] overflow-x-hidden overflow-y-auto sticky top-0",
        "hidden md:flex flex-col transition-all duration-200 ease-in-out",
        collapsed ? "w-[68px]" : "w-[240px]"
      )}
    >
      <div
        className={cn(
          "flex gap-4 shrink-0 items-center",
          collapsed ? "flex-col gap-4 mt-4 px-4" : "py-6 h-auto px-4 flex-row"
        )}
      >
        <div className="flex gap-4 items-center" id="companyLogo">
          <Image
            className="size-8 rounded-full"
            alt="yumi logo"
            height={40}
            width={40}
            src="/yumi.png"
          />
          <span
            className={cn(
              "font-semibold text-lg",
              collapsed ? "hidden" : "block"
            )}
          >
            {t("title")}
          </span>
        </div>
        <button onClick={() => setCollapsed(!collapsed)}>
          <LayoutLeft
            className="text-gray-400 cursor-pointer hover:text-black"
            width={20}
            height={20}
          />
        </button>
      </div>
      <nav
        className={cn(
          "flex flex-col gap-3.5",
          collapsed ? "mt-6 items-center" : "mt-1 pl-5 pr-8"
        )}
      >
        {dynamicNavigations.map((nav, i) => (
          <NavLink
            key={i}
            href={nav.href}
            icon={nav.icon}
            name={nav.name}
            collapsed={collapsed}
            disabled={
              notFound &&
              [
                "/dashboard/yumi",
                "/dashboard/yumi/support",
                "/dashboard/yumi/test-checklist",
                "/dashboard/yumi/call-center",
              ].includes(nav.href)
            }
          />
        ))}
      </nav>
    </aside>
  );
}
