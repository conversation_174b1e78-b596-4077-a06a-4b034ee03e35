import { CheckCircle, File06, Grid01, Stars02 } from "@untitled-ui/icons-react";
import { PhoneCall } from "lucide-react";

export const getSidebarNavigation = (t: (key: string) => string) => [
  {
    name: t("setup"),
    icon: <Grid01 height={16} width={16} />,
    href: "/dashboard/yumi/setup",
  },
  {
    name: t("chatWithYumi"),
    icon: <Stars02 height={16} width={16} />,
    href: "/dashboard/yumi",
  },
  {
    name: t("callCenter"),
    icon: <PhoneCall height={16} width={16} />,
    href: "/dashboard/yumi/call-center",
  },
  {
    name: t("internalSupport"),
    icon: <File06 height={16} width={16} />,
    href: "/dashboard/yumi/support",
  },
  {
    name: t("testChecklist"),
    icon: <CheckCircle height={16} width={16} />,
    href: "/dashboard/yumi/test-checklist",
  },
];
