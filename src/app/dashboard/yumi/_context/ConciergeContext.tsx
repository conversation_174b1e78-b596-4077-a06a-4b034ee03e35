"use client";
import { sandboxUrl } from "@/config/baseUrl";
import { useProfile } from "@/hooks/useProfile";
import React, { createContext, useContext, useEffect, useState } from "react";

interface ConciergeData {
  vector_index: string;
  concierge: string;
  language?: string;
  gender: string;
  organization: string;
  personality: string;
  sandbox_id: string;
}

interface ConciergeContextType {
  loading: boolean;
  error: string | null;
  data: ConciergeData | null;
  avatarUrl: string | null;
  refetch: () => void;
  notFound: boolean;
}

const ConciergeContext = createContext<ConciergeContextType | undefined>(undefined);

export const useConcierge = () => {
  const ctx = useContext(ConciergeContext);
  if (!ctx) throw new Error("useConcierge must be used within ConciergeProvider");
  return ctx;
};

export const ConciergeProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<ConciergeData | null>(null);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [notFound, setNotFound] = useState(false);
  const [currentUserId, setCurrentUserId] = useState<string | undefined>(undefined);
  const { profile: user, isLoading, error: profileError, refetch } = useProfile();

  const fetchAll = async (uid?: string) => {
    const userIdToUse = uid || currentUserId;
    if (!userIdToUse) return;
    setCurrentUserId(userIdToUse);
    setLoading(true);
    setError(null);
    setNotFound(false);
    try {
      const setupRes = await fetch(`${sandboxUrl}/sandbox/details?user_id=${userIdToUse}`);
      if (setupRes.status === 404) {
        setNotFound(true);
        setData(null);
        setAvatarUrl(null);
        setLoading(false);
        return;
      }
      const setupJson = await setupRes.json();
      if (!setupRes.ok || setupJson.error) throw new Error(setupJson.error || "Failed to fetch setup");
      setData(setupJson);
      setNotFound(false);

      // Fetch avatar
      const avatarRes = await fetch(`${sandboxUrl}/sandbox/concierge_avatar?user_id=${userIdToUse}`);
      if (avatarRes.ok) {
        const blob = await avatarRes.blob();
        // If the blob is empty, show default avatar
        if (blob.size === 0) {
          setAvatarUrl(null);
        } else {
          setAvatarUrl(URL.createObjectURL(blob));
        }
      } else {
        setAvatarUrl(null);
      }
    } catch (err: any) {
      setError(err.message || "Unknown error");
      setData(null);
      setAvatarUrl(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.user_id) {
      setCurrentUserId(user.user_id);
      fetchAll(user.user_id);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.user_id]);

  return (
    <ConciergeContext.Provider value={{
      loading,
      error,
      data,
      avatarUrl,
      refetch: () => fetchAll(), // No param needed, uses currentUserId
      notFound
    }}>
      {children}
    </ConciergeContext.Provider>
  );
};