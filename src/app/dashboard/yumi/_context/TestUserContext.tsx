import React, { createContext, useContext, useState, useEffect } from "react";
import { useTestUsers, TestUser } from "../support/_hooks/useTestUsers";
import { useConcierge } from "./ConciergeContext";

interface TestUserContextType {
  testUsers: TestUser[];
  fakeUserId: string | undefined;
  setFakeUserId: (id: string) => void;
  selectedTestUser?: TestUser;
}

const TestUserContext = createContext<TestUserContextType | undefined>(undefined);

export const TestUserProvider = ({ children }: { children: React.ReactNode }) => {
  const { data: conciergeData } = useConcierge();
  const { data: testUsers = [] } = useTestUsers(conciergeData?.sandbox_id);
  const [fakeUserId, setFakeUserId] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (testUsers.length > 0) {
      setFakeUserId(testUsers[0].user_id);
    }
  }, [testUsers]);

  const selectedTestUser = testUsers.find((u) => u.user_id === fakeUserId);

  return (
    <TestUserContext.Provider value={{ testUsers, fakeUserId, setFakeUserId, selectedTestUser }}>
      {children}
    </TestUserContext.Provider>
  );
};

export const useTestUserContext = () => {
  const ctx = useContext(TestUserContext);
  if (!ctx) throw new Error("useTestUserContext must be used within TestUserProvider");
  return ctx;
};