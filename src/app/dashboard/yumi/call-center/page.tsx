"use client";

import React, { useState } from "react";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import type { TestUser } from "../support/_hooks/useTestUsers";
import { useTestUserContext } from "../_context/TestUserContext";
import { Button } from "../_components/button";
import { useConcierge } from "../_context/ConciergeContext";
import { Phone } from "lucide-react";
import { useTranslations } from "next-intl";

const CALL_NUMBER = "+****************";
import { useCallSession } from "./useCallSession";

export default function CallCenterPage() {
  const { testUsers } = useTestUserContext();
  const [open, setOpen] = useState(false);
  const [callingUser, setCallingUser] = useState<TestUser | undefined>(undefined);
  const { data: conciergeData } = useConcierge();
  const t = useTranslations("DashboardYumiSandbox.SandboxCallCenterPage");
  const {
    callStatusKey,
    isCalling,
    isCallActive,
    isConnecting,
    errorKey,
    disconnectReason,
    startCall,
    stopCall,
    retry,
  } = useCallSession(conciergeData);

  return (
    <div className="mx-auto bg-white rounded-lg shadow min-h-[calc(100vh-8rem)] p-4 sm:p-6 mt-4 sm:mt-6">
      <h2 className="text-xl font-semibold mb-1">{t("callCenterHeader")}</h2>
      <p className="text-gray-700 mb-6">
        {conciergeData?.concierge} {t("callCenterDescription")}
      </p>
      <div className="mb-4">{t("presetUsersBelow")}</div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6 w-full">
        {testUsers.map((user: TestUser, idx: number) => (
          <div
            key={user.user_id}
            className="border rounded-md p-4 sm:p-6 flex flex-col items-start shadow-sm bg-white min-w-0"
          >
            <div className="mb-4 text-xs text-gray-500">
              {t("callUsing")}
            </div>
            <div className="mb-4 text-lg font-bold break-words">
              {user.firstname} {user.lastname}
            </div>
            <Button
              onClick={() => {
                setCallingUser(user);
                setOpen(true);
                startCall(user);
              }}
              className=""
              disabled={isCalling || isConnecting}
            >
              {isCalling && callingUser?.user_id === user.user_id ? (
                <span className="flex items-center gap-2">
                  <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></span>
                  {t("InitiateCall")}
                </span>
              ) : (
                t("InitiateCall")
              )}
            </Button>
          </div>
        ))}
      </div>

      <Dialog open={open} onOpenChange={(v) => {
        setOpen(v);
        if (!v) stopCall("user");
      }}>
        <DialogContent
          hideClose
          className="flex flex-col items-center justify-center py-8 px-4 sm:py-10 sm:px-8 w-full max-w-xs sm:max-w-md"
          onInteractOutside={e => e.preventDefault()}
        >
          <DialogTitle className="sr-only">Call Dialog</DialogTitle>
          {callingUser && (
            <>
              <div className="mb-8 text-xs text-center bg-[#0F172A0D] text-[#0F172A] rounded-md p-2">
                {t("callingAs", {
                  firstname: callingUser.firstname,
                  lastname: callingUser.lastname,
                })}
              </div>
              <div className="mb-10">
                <div className="text-2xl font-semibold text-center">
                  {conciergeData?.concierge}
                </div>
                <div className="text-center text-gray-700">{CALL_NUMBER}</div>
              </div>
              <div className="mb-4 text-center text-sm text-gray-600 min-h-[24px]" role="status">
                {t(callStatusKey, { defaultValue: callStatusKey })}
              </div>
              {errorKey && (
                <div className="mb-2 text-center text-sm text-red-600">
                  {t(errorKey, { defaultValue: errorKey })}
                </div>
              )}
              {disconnectReason === "server" && (
                <Button onClick={() => retry(callingUser)} className="mb-2">{t("retry")}</Button>
              )}
              <button
                aria-label="End call"
                className="bg-red-600 hover:bg-red-700 rounded-full p-5 flex items-center justify-center cursor-pointer"
                onClick={() => {
                  setOpen(false);
                  stopCall("user");
                }}
                disabled={!isCalling && !isCallActive && !isConnecting}
                autoFocus
              >
                {isConnecting ? (
                  <span className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></span>
                ) : (
                  <Phone className="w-8 h-8 text-white" />
                )}
              </button>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
