import { useState, useRef, useCallback } from "react";
import type { TestUser } from "../support/_hooks/useTestUsers";
import { callCenterUrl } from "@/config/baseUrl";

export function useCallSession(conciergeData: any) {
  const [callStatusKey, setCallStatusKey] = useState<string>("idle");
  const [isCalling, setIsCalling] = useState(false);
  const [isCallActive, setIsCallActive] = useState(false);
  const [errorKey, setErrorKey] = useState<string | null>(null);
  const [disconnectReason, setDisconnectReason] = useState<"user"|"error"|"server"|null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const audioContextRef = useRef<AudioContext | null>(null);
  const microphoneNodeRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const workletNodeRef = useRef<AudioWorkletNode | ScriptProcessorNode | null>(null);
  const websocketRef = useRef<WebSocket | null>(null);
  const audioQueueRef = useRef<Int16Array[]>([]);
  const isPlayingRef = useRef(false);
  const streamRef = useRef<MediaStream | null>(null);
  const retryTimeout = useRef<NodeJS.Timeout | null>(null);

  // Helper: get sample rates
  const getSampleRates = useCallback(async () => {
    const tempCtx = new (window.AudioContext || (window as any).webkitAudioContext)();
    const playbackSampleRate = tempCtx.sampleRate;
    await tempCtx.close();
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    const micTrack = stream.getAudioTracks()[0];
    const settings = micTrack.getSettings();
    const micSampleRate = settings.sampleRate || playbackSampleRate;
    micTrack.stop();
    return { micSampleRate, playbackSampleRate };
  }, []);

  // Helper: play audio from queue
  const playNextInQueue = useCallback(() => {
    if (isPlayingRef.current || audioQueueRef.current.length === 0) return;
    isPlayingRef.current = true;
    const int16Array = audioQueueRef.current.shift();
    if (!int16Array) {
      isPlayingRef.current = false;
      return;
    }
    const float32Array = new Float32Array(int16Array.length);
    for (let i = 0; i < int16Array.length; i++) {
      float32Array[i] = int16Array[i] / 32767.0;
    }
    const ctx = audioContextRef.current;
    if (!ctx) {
      isPlayingRef.current = false;
      return;
    }
    const audioBuffer = ctx.createBuffer(1, float32Array.length, ctx.sampleRate);
    audioBuffer.getChannelData(0).set(float32Array);
    const source = ctx.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(ctx.destination);
    source.start();
    source.onended = () => {
      isPlayingRef.current = false;
      playNextInQueue();
    };
  }, []);


  // Stop call logic
  const stopCall = useCallback((reason: "user"|"error"|"server" = "user") => {
    setIsCalling(false);
    setIsCallActive(false);
    setIsConnecting(false);
    setCallStatusKey(
      reason === "user"
        ? "callEnded"
        : reason === "server"
        ? "callDroppedByServer"
        : "callEndedWithError"
    );
    setDisconnectReason(reason);
    // Clean up audio
    try {
      if (microphoneNodeRef.current) microphoneNodeRef.current.disconnect();
      if (workletNodeRef.current) workletNodeRef.current.disconnect();
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    } catch {}
    microphoneNodeRef.current = null;
    workletNodeRef.current = null;
    audioContextRef.current = null;
    streamRef.current = null;
    // Clean up websocket
    if (websocketRef.current && websocketRef.current.readyState === WebSocket.OPEN) {
      websocketRef.current.close();
    }
    websocketRef.current = null;
    // Clean up audio queue
    audioQueueRef.current = [];
    isPlayingRef.current = false;
    if (retryTimeout.current) {
      clearTimeout(retryTimeout.current);
      retryTimeout.current = null;
    }
  }, []);

  // Start call logic
  const startCall = useCallback(async (user: TestUser) => {
    setIsCalling(true);
    setIsCallActive(false);
    setIsConnecting(true);
    setCallStatusKey("connecting");
    setErrorKey(null);
    setDisconnectReason(null);

    try {
      const { micSampleRate, playbackSampleRate } = await getSampleRates();
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;
      const ctx = new (window.AudioContext || (window as any).webkitAudioContext)({ sampleRate: playbackSampleRate });
      audioContextRef.current = ctx;
      const micNode = ctx.createMediaStreamSource(stream);
      microphoneNodeRef.current = micNode;
      let workletNode: AudioWorkletNode | ScriptProcessorNode;
      // Try AudioWorklet first
      if (ctx.audioWorklet && ctx.audioWorklet.addModule) {
        try {
          // Inline worklet processor
          const processorCode = `
            class PCMWorkletProcessor extends AudioWorkletProcessor {
              process(inputs) {
                const input = inputs[0][0];
                if (input) {
                  this.port.postMessage(input);
                }
                return true;
              }
            }
            registerProcessor('pcm-worklet', PCMWorkletProcessor);
          `;
          const blob = new Blob([processorCode], { type: 'application/javascript' });
          const url = URL.createObjectURL(blob);
          await ctx.audioWorklet.addModule(url);
          workletNode = new AudioWorkletNode(ctx, 'pcm-worklet');
        } catch {
          // Fallback to ScriptProcessorNode
          workletNode = ctx.createScriptProcessor(1024, 1, 1);
        }
      } else {
        workletNode = ctx.createScriptProcessor(1024, 1, 1);
      }
      workletNodeRef.current = workletNode;

      // WebSocket connection
      const user_id = user.user_id;
      const sandbox_id = conciergeData?.sandbox_id || "sandbox";
      const wsUrl = `wss://${callCenterUrl}/call?user_id=${encodeURIComponent(user_id)}&sandbox_id=${encodeURIComponent(sandbox_id)}&sample_rate=${micSampleRate}&playback_rate=${playbackSampleRate}`;
      const ws = new WebSocket(wsUrl);
      websocketRef.current = ws;
      ws.binaryType = 'arraybuffer';

      ws.onopen = () => {
        setCallStatusKey("callPickedUp");
        setIsCallActive(true);
        setIsConnecting(false);
        // Start sending mic audio
        if (workletNode instanceof AudioWorkletNode) {
          workletNode.port.onmessage = (event) => {
            if (ws.readyState === WebSocket.OPEN) {
              ws.send(event.data.buffer);
            }
          };
        } else {
          (workletNode as ScriptProcessorNode).onaudioprocess = (event) => {
            const pcmData = event.inputBuffer.getChannelData(0);
            if (ws.readyState === WebSocket.OPEN) {
              ws.send(pcmData.buffer);
            }
          };
        }
        micNode.connect(workletNode);
        workletNode.connect(ctx.destination);
      };

      ws.onmessage = (event) => {
        if (typeof event.data === 'string') {
          // handle control messages if needed
          return;
        }
        const int16Array = new Int16Array(event.data);
        audioQueueRef.current.push(int16Array);
        playNextInQueue();
      };

      ws.onclose = (ev) => {
        setDisconnectReason(ev.wasClean ? "user" : "server");
        stopCall("server");
      };
      ws.onerror = (error) => {
        setCallStatusKey('wsError');
        setErrorKey('wsError');
        setDisconnectReason("error");
        stopCall("error");
      };
    } catch (err: any) {
      setCallStatusKey('error');
      setErrorKey(err?.message ? `error.${err.message}` : 'error.unknown');
      setIsCalling(false);
      setIsCallActive(false);
      setIsConnecting(false);
    }
  }, [conciergeData, getSampleRates, playNextInQueue, stopCall]);

  // Retry logic (simple backoff)
  const retry = useCallback((user: TestUser) => {
    stopCall();
    setTimeout(() => {
      startCall(user);
    }, 2000);
  }, [startCall, stopCall]);

  return {
    callStatusKey,
    isCalling,
    isCallActive,
    isConnecting,
    errorKey,
    disconnectReason,
    startCall,
    stopCall,
    retry,
  };
}
