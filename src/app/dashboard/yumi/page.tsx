// @ts-nocheck

"use client";

import React, { useEffect, useRef, useState, useMemo } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { v4 as uuidv4 } from "uuid";
import { useProfile } from "@/hooks/useProfile";
import TicketForm, { TicketFormProps } from "./_components/TicketForm";
import { ArrowLeft, Loader } from "lucide-react";
import ChatBox from "./_components/ChatBox";
import { querySandbox, useGetQueryYumi, yumiAPI } from "@/services/api_hooks";
import axios from "axios";
import Typed from "react-typed";
import { TypedBotMessage } from "@/components/TypingEffect/typedBotMessage";
import Link from "next/link";
import { useConcierge } from "./_context/ConciergeContext";
import { sandboxApi } from "@/lib/api";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { useTestUserContext } from "./_context/TestUserContext";
import { sandboxEnv } from "@/config/baseUrl";
import { useTranslations } from "next-intl";

type Message = {
  sender: "user" | "bot";
  text: string;
};

type ChatBotResponse = {
  text: string;
  usedTools: Array<{
    tool: string;
    toolInput: Record<string, any>;
    toolOutput: any;
  }>;
  question: string;
  chatId: string;
  chatMessageId: string;
  isStreamValid: boolean;
  sessionId: string;
  memoryType: string;
};

export default function SandboxHomePage() {
  const router = useRouter();
  const t = useTranslations("DashboardYumiSandbox.SandboxHomePage");
  const { profile: user, isLoading, error, refetch } = useProfile();
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [submittingTicket, setSubmittingTicket] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [sharedAttachments, setSharedAttachments] = useState<File[]>([]);
  const [autoTicketData, setAutoTicketData] = useState<null | {
    summary: string;
    contact_email: string;
    subject: string;
    priority: number;
    name: string;
    type: string;
  }>(null);
  const {
    loading,
    error: conciergeError,
    data,
    avatarUrl,
    refetch: refetchConcierge,
  } = useConcierge();

  const [showFakeUserPopover, setShowFakeUserPopover] = useState(false);

  const { testUsers, fakeUserId, setFakeUserId, selectedTestUser } =
    useTestUserContext();

  useEffect(() => {
    setSessionId(uuidv4());
  }, []);

  // Regenerate session id when fake user changes
  useEffect(() => {
    setSessionId(uuidv4());
  }, [fakeUserId]);

  useEffect(() => {
    if (!sessionId || !user?.user_id || !data?.sandbox_id) return;

    const storeChatLog = async () => {
      const payload = {
        sessionid: sessionId,
        fake_user_id: fakeUserId,
        sandbox_id: data?.sandbox_id,
      };
      try {
        const response = await sandboxApi.post("/user/store-chatlog", payload);
        console.log("chat log sent");
      } catch (error) {
        console.error("Error storing chat log:", error);
      }
    };

    storeChatLog();
  }, [user?.user_id, sessionId, fakeUserId, data?.sandbox_id]);

  const handleSendMessage = async (text: string, attachments?: File[]) => {
    // Create user message with text and attachment info
    const messageText = text.trim();
    const attachmentInfo =
      attachments && attachments.length > 0
        ? " " +
          attachments
            .map((file) => {
              const name = file.name;
              return name.length > 25 ? name.substring(0, 25) + "..." : name;
            })
            .join(", ")
        : "";

    setMessages((prev) => [
      ...prev,
      {
        sender: "user",
        text:
          messageText +
          " " +
          (attachmentInfo ? `Attachment: [${attachmentInfo}]` : ""),
      },
    ]);
    setIsTyping(true);

    try {
      // Prepare uploads array if attachments exist
      const uploads = attachments
        ? await Promise.all(
            attachments.map(async (file) => {
              const isImage = file.type.startsWith("image/");
              const isPdfOrDoc =
                file.type.includes("pdf") ||
                file.type.includes("word") ||
                file.type.includes("document");

              if (isPdfOrDoc) {
                // For PDF/DOC files, use the /attachments/ API
                try {
                  const formData = new FormData();
                  formData.append("files", file);

                  const chatflowId = "d93529a6-b8c0-4b63-a9b3-744a7dbdc321";
                  const chatId = sessionId;

                  const response = await axios.post(
                    `https://yumi.up.railway.app/api/v1/attachments/${chatflowId}/${chatId}`,
                    formData,
                    {
                      headers: {
                        "Content-Type": "multipart/form-data",
                      },
                    }
                  );

                  // Extract content from response
                  const attachmentData = response.data[0]; // Assuming single file response
                  const content = attachmentData.content;

                  // Convert content text to base64
                  const base64Content = btoa(
                    unescape(encodeURIComponent(content))
                  );
                  const base64Data = `data:text/plain;base64,${base64Content}`;

                  return {
                    data: base64Data,
                    type: "file:full",
                    name: file.name,
                    mime: file.type,
                  };
                } catch (error) {
                  console.error("Error processing PDF/DOC file:", error);
                  // Fallback to original method if API fails
                  const base64Data = await new Promise<string>((resolve) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result as string);
                    reader.readAsDataURL(file);
                  });

                  return {
                    data: base64Data,
                    type: "file:full",
                    name: file.name,
                    mime: file.type,
                  };
                }
              } else if (isImage) {
                // For images, use the original method
                const base64Data = await new Promise<string>((resolve) => {
                  const reader = new FileReader();
                  reader.onload = () => resolve(reader.result as string);
                  reader.readAsDataURL(file);
                });

                return {
                  data: base64Data,
                  type: "file",
                  name: file.name,
                  mime: file.type,
                };
              } else {
                // For other file types, use the original method
                const base64Data = await new Promise<string>((resolve) => {
                  const reader = new FileReader();
                  reader.onload = () => resolve(reader.result as string);
                  reader.readAsDataURL(file);
                });

                return {
                  data: base64Data,
                  type: "file",
                  name: file.name,
                  mime: file.type,
                };
              }
            })
          )
        : undefined;

      // Generate contextual prompt based on attachments when no text is provided
      let defaultPrompt = "see this";
      if (!messageText && attachments && attachments.length > 0) {
        const fileTypes = attachments.map((file) => {
          if (file.type.startsWith("image/")) return "image";
          if (file.type.includes("pdf")) return "document";
          if (file.type.includes("word") || file.type.includes("document"))
            return "document";
          return "file";
        });

        const hasImages = fileTypes.includes("image");
        const hasDocuments = fileTypes.includes("document");

        if (hasImages && hasDocuments) {
          defaultPrompt =
            "Please analyze these files and help me understand their content.";
        } else if (hasImages) {
          defaultPrompt =
            fileTypes.length > 1 ? "What do you see?" : "What do you see?";
        } else if (hasDocuments) {
          defaultPrompt =
            fileTypes.length > 1 ? "summarize these." : "summarize this.";
        } else {
          defaultPrompt =
            fileTypes.length > 1 ? "analyze these " : " analyze this";
        }
      }

      const result: ChatBotResponse = await querySandbox({
        question: messageText || defaultPrompt,
        uploads: uploads,
        overrideConfig: {
          vars: {
            vector_index: data?.vector_index || "",
            concierge: data?.concierge || "",
            organization: data?.organization || "",
            personality: data?.personality || "",
            userid: fakeUserId || "",
            env: sandboxEnv || "",
          },
          sessionId: sessionId || "",
        },
      });

      setMessages((prev) => [...prev, { sender: "bot", text: result.text }]);

      const intervention = Array.isArray(result.usedTools)
        ? result.usedTools.find((t) => t.tool === "Escalation")
        : undefined;

      if (intervention) {
        let out = intervention.toolOutput;
        if (typeof out === "string") {
          try {
            out = JSON.parse(out);
          } catch (e) {
            console.error("Failed to parse toolOutput:", e);
            out = null;
          }
        }
        if (out) {
          setAutoTicketData({
            summary: out.summary,
            contact_email: out.contact_email,
            subject: out.subject,
            priority: out.priority,
            name: out.name,
            type: out.type,
          });
        }
      }
    } catch (err) {
      console.log("ChatBot error:", err);
      if (
        err?.response?.data?.message?.includes(
          "400 This model's maximum context length"
        )
      ) {
        setMessages((prev) => [
          ...prev,
          {
            sender: "bot",
            text: "Input too large. Please reduce content size and try again.",
          },
        ]);
      } else {
        setMessages((prev) => [
          ...prev,
          { sender: "bot", text: "Something went wrong. Please try again." },
        ]);
      }
    } finally {
      setIsTyping(false);
    }
  };

  const handleSubmitTicket = async (
    payload: Parameters<TicketFormProps["onSubmitPayload"]>[0]
  ) => {
    setSubmittingTicket(true);

    try {
      let result;

      // If payload is FormData (i.e., you’re uploading files), use axios.post directly
      const response = await sandboxApi.post(
        "/ticket/create",
        payload,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
        // NOTE: No headers needed—axios sets the multipart boundary automatically.
      );

      setSubmittingTicket(false);
      console.log("Ticket created:", result);

      setMessages((m) => [...m, { sender: "bot", text: t("ticketCreated") }]);
      setAutoTicketData(null);
    } catch (err: any) {
      setSubmittingTicket(false);

      // If axios received a non-2xx status, err.response will be defined
      if (err.response) {
        console.error("API Error:", err.response.data);
        // You could inspect err.response.status or err.response.data.message here
        // alert(
        //   "Failed to create ticket: " +
        //     (err.response.data?.message || err.response.status)
        // );
      } else {
        console.error("Error submitting ticket:", err);
        // alert("Failed to create ticket. Please try again.");
      }
    }
  };

  const autoTicketInitialValues = useMemo(() => {
    if (!autoTicketData) return undefined;
    return {
      fullName: autoTicketData.name,
      email: autoTicketData.contact_email,
      issueType: autoTicketData.type,
      priority: autoTicketData.priority.toString(),
      subject: autoTicketData.subject,
      body: autoTicketData.summary,
    };
  }, [autoTicketData]);

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  }, [messages, isTyping]);

  useEffect(() => {
    setMessages([]);
    setAutoTicketData(null); // Optionally clear auto ticket data as well
  }, [fakeUserId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <span>{t("loading")}</span>
      </div>
    );
  }

  return (
    <div className=" w-full h-screen">
      <div className="flex flex-col lg:flex-row gap-10 mx-auto h-full  max-w-[1600px]  mt-5">
        {/* Left Panel */}
        <div className="relative max-h-[650px] w-[100%] lg:w-[50%]">
          <div className="md:max-h-[650px] h-full flex flex-col justify-between overflow-hidden">
            {messages.length === 0 && (
              <div className="flex items-center justify-center flex-col px-6 py-4 pt-32 mb-8">
                <Image
                  src={avatarUrl && avatarUrl.trim() ? avatarUrl : "/default-avatar.png"}
                  alt={data?.concierge}
                  width={40}
                  height={40}
                  className="rounded-full object-cover w-16 h-16 md:w-20 md:h-20 lg:w-24 lg:h-24"
                />
                <h1 className="text-2xl md:text-3xl font-bold text-center mt-4">
                  {t("needHelp")} <br />
                  <span className="bg-gradient-to-b from-[#0F172A] to-[#9CC4FF] bg-clip-text text-transparent">
                    {data?.concierge}
                  </span>
                </h1>
                <p className="text-gray-500 mt-2">{t("getStartedPrompt")}</p>
              </div>
            )}

            {messages.length > 0 && (
              <div
                ref={chatContainerRef}
                className="flex flex-col flex-1 overflow-y-auto min-h-0 px-6 py-4 space-y-4"
              >
                {messages.map((msg, i) => (
                  <div
                    key={i}
                    className={`flex items-start space-x-3 ${
                      msg.sender === "user"
                        ? "flex-row-reverse space-x-reverse"
                        : ""
                    }`}
                  >
                    {/* Profile Picture - only show for bot messages */}
                    {msg.sender === "bot" && (
                      <Image
                        src={avatarUrl && avatarUrl.trim() ? avatarUrl : "/default-avatar.png"}
                        alt={data?.concierge}
                        width={40}
                        height={40}
                        className="rounded-full object-cover w-10 h-10 flex-shrink-0 mt-1"
                      />
                    )}

                    {/* Message Bubble */}
                    <div
                      className={`
              max-w-[80%] p-3 rounded-lg break-words
              ${
                msg.sender === "user"
                  ? "bg-indigo-100 text-gray-900"
                  : "bg-gray-100 text-gray-900"
              }
            `}
                    >
                      {msg.sender === "bot" ? (
                        <div className="prose prose-sm max-w-none">
                          {/* <ReactMarkdown remarkPlugins={[remarkGfm]}>
                            {msg.text}
                          </ReactMarkdown> */}
                          <TypedBotMessage text={msg.text} messageIndex={i} />
                        </div>
                      ) : (
                        <span>{msg.text}</span>
                      )}
                    </div>
                  </div>
                ))}

                {isTyping && (
                  <div className="flex items-start space-x-3">
                    <Image
                      src={avatarUrl && avatarUrl.trim() ? avatarUrl : "/default-avatar.png"}
                      alt={data?.concierge}
                      width={40}
                      height={40}
                      className="rounded-full object-cover w-10 h-10 flex-shrink-0 mt-1"
                    />
                    <div className="bg-gray-100 p-3 rounded-lg max-w-[60%]">
                      <div className="flex space-x-1 items-center">
                        <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce delay-100"></div>
                        <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce delay-200"></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className=" px-6 pb-1 pt-4 bottom-0">
              <ChatBox
                onSendMessage={handleSendMessage}
                onAttachmentsChange={setSharedAttachments}
                loading={isTyping}
              />
            </div>
          </div>
        </div>

        {/* Right Panel */}

        {autoTicketData ? (
          <TicketForm
            initialValues={{
              ...(autoTicketInitialValues || {}),
              fullName: selectedTestUser
                ? `${selectedTestUser.firstname} ${selectedTestUser.lastname}`.trim()
                : "",
              email: selectedTestUser?.email || "",
            }}
            onSubmitPayload={handleSubmitTicket}
            mode="auto"
            submitting={submittingTicket}
            sharedAttachments={sharedAttachments}
            fakeUserId={fakeUserId}
            sandboxId={data?.sandbox_id}
          />
        ) : (
          <TicketForm
            initialValues={{
              fullName: selectedTestUser
                ? `${selectedTestUser.firstname} ${selectedTestUser.lastname}`.trim()
                : "",
              email: selectedTestUser?.email || "",
              issueType: "",
              priority: "",
              subject: "",
              body: "",
            }}
            onSubmitPayload={handleSubmitTicket}
            submitting={submittingTicket}
            mode="manual"
            sharedAttachments={sharedAttachments}
            fakeUserId={fakeUserId}
            sandboxId={data?.sandbox_id}
          />
        )}
      </div>
    </div>
  );
}
