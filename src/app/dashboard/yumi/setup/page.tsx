"use client";
import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "../_components/tabs";
import { Loader, Upload } from "lucide-react";
import { useProfile } from "@/hooks/useProfile";
import Image from "next/image";
import { toast } from "sonner";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "../_components/button";
import { Textarea } from "../_components/textarea";
import { useConcierge } from "../_context/ConciergeContext";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import SandboxSubscribeModalWrapper from "@/app/dashboard/yumi/_components/SandboxSubscribeModalWrapper";
import { useGetQuery } from "@/services/api_hooks";
import { sandboxUrl } from "@/config/baseUrl";
import { useTranslations } from "next-intl";

function formatFileSize(size: number) {
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(0)} KB`;
  return `${(size / 1024 / 1024).toFixed(2)} MB`;
}

const SetupPage = () => {
  const t = useTranslations("DashboardYumiSandbox.SetupPage");
  const [tab, setTab] = useState("knowledge");
  const [orgName, setOrgName] = useState("");
  const [conciergeName, setConciergeName] = useState("");
  const [personalityPrompt, setPersonalityPrompt] = useState(
    t("proactiveText")
  );
  const [gender, setGender] = useState("male");
  const [language, setLanguage] = useState("en");
  const [conciergeImage, setConciergeImage] = useState<File | null>(null);
  const [knowledgeFiles, setKnowledgeFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { profile: user, isLoading, error, refetch } = useProfile();
  const {
    data: conciergeData,
    avatarUrl,
    notFound,
    loading,
    refetch: refetchConcierge,
  } = useConcierge();
  const [showSetupDialog, setShowSetupDialog] = useState(false);
  const [subscribeModal, setSubscribeModal] = useState(false);
  const [isDragActive, setIsDragActive] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const router = useRouter();

  // Add these state variables after your useState hooks:
  const [initialOrgName, setInitialOrgName] = useState("");
  const [initialConciergeName, setInitialConciergeName] = useState("");
  const [initialLanguage, setInitialLanguage] = useState("en");
  const [initialPersonalityPrompt, setInitialPersonalityPrompt] = useState("");
  const [initialAvatarUrl, setInitialAvatarUrl] = useState<string | null>(null);
  const [avatarError, setAvatarError] = useState(false);

  // Pre-fill fields when conciergeData loads
  useEffect(() => {
    if (conciergeData) {
      setOrgName(conciergeData.organization || "");
      setConciergeName(conciergeData.concierge || "");
      setLanguage(conciergeData.language || "en");
      setPersonalityPrompt(conciergeData.personality || "");
      setGender(conciergeData.gender || "");
      setInitialOrgName(conciergeData.organization || "");
      setInitialConciergeName(conciergeData.concierge || "");
      setInitialLanguage(conciergeData.language || "en");
      setInitialPersonalityPrompt(conciergeData.personality || "");
      setInitialAvatarUrl(avatarUrl || null);
      setShowSetupDialog(false);
    } else if (notFound) {
      // setShowSetupDialog(true);
      setSubscribeModal(true);
    }
  }, [conciergeData, notFound, avatarUrl]);

  const { data: subscriptionStatus, isLoading: subscriptionStatusLoading } =
    useGetQuery(
      "/api/subscribe/yumi-sandbox-subscription",
      ["get-sandbox-subscription-status"],
      {
        onError() {
          toast.error("Could not load alerts");
        },
      }
    );

  // Add a helper to check if any field has changed:
  const customizationChanged =
    orgName !== initialOrgName ||
    conciergeName !== initialConciergeName ||
    personalityPrompt !== initialPersonalityPrompt ||
    conciergeImage !== null ||
    language !== initialLanguage ||
    gender !== (conciergeData?.gender || "");

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragActive(false);
  };

  const handleFileDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      // Only accept allowed file types
      const allowedTypes = [
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ];
      const droppedFiles = Array.from(e.dataTransfer.files).filter((file) =>
        allowedTypes.includes(file.type)
      );
      setKnowledgeFiles((prev) => [...prev, ...droppedFiles]);
      e.dataTransfer.clearData();
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setKnowledgeFiles(Array.from(e.target.files));
    }
  };

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setConciergeImage(e.target.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;
    setIsSaving(true);
    const formData = new FormData();
    formData.append("organization", orgName);
    formData.append("concierge", conciergeName);
    formData.append("personality", personalityPrompt);
    formData.append("language", language);
    formData.append("gender", gender);
    formData.append("user_id", user.user_id);
    formData.append("email", user.email);
    formData.append("firstname", user.firstname);
    formData.append("lastname", user.lastname);

    // Append knowledge base files
    knowledgeFiles.forEach((file, idx) => {
      formData.append("knowledge_base_document", file);
    });

    // Append concierge image
    if (conciergeImage) {
      formData.append("concierge_avatar", conciergeImage);
    }

    try {
      const res = await fetch(`${sandboxUrl}/sandbox/create`, {
        method: "POST",
        body: formData,
      });

      if (!res.ok) {
        // Try to parse the error message from the response
        let errorMsg = "Failed to create concierge";
        try {
          const errorData = await res.json();
          errorMsg = errorData?.error || errorMsg;
        } catch {
          // fallback to default errorMsg
        }
        throw new Error(errorMsg);
      }
      toast.success(t("successToast"), {
        position: "top-right",
        className: "p-4",
      });
      refetchConcierge?.();
      router.push("/dashboard/yumi");
    } catch (err: any) {
      let message = "Something went wrong";
      // If the error message contains the specific sandbox error, show a friendly message
      if (err?.message?.includes("already created a sandbox environment")) {
        message =
          "You have already created a sandbox environment. Please use your existing sandbox or contact support if you need to reset it.";
      } else if (err?.message) {
        message = err.message;
      }
      toast.error(message, {
        position: "top-right",
        className: "p-4",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdateCustomization = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!conciergeData?.sandbox_id) return;
    setIsSaving(true);
    const formData = new FormData();
    formData.append("sandbox_id", conciergeData.sandbox_id);
    if (orgName) formData.append("organization", orgName);
    if (conciergeName) formData.append("concierge", conciergeName);
    if (personalityPrompt) formData.append("personality", personalityPrompt);
    if (language) formData.append("language", language);
    if (gender) formData.append("gender", gender);
    if (conciergeImage) formData.append("concierge_avatar", conciergeImage);

    try {
      const res = await fetch(`${sandboxUrl}/sandbox/update_customization`, {
        method: "POST",
        body: formData,
      });
      if (!res.ok) {
        let errorMsg = "Failed to update customization";
        try {
          const errorData = await res.json();
          errorMsg = errorData?.error || errorMsg;
        } catch {}
        throw new Error(errorMsg);
      }
      toast.success(t("customizationUpdated"), {
        position: "top-right",
        className: "p-4",
      });
      refetchConcierge();
    } catch (err: any) {
      toast.error(err?.message || "Something went wrong", {
        position: "top-right",
        className: "p-4",
      });
    } finally {
      setIsSaving(false);
      setIsEditing(false);
    }
  };

  // Add this helper to reset fields to conciergeData values
  const resetCustomizationFields = () => {
    setOrgName(conciergeData?.organization || "");
    setConciergeName(conciergeData?.concierge || "");
    setPersonalityPrompt(conciergeData?.personality || "");
    setLanguage(conciergeData?.language || "en");
    setConciergeImage(null);
  };

  // Fetch uploaded documents if conciergeData exists
  const { data: uploadedDocs = [], isLoading: docsLoading } = useQuery<
    string[]
  >({
    queryKey: ["knowledgebaseList", conciergeData?.sandbox_id],
    queryFn: async () => {
      if (!conciergeData?.sandbox_id) return [];
      const res = await fetch(
        `${sandboxUrl}/sandbox/get_knowledgebase_list?sandbox_id=${conciergeData.sandbox_id}`
      );
      if (!res.ok) throw new Error("Failed to fetch uploaded documents");
      return res.json();
    },
    enabled: !!conciergeData?.sandbox_id,
  });

  return (
    <div className="mx-auto mt-6 bg-white rounded-lg shadow p-6 min-h-[80vh]">
      {/* Show notice if notFound */}
      {notFound && (
        <div className="mb-6 flex items-center gap-2 bg-yellow-50 border border-yellow-300 text-yellow-800 rounded px-4 py-3">
          <svg
            className="w-5 h-5 text-yellow-500 mr-2 flex-shrink-0"
            fill="none"
            stroke="currentColor"
            strokeWidth={2}
            viewBox="0 0 24 24"
          >
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 8v4m0 4h.01"
            />
          </svg>
          <span>
            <strong>{t("finishSetupNotice")}</strong>
          </span>
        </div>
      )}

      <h2 className="text-xl font-semibold mb-6">{t("conciergeSetup")}</h2>
      <Tabs value={tab} onValueChange={setTab}>
        <TabsList className="mb-6 flex items-center justify-between w-full bg-white">
          <div className="flex gap-2 border-[#E2E8F0] border p-1 rounded-xl">
            <TabsTrigger value="knowledge">{t("knowledgeBaseTab")}</TabsTrigger>
            <TabsTrigger value="customization">
              {t("customizationTab")}
            </TabsTrigger>
          </div>
        </TabsList>

        {tab === "customization" && conciergeData ? (
          isEditing ? (
            <div className="flex gap-2 w-full items-center justify-end flex-wrap">
              <Button
                onClick={handleUpdateCustomization}
                disabled={
                  isSaving ||
                  !orgName.trim() ||
                  !conciergeName.trim() ||
                  !personalityPrompt.trim() ||
                  !customizationChanged
                }
              >
                {isSaving ? t("updating") : t("updateCustomization")}
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditing(false);
                  resetCustomizationFields();
                }}
                disabled={isSaving}
              >
                {t("cancelEdit")}
              </Button>
            </div>
          ) : (
            <div className="flex items-center justify-end w-full">
              <Button className="w-fit" onClick={() => setIsEditing(true)}>
                {t("editCustomization")}
              </Button>
            </div>
          )
        ) : !conciergeData ? (
          <div className="flex items-center justify-end w-full">
            <Button
              onClick={handleSubmit}
              disabled={
                isSaving ||
                !orgName.trim() ||
                !conciergeName.trim() ||
                !personalityPrompt.trim() ||
                knowledgeFiles.length === 0
              }
            >
              {isSaving ? t("saving") : t("saveAndSetup")}
            </Button>
          </div>
        ) : null}

        {/* Knowledge Base Tab */}
        <TabsContent value="knowledge">
          {!conciergeData && (
            <div className="mb-4 flex flex-wrap items-center gap-2">
              <span>{t("uploadInfo")}</span>
            </div>
          )}
          {/* Only show file input if there is NO conciergeData */}
          {!conciergeData && (
            <div
              className={`border-2 border-dashed rounded-lg bg-gray-50 flex flex-col items-center justify-center py-10 mb-4 cursor-pointer transition
        ${
          isDragActive
            ? "border-blue-400 bg-blue-50"
            : "border-gray-300 hover:border-blue-400"
        }
      `}
              onDrop={handleFileDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                onChange={handleFileSelect}
                accept=".doc,.docx,.pdf"
                multiple
              />
              <Upload className="w-8 h-8 text-gray-400 mb-2" />
              <div className="text-gray-500 text-sm mb-1">{t("upload")}</div>
              <div className="text-xs text-gray-400">{t("uploadSub")}</div>
            </div>
          )}

          {/* Feedback Table */}
          {!conciergeData ? (
            // Show files to upload table
            knowledgeFiles.length > 0 ? (
              <div className="mt-4">
                <div className="font-medium mb-2">{t("uploadedFiles")}</div>
                <div className="overflow-x-auto rounded-lg border mb-4">
                  <table className="min-w-full text-sm">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left font-semibold">
                          {t("table.sn")}
                        </th>
                        <th className="px-4 py-2 text-left font-semibold">
                          {t("table.fileName")}
                        </th>
                        <th className="px-4 py-2 text-left font-semibold">
                          {t("table.size")}
                        </th>
                        <th className="px-4 py-2 text-left font-semibold"></th>
                      </tr>
                    </thead>
                    <tbody>
                      {knowledgeFiles.map((file, idx) => (
                        <tr key={file.name + file.size} className="border-t">
                          <td className="px-4 py-2">{idx + 1}</td>
                          <td className="px-4 py-2">{file.name}</td>
                          <td className="px-4 py-2">
                            {formatFileSize(file.size)}
                          </td>
                          <td className="px-4 py-2">
                            <button
                              type="button"
                              className="text-red-500 hover:underline text-xs cursor-pointer"
                              onClick={() => {
                                setKnowledgeFiles(
                                  knowledgeFiles.filter((_, i) => i !== idx)
                                );
                              }}
                            >
                              Remove
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              <div className="mt-4">
                <div className="font-medium mb-2">{t("uploadedFiles")}</div>
                <div className="overflow-x-auto rounded-lg border mb-4">
                  <table className="min-w-full text-sm">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left font-semibold">
                          {t("table.sn")}
                        </th>
                        <th className="px-4 py-2 text-left font-semibold">
                          {t("table.fileName")}
                        </th>
                        <th className="px-4 py-2 text-left font-semibold">
                          {t("table.size")}
                        </th>
                        <th className="px-4 py-2 text-left font-semibold"></th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td
                          colSpan={4}
                          className="px-4 py-6 text-center text-gray-400"
                        >
                          {t("noFileSelected")}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            )
          ) : (
            <div className="mt-8">
              <div className="font-medium mb-2">{t("uploadedDocuments")}</div>
              <div className="overflow-x-auto rounded-lg border">
                <table className="min-w-full text-sm">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-2 text-left font-semibold">
                        {t("table.sn")}
                      </th>
                      <th className="px-4 py-2 text-left font-semibold">
                        {t("table.documentName")}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {uploadedDocs.map((doc, idx) => (
                      <tr key={idx} className="border-t">
                        <td className="px-4 py-2">{idx + 1}</td>
                        <td className="px-4 py-2">{doc}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </TabsContent>

        {/* Customization Tab */}
        <TabsContent value="customization">
          <div className="mb-4 font-medium">{t("conciergeSetup")}</div>
          <div className="text-sm text-gray-500 mb-6">
            {conciergeData
              ? t("customizationTabDescription", {
                  defaultValue:
                    "Update your concierge settings below. All fields are optional.",
                })
              : t("customizationTabSetup", {
                  defaultValue: "Set up your concierge details.",
                })}
          </div>
          <form
            className="max-w-lg space-y-6"
            onSubmit={conciergeData ? handleUpdateCustomization : handleSubmit}
          >
            <div className="flex items-center gap-4 mb-4">
              <label className="w-24 h-24 p-2 bg-gray-100 rounded flex items-center justify-center text-gray-400 text-xs cursor-pointer border border-gray-200">
                {conciergeImage ? (
                  <Image
                    src={URL.createObjectURL(conciergeImage)}
                    alt="avatar"
                    width={64}
                    height={64}
                    className="w-full h-full object-cover rounded"
                    onError={() => setAvatarError(true)}
                  />
                ) : avatarUrl && avatarUrl !== "" && !avatarError ? (
                  <Image
                    src={avatarUrl}
                    alt="Concierge Avatar"
                    width={64}
                    height={64}
                    className="w-full h-full object-cover rounded"
                    onError={() => setAvatarError(true)}
                  />
                ) : (
                  <span className="flex flex-col items-center justify-center text-xs text-gray-400 text-center">
                    <Image
                      src="/default-avatar.png"
                      alt="Concierge Avatar"
                      width={30}
                      height={30}
                      className="object-contain rounded"
                    />
                    {t("changeCon")}
                  </span>
                )}
                <input
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageSelect}
                  disabled={conciergeData ? !isEditing || isSaving : isSaving}
                />
              </label>
            </div>
            <div>
              <label className="block text-sm mb-1">
                {t("organizationName")}
              </label>
              <Input
                type="text"
                className="w-full border rounded px-3 py-2 text-sm"
                placeholder={t("organizationNamePlaceholder")}
                value={orgName}
                onChange={(e) => setOrgName(e.target.value)}
                required={!conciergeData}
                disabled={conciergeData ? !isEditing || isSaving : isSaving}
              />
            </div>
            <div>
              <label className="block text-sm mb-1">{t("conciergeName")}</label>
              <Input
                type="text"
                className="w-full border rounded px-3 py-2 text-sm"
                placeholder={t("conciergeNamePlaceholder")}
                value={conciergeName}
                onChange={(e) => setConciergeName(e.target.value)}
                required={!conciergeData}
                disabled={conciergeData ? !isEditing || isSaving : isSaving}
              />
            </div>
            <div>
              <label className="block text-sm mb-1">Gender</label>
              <Select
                value={gender}
                onValueChange={setGender}
                disabled={conciergeData ? !isEditing || isSaving : isSaving}
                required={!conciergeData}
              >
                <SelectTrigger className="w-full border rounded px-3 py-2 text-sm">
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm mb-1">{t("language")}</label>
              <Select
                value={language}
                onValueChange={setLanguage}
                disabled={conciergeData ? !isEditing || isSaving : isSaving}
                required
              >
                <SelectTrigger className="w-full border rounded px-3 py-2 text-sm">
                  <SelectValue placeholder={t("languagePlaceholder")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="zh-CN">中文</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm mb-1">
                {t("personalityPrompt")}
              </label>
              <Textarea
                rows={6}
                placeholder={t("personalityPromptPlaceholder")}
                value={personalityPrompt}
                onChange={(e) => setPersonalityPrompt(e.target.value)}
                required={!conciergeData}
                disabled={conciergeData ? !isEditing || isSaving : isSaving}
              />
            </div>
          </form>
        </TabsContent>
      </Tabs>

      {/* Setup Dialog */}
      <Dialog open={showSetupDialog} onOpenChange={setShowSetupDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("conciergeSetup")}</DialogTitle>
          </DialogHeader>
          <form
            className="space-y-4"
            onSubmit={async (e) => {
              e.preventDefault();
              setShowSetupDialog(false);
            }}
          >
            <div className="flex items-center justify-center mb-2">
              <label className="w-24 h-24 p-2 bg-gray-100 rounded flex items-center justify-center text-gray-400 text-xs cursor-pointer border border-gray-200">
                {conciergeImage ? (
                  <Image
                    src={URL.createObjectURL(conciergeImage)}
                    alt="avatar"
                    width={64}
                    height={64}
                    className="w-full h-full object-cover rounded"
                  />
                ) : avatarUrl ? (
                  <Image
                    src={avatarUrl}
                    alt="Concierge Avatar"
                    width={64}
                    height={64}
                    className="w-full h-full object-cover rounded"
                  />
                ) : (
                  <span className="flex flex-col items-center justify-center text-xs text-gray-400 text-center">
                    <Image
                      src="/default-avatar.png"
                      alt="Concierge Avatar"
                      width={30}
                      height={30}
                      className="object-contain rounded"
                    />
                    {t("changeCon")}
                  </span>
                )}
                <input
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageSelect}
                />
              </label>
            </div>
            <div>
              <label className="block text-sm mb-1">
                {t("organizationName")}
              </label>
              <input
                type="text"
                className="w-full border rounded px-3 py-2 text-sm"
                placeholder={t("organizationNamePlaceholder")}
                value={orgName}
                onChange={(e) => setOrgName(e.target.value)}
                required
              />
            </div>
            <div>
              <label className="block text-sm mb-1">{t("conciergeName")}</label>
              <input
                type="text"
                className="w-full border rounded px-3 py-2 text-sm"
                placeholder={t("conciergeNamePlaceholder")}
                value={conciergeName}
                onChange={(e) => setConciergeName(e.target.value)}
                required
              />
            </div>
            <div>
              <label className="block text-sm mb-1">
                {t("personalityPrompt")}
              </label>
              <textarea
                className="w-full border rounded px-3 py-2 text-sm"
                placeholder={t("personalityPromptPlaceholder")}
                value={personalityPrompt}
                onChange={(e) => setPersonalityPrompt(e.target.value)}
                required
                rows={6}
              />
            </div>
            <DialogFooter className="mt-4 flex-row justify-between">
              <DialogClose asChild>
                <button
                  type="button"
                  className="border border-gray-300 px-4 py-2 rounded bg-white text-gray-700 hover:bg-gray-100"
                >
                  {t("cancel")}
                </button>
              </DialogClose>
              <button
                type="submit"
                className="bg-[#0F172A] text-white px-4 py-2 rounded hover:bg-[#1E293B] transition"
              >
                {t("continue")}
              </button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {!subscriptionStatus?.active && notFound && (
        <SandboxSubscribeModalWrapper
          model={{}}
          onClose={() => setSubscribeModal(false)}
        />
      )}
    </div>
  );
};

export default SetupPage;
