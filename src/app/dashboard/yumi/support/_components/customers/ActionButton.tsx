import React from "react";
import { cn } from "@/lib/utils";

interface ActionButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "warning" | "destructive" | "default";
}

const base = "px-4 py-2 rounded-full cursor-pointer text-sm font-medium";
const variants = {
  primary: "bg-black text-white hover:bg-gray-900",
  warning: "bg-yellow-500 text-white hover:bg-yellow-600",
  destructive: "bg-red-600 text-white hover:bg-red-700",
  default: "bg-transparent border border-black text-gray-800 hover:bg-gray-300",
};

export default function ActionButton({
  variant = "default",
  className,
  ...props
}: ActionButtonProps) {
  return (
    <button className={cn(base, variants[variant], className)} {...props} />
  );
}
