"use client";
import React, { useEffect, useState } from "react";
import ChatSidebar from "./ChatSidebar";
import ChatWindow from "./ChatWindow";
import { useChatLog } from "../../_hooks/useChatLog";
import { useCallLog } from "../../_hooks/useCallLog";
import { useConcierge } from "../../../_context/ConciergeContext";
import { useTranslations } from "next-intl";
import type { ChatSession } from "../../_hooks/useChatLog";

interface ChatLogTabProps {
  userId: string;
}

type TabType = "chat" | "call";

export default function ChatLogTab({ userId }: ChatLogTabProps) {
  const { data: conciergeData } = useConcierge();
  const [tab, setTab] = useState<TabType>("chat");
  const {
    data: chatSessions = [],
    isLoading: isChatLoading,
    isError: isChatError,
  } = useChatLog(userId, conciergeData?.sandbox_id);
  const {
    data: callSessions = [],
    isLoading: isCallLoading,
    isError: isCallError,
  } = useCallLog(userId, conciergeData?.sandbox_id);

  const t = useTranslations("DashboardYumiSandbox.ChatLogTab");

  const [activeId, setActiveId] = useState<string | undefined>(undefined);

  // Pick sessions and loading/error state based on tab
  const sessions = tab === "chat" ? chatSessions : callSessions;
  const isLoading = tab === "chat" ? isChatLoading : isCallLoading;
  const isError = tab === "chat" ? isChatError : isCallError;

  // Normalize call sessions for sidebar and window
  const normalizedSessions: ChatSession[] = tab === "chat"
    ? chatSessions
    : callSessions.map((s, idx) => ({
        sessionId: s.id,
        title: `Call ${idx + 1}${s.stk ? `  (${s.stk})` : ""}`,
        startedAt: s.createdAt,
        resolvedAt: undefined,
        message: s.transcript.map((msg, i) => ({
          id: `${s.id}-${i}`,
          content: msg.content,
          sendfrom: msg.speaker === "Customer" ? "userMessage" : "apiMessage",
        })),
      }));

  useEffect(() => {
    if (normalizedSessions.length > 0) {
      setActiveId((prev) =>
        prev && normalizedSessions.some((s) => s.sessionId === prev)
          ? prev
          : normalizedSessions[0].sessionId
      );
    }
  }, [normalizedSessions, tab]);

  const activeSession = normalizedSessions.find((s) => s.sessionId === activeId);

  // Always render the tab switcher UI, and show loading, error, or empty state in the main panel as needed
  return (
    <div className="flex flex-col h-[600px] border rounded overflow-hidden">
      <div className="flex border-b bg-gray-50">
        <button
          className={`px-4 py-2 font-medium border-b-2 transition-colors ${
            tab === "chat"
              ? "border-blue-600 text-blue-700 bg-white"
              : "border-transparent text-gray-500 hover:text-blue-700"
          }`}
          onClick={() => setTab("chat")}
        >
          {t("chatTab", { defaultValue: "Chat Log" })}
        </button>
        <button
          className={`px-4 py-2 font-medium border-b-2 transition-colors ${
            tab === "call"
              ? "border-blue-600 text-blue-700 bg-white"
              : "border-transparent text-gray-500 hover:text-blue-700"
          }`}
          onClick={() => setTab("call")}
        >
          {t("callTab", { defaultValue: "Call Log" })}
        </button>
      </div>
      <div className="flex flex-1 min-h-0">
        <ChatSidebar
          sessions={normalizedSessions}
          activeId={activeId ?? ""}
          onSelect={setActiveId}
        />
        {/* Main panel: loading, error, empty, or chat window */}
        {isLoading ? (
          <div className="flex-1 flex flex-col h-full bg-white p-6 overflow-y-auto space-y-4 items-center justify-center">
            <div className="h-6 w-2/3 bg-gray-100 rounded mb-6 animate-pulse" />
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                className={`flex ${i % 2 === 0 ? "justify-start" : "justify-end"} mb-6`}
              >
                <div className="w-2/3">
                  <div className="h-4 w-1/2 bg-gray-200 rounded mb-2 animate-pulse" />
                  <div className="h-10 w-full bg-gray-100 rounded animate-pulse" />
                </div>
              </div>
            ))}
            <div className="h-4 w-1/2 bg-gray-100 rounded mx-auto mt-8 animate-pulse" />
          </div>
        ) : isError || !sessions.length ? (
          <div className="flex-1 flex flex-col items-center justify-center bg-white">
            <svg
              className="w-16 h-16 text-gray-300 mb-4"
              fill="none"
              stroke="currentColor"
              strokeWidth={1.5}
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M17.25 17.25V19.5A2.25 2.25 0 0 1 15 21.75H6.75A2.25 2.25 0 0 1 4.5 19.5V8.25A2.25 2.25 0 0 1 6.75 6h8.25A2.25 2.25 0 0 1 17.25 8.25v1.5M19.5 15.75l3-3m0 0l-3-3m3 3H9"
              />
            </svg>
            <div className="text-lg font-semibold text-gray-500 mb-1">
              {t("noSessions")}
            </div>
            <div className="text-sm text-gray-400">{t("noHistory")}</div>
          </div>
        ) : activeSession ? (
          <ChatWindow session={activeSession} userId={userId} />
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-400">
            {t("selectSession")}
          </div>
        )}
      </div>
    </div>
  );
}
