import React from "react";
import Image from "next/image";
import { useCustomer } from "../../_hooks/useCustomer";
import { useConcierge } from "../../../_context/ConciergeContext";
import { useTranslations } from "next-intl";

interface ChatMessageProps {
  message: {
    id: string;
    content: string;
    sendfrom: "userMessage" | "apiMessage";
    userId?: string; 
  };
}

const authorMap = {
  userMessage: { color: "text-gray-800" },
  apiMessage: { label: "Yumi", color: "text-blue-600" },
};

export default function ChatMessage({ message }: ChatMessageProps) {
  const { sendfrom, content, userId } = message;
  const color = authorMap[sendfrom].color;
  const { data, avatarUrl } = useConcierge();
  const t = useTranslations("DashboardYumiSandbox.ChatMessage");

  // Fetch customer name if this is a user message
  const { data: customerData } = useCustomer(userId || "");

  // Try to get the name from customerData, fallback to t('user')
  const customerName = customerData
    ? `${customerData.firstname} ${customerData.lastname}`
    : t("user");

  const customerAvatar =
    customerData && customerData[0] && customerData[0].avatar_url
      ? customerData[0].avatar_url
      : "/default-avatar.png";

  const label =
    sendfrom === "userMessage" ? customerName : data?.concierge || t("yumi");

  const isUser = sendfrom === "userMessage";

  return (
    <div
      className={`flex items-start gap-3 mb-2 ${
        isUser ? "justify-end" : "justify-start"
      }`}
    >
      {!isUser && (
        <div className="flex-none w-8 h-8 rounded-full flex items-center justify-center text-sm bg-gray-200 overflow-hidden">
          <Image
            src={avatarUrl || "/default-avatar.png"}
            alt={data?.concierge || t("yumi")}
            width={32}
            height={32}
            className="rounded-full object-cover"
          />
        </div>
      )}
      <div className={`flex-1 max-w-xs flex flex-col ${isUser ? "items-end" : "items-start"}`}>
        <div
          className={`flex items-baseline ${
            isUser ? "justify-end" : "justify-start"
          }`}
        >
          <span className={`font-medium ${color}`}>{label}</span>
        </div>
        <div
          className={`mt-1 text-sm whitespace-pre-wrap px-4 py-2 rounded-lg inline-block ${
            isUser
              ? "bg-blue-100 text-blue-900 ml-auto"
              : "bg-gray-100 text-gray-700 mr-auto"
          }`}
          style={isUser ? { alignSelf: "flex-end" } : {}}
        >
          {content}
        </div>
      </div>
      {isUser && (
        <div className="flex-none w-8 h-8 rounded-full flex items-center justify-center text-sm bg-gray-200 overflow-hidden">
          <Image
            src={customerAvatar}
            alt={customerName}
            width={32}
            height={32}
            className="rounded-full object-cover"
          />
        </div>
      )}
    </div>
  );
}
