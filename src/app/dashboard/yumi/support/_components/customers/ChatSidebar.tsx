import React from "react"
import { ChatSession } from "../../_hooks/useChatLog"

interface ChatSidebarProps {
  sessions: ChatSession[]
  activeId: string
  onSelect: (id: string) => void
}

export default function ChatSidebar({ sessions, activeId, onSelect }: ChatSidebarProps) {
  return (
    <div className="w-1/4 bg-gray-50 overflow-y-auto">
      {sessions.map(s => (
        <button
          key={s.sessionId}
          onClick={() => onSelect(s.sessionId)}
          className={`w-full text-left px-4 py-3 border-b hover:bg-gray-100 ${
            s.sessionId === activeId ? "bg-white font-medium" : "text-gray-600"
          }`}
        >
          {s.title}
        </button>
      ))}
    </div>
  )
}
