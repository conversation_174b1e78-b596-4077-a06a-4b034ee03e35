import React from "react";
import ChatMessage from "./ChatMessage";
import ChatDateSeparator from "./ChatDateSeparator";
import { ChatSession } from "../../_hooks/useChatLog";
import { useTranslations } from "next-intl";

interface ChatWindowProps {
  session: ChatSession;
  userId: string;
}

export default function ChatWindow({ session, userId }: ChatWindowProps) {
  const t = useTranslations("DashboardYumiSandbox.ChatWindow");
  return (
    <div className="flex-1 flex flex-col bg-white p-6 overflow-y-auto space-y-4">
      {/* Date separator / header */}
      {/* <ChatDateSeparator
        text={`${session.title} — conversation started at ${session.startedAt}`}
      /> */}

      {/* Messages */}
      <div className="space-y-6">
        {session.message.map((msg) => (
          <ChatMessage key={msg.id} message={{ ...msg, userId }} />
        ))}
      </div>

      {/* End notices */}
      {session.resolvedAt && (
        <div className="text-center text-xs text-gray-500 mt-8">
          {t("agentJoined", { time: session.resolvedAt })}
          <br />
          {t("agentResolved", { time: session.resolvedAt })}
        </div>
      )}
    </div>
  );
}
