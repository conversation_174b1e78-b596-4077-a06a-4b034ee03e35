import React from "react";
import InfoItem from "./InfoItem";
import { useCustomer } from "../../_hooks/useCustomer";
import { Badge } from "../tickets/Badge";
import { useTranslations } from "next-intl";

export default function CustomerInfoCard({ userId }: { userId: string }) {
  const { data, isLoading, isError } = useCustomer(userId);
  const t = useTranslations("DashboardYumiSandbox.CustomerInfoCard");

  if (isLoading) return <div>{t("loading")}</div>;
  if (isError || !data || data.length === 0) return <div>{t("notFound")}</div>;

  // Assuming the API returns an array with one customer object
  const customer = data;

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm grid grid-cols-1 md:grid-cols-3 gap-x-8 gap-y-4">
      <InfoItem
        label={t("fullName")}
        value={`${customer.firstname} ${customer.lastname}`}
      />
      <InfoItem label={t("email")} value={customer.email} />
      {/* <InfoItem label="Language" value={customer.language ?? "N/A"} /> */}
      {/* <InfoItem label="Timezone" value={customer.timezone ?? "N/A"} /> */}
      {/* <InfoItem
        label="Wallet Address"
        value={customer.wallet_address ?? "N/A"}
      /> */}
      {/* <InfoItem
        label="Sign-in Method"
        value={customer.signin_method ?? "N/A"}
      /> */}
      {/* <InfoItem
        label="Stripe Customer ID"
        value={customer.stripe_customer_id ?? "N/A"}
      /> */}
      {/* <InfoItem
        label="Payment Method"
        value={customer.payment_method ?? "N/A"}
      /> */}
      {/* <InfoItem
        label="Auto Renewal"
        value={customer.payment_auto_renewal ? "Enabled" : "Disabled"}
      /> */}
      {/* <InfoItem
        label="Next Billing Date"
        value={
          customer.next_billing_date
            ? new Date(customer.next_billing_date).toLocaleString()
            : "N/A"
        }
      /> */}
      <InfoItem
        label={t("availableCredits")}
        value={`$${customer.user_credits ?? 0}`}
      />
      <InfoItem
        label={t("ticketsRaised")}
        value={`${customer.ticket_count ?? 0}`}
      />
      {/* <InfoItem
        label="2FA Enabled"
        value={customer.two_factor_enabled ? "Yes" : "No"}
      /> */}
      {/* <InfoItem
        label="Created At"
        value={
          customer.created_at
            ? new Date(customer.created_at).toLocaleString()
            : "N/A"
        }
      /> */}
      {/* <InfoItem
        label="Updated At"
        value={
          customer.updated_at
            ? new Date(customer.updated_at).toLocaleString()
            : "N/A"
        }
      /> */}
      <div className="col-span-full md:col-auto">
        <InfoItem
          label={t("accountStatus")}
          value={
            <Badge variant={customer.is_active ? "active" : "active"}>
              {t("active")}
            </Badge>
          }
        />
      </div>
    </div>
  );
}
