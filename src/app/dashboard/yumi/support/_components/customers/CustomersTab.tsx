import React from "react";
import CustomerInfoCard from "./CustomerInfoCard";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "../../_ui/tabs";
import TransactionHistoryTab from "./TransactionHistoryTab";
import ChatLogTab from "./ChatLogTab";
import TicketsTab from "./TicketsTab";
import { usePathname } from "next/navigation";
import { Ticket } from "../../_types";
import { useConcierge } from "../../../_context/ConciergeContext";
import { useTranslations } from "next-intl";

export default function CustomerTabs({
  ticket,
  selectedId,
}: {
  ticket: Ticket;
  selectedId: string;
}) {
  const { data: conciergeData } = useConcierge();
  const pathname = usePathname();
  const isAdmin = pathname.startsWith("/admin");
  const t = useTranslations("DashboardYumiSandbox.CustomersTab");

  return (
    <Tabs defaultValue="details">
      <TabsList className="overflow-x-auto w-full sm:w-fit justify-between">
        <TabsTrigger value="details">{t("details")}</TabsTrigger>
        <TabsTrigger value="transactions">{t("transactions")}</TabsTrigger>
        <TabsTrigger value="chat">
          {conciergeData?.concierge} {t("chat")}
        </TabsTrigger>
        <TabsTrigger value="tickets">{t("tickets")}</TabsTrigger>
      </TabsList>

      <TabsContent value="details">
        <CustomerInfoCard userId={ticket.user_id ?? ""} />
      </TabsContent>
      <TabsContent value="transactions">
        <TransactionHistoryTab userId={ticket.user_id ?? ""} />
      </TabsContent>
      <TabsContent value="chat">
        <ChatLogTab userId={ticket.user_id ?? ""} />
      </TabsContent>
      <TabsContent value="tickets">
        <TicketsTab email={ticket.user_id ?? ""} selectedId={selectedId} />
      </TabsContent>
    </Tabs>
  );
}
