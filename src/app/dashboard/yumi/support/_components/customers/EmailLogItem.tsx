import React from "react";
import { EmailLogEntry } from "../../_types";

export default function EmailLogItem({ log }: { log: EmailLogEntry }) {
  return (
    <div className="space-y-1">
      {/* Header: from → to and timestamp */}
      <div className="flex justify-between text-xs text-gray-500">
        <span>
          <strong className="text-gray-700">{log.from}</strong> → {log.to}
        </span>
        <span>{log.timestamp}</span>
      </div>

      <p className="text-xs text-gray-500 italic">{log.subject}</p>

      {/* Body */}
      <p
        className="text-sm text-gray-700 whitespace-pre-wrap"
        dangerouslySetInnerHTML={{ __html: log.body }}
      />

      {/* Divider */}
      <div className="border-t border-gray-200 mt-4" />
    </div>
  );
}
