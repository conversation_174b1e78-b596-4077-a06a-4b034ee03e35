"use client";

import React from "react";
import EmailLogItem from "./EmailLogItem";
import { EmailLogEntry } from "../../_types";

interface EmailLogPanelProps {
  logs: EmailLogEntry[];
}

export default function EmailLogPanel({ logs }: EmailLogPanelProps) {
  return (
    <div className="h-[500px] overflow-y-auto space-y-6">
      {logs.map((log) => (
        <EmailLogItem key={log.id} log={log} />
      ))}
    </div>
  );
}

