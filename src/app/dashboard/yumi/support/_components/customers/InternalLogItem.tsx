

import React from "react";
import { InternalLog } from "../../_hooks/useInternalLogs";
import { useTranslations } from "next-intl";

export default function InternalLogItem({ log }: { log: InternalLog }) {
  const t = useTranslations("DashboardYumiSandbox.SupportPage.ticketsContent");
  // Use the same roleKeyMap as in TicketsContent
  const roleKeyMap: Record<string, string> = {
    "Customer Support Lead": "CustomerSupportLead",
    "Operations Team": "OperationsTeam",
    "Technical Team": "TechnicalTeam",
    "Product Team": "ProductTeam",
    "Marketing or Communications": "MarketingOrCommunications",
    "Leadership": "Leadership",
    "People & Culture (HR)": "PeopleAndCulture",
    "Finance & Billing": "FinanceAndBilling",
    "Compliance or Legal": "ComplianceOrLegal",
    "CEO": "CEO"
  };
  const getRoleLabel = (role: string) => t(`roles.${roleKeyMap[role] || role}`, { default: role });

  const renderRole = (role: string | string[]) => {
    if (Array.isArray(role)) {
      return role.map(getRoleLabel).join(", ");
    }
    return getRoleLabel(role);
  };

  return (
    <div className="space-y-1">
      {/* Header: from → to and timestamp */}
      <div className="flex justify-between text-xs text-gray-500">
        <span>
          <strong className="text-gray-700">{log.from_email}</strong>
          {" (" + renderRole(log.from_role) + ") "}→{" "}
          {Array.isArray(log.to_email) ? log.to_email.join(", ") : log.to_email}
          {" ("}
          {renderRole(log.to_role)}
          {")"}
        </span>
        <span>{new Date(log.created_at).toLocaleString()}</span>
      </div>

      {/* Message */}
      <p className="text-sm text-gray-700 whitespace-pre-wrap">{log.message}</p>

      {/* Divider */}
      <div className="border-t border-gray-200 mt-4" />
    </div>
  );
}
