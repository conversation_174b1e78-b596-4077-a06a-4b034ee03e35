"use client";

import React from "react";
import InternalLogItem from "./InternalLogItem";
import { InternalLog } from "../../_hooks/useInternalLogs";
import { useTranslations } from "next-intl";

interface InternalLogPanelProps {
  logs: InternalLog[];
}

export default function InternalLogPanel({ logs }: InternalLogPanelProps) {
  const t = useTranslations("DashboardYumiSandbox.InternalLogPanel");
  if (!logs.length) {
    return <div className="text-gray-400 text-sm p-4">{t("noLogs")}</div>;
  }
  return (
    <div className="h-[500px] overflow-y-auto space-y-6">
      {logs.map((log) => (
        <InternalLogItem key={log.id} log={log} />
      ))}
    </div>
  );
}
