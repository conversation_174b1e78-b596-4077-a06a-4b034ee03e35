import React, { useState } from "react";
import Image from "next/image";
import ActionButton from "./ActionButton";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
  DialogClose,
  DialogTrigger,
} from "../../_ui/dialog";
import { Button } from "../../_ui/button";
import { toast } from "sonner";
import { useTicket } from "../../_hooks/useTicket";
import { useCustomer } from "../../_hooks/useCustomer";
import { useAdjustCredit } from "../../_hooks/useAdjustCredit";
import { CustomerTicket, Ticket } from "../../_types";
import { useConcierge } from "../../../_context/ConciergeContext";
import { useTranslations } from "next-intl";

export default function ProfileHeader({
  customer,
  ticket,
}: {
  customer: CustomerTicket;
  ticket: Ticket;
}) {
  const [creditOpen, setCreditOpen] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [delta, setDelta] = useState(0);
  const [note, setNote] = useState("");
  const { data: conciergeData } = useConcierge();

  // Fetch up-to-date customer info
  const { data: customerData, refetch: refetchCustomer } = useCustomer(
    customer.user_id || ""
  );

  const { mutate: adjustCredit, isPending: isAdjusting } = useAdjustCredit();

  const t = useTranslations("DashboardYumiSandbox.ProfileHeader");

  return (
    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 overflow-y-auto max-h-[90vh] overflow-x-auto min-w-0">
      <div className="flex items-center gap-4 min-w-fit">
        <Image
          src={customer.imageUrl || "/default-avatar.png"}
          alt={customer.name}
          width={48}
          height={48}
          className="rounded-full"
        />
        <h2 className="text-xl font-semibold">{customer.name}</h2>
      </div>
      <div className="flex flex-wrap gap-2">
        <Dialog open={creditOpen} onOpenChange={setCreditOpen}>
          <DialogTrigger asChild>
            <ActionButton onClick={() => setCreditOpen(true)}>
              {t("creditActions")}
            </ActionButton>
          </DialogTrigger>

          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>{t("creditDialog.title")}</DialogTitle>
              <DialogDescription>
                {t("creditDialog.description")}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <span className="block text-sm font-medium text-gray-700">
                  {t("creditDialog.currentBalance")}
                </span>
                <p className="mt-1 text-lg font-semibold">
                  ${customerData?.[0]?.user_credits?.toLocaleString() ?? "0"}
                </p>
              </div>
              <label className="block text-sm font-medium">
                {t("creditDialog.amountLabel")}
              </label>
              <div className="flex items-center gap-2">
                {/* Changed to a normal number input */}
                <div className="flex items-center flex-1 border rounded px-3 py-2">
                  <span className="text-gray-600">$</span>
                  <input
                    type="number"
                    value={delta}
                    onChange={(e) => setDelta(Number(e.target.value))}
                    className="w-full border-none focus:ring-0 focus:outline-none ml-1"
                  />
                </div>
                <button
                  onClick={() => setDelta((d) => d + 1)}
                  className="px-3 py-2 bg-gray-900 text-white rounded"
                  type="button"
                >
                  {t("creditDialog.increment")}
                </button>
                <button
                  onClick={() => setDelta((d) => d - 1)}
                  className="px-3 py-2 bg-gray-900 text-white rounded"
                  type="button"
                >
                  {t("creditDialog.decrement")}
                </button>
              </div>

              <label className="block text-sm font-medium">
                {t("creditDialog.narration")}
              </label>
              <textarea
                rows={4}
                className="w-full rounded border p-2"
                value={note}
                onChange={(e) => setNote(e.target.value)}
              />
            </div>

            <DialogFooter className="flex justify-between">
              <DialogClose asChild>
                <Button variant="outline">{t("actions.cancel")}</Button>
              </DialogClose>
              <Button
                onClick={() => setConfirmOpen(true)}
                disabled={delta === 0 || note.trim() === "" || isAdjusting}
              >
                {t("creditDialog.saveChange")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* ───── Confirm Adjustment ───── */}
        <Dialog open={confirmOpen} onOpenChange={setConfirmOpen}>
          <DialogContent className="max-w-xs">
            <DialogHeader>
              <DialogTitle>{t("confirmDialog.title")}</DialogTitle>
              <DialogDescription>
                {t("confirmDialog.description", {
                  action:
                    delta > 0
                      ? t("confirmDialog.add")
                      : t("confirmDialog.subtract"),
                  amount: Math.abs(delta),
                })}
              </DialogDescription>
            </DialogHeader>

            <DialogFooter className="justify-end space-x-2">
              <DialogClose asChild>
                <Button variant="outline">{t("actions.cancel")}</Button>
              </DialogClose>
              <Button
                onClick={() => {
                  adjustCredit(
                    {
                      fake_user_id: customer.user_id ?? "",
                      amount: delta,
                      narration: note,
                      sandbox_id: conciergeData?.sandbox_id || "",
                    },
                    {
                      onSuccess: () => {
                        setConfirmOpen(false);
                        setCreditOpen(false);
                        setDelta(0);
                        setNote("");
                        toast.success(t("toast.creditsUpdated"), {
                          position: "top-right",
                          className: "p-4",
                        });
                        refetchCustomer();
                      },
                      onError: () => {
                        toast.error(t("toast.updateCreditsError"));
                      },
                    }
                  );
                }}
                disabled={isAdjusting}
              >
                {isAdjusting
                  ? t("actions.updating")
                  : t("confirmDialog.confirm")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
