// src/components/customers/TicketDetailsPanel.tsx
"use client";

import React from "react";
import Image from "next/image";
import { Ticket } from "../../_types";
import { Badge } from "../tickets/Badge";
import { sandboxUrl } from "@/config/baseUrl";
import { useTranslations } from "next-intl";

interface TicketDetailsPanelProps {
  ticket: Ticket;
}

export default function TicketDetailsPanel({
  ticket,
}: TicketDetailsPanelProps) {
  const {
    name: fullName,
    contact_email: email,
    type: issueType,
    priority,
    is_closed,
    subject,
    summary: description,
    stk,
    additional_info,
    attachment,
  } = ticket;

  const t = useTranslations(
    "DashboardYumiSandbox.SupportPage.ticketDetailsPanel"
  );

  // Status logic
  const status = is_closed ? t("status.resolved") : t("status.pending");
  const priorityLabel = priority;

  // Parse additional_info if present and valid JSON
  let parsedAdditionalInfo: { summary?: string }[] = [];
  if (additional_info) {
    try {
      const parsed = JSON.parse(additional_info);
      if (Array.isArray(parsed)) {
        parsedAdditionalInfo = parsed;
      }
    } catch {
      // Not valid JSON, ignore
    }
  }

  return (
    <div className="px-2 py-3 sm:px-4 md:px-6 md:py-4 overflow-y-auto h-[350px] sm:h-[500px]">
      {/* Grid of details */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 md:gap-x-8 gap-y-4">
        {/* Full Name */}
        <div>
          <p className="text-sm text-gray-500">{t("fullName")}</p>
          <p className="mt-1 text-sm font-medium text-gray-900">{fullName}</p>
        </div>

        {/* Email */}
        <div>
          <p className="text-sm text-gray-500">{t("email")}</p>
          <p className="mt-1 text-sm font-medium text-gray-900">{email}</p>
        </div>

        {/* Issue */}
        <div>
          <p className="text-sm text-gray-500">{t("issue")}</p>
          <p className="mt-1 text-sm font-medium text-gray-900">{issueType}</p>
        </div>

        {/* Status */}
        <div>
          <p className="text-sm text-gray-500">{t("status.label")}</p>
          <Badge variant={is_closed ? "resolved" : "pending"}>{status}</Badge>
        </div>

        {/* Priority */}
        <div>
          <p className="text-sm text-gray-500">{t("priority")}</p>
          <Badge>{priorityLabel}</Badge>
        </div>

        {/* Ticket ID (stk) */}
        <div>
          <p className="text-sm text-gray-500">{t("ticketId")}</p>
          <p className="mt-1 text-sm font-medium text-gray-900">{stk}</p>
        </div>
      </div>

      {/* Subject & Description */}
      <div className="mt-6 space-y-4">
        <div>
          <p className="text-sm text-gray-500">{t("subject")}</p>
          <p className="mt-1 text-sm font-medium text-gray-900 break-words">{subject}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">{t("description")}</p>
          <p className="mt-1 text-sm text-gray-700 break-words whitespace-pre-line">{description}</p>
        </div>
      </div>

      {/* Additional Info */}
      {parsedAdditionalInfo.length > 0 && (
        <div className="mt-6">
          <p className="text-sm text-gray-500">{t("additionalInfo")}</p>
          <ul className="mt-1 space-y-2 list-disc list-inside">
            {parsedAdditionalInfo.map((info, idx) => (
              <li key={idx} className="text-sm text-gray-700 break-words">
                {info.summary || JSON.stringify(info)}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Attachments */}
      {ticket.attachment && ticket.attachment.length > 0 && (
        <div className="mt-4">
          <div className="font-semibold mb-2">{t("attachments")}</div>
          <ul className="space-y-2">
            {ticket.attachment.map((fullPath: string, idx: number) => {
              const fileName =
                fullPath.split("/").pop() || `attachment-${idx + 1}`;
              const fileType = getFileType(fileName);

              return (
                <li key={fullPath} className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                  {/* Preview */}
                  {fileType === "image" && (
                    <div className="w-20 h-20 sm:w-16 sm:h-16 relative flex-shrink-0">
                      <Image
                        src={`${sandboxUrl}/ticket/get_attachment?full_path=${encodeURIComponent(
                          fullPath
                        )}`}
                        alt={t("imageAlt")}
                        width={80}
                        height={80}
                        className="object-cover rounded border w-full h-full"
                      />
                    </div>
                  )}
                  {fileType === "pdf" && (
                    <span className="w-20 h-20 sm:w-16 sm:h-16 flex items-center justify-center bg-gray-100 border rounded text-red-600 font-bold text-xl">
                      PDF
                    </span>
                  )}
                  {fileType === "other" && (
                    <span className="w-20 h-20 sm:w-16 sm:h-16 flex items-center justify-center bg-gray-100 border rounded text-gray-500 font-bold text-xl">
                      <svg width="24" height="24" fill="none">
                        <rect width="24" height="24" rx="4" fill="#e5e7eb" />
                        <text
                          x="12"
                          y="16"
                          textAnchor="middle"
                          fontSize="12"
                          fill="#6b7280"
                        >
                          FILE
                        </text>
                      </svg>
                    </span>
                  )}

                  {/* Download button and file name */}
                  <div>
                    <div className="text-xs text-gray-700 mb-1">
                      {t("attachmentLabel", { number: idx + 1 })}
                    </div>
                    <button
                      className="px-3 py-1 rounded bg-blue-100 text-blue-900 hover:bg-blue-200 text-sm w-full sm:w-auto"
                      onClick={async () => {
                        try {
                          const res = await fetch(
                            `${sandboxUrl}/ticket/get_attachment?full_path=${encodeURIComponent(
                              fullPath
                            )}`
                          );
                          if (!res.ok)
                            throw new Error("Failed to fetch attachment");
                          const blob = await res.blob();
                          const url = window.URL.createObjectURL(blob);
                          const a = document.createElement("a");
                          a.href = url;
                          a.download = fileName;
                          document.body.appendChild(a);
                          a.click();
                          a.remove();
                          window.URL.revokeObjectURL(url);
                        } catch (err) {
                          alert(t("downloadError"));
                          console.error(err);
                        }
                      }}
                    >
                      {t("download")}
                    </button>
                  </div>
                </li>
              );
            })}
          </ul>
        </div>
      )}
      {ticket.attachment_email && ticket.attachment_email.length > 0 && (
        <div className="mt-4">
          <div className="font-semibold mb-2">{t("emailAttachments")}</div>
          <ul className="space-y-2">
            {ticket.attachment_email.map((fullPath: string, idx: number) => {
              const fileName =
                fullPath.split("/").pop() || `attachment-${idx + 1}`;
              const fileType = getFileType(fileName);

              return (
                <li key={fullPath} className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                  {/* Preview */}
                  {fileType === "image" && (
                    <div className="w-20 h-20 sm:w-16 sm:h-16 relative flex-shrink-0">
                      <Image
                        src={`${sandboxUrl}/ticket/get_attachment?full_path=${encodeURIComponent(
                          fullPath
                        )}`}
                        alt={t("imageAlt")}
                        width={80}
                        height={80}
                        className="object-cover rounded border w-full h-full"
                      />
                    </div>
                  )}
                  {fileType === "pdf" && (
                    <span className="w-20 h-20 sm:w-16 sm:h-16 flex items-center justify-center bg-gray-100 border rounded text-red-600 font-bold text-xl">
                      PDF
                    </span>
                  )}
                  {fileType === "other" && (
                    <span className="w-20 h-20 sm:w-16 sm:h-16 flex items-center justify-center bg-gray-100 border rounded text-gray-500 font-bold text-xl">
                      <svg width="24" height="24" fill="none">
                        <rect width="24" height="24" rx="4" fill="#e5e7eb" />
                        <text
                          x="12"
                          y="16"
                          textAnchor="middle"
                          fontSize="12"
                          fill="#6b7280"
                        >
                          FILE
                        </text>
                      </svg>
                    </span>
                  )}

                  {/* Download button and file name */}
                  <div>
                    <div className="text-xs text-gray-700 mb-1">
                      {t("attachmentLabel", { number: idx + 1 })}
                    </div>
                    <button
                      className="px-3 py-1 rounded bg-blue-100 text-blue-900 hover:bg-blue-200 text-sm w-full sm:w-auto"
                      onClick={async () => {
                        try {
                          const res = await fetch(
                            `${sandboxUrl}/ticket/get_attachment?full_path=${encodeURIComponent(
                              fullPath
                            )}`
                          );
                          if (!res.ok)
                            throw new Error("Failed to fetch attachment");
                          const blob = await res.blob();
                          const url = window.URL.createObjectURL(blob);
                          const a = document.createElement("a");
                          a.href = url;
                          a.download = fileName;
                          document.body.appendChild(a);
                          a.click();
                          a.remove();
                          window.URL.revokeObjectURL(url);
                        } catch (err) {
                          alert(t("downloadError"));
                          console.error(err);
                        }
                      }}
                    >
                      {t("download")}
                    </button>
                  </div>
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
}

function getFileType(fileName: string) {
  const ext = fileName.split(".").pop()?.toLowerCase();
  if (["jpg", "jpeg", "png", "gif"].includes(ext || "")) return "image";
  if (ext === "pdf") return "pdf";
  return "other";
}
