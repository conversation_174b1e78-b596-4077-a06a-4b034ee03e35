// components/customers/TicketListItem.tsx
import React, { forwardRef } from "react";
import { format } from "date-fns";
import { UserTicket } from "../../_hooks/useUserTickets";
import { Badge } from "../tickets/Badge";
import { useTranslations } from "next-intl";

interface TicketListItemProps {
  ticket: UserTicket;
  active: boolean;
  onClick: () => void;
}

const TicketListItem = forwardRef<HTMLButtonElement, TicketListItemProps>(
  ({ ticket, active, onClick }, ref) => {
    const t = useTranslations("DashboardYumiSandbox.SupportPage.status");
    const tDate = useTranslations("DashboardYumiSandbox.SupportPage");
    return (
      <button
        ref={ref}
        onClick={onClick}
        className={`w-full flex items-center justify-between px-4 py-3 border-b hover:bg-gray-100 ${
          active ? "bg-white font-medium" : "text-gray-600"
        }`}
      >
        <span>
          {
            // Use a translation key for date format if you want to localize it, otherwise keep as is
            // Example: tDate('dateFormat', { date: ticket.created_at })
            // For now, keep the format as before:
            format(new Date(ticket.created_at), "yy-MM-dd HH:mm")
          }
        </span>
        <Badge variant={ticket.is_closed ? "resolved" : "pending"}>
          {ticket.is_closed ? t("resolved") : t("pending")}
        </Badge>
      </button>
    );
  }
);

TicketListItem.displayName = "TicketListItem";

export default TicketListItem;
