// components/customers/TicketMessage.tsx
import React from "react";
import { TicketMessage } from "../../_types";

export default function TicketMessageItem({
  message,
}: {
  message: TicketMessage;
}) {
  return (
    <div className="flex items-start gap-3">
      <div className="flex-none w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-sm">
        {message.author.charAt(0)}
      </div>
      <div className="flex-1">
        <div className="flex justify-between">
          <span className="font-medium text-gray-800">{message.author}</span>
          <span className="text-xs text-gray-400">{message.timestamp}</span>
        </div>
        <p className="mt-1 text-sm text-gray-700 whitespace-pre-wrap">
          {message.text}
        </p>
      </div>
    </div>
  );
}
