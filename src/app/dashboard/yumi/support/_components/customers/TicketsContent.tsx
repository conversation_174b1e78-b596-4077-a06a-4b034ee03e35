// src/components/customers/TicketsContent.tsx
"use client";

import React, { useState } from "react";
import ActionButton from "./ActionButton";
import EmailLogPanel from "./EmailLogPanel";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../_ui/tabs";
import TicketDetailsPanel from "./TicketDetailsPanel";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
  DialogClose,
  DialogTrigger,
} from "../../_ui/dialog";
import { Button } from "../../_ui/button";
import { useQueryClient } from "@tanstack/react-query";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectTrigger,
} from "../../_ui/select";
import { Textarea } from "../../_ui/textarea";
import InternalLogPanel from "./InternalLogPanel";
import { usePathname } from "next/navigation";
import { useTicket } from "../../_hooks/useTicket";
import { useEmailLogs } from "../../_hooks/useEmailLogs";
import { useResolveTicket } from "../../_hooks/useResolveTicket";
import { useEscalationMembers } from "../../_hooks/useEscalationMembers";
import { useEscalateTicket } from "../../_hooks/useEscalateTicket";
import { useReplyInternalMessage } from "../../_hooks/useReplyInternalMessage";
import { useInternalLogs } from "../../_hooks/useInternalLogs";
import { useConcierge } from "../../../_context/ConciergeContext";
import { useTranslations } from "next-intl";

interface TicketsContentProps {
  ticketId?: string;
  onResolve?: (id: string) => void;
}

export default function TicketsContent({
  ticketId,
  onResolve = () => {},
}: TicketsContentProps) {
  const t = useTranslations("DashboardYumiSandbox.SupportPage.ticketsContent");
  const tGlobal = useTranslations("global");
  const { data: conciergeData } = useConcierge();
  const {
    data: ticket,
    isLoading,
    isError,
    refetch: refetchTicket, // <-- get refetch function from useTicket
  } = useTicket(ticketId || "", conciergeData?.sandbox_id);
  const { data: emailLogs, isLoading: isLoadingLogs } = useEmailLogs(
    ticket?.user_id || "",
    conciergeData?.sandbox_id
  );
  const { mutate: resolveTicket, isPending: isResolving } = useResolveTicket();
  const queryClient = useQueryClient();
  const {
    data: escalationMembers = [],
    isLoading: isEscalationLoading,
    isError: isEscalationError,
  } = useEscalationMembers();
  const [open, setOpen] = useState(false);
  const [referOpen, setReferOpen] = useState(false);
  const [referTo, setReferTo] = useState<string[]>([]);
  const [referRoles, setReferRoles] = useState<string[]>([]);
  const [referUsers, setReferUsers] = useState<string[]>([]);
  const [internalExpanded, setInternalExpanded] = useState(false);
  const [referReason, setReferReason] = useState("");
  const [referDropdownOpen, setReferDropdownOpen] = useState(false);
  const [referConfirmOpen, setReferConfirmOpen] = useState(false);
  const { mutate: escalateTicket, isPending: isEscalating } =
    useEscalateTicket();
  const pathname = usePathname();

  // Add this line to fetch internal logs for the ticket
  const { data: internalLogs = [], refetch: refetchInternalLogs } =
    useInternalLogs(ticket?.stk || "");

  const handleResolve = () => {
    if (!ticket) return;
    const idxInt = ticket && ticket.idx ? Number(ticket.idx) : undefined;
    if (idxInt) {
      resolveTicket(idxInt, {
        onSuccess: () => {
          setOpen(false);
          onResolve(ticket.idx);
          toast.success(t("toast.ticketResolved"), {
            position: "top-right",
            className: "p-4",
          });
          refetchTicket();
          queryClient.invalidateQueries({ queryKey: ["userTickets"] }); // refetch tickets list
        },
      });
    }
  };

  if (!ticketId) {
    return (
      <div className="flex-1 flex flex-col bg-white p-6 space-y-4 items-center justify-center text-gray-400">
        {t("selectTicketPrompt")}
      </div>
    );
  }

  if (isLoading || isLoadingLogs) {
    return (
      <div className="flex-1 flex flex-col bg-white p-6 space-y-4">
        {/* Tabs skeleton */}
        <div className="h-10 w-1/3 bg-gray-100 rounded animate-pulse mb-4" />
        {/* Ticket details skeleton */}
        <div className="h-32 w-full bg-gray-100 rounded animate-pulse mb-4" />
        {/* Email log skeleton */}
        <div className="h-48 w-full bg-gray-50 rounded animate-pulse" />
      </div>
    );
  }

  if (isError || !ticket) {
    return (
      <div className="flex-1 flex flex-col bg-white p-6 space-y-4 items-center justify-center text-gray-400">
        {t("error.couldNotLoadTicket")}
      </div>
    );
  }

  const nonInternalEmails = new Set(
    escalationMembers.filter((m) => m.role !== "internal").map((m) => m.email)
  );

  // Then filter out any internal-member whose email is in that set
  const filteredMembers = escalationMembers.filter(
    (m) => !(m.role === "internal" && nonInternalEmails.has(m.email))
  );

  // Now build your groups off the filtered list
  const uniqueRoles = Array.from(new Set(filteredMembers.map((m) => m.role)));

  const impliedEmailsByRole = new Set(
    referRoles.flatMap((role) =>
      escalationMembers.filter((m) => m.role === role).map((m) => m.email)
    )
  );

  // 1. Map backend role to translation key
  const roleKeyMap: Record<string, string> = {
    "Customer Support Lead": "CustomerSupportLead",
    "Operations Team": "OperationsTeam",
    "Technical Team": "TechnicalTeam",
    "Product Team": "ProductTeam",
    "Marketing or Communications": "MarketingOrCommunications",
    "Leadership": "Leadership",
    "People & Culture (HR)": "PeopleAndCulture",
    "Finance & Billing": "FinanceAndBilling",
    "Compliance or Legal": "ComplianceOrLegal",
  };

  // 2. Helper to get translated role
  const getRoleLabel = (role: string) =>
    t(`roles.${roleKeyMap[role] || role}`, { default: role });

  return (
    <div className="relative flex-1 flex flex-col bg-white p-6 space-y-4">
      <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between overflow-x-auto">
        <Tabs
          defaultValue="details"
          className="flex-1 border-b border-gray-300"
        >
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
            <TabsList className="pb-0 overflow-auto justify-between w-full sm:w-fit">
              <TabsTrigger
                value="details"
                className="data-[state=active]:bg-transparent data-[state=active]:text-black data-[state=active]:shadow-none data-[state=active]:border-b data-[state=active]:border-b-blue-300 data-[state=active]:rounded-none rounded-none text-gray-400 "
              >
                {t("tabs.details")}
              </TabsTrigger>
              <TabsTrigger
                value="email"
                className="data-[state=active]:bg-transparent data-[state=active]:text-black data-[state=active]:shadow-none data-[state=active]:border-b data-[state=active]:border-b-blue-300 data-[state=active]:rounded-none rounded-none text-gray-400 "
              >
                {t("tabs.emailLog")}
              </TabsTrigger>
              <TabsTrigger
                value="internal"
                className="data-[state=active]:bg-transparent data-[state=active]:text-black data-[state=active]:shadow-none data-[state=active]:border-b data-[state=active]:border-b-blue-300 data-[state=active]:rounded-none rounded-none text-gray-400 "
              >
                {t("tabs.internalLogs")}
              </TabsTrigger>
            </TabsList>
            {/* Action buttons: move below tabs on mobile, inline on desktop */}
            <div className="flex flex-col gap-2 w-full sm:w-auto sm:flex-row sm:items-center sm:gap-2">
              {/* Refer Issue Dialog */}
              <Dialog open={referOpen} onOpenChange={setReferOpen}>
                <DialogTrigger asChild>
                  <ActionButton onClick={() => setReferOpen(true)} className="w-[70%] sm:w-auto">
                    {t("referIssue")}
                  </ActionButton>
                </DialogTrigger>
                <DialogContent className="!top-[5%] !translate-y-0">
                  <DialogHeader>
                    <DialogTitle>{t("referDialog.title")}</DialogTitle>
                    <DialogDescription>
                      {t("referDialog.description")}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <label
                        className="block text-sm font-medium mb-1"
                        htmlFor="refer-to"
                      >
                        {t("referDialog.referTo")}
                      </label>
                      <Select
                        open={referDropdownOpen}
                        onOpenChange={setReferDropdownOpen}
                      >
                        <SelectTrigger id="refer-to" className="w-full">
                          {referRoles.length + referUsers.length === 0 ? (
                            isEscalationLoading ? (
                              tGlobal("loading")
                            ) : (
                              t("referDialog.selectWho")
                            )
                          ) : (
                            <div className="flex flex-wrap gap-1">
                              {[...referRoles, ...referUsers]
                                .slice(0, 2)
                                .map((value) => (
                                  <span
                                    key={value}
                                    className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full"
                                  >
                                    {referRoles.includes(value)
                                      ? getRoleLabel(value)
                                      : value}
                                  </span>
                                ))}
                              {referRoles.length + referUsers.length > 2 && (
                                <span className="inline-block text-xs text-gray-500 ml-1">
                                  +{referRoles.length + referUsers.length - 2}{" "}
                                  more
                                </span>
                              )}
                            </div>
                          )}
                        </SelectTrigger>
                        <SelectContent>
                          {/* 1) Non‑internal roles */}
                          {uniqueRoles
                            .filter((role) => role !== "internal")
                            .map((role) => (
                              <SelectGroup key={role}>
                                <div className="flex items-center px-2 py-1">
                                  <input
                                    type="checkbox"
                                    checked={referRoles.includes(role)}
                                    onChange={(e) => {
                                      if (e.target.checked) {
                                        setReferRoles((r) => [...r, role]);
                                      } else {
                                        setReferRoles((r) =>
                                          r.filter((rr) => rr !== role)
                                        );
                                      }
                                    }}
                                    id={`role-checkbox-${role}`}
                                    className="mr-2"
                                  />
                                  <label htmlFor={`role-checkbox-${role}`}>
                                    {getRoleLabel(role)}
                                  </label>
                                </div>
                              </SelectGroup>
                            ))}
                        </SelectContent>
                      </Select>
                      {isEscalationError && (
                        <div className="text-xs text-red-500 mt-1">
                          {t("referDialog.failedToLoad")}
                        </div>
                      )}
                    </div>
                    <div>
                      <label
                        className="block text-sm font-medium mb-1"
                        htmlFor="refer-reason"
                      >
                        {t("referDialog.reason")}
                      </label>
                      <Textarea
                        id="refer-reason"
                        value={referReason}
                        onChange={(e) => setReferReason(e.target.value)}
                        placeholder={t("referDialog.reasonPlaceholder")}
                        rows={6}
                      />
                    </div>
                  </div>
                  <DialogFooter className="flex justify-between w-full items-center">
                    <DialogClose asChild>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setReferOpen(false)}
                      >
                        {tGlobal("Cancel")}
                      </Button>
                    </DialogClose>
                    <Button
                      type="button"
                      onClick={() => setReferConfirmOpen(true)}
                      disabled={!referReason}
                    >
                      {t("referDialog.referIssue")}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
              {/* Confirmation Dialog */}
              <Dialog
                open={referConfirmOpen}
                onOpenChange={setReferConfirmOpen}
              >
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{t("referDialog.confirmTitle")}</DialogTitle>
                    <DialogDescription>
                      {t("referDialog.confirmDescription")}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-2">
                    <div>
                      <span className="font-medium">{t("referDialog.to")}</span>
                      <ul className="list-disc list-inside text-sm mt-1">
                        {[...referRoles, ...referUsers].map((item) => (
                          <li key={item}>
                            {referRoles.includes(item) ? getRoleLabel(item) : item}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <span className="font-medium">
                        {t("referDialog.reason")}
                      </span>
                      <div className="text-sm mt-1">{referReason}</div>
                    </div>
                  </div>
                  <DialogFooter>
                    <DialogClose asChild>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setReferConfirmOpen(false)}
                      >
                        {tGlobal("Cancel")}
                      </Button>
                    </DialogClose>
                    <Button
                      type="button"
                      onClick={() => {
                        if (!ticket) return;
                        const users: { email: string; role: string }[] = [
                          // Expand each selected role into *all* members of that role
                          ...referRoles.flatMap((role) =>
                            escalationMembers
                              .filter((m) => m.role === role)
                              .map((m) => ({ email: m.email, role }))
                          ),
                          // And each selected internal user as their own entry
                          ...referUsers.map((email) => ({
                            email,
                            role: "internal",
                          })),
                        ];

                        escalateTicket(
                          {
                            stk: ticket.stk,
                            reason: referReason,
                            users,
                          },
                          {
                            onSuccess: () => {
                              setReferConfirmOpen(false);
                              setReferOpen(false);
                              setReferRoles([]);
                              setReferUsers([]);
                              setReferReason("");
                              toast.success(t("toast.ticketReferred"), {
                                position: "top-right",
                                className: "p-4",
                              });
                              refetchInternalLogs();
                            },
                            onError: () => {
                              toast.error(t("toast.failedToRefer"));
                            },
                          }
                        );
                      }}
                      disabled={isEscalating}
                    >
                      {isEscalating
                        ? t("referDialog.referring")
                        : t("referDialog.confirm")}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
              {/* </>
              )} */}

              {/* Resolve Ticket Dialog */}
              <Dialog open={open} onOpenChange={setOpen}>
                <DialogTrigger asChild>
                  <ActionButton
                    variant="primary"
                    onClick={() => setOpen(true)}
                    disabled={isResolving || ticket.is_closed}
                    className="w-[70%] sm:w-auto"
                  >
                    {ticket.is_closed
                      ? t("resolveDialog.resolved")
                      : isResolving
                      ? t("resolveDialog.resolving")
                      : t("resolveDialog.resolveTicket")}
                  </ActionButton>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{t("resolveDialog.title")}</DialogTitle>
                    <DialogDescription>
                      {t("resolveDialog.description")}
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter>
                    <DialogClose asChild>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setOpen(false)}
                      >
                        {tGlobal("Cancel")}
                      </Button>
                    </DialogClose>
                    <Button onClick={handleResolve} disabled={isResolving}>
                      {isResolving
                        ? t("resolveDialog.resolving")
                        : t("resolveDialog.confirm")}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
          <TabsContent value="details" className="h-full">
            <TicketDetailsPanel ticket={ticket} />
          </TabsContent>
          <TabsContent value="email">
            <EmailLogPanel
              logs={(emailLogs ?? []).filter((log) => log.stk === ticket.stk)}
            />
          </TabsContent>
          <TabsContent value="internal">
            <InternalLogPanel logs={internalLogs} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
