// components/customers/TicketsSidebar.tsx
import React, { useEffect, useRef } from "react";
import TicketListItem from "./TicketListItem";
import { UserTicket } from "../../_hooks/useUserTickets";

interface TicketsSidebarProps {
  tickets: UserTicket[];
  activeId: string | undefined;
  onSelect: (id: string) => void;
}

export default function TicketsSidebar({
  tickets,
  activeId,
  onSelect,
}: TicketsSidebarProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const activeRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (activeRef.current && containerRef.current) {
      // Scroll the active ticket into view (top of the sidebar)
      activeRef.current.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
        inline: "nearest",
      });
    }
  }, [activeId]);

  return (
    <div ref={containerRef} className="bg-gray-50 overflow-y-auto">
      {tickets.map((t) => (
        <TicketListItem
          key={t.stk}
          ticket={t}
          active={t.stk === activeId}
          onClick={() => onSelect(t.stk)}
          ref={t.stk === activeId ? activeRef : undefined}
        />
      ))}
    </div>
  );
}
