"use client";
import React, { useState, useEffect } from "react";
import TicketsSidebar from "./TicketsSidebar";
import TicketsContent from "./TicketsContent";
import { useUserTickets } from "../../_hooks/useUserTickets";
import { useConcierge } from "../../../_context/ConciergeContext";

interface TicketsTabProps {
  email: string;
  selectedId?: string;
}

export default function TicketsTab({ email, selectedId }: TicketsTabProps) {
  const { data: conciergeData } = useConcierge();
  const {
    data: tickets = [],
    isLoading,
    isError,
  } = useUserTickets(email, conciergeData?.sandbox_id);
  const [activeId, setActiveId] = useState<string | undefined>(selectedId);

  // When tickets or selectedId change, ensure activeId matches selectedId if present in tickets
  useEffect(() => {
    if (tickets.length && selectedId) {
      const found = tickets.find((t) => t.stk === selectedId);
      setActiveId(found ? selectedId : tickets[0]?.stk);
    } else if (tickets.length) {
      setActiveId(tickets[0]?.stk);
    }
  }, [tickets, selectedId]);

  if (isLoading)
    return (
      <div className="flex h-[600px] items-center justify-center w-full">
        {/* Sidebar skeleton */}
        <div className="h-full w-56 bg-gray-100 border-r flex-shrink-0 animate-pulse" />
        {/* Ticket content skeleton */}
        <div className="flex-1 flex flex-col h-full">
          <div className="h-12 w-full bg-gray-100 border-b animate-pulse" />
          <div className="flex-1 w-full bg-gray-50 animate-pulse" />
          <div className="h-16 w-full bg-gray-100 border-t animate-pulse" />
        </div>
      </div>
    );
  if (isError) return <div>Could not load tickets.</div>;
  if (!tickets.length) return <div>No tickets found.</div>;

  return (
    <div className="flex h-[600px] border rounded overflow-x-auto overflow-y-hidden">
      <div className="hidden sm:block h-full">
        <TicketsSidebar
          tickets={tickets}
          activeId={activeId}
          onSelect={setActiveId}
        />
      </div>
      <div className="flex-1 min-w-0">
        <TicketsContent ticketId={activeId} />
      </div>
    </div>
  );
}
