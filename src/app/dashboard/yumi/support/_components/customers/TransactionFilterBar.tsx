// components/customers/TransactionFilterBar.tsx
"use client";

import React from "react";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectScrollUpButton,
  SelectItem,
  SelectScrollDownButton,
} from "@/components/ui/select";
import { SelectViewport } from "@radix-ui/react-select";
import { useTranslations } from "next-intl";

interface TransactionFilterBarProps {
  period: string;
  onPeriodChange: (v: string) => void;
  model: string;
  onModelChange: (v: string) => void;
}

const PERIODS = ["All Time", "Last 7 Days", "This Month", "Custom…"];
const MODELS = ["All", "Orion", "Hermes", "Olympus"];

export function TransactionFilterBar({
  period,
  onPeriodChange,
  model,
  onModelChange,
}: TransactionFilterBarProps) {
  const t = useTranslations(
    "DashboardYumiSandbox.SupportPage.transactionFilterBar"
  );
  return (
    <div className="flex flex-wrap items-center gap-4 mb-4">
      <Select value={period} onValueChange={onPeriodChange}>
        <SelectTrigger className="w-[180px]">
          {t("period")}:{" "}
          <SelectValue placeholder={t(`periodOptions.${period}`) || period} />
        </SelectTrigger>
        <SelectContent>
          <SelectScrollUpButton />
          <SelectViewport>
            {PERIODS.map((p) => (
              <SelectItem key={p} value={p}>
                {t(`periodOptions.${p}`) || p}
              </SelectItem>
            ))}
          </SelectViewport>
          <SelectScrollDownButton />
        </SelectContent>
      </Select>

      {/* <Select value={model} onValueChange={onModelChange}>
        <SelectTrigger className="w-[180px]">
          {t("model")}:{" "}
          <SelectValue placeholder={t(`modelOptions.${model}`) || model} />
        </SelectTrigger>
        <SelectContent>
          <SelectScrollUpButton />
          <SelectViewport>
            {MODELS.map((m) => (
              <SelectItem key={m} value={m}>
                {t(`modelOptions.${m}`) || m}
              </SelectItem>
            ))}
          </SelectViewport>
          <SelectScrollDownButton />
        </SelectContent>
      </Select> */}
    </div>
  );
}
