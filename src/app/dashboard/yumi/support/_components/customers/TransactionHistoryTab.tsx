// components/customers/TransactionHistoryTab.tsx
"use client";

import React, { useState } from "react";
import { TransactionFilterBar } from "./TransactionFilterBar";
import { TransactionTable } from "./TransactionTable";
import { subDays, startOfMonth, endOfMonth, format } from "date-fns";
import { useTransactionHistory } from "../../_hooks/useTransactionHistory";
import { Pagination } from "../tickets/Pagination";
import { useTranslations } from "next-intl";

interface TransactionHistoryTabProps {
  userId: string;
}

export default function TransactionHistoryTab({
  userId,
}: TransactionHistoryTabProps) {
  const t = useTranslations(
    "DashboardYumiSandbox.SupportPage.transactionHistoryTab"
  );
  const [period, setPeriod] = useState("All Time");
  const [model, setModel] = useState("All");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [customFrom, setCustomFrom] = useState<string | undefined>();
  const [customTo, setCustomTo] = useState<string | undefined>();

  // Convert period to fromDate/toDate
  let fromDate: string | undefined;
  let toDate: string | undefined;
  if (period === "Last 7 Days") {
    fromDate = format(subDays(new Date(), 7), "yyyy-MM-dd");
    toDate = format(new Date(), "yyyy-MM-dd");
  } else if (period === "This Month") {
    fromDate = format(startOfMonth(new Date()), "yyyy-MM-dd");
    toDate = format(endOfMonth(new Date()), "yyyy-MM-dd");
  } else if (period === "Custom…") {
    fromDate = customFrom;
    toDate = customTo;
  } else {
    fromDate = undefined;
    toDate = undefined;
  }
  // For "Custom…" you would add your own date pickers and logic

  // Convert model to search (if not "All")
  const search = model !== "All" ? model : undefined;

  const { data, isLoading, isError } = useTransactionHistory(userId, {
    page,
    limit: pageSize,
    fromDate,
    toDate,
    search,
  });

  return (
    <div>
      {/* <TransactionFilterBar
        period={period}
        onPeriodChange={(v) => {
          setPeriod(v);
          setPage(1);
          if (v !== "Custom…") {
            setCustomFrom(undefined);
            setCustomTo(undefined);
          }
        }}
        model={model}
        onModelChange={(v) => {
          setModel(v);
          setPage(1);
        }}
      /> */}

      {period === "Custom…" && (
        <div className="flex gap-2 mb-4">
          <label>
            {t("custom.from")}:{" "}
            <input
              type="date"
              value={customFrom || ""}
              onChange={(e) => {
                setCustomFrom(e.target.value);
                if (customTo && e.target.value && customTo < e.target.value) {
                  setCustomTo(undefined);
                }
              }}
              className="border rounded px-2 py-1"
              max={customTo || undefined}
            />
          </label>
          <label>
            {t("custom.to")}:{" "}
            <input
              type="date"
              value={customTo || ""}
              onChange={(e) => setCustomTo(e.target.value)}
              className="border rounded px-2 py-1"
              min={customFrom || undefined}
              disabled={!customFrom}
            />
          </label>
        </div>
      )}

      {isError ? (
        <div>{t("error")}</div>
      ) : (
        <TransactionTable
          data={isLoading || !data ? [] : data.history}
          isLoading={isLoading}
        />
      )}

      {data && (
        <div className="flex items-center justify-between py-4">
          <p className="text-sm text-gray-600">
            {t("showingRows", {
              count: isLoading ? 0 : data.history.length,
              total: isLoading ? 0 : data.pagination.total,
            })}
          </p>
          <Pagination
            page={data.pagination.currentPage}
            totalPages={data.pagination.totalPages}
            onPageChange={setPage}
            pageSize={pageSize}
            onPageSizeChange={(size) => {
              setPageSize(size);
              setPage(1);
            }}
            pageSizeOptions={[10, 25, 50]}
          />
        </div>
      )}
    </div>
  );
}
