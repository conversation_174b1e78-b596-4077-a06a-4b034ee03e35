// components/customers/TransactionTable.tsx
"use client";

import React from "react";
import { format } from "date-fns";
import { useTranslations } from "next-intl";

interface Transaction {
  type: string;
  timestamp: string;
  amount?: string;
  reason: string;
  plan?: string;
  start_date?: string;
  end_date?: string;
}

interface TransactionTableProps {
  data: Transaction[];
  isLoading?: boolean;
}

export function TransactionTable({ data, isLoading }: TransactionTableProps) {
  const t = useTranslations(
    "DashboardYumiSandbox.SupportPage.transactionTable"
  );
  // Skeleton rows for loading state
  const skeletonRows = Array.from({ length: 8 });

  return (
    <div className="overflow-x-auto border rounded">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {[
              t("table.dateTime"),
              t("table.amount"),
              t("table.type"),
              t("table.narration"),
            ].map((col) => (
              <th
                key={col}
                className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase"
              >
                {col}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-100">
          {isLoading
            ? skeletonRows.map((_, i) => (
                <tr key={i} className="animate-pulse">
                  <td className="px-4 py-2">
                    <div className="h-4 bg-gray-200 rounded w-24" />
                  </td>
                  <td className="px-4 py-2">
                    <div className="h-4 bg-gray-200 rounded w-16" />
                  </td>
                  <td className="px-4 py-2">
                    <div className="h-4 bg-gray-200 rounded w-20" />
                  </td>
                  <td className="px-4 py-2">
                    <div className="h-4 bg-gray-200 rounded w-40" />
                  </td>
                </tr>
              ))
            : data.map((tx, i) => (
                <tr key={i} className="hover:bg-gray-50">
                  <td className="px-4 py-2 text-sm">
                    {tx.timestamp
                      ? format(new Date(tx.timestamp), "yy-MM-dd HH:mm")
                      : ""}
                  </td>
                  <td
                    className={`px-4 py-2 text-sm ${
                      tx.amount && Number(tx.amount) < 0
                        ? "text-red-600"
                        : "text-green-600"
                    }`}
                  >
                    {tx.amount
                      ? Number(tx.amount) > 0
                        ? `+${tx.amount}`
                        : tx.amount
                      : ""}
                  </td>
                  <td className="px-4 py-2 text-sm">{tx.type}</td>
                  <td className="px-4 py-2 text-sm">{tx.reason}</td>
                </tr>
              ))}
        </tbody>
      </table>
    </div>
  );
}
