// components/tickets/AllTicketsTable.tsx
import React, { useState } from "react";
import { FilterBar } from "./FilterBar";
import { TicketTable } from "./TicketTable";
import { Pagination } from "./Pagination";
import { DateRange } from "./DateRangePicker";
import { subDays, startOfMonth, endOfMonth } from "date-fns";
import { Ticket } from "../../_types";
import { useTranslations } from "next-intl";

// const priorityLevels = [
//   { label: "Low", value: "1" },
//   { label: "Medium", value: "2" },
//   { label: "High", value: "3" },
//   { label: "Very High", value: "4" },
//   { label: "Urgent", value: "5" },
// ];

// function getPriorityLabel(priority: string) {
//   return priorityLevels.find((p) => p.value === priority)?.label || priority;
// }

function toDateOnly(date: Date) {
  return new Date(date.getFullYear(), date.getMonth(), date.getDate());
}

export const AllTicketsTable: React.FC<{ tickets: Ticket[] }> = ({
  tickets,
}) => {
  const t = useTranslations("DashboardYumiSandbox.SupportPage.allTicketsTable");
  const [search, setSearch] = useState("");
  const [status, setStatus] = useState("All");
  const [priority, setPriority] = useState("All");
  const [period, setPeriod] = useState("All Time");
  const [dateRange, setDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined,
  });
  const [page, setPage] = useState(1);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const toggleSortDirection = () =>
    setSortDirection((d) => (d === "asc" ? "desc" : "asc"));

  // Handle period logic
  let fromDate: Date | undefined = undefined;
  let toDate: Date | undefined = undefined;
  if (period === "Today") {
    const now = new Date();
    fromDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    toDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  } else if (period === "Last 7 Days") {
    fromDate = subDays(new Date(), 7);
    toDate = new Date();
  } else if (period === "This Month") {
    fromDate = startOfMonth(new Date());
    toDate = endOfMonth(new Date());
  } else if (period === "Custom…") {
    fromDate = dateRange.from;
    toDate = dateRange.to;
  }

  // Filtering logic
  const filtered = tickets
    .filter((t) =>
      [t.name, t.contact_email, t.type]
        .join(" ")
        .toLowerCase()
        .includes(search.toLowerCase())
    )
    .filter(
      (t) =>
        status === "All" || (status === "Closed" ? t.is_closed : !t.is_closed)
    )
    .filter((t) => priority === "All" || t.priority === priority)
    .filter((t) => {
      if (period === "All Time") return true;
      if (period === "Custom…") {
        if (!fromDate || !toDate) return true;
        const d = toDateOnly(new Date(t.created_at));
        return d >= toDateOnly(fromDate) && d <= toDateOnly(toDate);
      }
      if (!fromDate || !toDate) return true;
      const d = toDateOnly(new Date(t.created_at));
      return d >= toDateOnly(fromDate) && d <= toDateOnly(toDate);
    });

  const pageSize = 10;
  const totalPages = Math.ceil(filtered.length / pageSize);
  const pageData = filtered.slice((page - 1) * pageSize, page * pageSize);

  return (
    <div>
      <h1 className="text-lg font-semibold mb-4">{t("header")}</h1>
      <FilterBar
        search={search}
        onSearchChange={setSearch}
        status={status}
        onStatusChange={setStatus}
        priority={priority}
        onPriorityChange={setPriority}
        period={period}
        onPeriodChange={(v) => {
          setPeriod(v);
          setPage(1);
          if (v !== "Custom…") setDateRange({ from: undefined, to: undefined });
        }}
        dateRange={dateRange}
        onDateRangeChange={setDateRange}
      />
      <TicketTable
        data={pageData}
        // getPriorityLabel={getPriorityLabel}
        page={page}
        pageSize={pageSize}
        sortDirection={sortDirection}
        onDateHeaderClick={toggleSortDirection}
      />
      <Pagination
        page={page}
        totalPages={totalPages}
        pageSize={pageSize}
        onPageChange={(newPage) => setPage(newPage)}
        onPageSizeChange={() => {}}
      />
    </div>
  );
};
