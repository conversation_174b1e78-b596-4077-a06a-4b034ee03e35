// components/tickets/Badge.tsx
import React from "react";

export interface BadgeProps {
  children: React.ReactNode;
  variant?:
    | "pending"
    | "resolved"
    | "low"
    | "medium"
    | "high"
    | "very high"
    | "urgent"
    | "active"
    | "suspended";
}

const colorMap = {
  pending: "bg-yellow-100 text-yellow-800",
  resolved: "bg-green-100 text-green-800",
  low: "bg-gray-100 text-gray-800",
  medium: "bg-blue-100 text-blue-800",
  high: "bg-orange-200 text-orange-800",
  "very high": "bg-red-200 text-red-800",
  urgent: "bg-pink-200 text-pink-800",
  active: "bg-green-100 text-green-800",
  suspended: "bg-red-100 text-red-800",
};

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = "pending",
}) => (
  <span
    className={[
      "inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium",
      colorMap[variant],
    ].join(" ")}
  >
    {children}
  </span>
);
