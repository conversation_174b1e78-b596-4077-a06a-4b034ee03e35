// components/tickets/DateRangePicker.tsx
"use client";

import * as React from "react";
import * as Popover from "@radix-ui/react-popover";
import { format } from "date-fns";
import type { DateRange as DayPickerDateRange } from "react-day-picker";
import { Button } from "../../_ui/button";
import { RangeCalendar } from "./RangeCalendar";
import { useTranslations } from "next-intl";

export type DateRange = DayPickerDateRange;

interface DateRangePickerProps {
  range: DateRange;
  onChange: (range: DateRange) => void;
}

export function DateRangePicker({ range, onChange }: DateRangePickerProps) {
  const { from, to } = range;
  const t = useTranslations("DashboardYumiSandbox.SupportPage.dateRangePicker");
  const label =
    from && to
      ? t("rangeLabel", {
          from: format(from, "MM/dd/yyyy"),
          to: format(to, "MM/dd/yyyy"),
        })
      : t("chooseRange");

  return (
    <Popover.Root>
      <Popover.Trigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          {label}
        </Button>
      </Popover.Trigger>

      <Popover.Portal>
        <Popover.Content
          className="bg-white p-2 rounded-xl shadow-lg"
          side="bottom"
          align="start"
        >
          <RangeCalendar
            mode="range"
            selected={range}
            onSelect={(value) => value && onChange(value as DateRange)}
            className="!p-0"
          />
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
}
