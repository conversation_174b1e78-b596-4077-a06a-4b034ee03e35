// components/tickets/FilterBar.tsx
import React from "react";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectScrollUpButton,
  SelectScrollDownButton,
} from "@/components/ui/select";
import { SelectViewport } from "@radix-ui/react-select";
import { DateRange } from "./DateRangePicker";
import { Search } from "lucide-react";
import { useTranslations } from "next-intl";

export interface FilterBarProps {
  search: string;
  onSearchChange: (v: string) => void;
  status: string;
  onStatusChange: (v: string) => void;
  priority: string;
  onPriorityChange: (v: string) => void;
  period: string;
  onPeriodChange: (v: string) => void;
  dateRange: DateRange;
  onDateRangeChange: (range: DateRange) => void;
}

const priorityLevels = ["1", "2", "3", "4", "5"];

const PERIODS = ["All Time", "Today", "Last 7 Days", "This Month", "Custom…"];

export const FilterBar: React.FC<FilterBarProps> = ({
  search,
  onSearchChange,
  status,
  onStatusChange,
  priority,
  onPriorityChange,
  period,
  onPeriodChange,
  dateRange,
  onDateRangeChange,
}) => {
  const t = useTranslations("DashboardYumiSandbox.SupportPage.filterBar");
  return (
    <div className="flex flex-wrap items-center gap-4 mb-4">
      {/* Search */}
      <div className="flex-1 min-w-[200px] relative">
        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-black">
          <Search width={18} height={18} />
        </span>
        <input
          type="text"
          value={search}
          onChange={(e) => onSearchChange(e.target.value)}
          placeholder={t("searchPlaceholder")}
          className="w-[230px] border rounded-lg px-9 py-2 text-sm"
        />
      </div>

      {/* Period Select */}
      <Select value={period} onValueChange={onPeriodChange}>
        <SelectTrigger className="w-[180px]">
          {t("period")}: {t(`periodOptions.${period}`) || period}
        </SelectTrigger>
        <SelectContent>
          <SelectScrollUpButton />
          <SelectViewport>
            {PERIODS.map((p) => (
              <SelectItem key={p} value={p}>
                {t(`periodOptions.${p}`) || p}
              </SelectItem>
            ))}
          </SelectViewport>
          <SelectScrollDownButton />
        </SelectContent>
      </Select>

      {/* Custom Date Range Pickers */}
      {period === "Custom…" && (
        <div className="flex gap-2">
          <label>
            {t("from")}:{" "}
            <input
              type="date"
              value={
                dateRange.from ? dateRange.from.toISOString().slice(0, 10) : ""
              }
              onChange={(e) => {
                const from = e.target.value
                  ? new Date(e.target.value)
                  : undefined;
                onDateRangeChange({
                  from,
                  to:
                    dateRange.to && from && dateRange.to < from
                      ? undefined
                      : dateRange.to,
                });
              }}
              max={
                dateRange.to
                  ? dateRange.to.toISOString().slice(0, 10)
                  : undefined
              }
              className="border rounded px-2 py-1"
            />
          </label>
          <label>
            {t("to")}:{" "}
            <input
              type="date"
              value={
                dateRange.to ? dateRange.to.toISOString().slice(0, 10) : ""
              }
              onChange={(e) => {
                const to = e.target.value
                  ? new Date(e.target.value)
                  : undefined;
                onDateRangeChange({
                  from: dateRange.from,
                  to,
                });
              }}
              min={
                dateRange.from
                  ? dateRange.from.toISOString().slice(0, 10)
                  : undefined
              }
              className="border rounded px-2 py-1"
              disabled={!dateRange.from}
            />
          </label>
        </div>
      )}

      {/* Status Select */}
      <Select value={status} onValueChange={onStatusChange}>
        <SelectTrigger className="w-[180px]">
          {t("status")}: {t(`statusOptions.${status}`) || status}
        </SelectTrigger>
        <SelectContent>
          <SelectScrollUpButton />
          <SelectViewport>
            {["All", "Pending", "Resolved"].map((s) => (
              <SelectItem key={s} value={s}>
                {t(`statusOptions.${s}`) || s}
              </SelectItem>
            ))}
          </SelectViewport>
          <SelectScrollDownButton />
        </SelectContent>
      </Select>

      {/* Priority Select */}
      <Select value={priority} onValueChange={onPriorityChange}>
        <SelectTrigger className="w-[180px]">
          {t("priority")}:{" "}
          {priority === "All" ? t("priorityOptions.All") : priority}
        </SelectTrigger>
        <SelectContent>
          <SelectScrollUpButton />
          <SelectViewport>
            {["All", ...priorityLevels].map((p) => (
              <SelectItem key={p} value={p}>
                {p === "All" ? t("priorityOptions.All") : p}
              </SelectItem>
            ))}
          </SelectViewport>
          <SelectScrollDownButton />
        </SelectContent>
      </Select>
    </div>
  );
};
