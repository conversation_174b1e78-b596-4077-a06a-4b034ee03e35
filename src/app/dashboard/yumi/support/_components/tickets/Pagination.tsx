// components/tickets/Pagination.tsx
import React from "react";
import { useTranslations } from "next-intl";

interface PaginationProps {
  page: number;
  totalPages: number;
  onPageChange: (newPage: number) => void;
  pageSize: number;
  onPageSizeChange: (size: number) => void;
  pageSizeOptions?: number[];
}

export const Pagination: React.FC<PaginationProps> = ({
  page,
  totalPages,
  onPageChange,
  pageSize,
  onPageSizeChange,
  pageSizeOptions = [10, 25, 50],
}) => {
  const t = useTranslations("DashboardYumiSandbox.SupportPage.pagination");
  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between py-4 gap-2">
      <span className="text-sm text-gray-600">
        {t("pageInfo", { page, totalPages })}
      </span>
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-1">
          <span className="text-xs text-gray-500">{t("rowsPerPage")}</span>
          {pageSizeOptions.map((opt) => (
            <button
              key={opt}
              onClick={() => onPageSizeChange(opt)}
              className={`px-2 py-1 rounded border text-xs ${
                pageSize === opt
                  ? "bg-blue-600 text-white border-blue-600"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-100"
              }`}
              disabled={pageSize === opt}
            >
              {opt}
            </button>
          ))}
        </div>
        <div className="space-x-2">
          <button
            onClick={() => onPageChange(page - 1)}
            disabled={page === 1}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            {t("previous")}
          </button>
          <button
            onClick={() => onPageChange(page + 1)}
            disabled={page === totalPages}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            {t("next")}
          </button>
        </div>
      </div>
    </div>
  );
};
