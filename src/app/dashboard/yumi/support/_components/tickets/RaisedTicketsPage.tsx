// components/tickets/RaisedTicketsPage.tsx
"use client";
import React, { useMemo, useState } from "react";
import { Tabs, TabsList, TabsTrigger } from "../../_ui/tabs";
import TicketCountCard from "./TicketCountCard";
import { FilterBar } from "./FilterBar";
import { TicketTable } from "./TicketTable";
import { Pagination } from "./Pagination";
import { summarizeTickets } from "./utils";
import { InfoIcon } from "lucide-react";
import { DateRange } from "./DateRangePicker";
import { subDays, startOfMonth, endOfMonth } from "date-fns";
import { Ticket } from "../../_types";
import { useTranslations } from "next-intl";

interface RaisedTicketsPageProps {
  tickets: Ticket[];
  page: number;
  setPage: (page: number) => void;
  status: string;
  setStatus: (status: string) => void;
}

export default function RaisedTicketsPage({
  tickets,
  page,
  setPage,
  status,
  setStatus,
}: RaisedTicketsPageProps) {
  const t = useTranslations("DashboardYumiSandbox.SupportPage");
  const [tab, setTab] = useState<"all" | "pending" | "resolved">("all");
  const [search, setSearch] = useState("");
  const [priority, setPriority] = useState("All");
  const [dateRange, setDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined,
  });
  const [pageSize, setPageSize] = useState(10);
  const [dateRangePickerOpen, setDateRangePickerOpen] = useState(false);
  const [period, setPeriod] = useState("All Time");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const toggleSortDirection = () =>
    setSortDirection((d) => (d === "asc" ? "desc" : "asc"));

  // 1. Compute fromDate and toDate FIRST
  let fromDate: Date | undefined = undefined;
  let toDate: Date | undefined = undefined;
  if (period === "Today") {
    const now = new Date();
    fromDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    toDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  } else if (period === "Last 7 Days") {
    fromDate = subDays(new Date(), 7);
    toDate = new Date();
  } else if (period === "This Month") {
    fromDate = startOfMonth(new Date());
    toDate = endOfMonth(new Date());
  } else if (period === "Custom…") {
    fromDate = dateRange.from;
    toDate = dateRange.to;
  }
  function toDateOnly(date: Date) {
    return new Date(date.getFullYear(), date.getMonth(), date.getDate());
  }

  // Sync tab and status
  const handleTabChange = (value: string) => {
    setTab(value as any);
    setPage(1);
    if (value === "all") setStatus("All");
    else setStatus(value.charAt(0).toUpperCase() + value.slice(1));
  };

  const handleStatusChange = (value: string) => {
    setStatus(value);
    if (value === "All") setTab("all");
    else setTab(value.toLowerCase() as any);
    setPage(1);
  };

  // 1) filter by tab
  // --- 1) Filtered list (all tickets → tab → search → status → priority → date) ---
  const filtered = useMemo(() => {
    const tabFiltered = tickets.filter((t) =>
      tab === "all" ? true : tab === "pending" ? !t.is_closed : !!t.is_closed
    );
    return tabFiltered
      .filter((t) =>
        [t.name, t.contact_email, t.type]
          .join(" ")
          .toLowerCase()
          .includes(search.toLowerCase())
      )
      .filter((t) =>
        status === "All"
          ? true
          : status === "Pending"
          ? !t.is_closed
          : status === "Resolved"
          ? !!t.is_closed
          : true
      )
      .filter((t) => (priority === "All" ? true : t.priority === priority))
      .filter((t) => {
        if (period === "All Time") return true;
        if (!fromDate || !toDate) return true;
        const d = toDateOnly(new Date(t.created_at));
        return d >= toDateOnly(fromDate) && d <= toDateOnly(toDate);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tickets, tab, search, status, priority, period, dateRange]);

  // --- 2) Sort the entire filtered list by date ---
  const sorted = useMemo(() => {
    return [...filtered].sort((a, b) => {
      const aTime = new Date(a.created_at).getTime();
      const bTime = new Date(b.created_at).getTime();
      return sortDirection === "asc" ? aTime - bTime : bTime - aTime;
    });
  }, [filtered, sortDirection]);

  const { total, pending, resolved } = summarizeTickets(tickets);

  // 3) paginate
  const totalPages = Math.ceil(sorted.length / pageSize);
  const pageData = sorted.slice((page - 1) * pageSize, page * pageSize);

  return (
    <div className="p-6 space-y-6">
      {/* Header + Tabs */}
      <div>
        <h1 className="text-2xl font-semibold mb-4">{t("header")}</h1>

        <Tabs value={tab} onValueChange={handleTabChange}>
          <TabsList>
            {[
              {
                val: "all",
                label: t("tabs.all"),
              },
              {
                val: "pending",
                label: t("tabs.pending"),
              },
              {
                val: "resolved",
                label: t("tabs.resolved"),
              },
            ].map(({ val, label }) => (
              <TabsTrigger key={val} value={val}>
                {label}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <TicketCountCard
          title={t("summary.all")}
          count={total}
          description={t("summary.total")}
        />
        <TicketCountCard
          title={t("summary.pending")}
          count={pending}
          description={t("summary.pendingTotal")}
          icon={<InfoIcon className="w-4 h-4 text-muted-foreground" />}
        />
        <TicketCountCard
          title={t("summary.resolved")}
          count={resolved}
          description={t("summary.resolvedTotal")}
        />
      </div>

      {/* Table & Filters */}
      <div className="bg-white rounded-xl p-6 shadow-sm space-y-4">
        <FilterBar
          search={search}
          onSearchChange={setSearch}
          status={status}
          onStatusChange={handleStatusChange}
          priority={priority}
          onPriorityChange={setPriority}
          period={period}
          onPeriodChange={(v) => {
            setPeriod(v);
            setPage(1);
            if (v !== "Custom…")
              setDateRange({ from: undefined, to: undefined });
          }}
          dateRange={dateRange}
          onDateRangeChange={setDateRange}
        />

        {/* Date Range Picker Modal */}
        {dateRangePickerOpen && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
            <div className="bg-white rounded-lg shadow-lg p-6 min-w-[320px]">
              <h2 className="text-lg font-semibold mb-4">
                {t("dateRange.select")}
              </h2>
              <div className="flex gap-4 mb-4">
                <label>
                  {t("dateRange.from")}:{" "}
                  <input
                    type="date"
                    value={
                      dateRange.from
                        ? dateRange.from.toISOString().slice(0, 10)
                        : ""
                    }
                    onChange={(e) => {
                      const from = e.target.value
                        ? new Date(e.target.value)
                        : undefined;
                      setDateRange((r) => ({
                        from,
                        to: r.to && from && r.to < from ? undefined : r.to,
                      }));
                    }}
                    max={
                      dateRange.to
                        ? dateRange.to.toISOString().slice(0, 10)
                        : undefined
                    }
                    className="border rounded px-2 py-1"
                  />
                </label>
                <label>
                  {t("dateRange.to")}:{" "}
                  <input
                    type="date"
                    value={
                      dateRange.to
                        ? dateRange.to.toISOString().slice(0, 10)
                        : ""
                    }
                    onChange={(e) => {
                      const to = e.target.value
                        ? new Date(e.target.value)
                        : undefined;
                      setDateRange((r) => ({
                        from: r.from,
                        to,
                      }));
                    }}
                    min={
                      dateRange.from
                        ? dateRange.from.toISOString().slice(0, 10)
                        : undefined
                    }
                    className="border rounded px-2 py-1"
                    disabled={!dateRange.from}
                  />
                </label>
              </div>
              <div className="flex justify-end gap-2">
                <button
                  className="px-4 py-2 rounded border"
                  onClick={() => setDateRangePickerOpen(false)}
                  type="button"
                >
                  {t("dateRange.cancel")}
                </button>
                <button
                  className="px-4 py-2 rounded bg-blue-600 text-white"
                  onClick={() => setDateRangePickerOpen(false)}
                  type="button"
                  disabled={!dateRange.from || !dateRange.to}
                >
                  {t("dateRange.apply")}
                </button>
              </div>
            </div>
          </div>
        )}

        <TicketTable
          data={pageData}
          // getPriorityLabel={getPriorityLabel}
          page={page}
          pageSize={pageSize}
          sortDirection={sortDirection}
          onDateHeaderClick={toggleSortDirection}
        />

        <Pagination
          page={page}
          totalPages={totalPages}
          onPageChange={setPage}
          pageSize={pageSize}
          onPageSizeChange={(size) => {
            setPageSize(size);
            setPage(1);
          }}
          pageSizeOptions={[10, 25, 50]}
        />
      </div>
    </div>
  );
}
