// components/ui/RangeCalendar.tsx
"use client";

import * as React from "react";
import { DayPicker } from "react-day-picker";
import type { PropsRange } from "react-day-picker";
// import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { buttonVariants } from "../../_ui/button";

export interface RangeCalendarProps
  extends Omit<PropsRange, "className" | "classNames"> {
  className?: string;
  classNames?: Partial<Record<string, string>>;
  showOutsideDays?: boolean;
}

export function RangeCalendar({
  selected,
  onSelect,
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: RangeCalendarProps) {
  return (
    <DayPicker
      selected={selected}
      onSelect={onSelect}
      showOutsideDays={showOutsideDays}
      captionLayout="dropdown"
      fromYear={2000}
      toYear={new Date().getFullYear()}
      labels={{
        labelMonthDropdown: () => "",
        labelYearDropdown: () => "",
      }}
      className={cn("p-4 bg-white rounded-lg shadow-lg", className)}
      classNames={{
        months: "flex flex-col sm:flex-row gap-4",
        month: "flex flex-col gap-2",
        caption: "flex justify-between items-center pb-2 border-b mb-2",
        caption_dropdowns: "flex items-center gap-2",
        dropdown:
          "text-sm font-medium rounded-md border px-2 py-1 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500",
        nav: "flex items-center gap-2",
        nav_button: cn(
          buttonVariants({ variant: "outline" }),
          "p-1 hover:bg-gray-100"
        ),
        nav_button_previous: "",
        nav_button_next: "",
        table: "w-full border-spacing-1 border-separate",
        head_row: "flex",
        head_cell:
          "text-gray-500 font-semibold text-xs uppercase text-center w-8",
        row: "flex w-full mt-1",
        cell: cn(
          "relative w-8 h-8 flex items-center justify-center text-sm",
          "hover:bg-gray-100 focus-within:ring-2 focus-within:ring-indigo-500",
          classNames?.cell
        ),
        day: cn(
          buttonVariants({ variant: "ghost" }),
          "w-full h-full flex items-center justify-center p-0"
        ),
        day_selected: "bg-indigo-600 text-white",
        day_range_middle: "bg-indigo-100",
        day_range_start: "bg-indigo-600 text-white rounded-l-full",
        day_range_end: "bg-indigo-600 text-white rounded-r-full",
        day_today: "border border-indigo-500 text-indigo-700",
        day_outside: "text-gray-300",
        day_disabled: "text-gray-300 opacity-50 cursor-not-allowed",
        ...classNames,
      }}
      components={
        {
          // IconLeft: ({ className, ...p }: { className?: string; [key: string]: any }) => (
          //   <ChevronLeft className={cn("w-5 h-5 text-gray-600", className)} {...p} />
          // ),
          // IconRight: ({ className, ...p }: { className?: string; [key: string]: any }) => (
          //   <ChevronRight className={cn("w-5 h-5 text-gray-600", className)} {...p} />
          // ),
        }
      }
      {...props}
    />
  );
}
