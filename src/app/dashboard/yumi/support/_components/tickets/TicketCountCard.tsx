import React from "react";
import { CheckCircle } from "lucide-react";

interface TicketCountCardProps {
  title: string;
  count: number;
  description: string;
  icon?: React.ReactNode;
}

export default function TicketCountCard({
  title,
  count,
  description,
  icon = <CheckCircle className="w-4 h-4 text-muted-foreground" />,
}: TicketCountCardProps) {
  return (
    <div className="rounded-xl overflow-hidden border bg-white shadow-sm">
      <div className="bg-muted px-4 py-2 flex items-center gap-2">
        {icon}
        <span className="text-sm font-medium text-muted-foreground">
          {title}
        </span>
      </div>
      <div className="p-4">
        <h2 className="text-2xl font-bold">{count.toLocaleString()}</h2>
        <p className="text-muted-foreground text-sm">{description}</p>
      </div>
    </div>
  );
}
