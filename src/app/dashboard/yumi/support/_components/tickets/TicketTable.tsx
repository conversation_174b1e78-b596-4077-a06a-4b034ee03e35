// components/tickets/TicketTable.tsx
import React from "react";
import { Badge } from "./Badge";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { Ticket } from "../../_types";
import { useTranslations } from "next-intl";

interface TicketTableProps {
  data: Ticket[];
  page: number;
  pageSize: number;
  sortDirection: "asc" | "desc";
  onDateHeaderClick: () => void;
  onRowClick?: (ticket: Ticket) => void;
}

export const TicketTable: React.FC<TicketTableProps> = ({
  data,
  page,
  pageSize,
  sortDirection,
  onDateHeaderClick,
  onRowClick,
}) => {
  const router = useRouter();
  const t = useTranslations("DashboardYumiSandbox.SupportPage");

  // Map backend ticket types to display values
  const typeCodeMap: Record<string, string> = {
    GI: "GI",
    "General Inquiry": "GI",
    PSQ: "PSQ",
    "Product or Service Questions": "PSQ",
    OTI: "OTI",
    "Order or Transaction Issues": "OTI",
    BPQ: "BPQ",
    "Billing or Payment Questions": "BPQ",
    AAH: "AAH",
    "Account or Access Help": "AAH",
    TSE: "TSE",
    "Technical Support or Errors": "TSE",
    FS: "FS",
    "Feedback or Suggestions": "FS",
    PSC: "PSC",
    "Private or Sensitive Concerns": "PSC",
  };

  const getTypeDisplay = (type?: string) => {
    const code = typeCodeMap[type ?? ""] || "PSC";
    return t(`type.${code}`);
  };

  const SortArrow = () => (sortDirection === "asc" ? "↑" : "↓");

  return (
    <div className="overflow-x-auto border rounded">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {[
              t("table.sn"),
              <span
                key="date"
                className="flex items-center cursor-pointer"
                onClick={onDateHeaderClick}
              >
                {t("table.date")} &nbsp;
                <SortArrow />
              </span>,
              t("table.customer"),
              t("table.email"),
              t("table.issueType"),
              t("table.status"),
              t("table.priority"),
            ].map((col, idx) => (
              <th
                key={idx}
                className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase select-none"
              >
                {col}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-100">
          {data.map((ticket, i) => (
            <tr
              key={ticket.idx}
              className="hover:bg-gray-50 cursor-pointer"
              onClick={() =>
                onRowClick
                  ? onRowClick(ticket)
                  : router.push(`/dashboard/yumi/support/ticket/${ticket.stk}`)
              }
            >
              <td className="px-4 py-2 text-sm">
                {(page - 1) * pageSize + i + 1}
              </td>
              <td className="px-4 py-2 text-sm">
                {format(new Date(ticket.created_at), "yy-MM-dd HH:mm")}
              </td>
              <td className="px-4 py-2 text-sm">{ticket.name}</td>
              <td className="px-4 py-2 text-sm">
                <a
                  href={`mailto:${ticket.contact_email}`}
                  target="_blank"
                  onClick={(e) => e.stopPropagation()}
                  className="text-blue-600 underline p-2 hover:bg-gray-100 rounded-full"
                >
                  {ticket.contact_email}
                </a>
              </td>
              <td className="px-4 py-2 text-sm">
                {getTypeDisplay(ticket.type)}
              </td>
              <td className="px-4 py-2 text-sm">
                <Badge
                  variant={
                    ticket.is_closed == null
                      ? "low"
                      : ticket.is_closed
                      ? "resolved"
                      : "pending"
                  }
                >
                  {ticket.is_closed == null
                    ? t("status.unknown")
                    : ticket.is_closed
                    ? t("status.resolved")
                    : t("status.pending")}
                </Badge>
              </td>
              <td className="px-4 py-2 text-sm">
                <Badge
                  variant={(ticket.priority || "Unknown").toLowerCase() as any}
                >
                  {ticket.priority || t("priority.unknown")}
                </Badge>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
