import { useMutation } from "@tanstack/react-query";
import { sandboxApi } from "@/lib/api";

interface AdjustCreditPayload {
  fake_user_id: string | number;
  amount: number;
  narration: string;
  sandbox_id: string;
}

export function useAdjustCredit() {
  return useMutation({
    mutationFn: async (payload: AdjustCreditPayload) => {
      const { data } = await sandboxApi.post("/user/adjust-credits", payload);
      return data;
    },
  });
}
