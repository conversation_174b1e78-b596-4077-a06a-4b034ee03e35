import { useQuery } from "@tanstack/react-query";
import { sandboxApi } from "@/lib/api";

export interface CallTranscriptMessage {
  speaker: string;
  content: string;
}

export interface CallLogSession {
  id: string;
  createdAt: string;
  sandboxId: string;
  userId: string;
  transcript: CallTranscriptMessage[];
  stk?: string;
}

function mapApiToCallLogSessions(apiData: any[]): CallLogSession[] {
  return apiData.map((session) => ({
    id: String(session.id),
    createdAt: session.created_at,
    sandboxId: session.sandbox_id,
    userId: session.user_id,
    transcript: Array.isArray(session.transcript)
      ? session.transcript
      : (() => {
          try {
            return JSON.parse(session.transcript);
          } catch {
            return [];
          }
        })(),
    stk: session.stk,
  }));
}

export function useCallLog(userId: string, sandboxId?: string) {
  return useQuery<CallLogSession[]>({
    queryKey: ["callLog", userId, sandboxId],
    queryFn: async () => {
      const { data } = await sandboxApi.get(
        `/user/retrieve_transcript?sandbox_id=${sandboxId}&fake_user_id=${userId}`
      );
      if (data?.error) {
        return [];
      }
      const rawSessions = Array.isArray(data) ? data : [];
      return mapApiToCallLogSessions(rawSessions);
    },
    enabled: !!userId && !!sandboxId,
  });
}
