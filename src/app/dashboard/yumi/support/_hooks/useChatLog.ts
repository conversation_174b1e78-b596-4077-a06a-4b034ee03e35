import { useQuery } from "@tanstack/react-query";
import { api, sandboxApi } from "@/lib/api";

export interface ChatMessage {
  id: string;
  content: string;
  sendfrom: "userMessage" | "apiMessage";
}

export interface ChatSession {
  sessionId: string;
  title?: string;
  startedAt?: string;
  resolvedAt?: string;
  message: ChatMessage[];
}

function mapApiToChatSessions(apiData: any[]): ChatSession[] {
  return apiData.map((session, idx) => ({
    sessionId: session.sessionId,
    title: `Session ${idx + 1}`,
    startedAt: "", // You can fill this if you have a timestamp
    resolvedAt: undefined,
    message: session.message.map((msg: any, i: number) => ({
      id: `${session.sessionId}-${i}`,
      content: msg.content,
      sendfrom: msg.sendfrom,
    })),
  }));
}

export function useChatLog(userId: string, sandboxId?: string) {
  return useQuery<ChatSession[]>({
    queryKey: ["chatLog", userId, sandboxId],
    queryFn: async () => {
      const { data } = await sandboxApi.get(
        `/user/retrieve-chatlog?fake_user_id=${userId}&sandbox_id=${sandboxId}`
      );
      console.log("API raw data", data); // <-- Add this
      if (data?.error) {
        return [];
      }
      const rawSessions = Array.isArray(data) ? data : [];
      return mapApiToChatSessions(rawSessions);
    },
    enabled: !!userId && !!sandboxId,
  });
}
