// hooks/useEmailLogs.ts
import { useQuery } from "@tanstack/react-query";
import { api, sandboxApi } from "@/lib/api";
// import { EmailLogEntry } from "@/types";

function parseEmailContent(
  content: string | any[]
): { body: string; time: string; from: string; to: string }[] {
  if (Array.isArray(content)) {
    return content.map((item: any) => ({
      body: item.content,
      time: item.time,
      from: item.sendfrom,
      to: item.sendto,
    }));
  }
  try {
    const arr = JSON.parse(content);
    return arr.map((item: any) => ({
      body: item.content,
      time: item.time,
      from: item.sendfrom,
      to: item.sendto,
    }));
  } catch {
    return [];
  }
}

export function useEmailLogs(userId: string, sandboxId: string | undefined) {
  return useQuery({
    queryKey: ["emailLogs", userId, sandboxId],
    queryFn: async () => {
      const { data } = await sandboxApi.get(
        `/user/retrieve-email-logs?fake_user_id=${userId}&sandbox_id=${sandboxId}`
      );
      return (data as any[]).flatMap((entry) => {
        const parsed = parseEmailContent(entry.content);
        return parsed.map((msg, idx) => {
          const isFromSupport =
            msg.from?.includes("Yumi") ||
            msg.from?.includes("<EMAIL>");
          const subject = entry.is_confirmation_email
            ? "Confirmation Email"
            : isFromSupport
            ? "Support Email"
            : "Customer Email";

          return {
            id: entry.thread_id + "-" + idx,
            from: msg.from,
            to: msg.to,
            subject,
            body: msg.body,
            timestamp: msg.time,
            stk: entry.stk,
          };
        });
      });
    },
    enabled: !!userId,
  });
}
