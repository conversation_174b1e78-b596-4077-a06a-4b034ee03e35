import { useMutation } from "@tanstack/react-query";
import { sandboxApi } from "@/lib/api";

interface EscalateUser {
  email: string;
  role: string;
}

interface EscalateTicketPayload {
  stk: string;
  reason: string;
  users: EscalateUser[];
}

export function useEscalateTicket() {
  return useMutation({
    mutationFn: async (payload: EscalateTicketPayload) => {
      const { data } = await sandboxApi.post("/user/escalate-ticket", payload);
      return data;
    },
  });
}

