import { useQuery } from "@tanstack/react-query";
import { sandboxApi } from "@/lib/api";

export interface EscalationMember {
  role: string;
  email: string;
}

export function useEscalationMembers() {
  return useQuery<EscalationMember[], Error>({
    queryKey: ["escalation-members"],
    queryFn: async () => {
      const { data } = await sandboxApi.get("/user/escalation-members");
      return data;
    },
  });
}
