import { useQuery } from "@tanstack/react-query";
import { sandboxApi } from "@/lib/api";

export interface InternalLog {
  id: number;
  stk: string;
  to_email: string[];
  from_email: string;
  from_role: string;
  to_role: string[];
  message: string;
  created_at: string;
}

export function useInternalLogs(stk: string) {
  return useQuery<InternalLog[], Error>({
    queryKey: ["internal-logs", stk],
    queryFn: async () => {
      const { data } = await sandboxApi.get(
        `/user/internal-logs?stk=${encodeURIComponent(stk)}`
      );
      return data;
    },
    enabled: !!stk,
  });
}
