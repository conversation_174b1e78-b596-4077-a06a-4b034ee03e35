import { useMutation } from "@tanstack/react-query";
import { api } from "@/lib/api";

interface ReplyInternalMessagePayload {
  message: string;
  stk: string;
}

export function useReplyInternalMessage() {
  return useMutation({
    mutationFn: async (payload: ReplyInternalMessagePayload) => {
      const { data } = await api.post(
        "/api/authority/reply-to-internal-message",
        payload
      );
      return data;
    },
  });
}
