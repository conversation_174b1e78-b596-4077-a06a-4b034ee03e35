import { useQuery } from "@tanstack/react-query";
import { sandboxApi } from "@/lib/api";

export interface TestUser {
  email: string;
  firstname: string;
  id: number;
  lastname: string;
  sandbox_id: string;
  user_id: string;
}

async function fetchTestUsers(sandboxId?: string): Promise<TestUser[]> {
  if (!sandboxId) return [];
  const { data } = await sandboxApi.get(
    `/user_profile?sandbox_id=${sandboxId}`
  );
  return Array.isArray(data) ? data : [];
}

export function useTestUsers(sandboxId?: string) {
  return useQuery<TestUser[], Error>({
    queryKey: ["testUsers", sandboxId],
    queryFn: () => fetchTestUsers(sandboxId),
    enabled: !!sandboxId,
    refetchOnWindowFocus: false,
  });
}