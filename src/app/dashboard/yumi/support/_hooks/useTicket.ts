import { useQuery } from "@tanstack/react-query";
import { sandboxApi } from "@/lib/api";
import { Ticket } from "../_types";

export function useTicket(id: string, sandboxId: string | undefined) {
  return useQuery<Ticket, Error>({
    queryKey: ["ticket", id, sandboxId],
    enabled: !!id && !!sandboxId,
    queryFn: async () => {
      const { data } = await sandboxApi.get(
        `/ticket/ticket-details?stk=${id}&sandbox_id=${sandboxId}`
      );
      return data;
    },
  });
}
