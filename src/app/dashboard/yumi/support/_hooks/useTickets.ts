// src/hooks/useTickets.ts
import { useQuery } from "@tanstack/react-query";
import { sandboxApi } from "@/lib/api";
import { Ticket } from "../_types";

export function useTickets(sandboxId: string | undefined) {
  return useQuery<Ticket[], Error>({
    queryKey: ["tickets", sandboxId],
    enabled: !!sandboxId,
    queryFn: async () => {
      const { data } = await sandboxApi.get(`/ticket?sandbox_id=${sandboxId}`);
      return data;
    },
  });
}
