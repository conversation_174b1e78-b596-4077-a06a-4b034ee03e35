import { useQuery } from "@tanstack/react-query";
import { api, sandboxApi } from "@/lib/api";

interface Transaction {
  type: string;
  timestamp: string;
  amount?: string;
  reason: string;
  plan?: string;
  start_date?: string;
  end_date?: string;
}

interface TransactionHistoryResponse {
  balance: string;
  history: Transaction[];
  pagination: {
    total: number;
    totalPages: number;
    currentPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

interface TransactionHistoryFilters {
  page?: number;
  limit?: number;
  type?: string;
  search?: string;
  fromDate?: string;
  toDate?: string;
}

export function useTransactionHistory(
  fake_user_id: string,
  filters: TransactionHistoryFilters = {}
) {
  return useQuery<TransactionHistoryResponse, Error>({
    queryKey: [
      "transactionHistory",
      fake_user_id,
      filters.page,
      filters.limit,
      filters.type,
      filters.search,
      filters.fromDate,
      filters.toDate,
    ],
    queryFn: async () => {
      const { data } = await sandboxApi.get("/user/credit-history", {
        params: { fake_user_id, ...filters },
      });
      return data;
    },
    enabled: !!fake_user_id,
  });
}
