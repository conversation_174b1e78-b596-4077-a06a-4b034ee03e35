import { useQuery } from "@tanstack/react-query";
import { api, sandboxApi } from "@/lib/api";
import { Ticket } from "../_types";

export interface UserTicket {
  idx: string;
  type: string;
  subject: string;
  is_closed: boolean;
  created_at: string;
  stk: string;
}

export function useUserTickets(fake_user_id: string, sandbox_id: string | undefined) {
  return useQuery<Ticket[], Error>({
    queryKey: ["userTickets", fake_user_id, sandbox_id],
    queryFn: async () => {
      const { data } = await sandboxApi.get("/ticket/user-tickets", {
        params: { fake_user_id, sandbox_id },
      });
      return data.tickets;
    },
    enabled: !!fake_user_id && !!sandbox_id,
  });
}
