export interface Ticket {
  stk: string;
  created_at: string;
  updated_at: string;
  summary: string;
  type: string;
  source: string;
  raise_human: boolean;
  is_closed: boolean;
  user_id: string | null;
  contact_email: string;
  idx: string;
  subject: string;
  priority: string;
  name: string;
  additional_info: string | null;
  sessionid: string | null;
  total_tickets: string;
  attachment?: string[];
  attachment_email?: string[]
  imageUrl?: string;
}

export interface CustomerTicket {
  created_at: string;
  updated_at: string;
  summary: string;
  type: string;
  source: string;
  raise_human: boolean;
  imageUrl?: string;
  is_closed: boolean;
  user_id: string | null;
  contact_email: string;
  idx: string;
  subject: string;
  priority: string;
  name: string;
  stk: string;
  is_active: boolean;
  additional_info: string | null;
  sessionid: string | null;
  total_tickets: string;
  organizationName?: string;
  availableCredits: number;
  attachment?: string[] | null;
}

export interface EmailLogEntry {
  id: string;
  from: string;
  to: string;
  subject: string;
  body: string;
  timestamp: string;
  stk?: string;
}

export interface TicketMessage {
  id: string;
  author: string;
  text: string;
  timestamp: string;
}
