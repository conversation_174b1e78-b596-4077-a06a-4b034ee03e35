"use client";
import React, { useState } from "react";
import RaisedTicketsPage from "./_components/tickets/RaisedTicketsPage";
import { useTickets } from "./_hooks/useTickets";
import { useConcierge } from "../_context/ConciergeContext";
import { useTranslations } from "next-intl";

export default function HomePage() {
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState("All");
  const { data: conciergeData } = useConcierge();
  const t = useTranslations("DashboardYumiSandbox.SupportPage");

  const {
    data: tickets = [],
    isLoading,
    isError,
  } = useTickets(conciergeData?.sandbox_id);

  if (isLoading) {
    return (
      <div className="p-6 space-y-6">
        {/* Skeleton for header */}
        <div className="h-8 w-1/3 bg-gray-200 rounded animate-pulse mb-4" />
        {/* Skeleton for summary cards */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="rounded-xl border bg-white shadow-sm">
              <div className="bg-gray-100 px-4 py-4 animate-pulse" />
              <div className="p-4">
                <div className="h-8 w-16 bg-gray-200 rounded mb-2 animate-pulse" />
                <div className="h-4 w-32 bg-gray-100 rounded animate-pulse" />
              </div>
            </div>
          ))}
        </div>
        {/* Skeleton for table */}
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="h-6 w-1/4 bg-gray-200 rounded mb-4 animate-pulse" />
          <div className="overflow-x-auto border rounded">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {[
                    t("table.sn"),
                    t("table.date"),
                    t("table.customer"),
                    t("table.email"),
                    t("table.issueType"),
                    t("table.status"),
                    t("table.priority"),
                  ].map((col, idx) => (
                    <th
                      key={col}
                      className="px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase"
                    >
                      {col}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-100">
                {[...Array(8)].map((_, i) => (
                  <tr key={i} className="animate-pulse">
                    {Array(7)
                      .fill(0)
                      .map((_, j) => (
                        <td key={j} className="px-4 py-2">
                          <div className="h-4 bg-gray-200 rounded w-full" />
                        </td>
                      ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  }

  if (isError) return <div>{t("error")}</div>;

  return (
    <RaisedTicketsPage
      tickets={tickets}
      page={page}
      setPage={setPage}
      status={status}
      setStatus={setStatus}
    />
  );
}
