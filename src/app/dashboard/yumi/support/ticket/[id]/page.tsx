"use client";

import React from "react";
import { useTicket } from "../../_hooks/useTicket";
import { useCustomer } from "../../_hooks/useCustomer";
import { CustomerTicket } from "../../_types";
import { BreadcrumbBar } from "../../_components/customers/BreadcrumbBar";
import ProfileHeader from "../../_components/customers/ProfileHeader";
import CustomerTabs from "../../_components/customers/CustomersTab";
import { useConcierge } from "../../../_context/ConciergeContext";
import { useTranslations } from "next-intl";

export default function CustomerDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = React.use(params);
  const { data: conciergeData } = useConcierge();
  const t = useTranslations("DashboardYumiSandbox.SupportTicketDetailPage");

  const {
    data: ticket,
    isLoading,
    isError,
  } = useTicket(id, conciergeData?.sandbox_id);

  // Get user_id from ticket (if available)
  const user_id = ticket?.user_id;
  const { data: customerArr, isLoading: isCustomerLoading } = useCustomer(
    user_id || ""
  );

  // Always use the first customer from the array, or undefined
  const customerData = customerArr;
  // Array.isArray(customerArr) && customerArr.length > 0
  //   ? customerArr[0]
  //   : undefined;

  if (isLoading || (user_id && isCustomerLoading)) {
    return (
      <div className="p-6 space-y-6">
        {/* Breadcrumb skeleton */}
        <div className="h-6 w-1/3 bg-gray-200 rounded animate-pulse mb-4" />
        {/* ProfileHeader skeleton */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-full bg-gray-200 animate-pulse" />
            <div className="h-6 w-32 bg-gray-200 rounded animate-pulse" />
          </div>
          <div className="flex flex-wrap gap-2">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="h-8 w-32 bg-gray-200 rounded animate-pulse"
              />
            ))}
          </div>
        </div>
        {/* Tabs skeleton */}
        <div className="h-10 w-full bg-gray-100 rounded animate-pulse mt-6" />
        {/* Tab content skeleton */}
        <div className="h-64 w-full bg-gray-100 rounded animate-pulse" />
      </div>
    );
  }

  if (isError || !ticket) return <div>{t("notFound")}</div>;

  // Build the CustomerTicket object, using ticket as fallback
  const customer: CustomerTicket = customerData
    ? {
        created_at: customerData.created_at ?? ticket.created_at,
        updated_at: customerData.updated_at ?? ticket.updated_at,
        summary: customerData.summary ?? ticket.summary,
        type: customerData.type ?? ticket.type,
        source: customerData.source ?? ticket.source,
        raise_human: customerData.raise_human ?? ticket.raise_human,
        imageUrl:
          customerData.imageUrl ??
          customerData.avatar_url ??
          ticket.imageUrl ??
          "",
        is_closed:
          typeof customerData.is_closed === "boolean"
            ? customerData.is_closed
            : ticket.is_closed,
        user_id: customerData.user_id ?? ticket.user_id,
        contact_email: customerData.contact_email ?? ticket.contact_email,
        idx: customerData.idx ?? ticket.idx,
        subject: customerData.subject ?? ticket.subject,
        priority: customerData.priority ?? ticket.priority,
        name: customerData.name ?? ticket.name,
        stk: customerData.stk ?? ticket.stk,
        is_active:
          typeof customerData.is_active === "boolean"
            ? customerData.is_active
            : true,
        additional_info:
          customerData.additional_info ?? ticket.additional_info ?? null,
        sessionid: customerData.sessionid ?? ticket.sessionid ?? null,
        total_tickets: customerData.total_tickets ?? ticket.total_tickets,
        organizationName: customerData.organizationName ?? "",
        availableCredits:
          typeof customerData.availableCredits === "number"
            ? customerData.availableCredits
            : 0,
        attachment: customerData.attachment ?? ticket.attachment ?? null,
      }
    : {
        created_at: ticket.created_at,
        updated_at: ticket.updated_at,
        summary: ticket.summary,
        type: ticket.type,
        source: ticket.source,
        raise_human: ticket.raise_human,
        imageUrl: ticket.imageUrl ?? "",
        is_closed: ticket.is_closed,
        user_id: ticket.user_id,
        contact_email: ticket.contact_email,
        idx: ticket.idx,
        subject: ticket.subject,
        priority: ticket.priority,
        name: ticket.name,
        stk: ticket.stk,
        is_active: true,
        additional_info: ticket.additional_info ?? null,
        sessionid: ticket.sessionid ?? null,
        total_tickets: ticket.total_tickets,
        organizationName: "",
        availableCredits: 0,
        attachment: ticket.attachment ?? null,
      };

  return (
    <>
      <BreadcrumbBar
        items={[
          {
            label: t("breadcrumb.internalSupport"),
            href: "/dashboard/yumi/support",
          },
          { label: customer.name },
        ]}
      />
      <div className="p-6 space-y-6">
        <ProfileHeader customer={customer} ticket={ticket} />
        <CustomerTabs ticket={ticket} selectedId={ticket.idx} />
      </div>
    </>
  );
}
