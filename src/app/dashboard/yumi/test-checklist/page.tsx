"use client";

import React, { useState, useEffect } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { useConcierge } from "../_context/ConciergeContext";
import { useTranslations } from "next-intl";

const TestChecklistPage = () => {
  const { data: conciergeData } = useConcierge();
  const t = useTranslations("DashboardYumiSandbox.TestChecklistPage");
  const checklist = [
    {
      title: t("checklist.chatFunctionality.title"),
      description: (
        <>
          <div className="text-sm text-gray-700 mb-2">
            <b>
              {
                t("checklist.chatFunctionality.goal", {
                  concierge: conciergeData?.concierge || "",
                }).split(":")[0]
              }
              :
            </b>{" "}
            {
              t("checklist.chatFunctionality.goal", {
                concierge: conciergeData?.concierge || "",
              }).split(": ")[1]
            }
          </div>
          <ul className="list-disc ml-6 text-sm text-gray-700 space-y-1">
            <li>
              {t("checklist.chatFunctionality.askGeneral", {
                concierge: conciergeData?.concierge || "",
              })}
            </li>
            <li>{t("checklist.chatFunctionality.testSimilarity")}</li>
          </ul>
        </>
      ),
    },
    {
      title: t("checklist.ticketCreation.title"),
      description: (
        <>
          <div className="text-sm text-gray-700 mb-2">
            <b>
              {
                t("checklist.ticketCreation.goal", {
                  concierge: conciergeData?.concierge || "",
                }).split(":")[0]
              }
              :
            </b>{" "}
            {
              t("checklist.ticketCreation.goal", {
                concierge: conciergeData?.concierge || "",
              }).split(": ")[1]
            }
          </div>
          <ul className="list-disc ml-6 text-sm text-gray-700 space-y-1">
            <li>
              {t("checklist.ticketCreation.confirmTicket", {
                concierge: conciergeData?.concierge || "",
              })}
            </li>
            <li>
              {t("checklist.ticketCreation.checkConfirmation", {
                concierge: conciergeData?.concierge || "",
              })}
            </li>
          </ul>
        </>
      ),
    },
    {
      title: t("checklist.ticketCreationEmail.title"),
      description: (
        <>
          <div className="text-sm text-gray-700 mb-2">
            <b>
              {
                t("checklist.ticketCreationEmail.goal", {
                  concierge: conciergeData?.concierge || "",
                }).split(":")[0]
              }
              :
            </b>{" "}
            {
              t("checklist.ticketCreationEmail.goal", {
                concierge: conciergeData?.concierge || "",
              }).split(": ")[1]
            }
          </div>
          <ul className="list-disc ml-6 text-sm text-gray-700 space-y-1">
            <li>{t("checklist.ticketCreationEmail.sendSupportRequest")}</li>
            <li>
              {t("checklist.ticketCreationEmail.observeReply", {
                concierge: conciergeData?.concierge || "",
              })}
            </li>
            <li>{t("checklist.ticketCreationEmail.checkConfirmationEmail")}</li>
          </ul>
        </>
      ),
    },
    {
      title: t("checklist.ticketCreationCall.title"),
      description: (
        <>
          <div className="text-sm text-gray-700 mb-2">
            <b>
              {
                t("checklist.ticketCreationCall.goal", {
                  concierge: conciergeData?.concierge || "",
                }).split(":")[0]
              }
              :
            </b>{" "}
            {
              t("checklist.ticketCreationCall.goal", {
                concierge: conciergeData?.concierge || "",
              }).split(": ")[1]
            }
          </div>
          <ul className="list-disc ml-6 text-sm text-gray-700 space-y-1">
            <li>{t("checklist.ticketCreationCall.sendSupportRequest")}</li>
            <li>
              {t("checklist.ticketCreationCall.observeReply", {
                concierge: conciergeData?.concierge || "",
              })}
            </li>
            <li>{t("checklist.ticketCreationCall.checkConfirmationEmail")}</li>
          </ul>
        </>
      ),
    },
    {
      title: t("checklist.internalSupportView.title"),
      description: (
        <>
          <div className="text-sm text-gray-700 mb-2">
            <b>{t("checklist.internalSupportView.goal").split(":")[0]}:</b>{" "}
            {t("checklist.internalSupportView.goal").split(": ")[1]}
          </div>
          <ul className="list-disc ml-6 text-sm text-gray-700 space-y-1">
            <li>{t("checklist.internalSupportView.checkDashboard")}</li>
            <li>{t("checklist.internalSupportView.locateTickets")}</li>
            <li>{t("checklist.internalSupportView.verifyMetadata")}</li>
            <li>{t("checklist.internalSupportView.checkAttachments")}</li>
          </ul>
        </>
      ),
    },
    {
      title: t("checklist.escalationReferral.title"),
      description: (
        <>
          <div className="text-sm text-gray-700 mb-2">
            <b>{t("checklist.escalationReferral.goal").split(":")[0]}:</b>{" "}
            {t("checklist.escalationReferral.goal").split(": ")[1]}
          </div>
          <ul className="list-disc ml-6 text-sm text-gray-700 space-y-1">
            <li>{t("checklist.escalationReferral.selectIssue")}</li>
            <li>{t("checklist.escalationReferral.referInterface")}</li>
            <li>{t("checklist.escalationReferral.confirmAssignee")}</li>
          </ul>
        </>
      ),
    },
    {
      title: t("checklist.markResolved.title"),
      description: (
        <>
          <div className="text-sm text-gray-700 mb-2">
            <b>{t("checklist.markResolved.goal").split(":")[0]}:</b>{" "}
            {t("checklist.markResolved.goal").split(": ")[1]}
          </div>
          <ul className="list-disc ml-6 text-sm text-gray-700 space-y-1">
            <li>{t("checklist.markResolved.markResolved")}</li>
            <li>{t("checklist.markResolved.confirmTimestamp")}</li>
          </ul>
        </>
      ),
    },
  ];
  const [checked, setChecked] = useState(Array(checklist.length).fill(false));

  const handleCheck = (idx: number) => {
    setChecked((prev) => {
      const updated = [...prev];
      updated[idx] = !updated[idx];
      return updated;
    });
  };

  return (
    <div className="mx-auto bg-white rounded-lg shadow p-6 mt-6">
      <h2 className="text-2xl font-semibold mb-6">{t("title")}</h2>
      <Accordion type="multiple" defaultValue={["item-1"]} className="w-full">
        {checklist.map((item, idx) => (
          <AccordionItem key={item.title} value={`item-${idx + 1}`}>
            <AccordionTrigger className="text-base font-medium flex items-center gap-2">
              <div className="flex items-center gap-2">
                <Input
                  type="checkbox"
                  checked={checked[idx]}
                  onChange={() => handleCheck(idx)}
                  className="accent-black w-4 h-4"
                  aria-label={`Mark ${item.title} as complete`}
                  onClick={(e) => e.stopPropagation()}
                />
                {item.title}
              </div>
            </AccordionTrigger>
            <AccordionContent>{item.description}</AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};

export default TestChecklistPage;
