// app/error.js
'use client';

import { useEffect } from 'react';

export default function Error({ error, reset }) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Unhandled error:', error);
  }, [error]);

  return (
    <div className="mx-auto grid place-content-center p-4 bg- rounded -lg min-h-screen w-full bg-white">
      {/* <h2 className="text-xl font-normal text-sky300">Oops! Something went wrong! </h2> */}
      <p className="my-2">Oops! Something went wrong. Please refresh the page.</p>
      <button 
        onClick={() => reset()}
        className="px-4 py-2 mt-4 bg-primary text-white rounded hover:opacity-[0.9]"
      >
        Try again
      </button>
    </div>
  );
}