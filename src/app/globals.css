@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: #22263f;
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.font-festive {
  font-family: var(--font-festive), cursive;
}

@media (min-width: 2000px) {
  .landing-pic {
    max-width: 25vw;
  }
}

.linear {
  background: linear-gradient(
    to bottom,
    #f7f7f7 0%,
    #fafafa 47.84%,
    #f7f7f7 100%
  );
  box-shadow: 0px 4px 8px 1px rgba(215, 215, 215, 0.25);
}

/* override-styles.css */

/* Main container */
.react-tel-input {
  font-family: inherit !important;
}

.react-tel-input .form-control {
  width: 100% !important;
  height: 34px !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 0.375rem !important;
  font-size: 14px !important;
  line-height: 34px !important;
}

/* Flag dropdown button */
.react-tel-input .flag-dropdown {
  background-color: white !important;
  border: none !important;
  border-right: 1px solid #e2e8f0 !important;
  border-radius: 0.375rem 0 0 0.375rem !important;
}

.react-tel-input .selected-flag {
  padding: 0 8px 0 12px !important;
  width: auto !important;
  min-width: 110px !important;
  display: flex !important;
  align-items: center !important;
  border-radius: 0.375rem 0 0 0.375rem !important;
}

/* Add padding to the arrow */
.react-tel-input .selected-flag .arrow {
  right: 8px !important;
  left: auto !important;
  border-top: 4px solid #555 !important;
}

/* Phone number input field */
.react-tel-input input.form-control {
  padding-left: 12px !important;
  border-radius: 0 0.375rem 0.375rem 0 !important;
  background-color: white !important;
}

/* Dropdown menu */
.react-tel-input .country-list {
  margin: 4px 0 0 -1px !important;
  max-height: 240px !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e2e8f0 !important;
  overflow-y: auto !important;
}

/* Country list items */
.react-tel-input .country-list .country {
  padding: 8px 12px !important;
  display: flex !important;
  align-items: center !important;
}

.react-tel-input .country-list .country:hover {
  background-color: #f7fafc !important;
}

.react-tel-input .country-list .country.highlight {
  background-color: #f0f9ff !important;
}

.tgme_widget_login_button {
  display: inline-block;
  vertical-align: top;
  font-size: 13px;
  font-weight: 500;
  line-height: 20px;
  border-radius: 17px;
  background-color: red !important;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 7px 16px 6px;
  margin: 0;
  border: none;
  color: #fff;
  cursor: pointer;
}

.tv-lightweight-charts__watermark,
#tv-attr-logo {
  display: none !important;
}

/* Hide scrollbar utility */
.scrollbar-hide {
  /* Hide scrollbar for Chrome, Safari and Opera */
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}
