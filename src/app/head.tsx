// app/head.tsx
export default function Head() {
    return (
      <>
        {/* Tell the browser “fetch this SVG with high priority” */}
        <link
          rel="preload"
          href="/hero2.webp"
          as="image"
          type="image/svg+xml"
        />
        <link
          rel="preload"
          href="/officebackground.svg"
          as="image"
          type="image/svg+xml"
        />
      </>
    );
  }
  