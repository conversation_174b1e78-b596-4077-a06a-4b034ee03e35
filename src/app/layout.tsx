import type { <PERSON><PERSON><PERSON> } from "next";
import <PERSON>ript from "next/script";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";
import { NextIntlClientProvider } from "next-intl";
import { getLocale } from "next-intl/server";
import "./globals.css";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

import { ReactQueryProvider } from "@/Providers/ReactQueryProvider";
import { Toaster } from "sonner";
import { LanguageProvider } from "@/Providers/LanguageProvider";
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
// app/page.tsx (or app/page.jsx)

export const metadata: Metadata = {
  title: "AI WK",
  description:
    "Purpose-Built AI Work Models for Professionals Powering analysis, execution, and decision-making — always on, always efficient.",
  icons: {
    icon: "/favicon32.png",
  },
  keywords: [
    "AI WK",
    "trading alerts",
    "Hermes",
    "Orion",
    "Luca",
    "AI analytics",
    "Agents",
    "AI",
    "Work Model",
    "Support",
    "Yumi",
    "Freddie",
    "HermesX",
    "Olympus",
    "HermesC",
    "Banking",
    "Marketing",
    "Prediction",
  ],
  authors: [{ name: "AI WK Team", url: "https://ai-wk.com" }],
  creator: "AI WK",
  metadataBase: new URL("https://ai-wk.com"),
  openGraph: {
    title: "AI WK",
    description:
      "Purpose-Built AI Work Models for Professionals Powering analysis, execution, and decision-making — always on, always efficient.",
    url: "https://ai-wk.com",
    siteName: "AI WK",
    images: [
      {
        url: "/logo.jpg", // in your public folder
        width: 1200,
        height: 630,
        alt: "AI WK Preview Image",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AI WK",
    description:
      "Purpose-Built AI Work Models for Professionals Powering analysis, execution, and decision-making — always on, always efficient.",
    images: ["/logo.jpg"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "/",
  },
};

const figtree = Figtree({
  subsets: ["latin"],
  weight: ["500", "700"], // Add the weights you need
  variable: "--font-figtree", // Optional variable name
});

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();

  return (
    <html lang={locale}>
      <head>
        {/* Google Tag Manager */}
        <Script id="gtm-script" strategy="afterInteractive">
          {`
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-KDW92F4M');
          `}
        </Script>
        {/* End Google Tag Manager */}
      </head>
      <body className={`${figtree.className}   antialiased`}>
        {/* Google Tag Manager (noscript) */}
        <noscript>
          <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KDW92F4M"
            height="0" width="0" style={{display:'none',visibility:'hidden'}}></iframe>
        </noscript>
        {/* End Google Tag Manager (noscript) */}
        <NextIntlClientProvider>
          <Toaster richColors />
          <LanguageProvider>
            <div>
              <ReactQueryProvider>
                <main className="flex flex-col min-h-screen">{children}</main>
                <ReactQueryDevtools initialIsOpen={false} position="right"/>
              </ReactQueryProvider>
            </div>
          </LanguageProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
