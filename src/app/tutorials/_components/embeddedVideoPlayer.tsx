// @ts-nocheck
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { X } from "lucide-react";

type EmbeddedVideoPlayerProps = {
  isOpen: boolean;
  videoUrl?: string;
  onClose?: () => void;
};

const EmbeddedVideoPlayer = ({
  isOpen,
  videoUrl,
  onClose,
}: EmbeddedVideoPlayerProps) => {
  // Extract YouTube video ID from URL
  const getYouTubeVideoId = (url: string) => {
    if (!url) return null;
    const regExp =
      /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url?.match(regExp);
    return match && match[2].length === 11 ? match[2] : null;
  };

  const videoId = videoUrl ? getYouTubeVideoId(videoUrl) : null;

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose && onClose()}>
      <DialogContent
        className="sm:max-w-[720px] p-0 bg-white shadow-lg rounded-md [&>button]:hidden"
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <DialogTitle className="sr-only">YouTube Video</DialogTitle>
        <X
          width={20}
          color="white"
          height={20}
          onClick={() => onClose && onClose()}
          className="absolute -top-10 right-0 cursor-pointer"
        />
        {videoId && (
          <iframe
            className="rounded-md"
            width="100%"
            height="405"
            src={`https://www.youtube.com/embed/${videoId}`}
            title="YouTube video player"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          ></iframe>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EmbeddedVideoPlayer;
