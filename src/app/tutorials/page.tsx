export const metadata = {
  title: "AIWK Tutorials | How to Use AI Work Models & Assistants",
  description:
    "Step-by-step tutorials and guides for getting started with AIWK's AI work models and assistants. Learn how to automate workflows, boost productivity, and unlock the full power of AI for your business.",
  openGraph: {
    title: "AIWK Tutorials | How to Use AI Work Models & Assistants",
    description:
      "Step-by-step tutorials and guides for getting started with AIWK's AI work models and assistants. Learn how to automate workflows, boost productivity, and unlock the full power of AI for your business.",
    url: "https://ai-wk.com/tutorials",
    siteName: "AIWK",
    images: [
      {
        url: "/og-logo.png",
        width: 1200,
        height: 630,
        alt: "AIWK | AI Work Models for Professionals",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AIWK Tutorials | How to Use AI Work Models & Assistants",
    description:
      "Step-by-step tutorials and guides for getting started with AIWK's AI work models and assistants. Learn how to automate workflows, boost productivity, and unlock the full power of AI for your business.",
    images: ["/og-logo.png"],
    creator: "@ai_wk_com",
  },
  alternates: {
    canonical: "https://ai-wk.com/tutorials",
  },
};
import Tutorials from "./_components/Tutorials";

export default function TutorialsPage() {
  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-50 p-4">
      <Tutorials />
    </div>
  );
}
