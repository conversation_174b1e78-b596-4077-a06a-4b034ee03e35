"use client";

import { useMemo } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>mpo<PERSON><PERSON><PERSON>,
} from "recharts";

interface AgentDecision {
  time: number;
  currentPrice: number;
  entryPrice: number;
  takeProfit: number;
  stopLoss: number;
  confidence: number;
  position: string;
  reasoning: string;
  tradingAmount: number;
  tradingPrice: number;
  orderType: string;
  positionChange: number;
  timeStep: number;
  decisionId: string;
  originalTimestamp: string;
}

interface PriceData {
  time: number;
  open?: number;
  high?: number;
  low?: number;
  close?: number;
  value?: number; // Fallback for when OHLC data is not available
  volume?: number;
}

interface AgentDecisionChartProps {
  priceData: PriceData[];
  agentDecisions: AgentDecision[];
  height?: number;
  isLoading?: boolean;
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;

    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm font-medium text-gray-900">
          {new Date(label * 1000).toLocaleString("en-US", {
            timeZone: "America/New_York",
            month: "short",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
          })}
        </p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: ${entry.value?.toFixed(2)}
          </p>
        ))}
        {data.reasoning && (
          <p className="text-xs text-gray-600 mt-2 max-w-xs">
            {data.reasoning}
          </p>
        )}
      </div>
    );
  }
  return null;
};

// Custom dot component for trading actions
const TradingActionDot = (props: any) => {
  const { cx, cy, payload } = props;

  console.log("TradingActionDot render:", {
    cx,
    cy,
    tradingAmount: payload.tradingAmount,
    positionChange: payload.positionChange,
    orderType: payload.orderType,
  });

  if (!payload.tradingAmount || Math.abs(payload.tradingAmount) === 0) {
    return null;
  }

  const isBuy =
    payload.positionChange > 0 ||
    payload.orderType?.toLowerCase().includes("buy");
  const color = isBuy ? "#4CAF50" : "#F44336";
  const size = 8;

  if (isBuy) {
    // Upward triangle for buy
    return (
      <polygon
        points={`${cx},${cy - size} ${cx - size},${cy + size} ${cx + size},${
          cy + size
        }`}
        fill={color}
        stroke="white"
        strokeWidth={2}
      />
    );
  } else {
    // Downward triangle for sell
    return (
      <polygon
        points={`${cx},${cy + size} ${cx - size},${cy - size} ${cx + size},${
          cy - size
        }`}
        fill={color}
        stroke="white"
        strokeWidth={2}
      />
    );
  }
};

export default function AgentDecisionChart({
  priceData,
  agentDecisions,
  height = 400,
  isLoading = false,
}: AgentDecisionChartProps) {
  // Combine price data and agent decisions into chart data
  const chartData = useMemo(() => {
    console.log("AgentDecisionChart - Processing data:", {
      priceDataLength: priceData.length,
      agentDecisionsLength: agentDecisions.length,
      samplePriceData: priceData.slice(0, 3),
      sampleAgentDecisions: agentDecisions.slice(0, 3),
    });

    if (!priceData.length && !agentDecisions.length) {
      return [];
    }

    // Create a map of all timestamps
    const timeMap = new Map();

    // Add price data
    priceData.forEach((price) => {
      timeMap.set(price.time, {
        price: price.close || price.value || price.open,
        ...price,
      });
    });

    // Add agent decisions
    agentDecisions.forEach((decision) => {
      const existing = timeMap.get(decision.time) || { time: decision.time };

      console.log("Processing agent decision:", {
        time: decision.time,
        entryPrice: decision.entryPrice,
        takeProfit: decision.takeProfit,
        stopLoss: decision.stopLoss,
        tradingAmount: decision.tradingAmount,
        positionChange: decision.positionChange,
        currentPrice: decision.currentPrice,
        position: decision.position,
        reasoning: decision.reasoning,
      });

      timeMap.set(decision.time, {
        ...existing,
        currentPrice: decision.currentPrice,
        // Don't filter out zero values, let Recharts handle nulls
        entryPrice: decision.entryPrice || null,
        takeProfit: decision.takeProfit || null,
        stopLoss: decision.stopLoss || null,
        confidence: decision.confidence,
        reasoning: decision.reasoning,
        position: decision.position,
        tradingAmount: decision.tradingAmount,
        orderType: decision.orderType,
        positionChange: decision.positionChange,
        // Use current price as fallback for main price line if no market price
        price: existing.price || decision.currentPrice,
      });
    });

    // Convert to array and sort by time
    const result = Array.from(timeMap.values()).sort((a, b) => a.time - b.time);

    console.log("AgentDecisionChart - Chart data processed:", {
      resultLength: result.length,
      sampleResult: result.slice(0, 3),
      entryPriceCount: result.filter((item) => item.entryPrice).length,
      takeProfitCount: result.filter((item) => item.takeProfit).length,
      stopLossCount: result.filter((item) => item.stopLoss).length,
      tradingActionCount: result.filter(
        (item) => item.tradingAmount && Math.abs(item.tradingAmount) > 0
      ).length,
    });

    return result;
  }, [priceData, agentDecisions]);

  if (isLoading) {
    return (
      <div
        className="flex items-center justify-center bg-gray-50 rounded-lg"
        style={{ height: `${height}px` }}
      >
        <div className="text-gray-500">Loading chart data...</div>
      </div>
    );
  }

  if (!chartData.length) {
    return (
      <div
        className="flex items-center justify-center bg-gray-50 rounded-lg"
        style={{ height: `${height}px` }}
      >
        <div className="text-gray-500">No data available</div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-4">
        <div className="flex items-center gap-6 text-sm flex-wrap">
          <div className="flex items-center gap-2">
            <div className="w-4 h-0.5 bg-[#0D5EFF]"></div>
            <span className="text-[#474747]">Price</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-0.5 bg-[#2196F3]"></div>
            <span className="text-[#474747]">Entry Price</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-0.5 bg-[#4CAF50]"></div>
            <span className="text-[#474747]">Take Profit</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-0.5 bg-[#F44336]"></div>
            <span className="text-[#474747]">Stop Loss</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-b-[8px] border-l-transparent border-r-transparent border-b-[#4CAF50]"></div>
            <span className="text-[#474747]">Buy Orders</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-[#F44336]"></div>
            <span className="text-[#474747]">Sell Orders</span>
          </div>
        </div>
      </div>

      <div style={{ height: `${height}px` }}>
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
            <XAxis
              dataKey="time"
              type="number"
              scale="time"
              domain={["dataMin", "dataMax"]}
              tickFormatter={(value) => {
                const date = new Date(value * 1000);
                return date
                  .toLocaleDateString("en-US", {
                    timeZone: "America/New_York",
                    month: "2-digit",
                    day: "2-digit",
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: false,
                  })
                  .replace(",", " ");
              }}
              axisLine={false}
              tickLine={false}
              tick={{ fill: "#a0a7b4", fontSize: 12 }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fill: "#a0a7b4", fontSize: 12 }}
              tickFormatter={(value) => `$${value.toFixed(2)}`}
            />
            <Tooltip content={<CustomTooltip />} />

            {/* Main price line */}
            <Line
              type="monotone"
              dataKey="price"
              stroke="#0D5EFF"
              strokeWidth={2}
              dot={false}
              name="Price"
              connectNulls={false}
            />

            {/* Entry price line */}
            <Line
              type="monotone"
              dataKey="entryPrice"
              stroke="#2196F3"
              strokeWidth={2}
              dot={(props: any) => {
                const { cx, cy, payload } = props;

                console.log("Entry Price Dot Check:", {
                  tradingAmount: payload.tradingAmount,
                  positionChange: payload.positionChange,
                  orderType: payload.orderType,
                  position: payload.position,
                  reasoning: payload.reasoning,
                  allPayloadKeys: Object.keys(payload),
                });

                // Only show triangles when this line has valid data AND it's a test point
                const hasValidEntryPrice =
                  payload.entryPrice && payload.entryPrice > 0;
                const shouldShowTriangle =
                  hasValidEntryPrice && payload.time % 3 === 0;

                if (!shouldShowTriangle) return null;

                // SIMPLIFIED: Explicit alternating logic for testing
                const timeValue = payload.time || 0;
                const shouldBeBuy = timeValue % 6 === 0; // Every 6th point is a buy

                console.log("Entry triangle logic:", {
                  time: timeValue,
                  mod6: timeValue % 6,
                  shouldBeBuy,
                  willBeGreen: shouldBeBuy,
                });

                const size = 10;

                if (shouldBeBuy) {
                  // GREEN BUY TRIANGLE - Points UP
                  console.log("Rendering GREEN upward triangle at", {
                    cx,
                    cy,
                    time: timeValue,
                  });
                  return (
                    <polygon
                      points={`${cx},${cy - size} ${cx - size},${cy + size} ${
                        cx + size
                      },${cy + size}`}
                      fill="#4CAF50"
                      stroke="white"
                      strokeWidth={2}
                    />
                  );
                } else {
                  // RED SELL TRIANGLE - Points DOWN
                  console.log("Rendering RED downward triangle at", {
                    cx,
                    cy,
                    time: timeValue,
                  });
                  return (
                    <polygon
                      points={`${cx},${cy + size} ${cx - size},${cy - size} ${
                        cx + size
                      },${cy - size}`}
                      fill="#F44336"
                      stroke="white"
                      strokeWidth={2}
                    />
                  );
                }
              }}
              name="Entry Price"
              connectNulls={true}
            />

            {/* Take profit line */}
            <Line
              type="monotone"
              dataKey="takeProfit"
              stroke="#4CAF50"
              strokeWidth={2}
              dot={(props: any) => {
                const { cx, cy, payload } = props;

                // Only show triangles when this line has valid data AND it's a test point
                const hasValidTakeProfit =
                  payload.takeProfit && payload.takeProfit > 0;
                const shouldShowTriangle =
                  hasValidTakeProfit && payload.time % 3 === 0;

                if (!shouldShowTriangle) return null;

                // SIMPLIFIED: Explicit alternating logic for testing
                const timeValue = payload.time || 0;
                const shouldBeBuy = timeValue % 6 === 0; // Every 6th point is a buy
                const size = 10;

                if (shouldBeBuy) {
                  // GREEN BUY TRIANGLE - Points UP
                  return (
                    <polygon
                      points={`${cx},${cy - size} ${cx - size},${cy + size} ${
                        cx + size
                      },${cy + size}`}
                      fill="#4CAF50"
                      stroke="white"
                      strokeWidth={2}
                    />
                  );
                } else {
                  // RED SELL TRIANGLE - Points DOWN
                  return (
                    <polygon
                      points={`${cx},${cy + size} ${cx - size},${cy - size} ${
                        cx + size
                      },${cy - size}`}
                      fill="#F44336"
                      stroke="white"
                      strokeWidth={2}
                    />
                  );
                }
              }}
              name="Take Profit"
              connectNulls={true}
            />

            {/* Stop loss line */}
            <Line
              type="monotone"
              dataKey="stopLoss"
              stroke="#F44336"
              strokeWidth={2}
              dot={(props: any) => {
                const { cx, cy, payload } = props;

                // Only show triangles when this line has valid data AND it's a test point
                const hasValidStopLoss =
                  payload.stopLoss && payload.stopLoss > 0;
                const shouldShowTriangle =
                  hasValidStopLoss && payload.time % 3 === 0;

                if (!shouldShowTriangle) return null;

                // SIMPLIFIED: Explicit alternating logic for testing
                const timeValue = payload.time || 0;
                const shouldBeBuy = timeValue % 6 === 0; // Every 6th point is a buy
                const size = 10;

                if (shouldBeBuy) {
                  // GREEN BUY TRIANGLE - Points UP
                  return (
                    <polygon
                      points={`${cx},${cy - size} ${cx - size},${cy + size} ${
                        cx + size
                      },${cy + size}`}
                      fill="#4CAF50"
                      stroke="white"
                      strokeWidth={2}
                    />
                  );
                } else {
                  // RED SELL TRIANGLE - Points DOWN
                  return (
                    <polygon
                      points={`${cx},${cy + size} ${cx - size},${cy - size} ${
                        cx + size
                      },${cy - size}`}
                      fill="#F44336"
                      stroke="white"
                      strokeWidth={2}
                    />
                  );
                }
              }}
              name="Stop Loss"
              connectNulls={true}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
