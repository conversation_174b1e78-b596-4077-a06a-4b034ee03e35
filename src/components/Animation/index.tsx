import { motion } from "framer-motion";

export const PullUpAnimation = ({ children, isActive }: any) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={isActive ? { opacity: 1, y: 0 } : { opacity: 1, y: 10 }}
      transition={{
        duration: 0.8,
        // delay: 0.8,
        ease: "easeIn",
      }}
    >
      {children}
    </motion.div>
  );
};

export const FadeDownAnimation = ({ children, isActive }: any) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -30 }}
      animate={isActive ? { opacity: 1, y: 0 } : { opacity: 1, y: -10 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  );
};

export const SlideInLeftAnimation = ({ children, isActive }: any) => {
  return (
    <motion.div
    initial={{ opacity: 0, x: -100 }}
    whileInView={{ opacity: 1, x: 0 }}
    viewport={{ once: true, amount: 0.2 }} // Runs when 20% of the element is visible
    transition={{ duration: 0.8, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  );
};

export const SlideInRightAnimation = ({ children, isActive }: any) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: 100 }}
      whileInView={{ opacity: 1, x: 0 }}
      viewport={{ once: true, amount: 0.2 }} // Runs when 20% of the element is visible
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  );
};

export const StaggeredListAnimation = ({ children, isActive }: any) => {
  return (
    <motion.div
      initial="hidden"
      animate={isActive ? "show" : "hidden"}
      variants={{
        hidden: { opacity: 0 },
        show: {
          opacity: 1,
          transition: { staggerChildren: 0.2 },
        },
      }}
    >
      {children}
    </motion.div>
  );
};

export const StaggeredItemAnimation = ({ children }: any) => {
  return (
    <motion.div
      variants={{
        hidden: { opacity: 0, y: 20 },
        show: { opacity: 1, y: 0 },
      }}
    >
      {children}
    </motion.div>
  );
};
