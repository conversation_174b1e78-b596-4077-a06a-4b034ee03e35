"use client";

import { useEffect, useRef } from "react";
import { createChart, ColorType } from "lightweight-charts";

export default function AssetValueChart({
  data: chartData,
  fullDataLength,
  replayIndex,
  height = 200,
  hideTimeLabels = false,
  agentNames = [],
  onTimeRangeChange,
  syncedTimeRange,
}: {
  data: any[];
  fullDataLength: number;
  replayIndex: number;
  height?: number;
  hideTimeLabels?: boolean;
  agentNames?: any[];
  onTimeRangeChange?: (
    range: { from: number; to: number },
    sourceChart: string
  ) => void;
  syncedTimeRange?: { from: number; to: number } | null;
}) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);
  const seriesRefs = useRef<any[]>([]);
  const userInteractingRef = useRef<boolean>(false);
  const syncingRef = useRef<boolean>(false); // Track if we're currently syncing from another chart
  const lastCallbackTimeRef = useRef<number>(0); // Throttle callback calls

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = createChart(chartRef.current, {
      layout: {
        background: { type: ColorType.Solid, color: "white" },
        textColor: "#333",
      },
      autoSize: true, // Automatically size to parent container
      grid: {
        vertLines: { color: "#f0f0f0" },
        horzLines: { color: "#f0f0f0" },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: "#cccccc",
      },
      timeScale: {
        timeVisible: !hideTimeLabels,
        secondsVisible: false,
        borderVisible: false,
        ticksVisible: !hideTimeLabels,
        // Format time in ET (Eastern Time) like "04/22 12:24 ET"
        tickMarkFormatter: (time: any) => {
          const date = new Date(time * 1000);
          const formatted = date.toLocaleString("en-US", {
            timeZone: "America/New_York",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
          });
          // Convert "04/22/2024, 12:24" to "04/22 12:24 ET"
          const [datePart, timePart] = formatted.split(", ");
          const [month, day] = datePart.split("/");
          return `${month}/${day} ${timePart} ET`;
        },
      },
      handleScroll: false,
      handleScale: false,
    });

    chartInstance.current = chart;

    // Clear previous series
    seriesRefs.current = [];

    // Create line series for each agent
    if (chartData.length > 0 && chartData[0].agentNames) {
      chartData[0].agentNames.forEach((agent: any, index: number) => {
        const series = chart.addLineSeries({
          color: agent.color || "#009dff",
          lineWidth: 2,
          title: agent.name,
        });
        seriesRefs.current.push({ series, agentKey: agent.key });
      });
    } else {
      // Fallback: create series based on agentNames prop
      agentNames.forEach((agent: any, index: number) => {
        const series = chart.addLineSeries({
          color: agent.color || "#009dff",
          lineWidth: 2,
          title: agent.name,
        });
        seriesRefs.current.push({ series, agentKey: agent.key });
      });
    }

    // Add event listeners to track user interaction
    // const timeScale = chart.timeScale();

    // Track when user starts interacting (scrolling/zooming)
    // timeScale.subscribeVisibleLogicalRangeChange((range) => {
    //   userInteractingRef.current = true;

    //   // Only call the synchronization callback if this change was initiated by user interaction, not syncing
    //   // Also throttle the calls to prevent excessive updates
    //   const now = Date.now();
    //   if (
    //     onTimeRangeChange &&
    //     range &&
    //     !syncingRef.current &&
    //     now - lastCallbackTimeRef.current > 100
    //   ) {
    //     lastCallbackTimeRef.current = now;
    //     onTimeRangeChange(range, "asset");
    //   }

    //   // Reset the interaction flag after a short delay
    //   setTimeout(() => {
    //     userInteractingRef.current = false;
    //   }, 100);
    // });

    return () => {
      chart.remove();
    };

    // Only run on mount/unmount
    // eslint-disable-next-line
  }, []);

  // Set a default visible range (e.g., last 50 bars or all if less)
  const setDefaultZoom = () => {
    if (chartInstance.current && chartData.length > 0) {
      const totalBars = chartData.length;
      const visibleBars = Math.min(50, fullDataLength); // Show last 50 bars or all
      chartInstance.current.timeScale().setVisibleLogicalRange({
        from: totalBars - visibleBars,
        to: totalBars - 1,
      });
    }
  };

  // Update data when chartData changes
  useEffect(() => {
    if (seriesRefs.current.length === 0 || !chartInstance.current) return;

    // Check if chart instance is still valid to prevent "Object is disposed" error
    try {
      // Store current visible range before updating data
      const currentRange = chartInstance.current
        .timeScale()
        .getVisibleLogicalRange();

      // Update each series with its data
      seriesRefs.current.forEach(({ series, agentKey }) => {
        const seriesData = chartData
          .filter(
            (item) => item[agentKey] !== null && item[agentKey] !== undefined
          )
          .map((item) => ({
            time: item.time,
            value: item[agentKey],
          }));

        series.setData(seriesData);
      });

      // Don't auto-adjust view if user is currently interacting with the chart
      if (userInteractingRef.current) {
        return;
      }
    } catch (error) {
      console.warn("Chart instance disposed, skipping data update");
      return;
    }

    // Always fit content to show the full time range for asset value chart
    try {
      chartInstance.current.timeScale().fitContent();
    } catch (error) {
      console.warn("Chart instance disposed, skipping fitContent");
    }
  }, [chartData]);

  useEffect(() => {
    setDefaultZoom();
  }, [chartData.length]);

  // For asset value chart, always show the full time range
  useEffect(() => {
    if (chartInstance.current && chartData.length > 0) {
      try {
        chartInstance.current.timeScale().fitContent();
      } catch (error) {
        console.warn("Chart instance disposed, skipping fitContent");
      }
    }
  }, [chartData.length]);

  // Handle synced time range changes from other charts
  useEffect(() => {
    if (
      syncedTimeRange &&
      chartInstance.current &&
      !userInteractingRef.current
    ) {
      syncingRef.current = true; // Mark that we're syncing
      const timeScale = chartInstance.current.timeScale();

      // Use requestAnimationFrame to ensure smooth updates
      requestAnimationFrame(() => {
        if (chartInstance.current) {
          try {
            timeScale.setVisibleLogicalRange(syncedTimeRange);

            // Reset syncing flag after animation frame
            setTimeout(() => {
              syncingRef.current = false;
            }, 150); // Longer delay to prevent feedback
          } catch (error) {
            console.warn("Chart instance disposed, skipping sync");
            syncingRef.current = false;
          }
        }
      });
    }
  }, [syncedTimeRange]);

  return (
    <div
      ref={chartRef}
      style={{
        width: "100%",
        height: `${height}px`,
        position: "relative",
      }}
    />
  );
}
