"use client";

import React, { useEffect, useRef, useState } from "react";
import { createChart, CrosshairMode } from "lightweight-charts";
import moment from "moment";
import { formatInTimeZone } from "date-fns-tz";

export default function CandlestickWithVolume({ data }: { data: any[] }) {
  const containerRef = useRef<HTMLDivElement>(null);
  const priceChartRef = useRef<HTMLDivElement>(null);
  const volumeChartRef = useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState(0);

  useEffect(() => {
    if (!containerRef.current) return;
    setWidth(containerRef.current.clientWidth);

    const handleResize = () => {
      if (containerRef.current) {
        setWidth(containerRef.current.clientWidth);
      }
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    if (!priceChartRef.current || !volumeChartRef.current || width === 0)
      return;

    // === SEPARATE PRICE CHART ===
    const priceChart = createChart(priceChartRef.current, {
      width,
      height: 600, // Dedicated height for price chart
      layout: { background: { color: "transparent" }, textColor: "#333" },
      grid: { vertLines: { color: "#eee" }, horzLines: { color: "#eee" } },
      crosshair: { mode: CrosshairMode.Normal },
      rightPriceScale: {
        borderVisible: false,
        scaleMargins: {
          top: 0.1,
          bottom: 0.1,
        },
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
        borderVisible: false,
        // Format as "YY-MM-DD HH:mm" in ET timezone
        tickMarkFormatter: (time: any) => {
          try {
            const date = new Date(time * 1000);
            // Convert to ET timezone and format as "YY-MM-DD HH:mm"
            const etDate = formatInTimeZone(
              date,
              "America/New_York",
              "yy-MM-dd HH:mm"
            );
            return etDate;
          } catch (error) {
            // Fallback to simple format if timezone formatting fails
            const date = new Date(time * 1000);
            const year = date.getFullYear().toString().slice(-2);
            const month = String(date.getMonth() + 1).padStart(2, "0");
            const day = String(date.getDate()).padStart(2, "0");
            const hours = String(date.getHours()).padStart(2, "0");
            const minutes = String(date.getMinutes()).padStart(2, "0");
            return `${year}-${month}-${day} ${hours}:${minutes}`;
          }
        },
      },
    });

    // === SEPARATE VOLUME CHART ===
    const volumeChart = createChart(volumeChartRef.current, {
      width,
      height: 300, // Dedicated height for volume chart
      layout: {
        background: { color: "transparent" },
        textColor: "#333",
        fontFamily:
          '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      },
      grid: { vertLines: { color: "#eee" }, horzLines: { color: "#eee" } },
      crosshair: { mode: CrosshairMode.Normal },
      rightPriceScale: {
        borderVisible: false,
        scaleMargins: {
          top: 0.2, // Increased top margin to prevent label cutoff
          bottom: 0.1,
        },
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
        borderVisible: false,
        // Format as "YY-MM-DD HH:mm" in ET timezone
        tickMarkFormatter: (time: any) => {
          try {
            const date = new Date(time * 1000);
            // Convert to ET timezone and format as "YY-MM-DD HH:mm"
            const etDate = formatInTimeZone(
              date,
              "America/New_York",
              "yy-MM-dd HH:mm"
            );
            return etDate;
          } catch (error) {
            // Fallback to simple format if timezone formatting fails
            const date = new Date(time * 1000);
            const year = date.getFullYear().toString().slice(-2);
            const month = String(date.getMonth() + 1).padStart(2, "0");
            const day = String(date.getDate()).padStart(2, "0");
            const hours = String(date.getHours()).padStart(2, "0");
            const minutes = String(date.getMinutes()).padStart(2, "0");
            return `${year}-${month}-${day} ${hours}:${minutes}`;
          }
        },
      },
    });

    // === CANDLESTICK SERIES (Price Chart) ===
    const candleSeries = priceChart.addCandlestickSeries({
      upColor: "#26a69a",
      downColor: "#ef5350",
      borderUpColor: "#26a69a",
      borderDownColor: "#ef5350",
      wickUpColor: "#26a69a",
      wickDownColor: "#ef5350",
      priceFormat: {
        type: "price",
        precision: 2,
        minMove: 0.01,
      },
    });

    // === VOLUME SERIES (Volume Chart) ===
    const volumeSeries = volumeChart.addHistogramSeries({
      priceFormat: {
        type: "volume",
        precision: 0,
        minMove: 1,
      },
      color: "#888",
    });

    // === PREPARE DATA FOR BOTH SERIES ===
    const processedData = data.map((d) => {
      let time;

      // Handle different time formats
      if (typeof d.time === "number") {
        // Unix timestamp (for hourly data)
        time = d.time;
      } else if (d.time && typeof d.time === "string") {
        // ISO string - check if it should be hourly or daily
        if (d.time.includes("T") && d.originalTimestamp) {
          // Convert to Unix timestamp for hourly precision
          time = Math.floor(new Date(d.time).getTime() / 1000);
        } else {
          // Daily format - extract date only
          time = d.time.split("T")[0];
        }
      } else if (d.ts) {
        // Fallback timestamp
        time = Math.floor(d.ts / 60);
      } else {
        // Default fallback
        time = Math.floor(Date.now() / 1000);
      }

      return {
        time: time,
        open: d.open,
        high: d.high,
        low: d.low,
        close: d.close,
        volume: d.volume ?? Math.random() * 1000,
      };
    });

    // Prepare candlestick data
    const candleData = processedData.map((d) => ({
      time: d.time,
      open: d.open,
      high: d.high,
      low: d.low,
      close: d.close,
    }));

    // Prepare volume data with colors based on price movement
    const volumeData = processedData.map((d) => ({
      time: d.time,
      value: d.volume,
      color: d.close >= d.open ? "#26a69a" : "#ef5350",
    }));

    // Debug logging
    console.log("CandlestickChart - Original data sample:", data.slice(0, 3));
    console.log(
      "CandlestickChart - Processed data sample:",
      processedData.slice(0, 3)
    );

    // Set data for both series
    candleSeries.setData(candleData);
    volumeSeries.setData(volumeData);

    // === ADD REFERENCE LINES TO PRICE CHART (like in the image) ===
    // Add horizontal reference lines similar to the image
    if (processedData.length > 0) {
      const lastPrice = processedData[processedData.length - 1].close;
      const avgPrice =
        processedData.reduce((sum, d) => sum + d.close, 0) /
        processedData.length;

      // Add reference lines
      priceChart
        .addLineSeries({
          color: "#ef5350",
          lineWidth: 1,
          lineStyle: 1, // Dotted line
          priceLineVisible: false,
        })
        .setData([
          { time: processedData[0].time, value: lastPrice },
          {
            time: processedData[processedData.length - 1].time,
            value: lastPrice,
          },
        ]);

      priceChart
        .addLineSeries({
          color: "#9e9e9e",
          lineWidth: 1,
          lineStyle: 2, // Dashed line
          priceLineVisible: false,
        })
        .setData([
          { time: processedData[0].time, value: avgPrice },
          {
            time: processedData[processedData.length - 1].time,
            value: avgPrice,
          },
        ]);
    }

    // Fit content for both charts
    priceChart.timeScale().fitContent();
    volumeChart.timeScale().fitContent();

    // === SYNC TIME SCALES BETWEEN CHARTS ===
    // Sync price chart time scale changes to volume chart
    priceChart.timeScale().subscribeVisibleTimeRangeChange(() => {
      const timeRange = priceChart.timeScale().getVisibleRange();
      if (timeRange) {
        volumeChart.timeScale().setVisibleRange(timeRange);
      }
    });

    // Sync volume chart time scale changes to price chart
    volumeChart.timeScale().subscribeVisibleTimeRangeChange(() => {
      const timeRange = volumeChart.timeScale().getVisibleRange();
      if (timeRange) {
        priceChart.timeScale().setVisibleRange(timeRange);
      }
    });

    // === SYNC LOGICAL RANGE CHANGES ===
    // Sync price chart logical range changes to volume chart
    priceChart.timeScale().subscribeVisibleLogicalRangeChange(() => {
      const logicalRange = priceChart.timeScale().getVisibleLogicalRange();
      if (logicalRange) {
        volumeChart.timeScale().setVisibleLogicalRange(logicalRange);
      }
    });

    // Sync volume chart logical range changes to price chart
    volumeChart.timeScale().subscribeVisibleLogicalRangeChange(() => {
      const logicalRange = volumeChart.timeScale().getVisibleLogicalRange();
      if (logicalRange) {
        priceChart.timeScale().setVisibleLogicalRange(logicalRange);
      }
    });

    return () => {
      priceChart.remove();
      volumeChart.remove();
    };
  }, [data, width]);

  return (
    <div
      ref={containerRef}
      style={{ width: "100%", display: "flex", flexDirection: "column" }}
    >
      {/* Price Chart */}
      <div className="mb-0">
        <div ref={priceChartRef} style={{ width: "100%", height: 600 }} />
      </div>

      {/* Volume Chart */}
      <div className="mt-0">
        <div
          ref={volumeChartRef}
          style={{
            width: "100%",
            height: 300,
            paddingTop: "10px", // Add padding to prevent label cutoff
            paddingRight: "10px", // Add padding for right-side labels
          }}
        />
      </div>
    </div>
  );
}
