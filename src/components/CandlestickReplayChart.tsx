"use client";

import { useEffect, useRef } from "react";
import { createChart, ColorType } from "lightweight-charts";

export default function CandlestickReplayChart({
  data: chartData,
  fullDataLength,
  replayIndex,
  participantActions = [],
  eventMarkers = [],
  onTimeRangeChange,
  syncedTimeRange,
  height = 300,
}: {
  data: any[];
  fullDataLength: number;
  replayIndex: number;
  participantActions?: any[];
  eventMarkers?: any[];
  onTimeRangeChange?: (
    range: { from: number; to: number },
    sourceChart: string
  ) => void;
  syncedTimeRange?: { from: number; to: number } | null;
  height?: number;
}) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);
  const seriesRef = useRef<any>(null);
  const volumeSeriesRef = useRef<any>(null);
  const userInteractingRef = useRef<boolean>(false);
  const syncingRef = useRef<boolean>(false); // Track if we're currently syncing from another chart
  const lastCallbackTimeRef = useRef<number>(0); // Throttle callback calls
  const markersContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = createChart(chartRef.current, {
      layout: {
        background: { type: ColorType.Solid, color: "white" },
        textColor: "#333",
      },
      autoSize: true, // Automatically size to parent container
      grid: {
        vertLines: { color: "#f0f0f0" },
        horzLines: { color: "#f0f0f0" },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: "#cccccc",
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
        // Format time in ET (Eastern Time) like "04/22 12:24 ET"
        tickMarkFormatter: (time: any) => {
          const date = new Date(time * 1000);
          const formatted = date.toLocaleString("en-US", {
            timeZone: "America/New_York",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
          });
          // Convert "04/22/2024, 12:24" to "04/22 12:24 ET"
          const [datePart, timePart] = formatted.split(", ");
          const [month, day] = datePart.split("/");
          return `${month}/${day} ${timePart} ET`;
        },
      },
      handleScroll: false,
      handleScale: false,
    });

    chartInstance.current = chart;

    // Create candlestick series
    const series = chart.addCandlestickSeries({
      upColor: "#26a69a",
      downColor: "#ef5350",
      borderVisible: false,
      wickUpColor: "#26a69a",
      wickDownColor: "#ef5350",
      priceScaleId: "right",
    });

    // Create volume series
    const volumeSeries = chart.addHistogramSeries({
      color: "#26a69a",
      priceFormat: {
        type: "volume",
      },
      priceScaleId: "volume",
    });

    // Configure volume price scale
    chart.priceScale("volume").applyOptions({
      scaleMargins: {
        top: 0.7,
        bottom: 0,
      },
    });

    seriesRef.current = series;
    volumeSeriesRef.current = volumeSeries;

    // Prepare data for both series
    const candlestickData = chartData.map((item: any) => ({
      time: item.time,
      open: item.open || item.value,
      high: item.high || item.value,
      low: item.low || item.value,
      close: item.close || item.value,
    }));

    const volumeData = chartData
      .filter((item: any) => item.volume !== undefined && item.volume !== null)
      .map((item: any) => ({
        time: item.time,
        value: item.volume,
        color:
          (item.close || item.value) >= (item.open || item.value)
            ? "#26a69a"
            : "#ef5350",
      }));

    series.setData(candlestickData);
    if (volumeData.length > 0) {
      volumeSeries.setData(volumeData);
    }

    // Store series reference for updating markers
    seriesRef.current = series;

    // Initially fit content to establish the full range
    chart.timeScale().fitContent();

    // Resize handler
    const handleResize = () => {
      if (chartRef.current) {
        const containerRect = chartRef.current.getBoundingClientRect();
        const newWidth =
          Math.floor(containerRect.width) || chartRef.current.clientWidth;
        const newHeight =
          height === 0
            ? Math.floor(containerRect.height) || chartRef.current.clientHeight
            : height;

        chart.applyOptions({
          width: newWidth,
          height: newHeight,
        });
      }
    };

    window.addEventListener("resize", handleResize);

    // Add ResizeObserver for more reliable container size tracking
    let resizeObserver: ResizeObserver | null = null;
    let resizeTimeout: NodeJS.Timeout | null = null;

    if (chartRef.current && window.ResizeObserver) {
      resizeObserver = new ResizeObserver((entries) => {
        if (resizeTimeout) clearTimeout(resizeTimeout);

        resizeTimeout = setTimeout(() => {
          for (const entry of entries) {
            const { width, height: containerHeight } = entry.contentRect;
            const newWidth = Math.max(100, Math.floor(width));
            const newHeight =
              height === 0
                ? Math.max(100, Math.floor(containerHeight))
                : height;

            // Ensure width doesn't exceed container
            const maxWidth =
              chartRef.current?.parentElement?.clientWidth || newWidth;
            const constrainedWidth = Math.min(newWidth, maxWidth);

            chart.applyOptions({
              width: constrainedWidth,
              height: newHeight,
            });
          }
        }, 50); // 50ms debounce
      });
      resizeObserver.observe(chartRef.current);
    }

    return () => {
      chart.remove();
    };

    // Only run on mount/unmount
    // eslint-disable-next-line
  }, []);

  // Update data only when chartData changes
  useEffect(() => {
    if (seriesRef.current && chartInstance.current) {
      // Store current visible range before updating data
      const currentRange = chartInstance.current
        .timeScale()
        .getVisibleLogicalRange();

      // Update the data
      seriesRef.current.setData(chartData);

      // Don't auto-adjust view if user is currently interacting with the chart
      if (userInteractingRef.current) {
        return;
      }

      // Only fit content if this is the first data load (no previous range)
      // or if the data array is very small (likely initial load)
      if (!currentRange || chartData.length <= 2) {
        chartInstance.current.timeScale().fitContent();
      } else {
        // For replay updates, maintain left-aligned view and scroll as data grows
        const dataLength = chartData.length;
        const currentVisibleRange = currentRange.to - currentRange.from;

        // If we're near the end of visible data and new data is being added, scroll forward
        if (dataLength > 0 && !userInteractingRef.current) {
          const newRange = chartInstance.current
            .timeScale()
            .getVisibleLogicalRange();
          if (newRange) {
            // Keep the same visible range width but potentially shift right as data grows
            const shouldScroll = currentRange.to >= newRange.to - 2; // Near the end

            if (shouldScroll) {
              // Scroll to show the latest data while maintaining range width
              chartInstance.current.timeScale().setVisibleLogicalRange({
                from: Math.max(
                  newRange.from,
                  newRange.to - currentVisibleRange
                ),
                to: newRange.to,
              });
            } else {
              // Preserve the current visible range for ongoing updates
              const maxLogicalIndex = Math.max(0, chartData.length - 1);
              const preservedRange = {
                from: Math.max(0, Math.min(currentRange.from, maxLogicalIndex)),
                to: Math.max(0, Math.min(currentRange.to, maxLogicalIndex)),
              };

              if (preservedRange.from < preservedRange.to) {
                chartInstance.current
                  .timeScale()
                  .setVisibleLogicalRange(preservedRange);
              }
            }
          }
        }
      }
    }
  }, [chartData]);

  // Set a default visible range (e.g., last 50 bars or all if less)
  const setDefaultZoom = () => {
    if (chartInstance.current && chartData.length > 0) {
      const totalBars = chartData.length;
      const visibleBars = Math.min(50, fullDataLength); // Show last 50 bars or all

      // Calculate from and to indices with proper bounds checking
      const toIndex = Math.max(0, totalBars - 1);
      const fromIndex = Math.max(0, Math.min(toIndex, totalBars - visibleBars));

      // Ensure from index is always less than to index
      if (fromIndex < toIndex) {
        chartInstance.current.timeScale().setVisibleLogicalRange({
          from: fromIndex,
          to: toIndex,
        });
      } else {
        // Fallback: show all data if the calculated range is invalid
        chartInstance.current.timeScale().fitContent();
      }
    }
  };

  useEffect(() => {
    setDefaultZoom();
  }, [chartData.length]);

  // Right-align the visible range based on replayIndex
  useEffect(() => {
    if (chartInstance.current && fullDataLength > 0) {
      const visibleBars = Math.min(50, fullDataLength);
      const rightEdge = Math.max(0, fullDataLength - 1);
      const leftEdge = Math.max(
        0,
        rightEdge - Math.max(visibleBars, replayIndex) + 1
      );

      // Ensure leftEdge is always less than rightEdge
      if (leftEdge < rightEdge) {
        chartInstance.current.timeScale().setVisibleLogicalRange({
          from: leftEdge,
          to: rightEdge,
        });
      } else {
        // Fallback: show all data if the calculated range is invalid
        chartInstance.current.timeScale().fitContent();
      }
    }
  }, [replayIndex, fullDataLength]);

  // Handle synced time range changes from other charts
  useEffect(() => {
    if (
      syncedTimeRange &&
      chartInstance.current &&
      !userInteractingRef.current
    ) {
      syncingRef.current = true; // Mark that we're syncing
      const timeScale = chartInstance.current.timeScale();

      // Use requestAnimationFrame to ensure smooth updates
      requestAnimationFrame(() => {
        if (chartInstance.current) {
          timeScale.setVisibleLogicalRange(syncedTimeRange);

          // Reset syncing flag after animation frame
          setTimeout(() => {
            syncingRef.current = false;
          }, 150); // Longer delay to prevent feedback
        }
      });
    }
  }, [syncedTimeRange]);

  // Update participant action markers dynamically as replay progresses
  useEffect(() => {
    if (!seriesRef.current || !participantActions) return;

    // Filter actions to only show those that have timestamps within the current chart data
    const chartTimes = chartData.map((item) => item.time);
    const minTime = Math.min(...chartTimes);
    const maxTime = Math.max(...chartTimes);

    const visibleActions = participantActions.filter(
      (action) => action.time >= minTime && action.time <= maxTime
    );

    console.log("Updating action markers:", {
      totalActions: participantActions.length,
      visibleActions: visibleActions.length,
      chartTimeRange: { minTime, maxTime },
      chartDataLength: chartData.length,
    });

    // Create markers for visible actions
    const markers = visibleActions.map((action) => ({
      time: action.time,
      position:
        action.type === "buy" ? ("belowBar" as const) : ("aboveBar" as const),
      color: action.participantColor,
      shape:
        action.type === "buy" ? ("arrowUp" as const) : ("arrowDown" as const),
      size: 0.8, // Smaller size for cleaner look
    }));

    // Update markers on the series
    seriesRef.current.setMarkers(markers);

    console.log("Set markers:", markers.length);
  }, [participantActions, chartData]);

  // Add custom HTML event markers to the chart
  useEffect(() => {
    if (!chartInstance.current || !chartRef.current || !eventMarkers.length)
      return;

    // Clear existing markers
    const existingMarkers = chartRef.current.querySelectorAll(".event-marker");
    existingMarkers.forEach((marker) => marker.remove());

    // Create HTML markers for events that have timestamps and are within the current replay data
    const validEvents = eventMarkers.filter((event: any) => {
      return (
        event.eventTimestamp &&
        event.priceChartIndex !== -1 &&
        event.priceChartIndex < chartData.length
      );
    });

    validEvents.forEach((event: any) => {
      // Get the first letter of the event title for the marker
      const markerText = event.title
        ? event.title.charAt(0).toUpperCase()
        : "E";

      // Use consistent background color for all markers
      const backgroundColor = "#3B82F6"; // Blue background for all markers
      const textColor = "white";

      // Get the time coordinate for positioning
      const eventTime =
        chartData[event.priceChartIndex]?.time || event.eventTimestamp;
      const timeCoordinate = chartInstance.current
        .timeScale()
        .timeToCoordinate(eventTime);

      if (timeCoordinate === null) return;

      // Create the marker element
      const markerElement = document.createElement("div");
      markerElement.className = "event-marker";
      markerElement.style.cssText = `
        position: absolute;
        left: ${timeCoordinate - 12}px;
        top: 10px;
        width: 24px;
        height: 24px;
        background-color: ${backgroundColor};
        color: ${textColor};
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        cursor: pointer;
        z-index: 1000;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        transition: all 0.2s ease;
        pointer-events: auto;
      `;
      markerElement.textContent = markerText;

      // Create custom tooltip
      const tooltip = document.createElement("div");
      tooltip.className = "event-marker-tooltip";
      tooltip.style.cssText = `
        position: absolute;
        top: 32px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.95);
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        font-size: 13px;
        white-space: normal;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s ease;
        z-index: 1001;
        box-shadow: 0 4px 12px rgba(0,0,0,0.4);
        border: 1px solid rgba(255,255,255,0.1);
        min-width: 250px;
        max-width: 310px;
        width: max-content;
      `;
      tooltip.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 6px; color: #60A5FA;">${
          event.title
        }</div>
        <div style="color: #E5E7EB; line-height: 1.4; margin-bottom: 4px;">${
          event.description
        }</div>
        ${
          event.eventTimestamp
            ? `<div style="color: #9CA3AF; font-size: 11px; margin-top: 6px; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 4px;">${new Date(
                event.eventTimestamp * 1000
              ).toLocaleString()}</div>`
            : ""
        }
      `;

      // Add arrow pointing up to the marker
      const arrow = document.createElement("div");
      arrow.style.cssText = `
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 6px solid rgba(0, 0, 0, 0.95);
      `;
      tooltip.appendChild(arrow);
      markerElement.appendChild(tooltip);

      // Add hover effects
      markerElement.addEventListener("mouseenter", () => {
        markerElement.style.transform = "scale(1.2)";
        markerElement.style.boxShadow = "0 4px 8px rgba(0,0,0,0.3)";
        tooltip.style.opacity = "1";
      });

      markerElement.addEventListener("mouseleave", () => {
        markerElement.style.transform = "scale(1)";
        markerElement.style.boxShadow = "0 2px 4px rgba(0,0,0,0.2)";
        tooltip.style.opacity = "0";
      });

      // Add click handler to jump to event
      markerElement.addEventListener("click", () => {
        // You can add click functionality here if needed
        console.log("Event marker clicked:", event.title);
      });

      chartRef.current?.appendChild(markerElement);
    });

    console.log("Created HTML event markers:", validEvents.length);

    // Update marker positions when the time scale changes
    const updateMarkerPositions = () => {
      if (!chartRef.current || !chartInstance.current) return;

      const markers = chartRef.current.querySelectorAll(".event-marker");
      markers.forEach((marker, index) => {
        const event = validEvents[index];
        if (event) {
          const eventTime =
            chartData[event.priceChartIndex]?.time || event.eventTimestamp;
          const timeCoordinate = chartInstance.current
            .timeScale()
            .timeToCoordinate(eventTime);

          if (timeCoordinate !== null) {
            (marker as HTMLElement).style.left = `${timeCoordinate - 12}px`;
          }
        }
      });
    };

    // Subscribe to time scale changes to update marker positions
    const timeScale = chartInstance.current.timeScale();
    timeScale.subscribeVisibleLogicalRangeChange(updateMarkerPositions);

    // Cleanup function
    return () => {
      timeScale.unsubscribeVisibleLogicalRangeChange(updateMarkerPositions);
    };
  }, [eventMarkers, chartData, replayIndex]);

  // Cleanup markers on unmount
  useEffect(() => {
    const chartElement = chartRef.current;
    return () => {
      if (chartElement) {
        const existingMarkers = chartElement.querySelectorAll(".event-marker");
        existingMarkers.forEach((marker) => marker.remove());
      }
    };
  }, []);

  return (
    <div
      ref={chartRef}
      style={{
        width: "100%",
        height: height === 0 ? "100%" : `${height}px`,
        position: "relative",
        maxWidth: "100%",
        minWidth: 0,
        boxSizing: "border-box",
      }}
    />
  );
}
