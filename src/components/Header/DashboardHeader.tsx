"use client";

import { useEffect, useMemo, useState } from "react";
import { useTranslations } from "next-intl";
import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import {
  Home,
  CreditCard,
  Settings,
  ChevronLeft,
  ChevronRight,
  Lock,
  LayoutGrid,
  PanelLeft,
  LogOut,
  ChevronUp,
  ChevronDown,
  Plus,
  Menu,
  X,
  Bell,
  Video,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import Image from "next/image";
import { Orbitron } from "next/font/google";
import { mainNavItems, modelNavItems } from "@/utils/data";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { logout, useGetQuery } from "@/services/api_hooks";
import useLocalStorage from "@/hooks/use-local-storage";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import OrganizationModal from "../modals/AddOrganization";
import LanguageDropdown from "../LanguageDropdown";
import LocaleButtons from "../localeButtons";
import Cookies from "js-cookie";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { freddieFrontendUrl } from "@/config/baseUrl";
interface NavTranslations {
  [key: string]: string;
}

const orbitron = Orbitron({
  subsets: ["latin"],
  weight: ["400", "700"],
  variable: "--font-orbitron",
});

const DashboardHeader = () => {
  // use next-intl for i18n
  const t = useTranslations("DashboardHome.DashboardHeader");

  const tDashboardNav = useTranslations("DashboardNav");

  // create Nav translations object
  // Every time Nav is updated, it must be updated here
  const DashboardNavTranslations: NavTranslations = {};
  ["Home", "Credits", "Settings"].map((key) => {
    DashboardNavTranslations[key] = tDashboardNav(`${key}`);
    return;
  });

  const [sheetOpen, setSheetOpen] = useState(false);
  const [open, setOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const [userCredentials, setName] = useLocalStorage("authCred", "");
  const [processedRecentlyUsed, setProcessedRecentlyUsed] = useState<any[]>([]);
  const [userCred, setUserCred] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { data: recentlyUsedData, isLoading } = useGetQuery(
    "/models/recent",
    ["get-recently-used"],
    {
      onError() {
        toast.error(t("toast.notLoadProfile"));
      },
    }
  );

  const {
    data: profile,
    isLoading: profileLoading,
    isRefetching,
  } = useGetQuery("/auth/profile", ["profile"], {
    refetchOnWindowFocus: false,
  });

  const accessToken = Cookies.get("accessToken");
  const refreshToken = Cookies.get("refreshToken");

  const modelCards = useMemo(
    () => [
      {
        id: "market-monitor",
        tab_label: t("cards.HermesX.tabLabel"),
        label: t("cards.HermesX.label"),
        title: t("cards.HermesX.title"),
        img: "/hermes_rec.svg",
        link: "/dashboard/hermes",
      },
      {
        id: "trader",
        tab_label: t("cards.HermesC.tabLabel"),
        label: t("cards.HermesC.label"),
        title: t("cards.HermesC.title"),
        img: "/hermes_rec.svg",
        link: "/dashboard/hermes-c",
      },
      {
        id: "accountant-ai",
        tab_label: t("cards.Luca.tabLabel"),
        label: t("cards.Luca.label"),
        title: t("cards.Luca.title"),
        img: "/luca_rec.svg",
        link: accessToken
          ? `https://luca.ai-wk.com/?token=${accessToken}&refresh_token=${refreshToken}`
          : "/dashboard/home",
      },
      {
        id: "hr",
        tab_label: t("cards.Freddie.tabLabel"),
        label: t("cards.Freddie.label"),
        title: t("cards.Freddie.title"),
        img: "/freddie_rec.svg",
        link: accessToken
          ? `${freddieFrontendUrl}/?token=${accessToken}&refresh_token=${refreshToken}`
          : "/dashboard/home",
      },
      {
        id: "equity-analyst",
        tab_label: t("cards.Orion.tabLabel"),
        label: t("cards.Orion.label"),
        title: t("cards.Orion.title"),
        img: "/orion_rec.svg",
        link: "/dashboard/orion",
        isLocked: false,
      },
      {
        id: "olympus",
        tab_label: t("cards.Olympus.tabLabel"),
        label: t("cards.Olympus.label"),
        title: t("cards.Olympus.title"),
        img: "/olympus_rec.svg",
        link: "/dashboard/olympus",
        isLocked: true,
      },
      {
        id: "yumi-sandbox",
        tab_label: t("cards.Yumi.tabLabel"),
        label: t("cards.Yumi.label"),
        title: t("cards.Yumi.title"),
        img: "/yumi_rec.svg",
        link: "/dashboard/yumi/setup",
        isLocked: true,
      },
    ],
    [accessToken, refreshToken]
  );

  // Helper function to render the correct icon
  const renderIconRecentlyUsed = (iconName: string, size: number = 18) => {
    switch (iconName) {
      case "Luca":
        return (
          <Image
            src="/luca_thumbnail.svg"
            height={24}
            width={20}
            alt="logo"
            className="h-full w-full"
          />
        );
      case "Hermes X":
        return (
          <Image
            src="/hermes_thumbnail.svg"
            height={24}
            width={20}
            alt="logo"
            className="h-full w-full"
          />
        );
      case "Hermes C":
        return (
          <Image
            src="/hermes_thumbnail.svg"
            height={24}
            width={20}
            alt="logo"
            className="h-full w-full"
          />
        );
      case "Orion":
        return (
          <Image
            src="/orion_thumbnail.svg"
            height={24}
            width={20}
            alt="logo"
            className="h-full w-full"
          />
        );
      case "Freddie":
        return (
          <Image
            src="/freddie_thumbnail.png"
            height={24}
            width={20}
            alt="logo"
            className="h-full w-full"
          />
        );
      case "Yumi Sandbox":
        return (
          <Image
            src="/yumi.png"
            height={24}
            width={20}
            alt="logo"
            className="h-full w-full"
          />
        );
      case "Olympus":
        return (
          <Image
            src="/olympus_thumbnail.png"
            height={24}
            width={20}
            alt="logo"
            className="h-full w-full"
          />
        );
      case "model":
      default:
        return <div className="w-5 h-5 bg-gray-300 rounded-sm"></div>;
    }
  };

  const renderIcon = (iconName: string, size: number = 18) => {
    switch (iconName) {
      case "home":
        return <LayoutGrid size={size} />;
      case "credit-card":
        return <CreditCard size={size} />;
      case "settings":
        return <Settings size={size} />;
      case "model":
      default:
        return <div className="w-5 h-5 bg-gray-300 rounded-sm"></div>;
    }
  };

  const handleLogout = () => {
    logout();
    setOpen(false);
    setSheetOpen(false);
    router.push("/login");
  };

  const getInitials = () => {
    const fullName =
      profile?.profile?.full_name || profile?.profile?.name || "";

    if (fullName && fullName.trim() !== "") {
      const nameParts = fullName.trim().split(" ");
      if (nameParts.length >= 2) {
        return (
          nameParts[0][0] + nameParts[nameParts.length - 1][0]
        ).toUpperCase();
      } else if (nameParts.length === 1) {
        return nameParts[0][0].toUpperCase();
      }
    }

    const firstName = profile?.profile.firstname || "";
    const lastName = profile?.profile?.lastname || "";

    const firstInitial = firstName.charAt(0).toUpperCase();
    const lastInitial = lastName.charAt(0).toUpperCase();

    return firstInitial + lastInitial || "?";
  };

  const getDisplayName = () => {
    const fullName = profile?.profile.full_name || profile?.profile?.name;

    if (fullName && fullName.trim() !== "") {
      return fullName;
    }

    const firstName = profile?.profile?.firstname || "";
    const lastName = profile?.profile?.lastname || "";

    if (firstName || lastName) {
      return `${firstName} ${lastName}`.trim();
    }

    return "User";
  };

  // Process recently used models data
  useEffect(() => {
    if (recentlyUsedData && recentlyUsedData.length > 0) {
      const enhancedRecentModels = recentlyUsedData.map((apiModel: any) => {
        // Find the matching static model data by slug/id
        const staticModel = modelCards?.find(
          (staticModel) => staticModel.id === apiModel.slug
        );

        // Return merged object with API data + image and link from static data
        return {
          ...apiModel,
          // Add properties from static model if available, otherwise use defaults
          img: staticModel?.img || "/default.svg",
          link: staticModel?.link || `/dashboard/${apiModel?.slug}`,
          tab_label: staticModel?.tab_label || apiModel?.name,
          label: staticModel?.label || apiModel.description,
          isLocked: apiModel?.unlocked === false, // Convert is_unlocked to isLocked
        };
      });

      setProcessedRecentlyUsed(enhancedRecentModels);
    }
  }, [recentlyUsedData, modelCards]);

  return (
    <>
      <div className="h-[65px] bg-white mb-2 sticky top-0 z-50 border-b border-gray-200">
        <div className="mx-auto max-w-[1400px] flex items-center justify-between h-full px-[2%]">
          {/* Left side - Menu button and Logo */}
          <div className="flex items-center gap-4">
            <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="p-2 md:hidden">
                  <Menu size={40} />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[90%] p-0 overflow-y-auto">
                <SheetHeader className="p-4 border-b">
                  <SheetTitle className="flex items-center gap-2 text-left">
                    <Image
                      src="/logo-black.svg"
                      height={50}
                      width={50}
                      alt="logo"
                    />
                    <span
                      className={`${orbitron.className} text-[24px] font-[800]`}
                    >
                      ai-wk
                    </span>
                  </SheetTitle>
                </SheetHeader>

                {/* Navigation Content */}
                <div className="flex flex-col justify-between h-screen">
                  <nav className="py-4 px-4 space-y-1 overflow-y-auto">
                    {/* Main Navigation Items */}
                    {mainNavItems.map((item) => (
                      <NavItem
                        key={item.href}
                        href={item.href}
                        icon={renderIcon(item.icon)}
                        text={DashboardNavTranslations[item.name] ?? item.name}
                        isActive={
                          pathname === `/dashboard/${item.href.toLowerCase()}`
                        }
                        isLocked={item.isLocked}
                        onClick={() => setSheetOpen(false)}
                      />
                    ))}

                    {/* Models Section Header */}
                    <div className="pt-4 pb-2">
                      <p className="font-medium text-black text-sm mb-2 px-3">
                        {t("MyModels")}
                      </p>
                    </div>

                    {/* Model Navigation Items with Skeleton Loader */}
                    {isLoading
                      ? Array(3)
                          .fill(0)
                          .map((_, index) => (
                            <div
                              key={`skeleton-model-${index}`}
                              className="flex items-center px-3 py-2 gap-3"
                            >
                              <Skeleton className="h-5 w-5 rounded-sm" />
                              <Skeleton className="h-4 w-24" />
                            </div>
                          ))
                      : processedRecentlyUsed?.map((item) => (
                          <NavItemRecentlyUsed
                            key={item.slug}
                            href={item.link || "/dashboard/home"}
                            icon={renderIconRecentlyUsed(item.name)}
                            text={item.name}
                            isActive={
                              pathname ===
                              `/dashboard/${item.name.toLowerCase()}`
                            }
                            isLocked={item.isLocked}
                            isDirectLink={
                              item.slug === "accountant-ai" &&
                              userCred?.token?.accessToken
                            }
                            onClick={() => setSheetOpen(false)}
                          />
                        ))}
                  </nav>

                  {/* User Profile Section */}
                  <div className="p-4 hover:bg-gray-100">
                    <Popover open={open} onOpenChange={setOpen}>
                      <PopoverTrigger asChild>
                        <button className="flex items-center py-2 gap-2 w-full cursor-pointer">
                          {profileLoading || isRefetching ? (
                            <>
                              <Skeleton className="w-8 h-8 rounded-full" />
                              <Skeleton className="h-4 w-24 flex-1" />
                            </>
                          ) : (
                            <>
                              {profile?.profile?.avatar_url ? (
                                <div className="w-8 h-8 relative rounded-full overflow-hidden">
                                  <Image
                                    src={profile?.profile?.avatar_url || ""}
                                    alt="Profile avatar"
                                    fill
                                    sizes="32px"
                                    className="object-cover"
                                  />
                                </div>
                              ) : (
                                <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center text-white">
                                  {getInitials()}
                                </div>
                              )}
                              <div className="flex-1 min-w-0 text-left">
                                <p className="text-sm font-medium truncate text-black">
                                  {getDisplayName()}
                                </p>
                              </div>
                              {open ? (
                                <ChevronUp size={20} />
                              ) : (
                                <ChevronDown size={20} />
                              )}
                            </>
                          )}
                        </button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-60 p-0"
                        side="top"
                        align="end"
                      >
                        <div className="grid gap-2">
                          <div className="px-3 py-2 rounded">
                            <p className="text-xs text-gray-400">
                              {t("OtherBusinesses")}
                            </p>
                          </div>
                          <hr />
                          <div
                            className="px-3 py-2 hover:bg-gray-50 rounded cursor-pointer flex items-center justify-between gap-2 text-red-500"
                            onClick={handleLogout}
                          >
                            <p className="text-sm">{t("LogOut")}</p>
                            <LogOut size={16} />
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>

          {/* Right side - Help */}
          <div className="flex items-center gap-2 text-[#7F7F81] text-[14px] cursor-pointer">
            <LocaleButtons />

            {/* <LanguageDropdown/> */}
            <TooltipProvider delayDuration={0}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href="/tutorials"
                    className="flex items-center justify-center text-sm text-[#0F172A] hover:text-gray-900"
                  >
                    <Image
                      src="/cam-recorder.svg"
                      alt={"cam-recorder"}
                      width={24}
                      height={24}
                      className="object-cover"
                      color="#6a7282"
                    />
                    {/* <Video size={12} className="text-gray-500" /> */}
                  </Link>
                </TooltipTrigger>
                <TooltipContent>{t("tutorials")}</TooltipContent>
              </Tooltip>
              {/*        <Link
              href="/dashboard/tutorials"
              className="flex items-center justify-center text-sm text-gray-500 hover:text-gray-900 gap-2 bg-[#F9F9F9] rounded-lg p-2"
            >
              <Bell width={26} height={26} color="#6a7282" />
            </Link> */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href="/dashboard/support"
                    className="flex items-center justify-center text-sm text-[#0F172A] hover:text-gray-900"
                  >
                    <Image
                      src="/help-icon.svg"
                      alt="help icon"
                      height={26}
                      width={26}
                    />
                    {/* {t("NeedHelp")} */}
                  </Link>
                </TooltipTrigger>
                <TooltipContent>{t("NeedHelp")}</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>

      <OrganizationModal open={isModalOpen} onOpenChange={setIsModalOpen} />
    </>
  );
};

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  text: string;
  isActive: boolean;
  isLocked?: boolean;
  onClick?: () => void;
}

function NavItem({
  href,
  icon,
  text,
  isActive,
  isLocked,
  onClick,
}: NavItemProps) {
  return (
    <Link
      href={`/dashboard/${href}` || "#"}
      className={cn(
        "flex items-center px-3 py-2 text-sm rounded-md",
        isActive
          ? "bg-[#FAFAFA] text-[#03061D]"
          : "text-[#A7A7A7] hover:bg-gray-100 hover:text-gray-900",
        isLocked ? "justify-between" : "gap-3"
      )}
      onClick={onClick}
    >
      <div className="flex items-center gap-3">
        <span className="shrink-0">{icon}</span>
        <span>{text}</span>
      </div>

      {isLocked && (
        <span className="text-[#A7A7A7]">
          <Image
            src="/padlock-gray.svg"
            alt="lock icon"
            height={15}
            width={15}
          />
        </span>
      )}
    </Link>
  );
}

interface NavItemRecentlyUsedProps {
  href: string;
  icon: React.ReactNode;
  text: string;
  isActive: boolean;
  isLocked?: boolean;
  isDirectLink?: boolean;
  onClick?: () => void;
}

function NavItemRecentlyUsed({
  href,
  icon,
  text,
  isActive,
  isLocked,
  isDirectLink,
  onClick,
}: NavItemRecentlyUsedProps) {
  const LinkComponent = isDirectLink ? "a" : Link;

  return (
    <LinkComponent
      href={href}
      className={cn(
        "flex items-center px-3 py-2 text-sm rounded-md",
        isActive
          ? "bg-[#FAFAFA] text-[#03061D]"
          : "text-[#A7A7A7] hover:bg-gray-100 hover:text-gray-900",
        isLocked ? "justify-between" : "gap-3"
      )}
      onClick={onClick}
      {...(isDirectLink
        ? { target: "_blank", rel: "noopener noreferrer" }
        : {})}
    >
      <div className="flex items-center gap-3">
        <div className="shrink-0 rounded-full h-[25px] w-[25px] overflow-hidden">
          {icon}
        </div>
        <span>{text}</span>
      </div>

      {isLocked && (
        <span className="text-[#A7A7A7]">
          <Image
            src="/padlock-gray.svg"
            alt="lock icon"
            height={15}
            width={15}
          />
        </span>
      )}
    </LinkComponent>
  );
}

export default DashboardHeader;
