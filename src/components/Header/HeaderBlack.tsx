"use client";

import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import Image from "next/image";
import { newLogo } from "../../assets";
import { siteConfig } from "@/static/site";
import { Orbitron } from "next/font/google";
import Logo from "../Logo";
import { AlignJustify } from "lucide-react";
import { usePathname } from "next/navigation";
import LocaleButtons from "../localeButtons";

const orbitron = Orbitron({
  subsets: ["latin"],
  weight: ["400", "700"], // Add the weights you need
  variable: "--font-orbitron", // Optional variable name
});

const HeaderBlack = ({ bgColor }: any) => {
  // use next-intl for i18n
  const t = useTranslations("HeaderBlack");
  const tModels = useTranslations("Home.Header.models");
  const tNav = useTranslations("Home.Header.nav");
  const tGlobal = useTranslations("global");

  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [isModelsExpanded, setIsModelsExpanded] = useState(false);

  const toggleModelsDropdown = (e: any) => {
    e.preventDefault();
    setIsModelsExpanded(!isModelsExpanded);
  };

  const models = [
    {
      name: tModels("Hermes.name"),
      description: tModels("Hermes.description"),
      href: "/hermes",
    },
    {
      name: tModels("Orion.name"),
      description: tModels("Orion.description"),
      href: "/orion",
    },
    {
      name: tModels("Olympus.name"),
      description: tModels("Olympus.description"),
      href: "/olympus",
    },
    {
      name: tModels("Luca.name"),
      description: tModels("Luca.description"),
      href: "/luca",
    },
    {
      name: tModels("Freddie.name"),
      description: tModels("Freddie.description"),
      href: "/freddie",
    },
    {
      name: tModels("Yumi.name"),
      description: tModels("Yumi.description"),
      href: "/yumi",
    },
    {
      name: tModels("CustomModels.name"),
      description: tModels("CustomModels.description"),
      href: "/custom-models",
    },
  ];

  // Handle body scroll lock more effectively
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }

    // Cleanup function
    return () => {
      document.body.style.overflow = "";
    };
  }, [isMenuOpen]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // Check if current path matches link or is a subpath of a model
  const isActive = (href: any) => {
    if (href === "/") {
      return pathname === "/";
    }

    // For models dropdown, check if any model path is active
    if (href === "#models") {
      return models.some((model) => pathname.startsWith(model.href));
    }

    return pathname === href || pathname.startsWith(href);
  };

  // Style for active and hover states
  const activeLinkStyle = "text-blue-400";

  return (
    <header className=" px-[5%] py-5   " style={{ backgroundColor: bgColor }}>
      <div className="flex items-center justify-between max-w-[1300px] mx-auto">
        <Logo icon="black" textColor="black" />

        {/* Desktop Navigation - Only visible on large devices (lg and up) */}
        <nav className="hidden lg:flex space-x-4 lg:space-x-8 text-black text-[16px]">
          <div
            className="relative group "
            onMouseEnter={() => setIsOpen(true)}
            onMouseLeave={() => setIsOpen(false)}
          >
            <div
              className={`hover:text-blue-400 transition-colors duration-200 cursor-pointer ${
                models.some((model) => pathname === model.href)
                  ? activeLinkStyle
                  : ""
              }`}
            >
              {tNav("Models")}
            </div>

            {/* Dropdown Menu */}
            <div
              className={`absolute bg-transparent z-50 w-[447px] min-h-[195px] left-0 overflow-hidden  transform transition-all duration-300 ease-in-out origin-top-left   ${
                isOpen
                  ? "opacity-100 scale-100"
                  : "opacity-0 scale-95 pointer-events-none"
              }`}
            >
              <div
                className={`bg-white mt-2 w-full rounded-md px-5 py-4 shadow-lg`}
              >
                <h3 className="text-[#828282] font-[500] text-[14px] mb-6">
                  {t("OurModels")}
                </h3>

                <div className="grid grid-cols-3 gap-6">
                  {models.map((model) => (
                    <Link
                      key={model.name}
                      href={model.href}
                      className={`group w-fit flex flex-col space-y-2 ${
                        pathname === model.href ? "text-blue-400" : ""
                      }`}
                    >
                      <div
                        className={`flex items-center hover:text-blue-400 ${
                          pathname === model.href
                            ? "text-blue-400"
                            : "text-[#22263F]"
                        } font-[500] text-[14px] transition-colors`}
                      >
                        {model.name}
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 group-hover:translate-x-1 transition-transform"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </div>
                      <p className="text-[#828282] text-[13px]">
                        {model.description}
                      </p>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <Link
            href="/pricing"
            className={`hover:text-blue-400 transition-colors duration-200 ${
              isActive("/pricing") ? activeLinkStyle : ""
            }`}
          >
            {tNav("Pricing")}
          </Link>
          <Link
            href="/about-us"
            className={`hover:text-blue-400 transition-colors duration-200 ${
              isActive("/about-us") ? activeLinkStyle : ""
            }`}
          >
            {tNav("AboutUs")}
          </Link>
          <Link
            href="/contact-us?tab=custom-models"
            className={`hover:text-blue-400 transition-colors duration-200 ${
              isActive("/contact-us") ? activeLinkStyle : ""
            }`}
          >
            {tNav("ContactUs")}
          </Link>
        </nav>

        {/* Desktop Buttons - Only visible on large devices (lg and up) */}
        <div className="hidden lg:flex space-x-4">
          <LocaleButtons />
          <Link href="/login">
            <button className="rounded border border-black text-black cursor-pointer hover:bg-white hover:text-navy-900 transition w-[88px] h-[37px] text-[14px]">
              {tGlobal("Login")}
            </button>
          </Link>
          <Link href="/sign-up">
            <button className="rounded bg-black text-white hover:opacity-[0.7] cursor-pointer transition font-medium w-[128px] h-[37px] text-[14px]">
              {tGlobal("GetStartedNow")}
            </button>
          </Link>
        </div>

        {/* Hamburger Menu Button - Visible on tablet and mobile (lg and down) */}
        <button
          className="lg:hidden text-black flex flex-col justify-center items-center  "
          onClick={toggleMenu}
          aria-label="Toggle menu"
          aria-expanded={isMenuOpen}
        >
          <AlignJustify className="" size={30} />
        </button>

        {/* Mobile/Tablet Full-Screen Menu with transition */}
        <div
          className={`lg:hidden fixed inset-0 bg-[#04142B] z-50 h-screen overflow-y-auto pb-5 transform transition-transform duration-300 ease-in-out ${
            isMenuOpen ? "translate-x-0" : "translate-x-full"
          }`}
        >
          {/* Menu header with logo and close button */}
          <div className="flex items-center justify-between p-4 border-b border-gray-800">
            <Logo icon="white" />

            {/* close icon */}
            <button
              className="text-white flex flex-col justify-center items-center w-10 h-10 p-2"
              onClick={toggleMenu}
              aria-label="Close menu"
            >
              <span className="block w-6 h-0.5 bg-white transition-transform duration-300 ease-in-out rotate-45"></span>
              <span className="block w-6 h-0.5 bg-white transition-transform duration-300 ease-in-out -rotate-45 -mt-0.5"></span>
            </button>
          </div>

          {/* Menu content */}
          <div className="flex-1 overflow-y-auto">
            <nav className="flex flex-col">
              {/* Centered menu items with animation */}
              <div className="flex-1 flex flex-col space-y-6 text-white text-lg px-4">
                {/* Models dropdown */}
                <div className="w-full mt-2">
                  <button
                    onClick={toggleModelsDropdown}
                    className={`flex items-center justify-s w-full py-2 border-gray-800 hover:text-blue-300 transition-colors duration-200 text-center ${
                      models.some((model) => pathname === model.href)
                        ? "text-blue-300"
                        : ""
                    }`}
                    style={{
                      animationDelay: "0s",
                      animation: isMenuOpen
                        ? "fadeInUp 0.5s ease forwards"
                        : "none",
                    }}
                  >
                    <span>Models</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className={`ml-2 h-5 w-5 transition-transform duration-300 ${
                        isModelsExpanded ? "rotate-180" : ""
                      }`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </button>

                  {/* Models dropdown content */}
                  <div
                    className={`overflow-auto transition-all duration-300 ease-in-out ${
                      isModelsExpanded
                        ? "max-h-96 opacity-100 mt-2"
                        : "max-h-0 opacity-0"
                    }`}
                  >
                    <div className="bg-[#071c3e] w-full  p-4 space-y-4">
                      {/* <h3 className="text-gray-300 text-sm border-b border-gray-700 pb-2">
                        Our Models
                      </h3> */}

                      {models.map((model, index) => (
                        <Link
                          key={model.href}
                          href={model.href}
                          className={`flex flex-col hover:bg-[#0a2550] p-2 rounded transition-colors ${
                            pathname === model.href ? "text-blue-300" : ""
                          }`}
                          onClick={toggleMenu}
                        >
                          <div
                            className={
                              pathname === model.href ? "text-blue-300" : ""
                            }
                          >
                            {model.name}
                          </div>
                          <span className="text-gray-400 text-sm">
                            {model.description}
                          </span>
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Other menu items */}
                {[
                  { href: "/pricing", label: tNav("Pricing") },
                  { href: "/about-us", label: tNav("AboutUs") },
                  {
                    href: "/contact-us?tab=custom-models",
                    label: tNav("ContactUs"),
                  },
                ].map((item, index) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`hover:text-blue-300 py-2 border-gray-800 transition-colors duration-200 w-full ${
                      pathname === item.href ? "text-blue-300" : ""
                    }`}
                    onClick={toggleMenu}
                    style={{
                      animationDelay: `${(index + 1) * 0.1}s`,
                      animation: isMenuOpen
                        ? "fadeInUp 0.5s ease forwards"
                        : "none",
                    }}
                  >
                    {item.label}
                  </Link>
                ))}
              </div>

              {/* Buttons */}
              <div className="mt-10 flex flex-col md:flex-row space-y-4 md:space-x-4  px-4">
                <Link href="/login" className="w-full " onClick={toggleMenu}>
                  <button className="rounded border border-white text-white hover:bg-white hover:text-navy-900 transition w-full py-2.5 text-base">
                    {tGlobal("Login")}
                  </button>
                </Link>
                <Link href="/sign-up" className="w-full " onClick={toggleMenu}>
                  <button className="rounded bg-white text-[#03061D] hover:bg-blue-100 transition font-medium w-full py-2.5 text-base">
                    {tGlobal("GetStartedNow")}
                  </button>
                </Link>
              </div>
            </nav>
          </div>
        </div>
      </div>

      {/* CSS for animations */}
      <style jsx global>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </header>
  );
};

export default HeaderBlack;
