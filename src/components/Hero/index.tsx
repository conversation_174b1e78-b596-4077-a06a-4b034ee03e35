"use client";

import React from "react";
import { useTranslations } from 'next-intl';
import Link from "next/link";
import { PullUpAnimation } from "../Animation";
import Image from "next/image";
import { Button } from "../ui/button";
import { Figtree } from "next/font/google";
import { ArrowRight } from "lucide-react";

const figtree = Figtree({
  subsets: ["latin"],
  weight: ["400", "700"], // Add the weights you need
  variable: "--font-figtree", // Optional variable name
});

const HeroSection = ({ font }: any) => {

  // use next-intl for i18n
  const t = useTranslations("Home.HeroSection");
  const tGlobal = useTranslations("global");

  return (
    <section className="  px-[5%] w-full ">
      <div className="max-w-[1300px] mx-auto">
        {/* <PullUpAnimation delay={1} yOffset={30}> */}
          <div className={` text-start space-y-5 lg:space-y-7 `}>
            <p className="rounded-full w-full max-w-[205px] min-h-[30px] bg-[#FFFFFF26] text-white text-center text-nowrap py-1 px-3 text-[14px] font-[500]">
              {t("subTitle")}
            </p>
            <h1
              className={`${font.className} text-3xl leading-[45px] md:leading-[61px] lg:leading-[81px] sm:text-4xl md:text-5xl lg:text-6xl xl:text-[78px] [@media(min-width:1700px)]:text-7xl font-bold text-white `}
            >
              {t("title.line1")}
              <br /> {t("title.line2")}
              <br /> {t("title.line3")}
            </h1>

            {/* Desktop buttons */}
            <div className=" md:flex md:flex-row md:space-x-4 space-y-4 justify-between items-center ">
              <p className="text-white  md:text-[20px] xl:text-[22px] [@media(min-width:1700px)]:text-4xl max-w-3xl ">
                {t("description")}
              </p>
              <Link href="/sign-up">
                <button className="flex cursor-pointer items-center justify-center gap-1 rounded-[8px] bg-white text-[#22263F] hover:bg-blue-100 transition font-semibold w-[160px] lg:w-[164px] h-[33px] lg:h-[43px] lg:text-[16px]">
                  {tGlobal("GetStartedNow")}
                  <ArrowRight className="w-[13px] h-[13px]" />
                </button>
              </Link>
              {/* <Link href="/login">
                <button className="rounded border border-white text-white hover:bg-white hover:text-navy-900 transition font-semibold w-[120px] lg:w-[138px] h-[50px] lg:h-[60px] text-lg lg:text-[20px]">
                  Read More
                </button>
              </Link> */}
            </div>

            <hr className="border-white" />
          </div>
        {/* </PullUpAnimation> */}
      </div>
    </section>
  );
};

export default HeroSection;
