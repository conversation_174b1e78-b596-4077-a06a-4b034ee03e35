import React, { useEffect, useRef } from "react";
import { createChart, CrosshairMode, LineType } from "lightweight-charts";

export default function LineReplayChart({ data }: { data?: any[] }) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);
  const seriesRef = useRef<any>(null);
  const userInteractingRef = useRef<boolean>(false);
  const height = 300;

  // Dummy data if none provided
  const dummyData = [
    { time: "2024-06-01", value: 120 },
    { time: "2024-06-02", value: 130 },
    { time: "2024-06-03", value: 125 },
    { time: "2024-06-04", value: 140 },
    { time: "2024-06-05", value: 135 },
    { time: "2024-06-06", value: 150 },
    { time: "2024-06-07", value: 145 },
    { time: "2024-06-08", value: 160 },
    { time: "2024-06-09", value: 155 },
    { time: "2024-06-10", value: 170 },
  ];
  const chartData = data && data.length > 0 ? data : dummyData;

  // Create chart only once
  useEffect(() => {
    if (!chartRef.current) return;
    const container = chartRef.current;
    const chart = createChart(container, {
      width: container.clientWidth,
      height,
      layout: { background: { color: "transparent" }, textColor: "#333" },
      grid: { vertLines: { color: "#eee" }, horzLines: { color: "#eee" } },
      crosshair: { mode: CrosshairMode.Normal },
      timeScale: { timeVisible: true, secondsVisible: false },
    });
    chartInstance.current = chart;
    const series = chart.addLineSeries({
      color: "#009dff",
      lineWidth: 2,
      lineType: LineType.Curved,
    });
    seriesRef.current = series;
    series.setData(chartData);
    chart.timeScale().fitContent();

    // Add event listeners to track user interaction
    const timeScale = chart.timeScale();

    // Track when user starts interacting (scrolling/zooming)
    timeScale.subscribeVisibleLogicalRangeChange(() => {
      userInteractingRef.current = true;

      // Reset the interaction flag after a short delay
      setTimeout(() => {
        userInteractingRef.current = false;
      }, 100);
    });

    // Resize handler
    const handleResize = () => {
      if (chartRef.current) {
        chart.resize(chartRef.current.clientWidth, height);
      }
    };
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
      chart.remove();
    };
    // Only run on mount/unmount
    // eslint-disable-next-line
  }, []);

  // Update data only when chartData changes
  useEffect(() => {
    if (seriesRef.current && chartInstance.current) {
      // Store current visible range before updating data
      const currentRange = chartInstance.current
        .timeScale()
        .getVisibleLogicalRange();

      // Update the data
      seriesRef.current.setData(chartData);

      // Don't auto-adjust view if user is currently interacting with the chart
      if (userInteractingRef.current) {
        return;
      }

      // Only fit content if this is the first data load (no previous range)
      // or if the data array is very small (likely initial load)
      if (!currentRange || chartData.length <= 2) {
        chartInstance.current.timeScale().fitContent();
      } else {
        // Preserve the user's current view by restoring the visible range
        // But ensure we don't go beyond the new data bounds
        const maxLogicalIndex = Math.max(0, chartData.length - 1);
        const preservedRange = {
          from: Math.max(0, Math.min(currentRange.from, maxLogicalIndex)),
          to: Math.max(0, Math.min(currentRange.to, maxLogicalIndex)),
        };

        // Only set the range if it's valid and user isn't interacting
        if (
          preservedRange.from < preservedRange.to &&
          !userInteractingRef.current
        ) {
          chartInstance.current
            .timeScale()
            .setVisibleLogicalRange(preservedRange);
        }
      }
    }
  }, [chartData]);

  return <div ref={chartRef} style={{ width: "100%", height }} />;
}
