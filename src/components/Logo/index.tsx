import Image from "next/image";
import Link from "next/link";
import React from "react";
import { siteConfig } from "@/static/site";
import { newLogo, logo2 } from "@/assets";
import { Orbitron } from "next/font/google";

const orbitron = Orbitron({
  subsets: ["latin"],
  weight: ["400", "700"], // Add the weights you need
  variable: "--font-orbitron", // Optional variable name
});

const Logo = ({
  textColor,
  icon,
  textSize = "24px",
  height = 50,
  width = 50,
}: any) => {
  return (
    <div className="logo">
      <Link href="/" className="inline-flex justify-start items-center gap-1.5">
        {icon == "white" ? (
          <Image
            src={newLogo}
            height={height}
            width={width}
            alt="logo"
            className="rounded-full accent-amber-300 "
          />
        ) : (
          <Image
            src="/logo-black.svg"
            height={height}
            width={width}
            alt="logo"
            className="rounded-full accent-amber-300 "
          />
        )}

        <div
          className={`justify-start text-white md:text-[34px] text-2xl font-extrabold ${orbitron.className}`}
          style={{ color: textColor, fontSize: textSize }}
        >
          {siteConfig.name}
        </div>
      </Link>
    </div>
  );
};

export default Logo;
