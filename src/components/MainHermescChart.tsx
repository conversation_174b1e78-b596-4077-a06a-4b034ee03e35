import { Button } from "@/components/ui/button";
import { useGetQueryHermes } from "@/services/api_hooks";
import { Info } from "lucide-react";
import React, { useState } from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
  Area,
  ComposedChart,
} from "recharts";

import {
  Tooltip as UITooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTranslations } from "next-intl";

interface ChartData {
  date: string;
  strategyReturn: number;
  passiveReturnToClose: number;
  passiveReturnToMax: number;
}

// Function to transform raw data for chart consumption
const transformData = (rawData: any[]): ChartData[] => {
  return rawData.map((item) => {
    const strat = item.strategy ?? 0;
    const close = item.close ?? 0;
    const max = item.max ?? 0;
    return {
      date: formatDate(item.date),
      strategyReturn: Number((strat).toFixed(2)),
      passiveReturnToClose: Number((close).toFixed(2)),
      passiveReturnToMax: Number((max).toFixed(2)),
    };
  });
};

// Helper function for date formatting
function formatDate(dateStr: string) {
  const date = new Date(dateStr);
  const year = date.getFullYear().toString().slice(-2);
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}

// Custom tick formatter to ensure first and last dates always show
const formatXAxisTick = (value: string, index: number, dataLength: number) => {
  if (dataLength <= 4) {
    // if there are 4 or fewer points, show all *except* the last
    return index === dataLength - 1 ? "" : value;
  }

  // never show the last tick
  if (index === dataLength - 1) {
    return "";
  }

  // always show the first tick
  if (index === 0) {
    return value;
  }

  // calculate roughly 3 evenly spaced middle ticks
  const interval = Math.floor((dataLength - 1) / 3);
  if (index % interval === 0) {
    return value;
  }

  // hide everything else
  return "";
};

// Custom legend component
const CustomLegend: React.FC = () => {
  const t = useTranslations("Home.StatisticsSection");

  const legendItems = [
    { name: t("legendCharts.rawStrategyReturn"), color: "#3b82f6" },
    { name: t("legendCharts.passiveReturnToClose"), color: "#686767" },
    { name: t("legendCharts.activeReturnToMax"), color: "#eab308" },
    { name: t("legendCharts.activeAlphaZone"), color: "#f3d372c5" },
  ];

  return (
    <div className="flex justify-center flex-wrap gap-6 mb-6">
      {legendItems.map((item, index) => (
        <div key={index} className="flex items-center gap-2">
          <div className="w-5 h-2" style={{ backgroundColor: item.color }} />
          <span className="text-xs text-gray-600 flex items-center gap-1">
            {item.name}{" "}
            {item.name === t("legendCharts.activeAlphaZone") && (
              <TooltipProvider delayDuration={100}>
                <UITooltip>
                  <TooltipTrigger asChild className="cursor-pointer">
                    <Info size={13} />
                  </TooltipTrigger>
                  <TooltipContent
                    className="max-w-[300px] gap-3 flex flex-col p-3 text-xs text-start font-[400] leading-5 bg-white text-black"
                    side="top"
                  >
                    <span>{t("legendCharts.activeAlphaZoneTooltip")}</span>
                  </TooltipContent>
                </UITooltip>
              </TooltipProvider>
            )}
          </span>
        </div>
      ))}
    </div>
  );
};

// Custom tooltip component
const CustomTooltip: React.FC<TooltipProps<any, any>> = ({
  active,
  payload,
  label,
}) => {
  if (active && payload && payload.length) {
    // Filter out the "Active return to max" entry
    const filtered = payload.filter(
      (entry) => entry.name !== "active alpha zone"
    );

    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm font-medium text-gray-700 mb-2">{`Date: ${label}`}</p>
        {filtered.map((entry, index) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {`${entry.name}: ${entry.value}`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const MainHermescChart = ({
  data,
  isLoading,
  isFetching,
  setIsPaused,
}: any) => {
  const t = useTranslations("Home.StatisticsSection");

  const chartData = transformData(data || []);

  const calculateGridBounds = (data: ChartData[]) => {
    if (!data || data.length === 0)
      return { min: 90, max: 150, ticks: [90, 100, 110, 120, 130, 140, 150] };

    const allValues = data.flatMap((item) => [
      item.strategyReturn,
      item.passiveReturnToClose,
      item.passiveReturnToMax,
    ]);

    const minValue = Math.min(...allValues);
    const maxValue = Math.max(...allValues);

    const gridMin = Math.floor(minValue / 10) * 10;
    const gridMax = Math.ceil(maxValue / 10) * 10;

    const ticks: number[] = [];
    for (let i = gridMin; i <= gridMax; i += 10) {
      ticks.push(i);
    }

    return {
      min: minValue,
      max: maxValue,
      ticks,
    };
  };

  const gridBounds = calculateGridBounds(chartData);

  return (
    <div
      className="w-full flex flex-col justify-between pb-6 h-full"
      onMouseEnter={() => setIsPaused?.(true)}
      onMouseLeave={() => setIsPaused?.(false)}
    >
      <div className="text-[18px] leading-[32px] mb-2 text-center">
        <strong>{t("hermescChartText")}</strong>
      </div>
      <CustomLegend />
      <div className="flex flex-col h-[300px] md:h-[310px]">
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart
            data={chartData}
            margin={{
              top: 0,
              right: 30,
              left: -20,
              bottom: 20,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
            <XAxis
              dataKey="date"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#666", dy: 15 }}
              tickFormatter={(value, index) =>
                formatXAxisTick(value, index, chartData.length)
              }
              interval={0}
            />
            <YAxis
              domain={[gridBounds.min, gridBounds.max]}
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#666" }}
              ticks={gridBounds.ticks}
            />
            <Tooltip content={<CustomTooltip />} />

            {/* Area fill between the two lines */}
            <Area
              type="linear"
              dataKey="passiveReturnToMax"
              name="active alpha zone"
              stroke="none"
              fill="#f3d372c5"
              fillOpacity={0.6}
            />
            <Area
              type="linear"
              dataKey="passiveReturnToClose"
              stroke="none"
              fill="white"
              fillOpacity={1}
            />

            <Line
              type="linear"
              dataKey="strategyReturn"
              stroke="#3b82f6"
              strokeWidth={2}
              dot={false}
              name={t("legendCharts.rawStrategyReturn")}
              activeDot={{
                r: 4,
                stroke: "#3b82f6",
                strokeWidth: 2,
                fill: "#fff",
              }}
            />
            <Line
              type="linear"
              dataKey="passiveReturnToClose"
              stroke="#686767"
              strokeWidth={2}
              dot={false}
              name={t("legendCharts.passiveReturnToClose")}
              activeDot={{
                r: 4,
                stroke: "#686767",
                strokeWidth: 2,
                fill: "#fff",
              }}
            />
            <Line
              type="linear"
              dataKey="passiveReturnToMax"
              stroke="#eab308"
              strokeWidth={2}
              dot={false}
              name={t("legendCharts.activeReturnToMax")}
              activeDot={{
                r: 4,
                stroke: "#eab308",
                strokeWidth: 2,
                fill: "#fff",
              }}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default MainHermescChart;
