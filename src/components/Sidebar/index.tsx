// @ts-nocheckl

// File: components/sidebar/sidebar.tsx
"use client";

import { useEffect, useMemo, useState } from "react";
import { useTranslations } from "next-intl";
import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import {
  Home,
  CreditCard,
  Settings,
  ChevronLeft,
  ChevronRight,
  Lock,
  LayoutGrid,
  PanelLeft,
  LogOut,
  ChevronUp,
  ChevronDown,
  Plus,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { Orbitron } from "next/font/google";
import { mainNavItems, modelNavItems } from "@/utils/data";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { logout, useGetQuery } from "@/services/api_hooks";
import useLocalStorage from "@/hooks/use-local-storage";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import OrganizationModal from "../modals/AddOrganization";
import { freddieFrontendUrl } from "@/config/baseUrl";
import Cookies from "js-cookie";

const orbitron = Orbitron({
  subsets: ["latin"],
  weight: ["400", "700"], // Add the weights you need
  variable: "--font-orbitron", // Optional variable name
});

interface SidebarProps {
  className?: string;
}

// Translation of Navigation
interface NavTranslations {
  [key: string]: string;
}


export function Sidebar({ className }: SidebarProps) {
  // use next-intl for i18n
  const t = useTranslations("DashboardHome.DashboardHeader");
  const tDashboardNav = useTranslations("DashboardNav");

  // create Nav translations object
  // Every time Nav is updated, it must be updated here
  const DashboardNavTranslations: NavTranslations = {};
  ["Home", "Credits", "Settings"].map((key) => {
    DashboardNavTranslations[key] = tDashboardNav(`${key}`);
    return;
  });

  const [collapsed, setCollapsed] = useState(false);
  const [open, setOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const [userCredentials, setName] = useLocalStorage("authCred", "");
  const [processedRecentlyUsed, setProcessedRecentlyUsed] = useState<any[]>([]);
  const [userCred, setUserCred] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // New: localStorage-cached data for instant load
  const [cachedProfile, setCachedProfile] = useState<any>(null);
  const [cachedRecentlyUsed, setCachedRecentlyUsed] = useState<any[]>([]);
  const [cacheLoaded, setCacheLoaded] = useState(false);

  const { data: recentlyUsedData, isLoading } = useGetQuery(
    "/models/recent",
    ["get-recently-used"],
    {
      onError() {
        toast.error(t("toast.notLoadProfile"));
      },
    }
  );

  const {
    data: profile,
    isLoading: profoileLoading,
    isRefetching,
  } = useGetQuery("/auth/profile", ["profile"], {
    refetchOnWindowFocus: false,
    //   onError() {
    //     toast.error("Could not load your profile");
    //   },
  });

  const accessToken = Cookies.get("accessToken");
  const refreshToken = Cookies.get("refreshToken");

  // Load cached profile and recently used models from localStorage on mount
  useEffect(() => {
    let cachedProf = null;
    let cachedRecent = [];
    try {
      const profStr = localStorage.getItem("profile");
      if (profStr) cachedProf = JSON.parse(profStr);
    } catch {}
    try {
      const recentStr = localStorage.getItem("recentlyUsedModels");
      if (recentStr) cachedRecent = JSON.parse(recentStr);
    } catch {}
    setCachedProfile(cachedProf);
    setCachedRecentlyUsed(Array.isArray(cachedRecent) ? cachedRecent : []);
    setCacheLoaded(true);
  }, []);

  // Cache profile and recently used data in localStorage when loaded
  useEffect(() => {
    if (profile && profile.profile) {
      try {
        localStorage.setItem("profile", JSON.stringify(profile));
      } catch (e) {
        // ignore quota errors
      }
    }
  }, [profile]);

  useEffect(() => {
    if (recentlyUsedData && Array.isArray(recentlyUsedData)) {
      try {
        localStorage.setItem("recentlyUsedModels", JSON.stringify(recentlyUsedData));
      } catch (e) {
        // ignore quota errors
      }
    }
  }, [recentlyUsedData]);

  const modelCards = useMemo(
    () => [
      {
        id: "market-monitor",
        tab_label: t("cards.HermesX.tabLabel"),
        label: t("cards.HermesX.label"),
        title: t("cards.HermesX.title"),
        img: "/hermes_rec.svg",
        link: "/dashboard/hermes",
      },
      {
        id: "trader",
        tab_label: t("cards.HermesC.tabLabel"),
        label: t("cards.HermesC.label"),
        title: t("cards.HermesC.title"),
        img: "/hermes_rec.svg",
        link: "/dashboard/hermes-c",
      },

      {
        id: "accountant-ai",
        tab_label: t("cards.Luca.tabLabel"),
        label: t("cards.Luca.label"),
        title: t("cards.Luca.title"),
        img: "/luca_rec.svg",
        link: accessToken
          ? `https://luca.ai-wk.com/?token=${accessToken}&refresh_token=${refreshToken}`
          : "/dashboard/home",
      },

      {
        id: "hr",
        tab_label: t("cards.Freddie.tabLabel"),
        label: t("cards.Freddie.label"),
        title: t("cards.Freddie.title"),
        img: "/freddie_rec.svg",
        link: accessToken
          ? `${freddieFrontendUrl}/?token=${accessToken}&refresh_token=${refreshToken}`
          : "/dashboard/home",
      },
      {
        id: "equity-analyst",
        tab_label: t("cards.Orion.tabLabel"),
        label: t("cards.Orion.label"),
        title: t("cards.Orion.title"),
        img: "/orion_rec.svg",
        link: "/dashboard/orion",
        isLocked: false,
      },
      {
        id: "olympus",
        tab_label: t("cards.Olympus.tabLabel"),
        label: t("cards.Olympus.label"),
        title: t("cards.Olympus.title"),
        img: "/olympus_rec.svg",
        link: "/dashboard/olympus",
        isLocked: true,
      },
      {
        id: "yumi-sandbox",
        tab_label: t("cards.Yumi.tabLabel"),
        label: t("cards.Yumi.label"),
        title: t("cards.Yumi.title"),
        img: "/yumi_rec.svg",
        link: "/dashboard/yumi/setup",
        isLocked: true,
      },
    ],
    [accessToken, refreshToken, t]
  );

  // Helper function to render the correct icon
  const renderIconRecentlyUsed = (iconName: string, size: number = 18) => {
    switch (iconName) {
      case "Luca":
        return (
          <Image
            src="/luca_thumbnail.svg"
            height={24}
            width={20}
            alt="logo"
            className="h-full w-full"
          />
        );
      case "Hermes X":
        return (
          <Image
            src="/hermes_thumbnail.svg"
            height={24}
            width={20}
            alt="logo"
            className="h-full w-full"
          />
        );
      case "Hermes C":
        return (
          <Image
            src="/hermes_thumbnail.svg"
            height={24}
            width={20}
            alt="logo"
            className="h-full w-full"
          />
        );
      case "Orion":
        return (
          <Image
            src="/orion_thumbnail.svg"
            height={24}
            width={20}
            alt="logo"
            className="h-full w-full"
          />
        );
      case "Freddie":
        return (
          <Image
            src="/freddie_thumbnail.png"
            height={24}
            width={20}
            alt="logo"
            className="h-full w-full"
          />
        );
      case "Yumi Sandbox":
        return (
          <Image
            src="/yumi.png"
            height={24}
            width={20}
            alt="logo"
            className="h-full w-full"
          />
        );
      case "Olympus":
        return (
          <Image
            src="/olympus_thumbnail.png"
            height={24}
            width={20}
            alt="logo"
            className="h-full w-full"
          />
        );
      case "model":
      default:
        return <div className="w-5 h-5 bg-gray-300 rounded-sm"></div>;
    }
  };

  const renderIcon = (iconName: string, size: number = 18) => {
    switch (iconName) {
      case "home":
        return <LayoutGrid size={size} />;
      case "credit-card":
        return <CreditCard size={size} />;
      case "settings":
        return <Settings size={size} />;
      case "model":
      default:
        return <div className="w-5 h-5 bg-gray-300 rounded-sm"></div>;
    }
  };

  const handleLogout = () => {
    logout(); // clear tokens
    setOpen(false); // close popover
    router.push("/login"); // go to login
  };

  const getInitials = () => {
    // First try the new fields
    const fullName =
      profile?.profile?.full_name || profile?.profile?.name || "";

    // If we have a full name, extract initials
    if (fullName && fullName.trim() !== "") {
      const nameParts = fullName.trim().split(" ");
      if (nameParts.length >= 2) {
        return (
          nameParts[0][0] + nameParts[nameParts.length - 1][0]
        ).toUpperCase();
      } else if (nameParts.length === 1) {
        return nameParts[0][0].toUpperCase();
      }
    }

    // Fall back to old fields if new ones aren't available
    const firstName = profile?.profile.firstname || "";
    const lastName = profile?.profile?.lastname || "";

    const firstInitial = firstName.charAt(0).toUpperCase();
    const lastInitial = lastName.charAt(0).toUpperCase();

    return firstInitial + lastInitial || "?"; // Default to "?" if no initials available
  };

  const getDisplayName = () => {
    // Prefer live profile, then cached profile
    const prof = profile?.profile || cachedProfile?.profile || {};

    // Try the new fields first
    const fullName = prof.full_name || prof.name;
    if (fullName && fullName.trim() !== "") {
      return fullName;
    }

    // Fall back to old fields
    const firstName = prof.firstname || "";
    const lastName = prof.lastname || "";
    if (firstName || lastName) {
      return `${firstName} ${lastName}`.trim();
    }

    return "User";
  };

  // Helper to merge API model data with static model card info
  const mergeModels = (apiModels: any[]) => {
    if (!apiModels || !Array.isArray(apiModels)) return [];
    return apiModels.map((apiModel: any) => {
      const staticModel = modelCards?.find(
        (staticModel) => staticModel.id === apiModel.slug
      );
      return {
        ...apiModel,
        img: staticModel?.img || "/default.svg",
        link: staticModel?.link || `/dashboard/${apiModel?.slug}`,
        tab_label: staticModel?.tab_label || apiModel?.name,
        label: staticModel?.label || apiModel.description,
        isLocked: apiModel?.unlocked === false,
      };
    });
  };

  // Show merged cached data instantly, then update with fresh API data
  useEffect(() => {
    if (cacheLoaded && cachedRecentlyUsed && cachedRecentlyUsed.length > 0) {
      setProcessedRecentlyUsed(mergeModels(cachedRecentlyUsed));
    }
  }, [cacheLoaded, modelCards]);

  useEffect(() => {
    if (recentlyUsedData && recentlyUsedData.length > 0) {
      setProcessedRecentlyUsed(mergeModels(recentlyUsedData));
    }
  }, [recentlyUsedData, modelCards]);

  return (
    <div
      className={cn(
        "bg-white h-screen border-[#f7f7f7] overflow-x-hidden justify-between overflow-y-auto sticky top-0 hidden md:flex flex-col duration-200 ease-in-out w-72 border-r transition-all duration-300",
        collapsed ? "w-16" : "w-[230px]",
        className
      )}
    >
      <div>
        <div className="flex items-center justify-between py-4 pl-7 pr-4">
          <div className="flex items-center gap-1">
            {!collapsed && (
              <Image src="/logo-black.svg" height={50} width={50} alt="logo" />
            )}
            {!collapsed && (
              <span
                className={`${orbitron.className} text-[24px] text-nowrap font-[800]`}
              >
                ai-wk
              </span>
            )}
          </div>
          <PanelLeft
            className="cursor-pointer"
            onClick={() => setCollapsed(!collapsed)}
            size={18}
          />
        </div>

        <nav className=" py-2 px-4 space-y-1">
          {/* Main Navigation Items */}
          {mainNavItems?.map((item) => (
            <NavItem
              key={item.href}
              href={item.href}
              icon={renderIcon(item.icon)}
              text={DashboardNavTranslations[item.name] ?? item.name}
              collapsed={collapsed}
              isActive={pathname === `/dashboard/${item.href.toLowerCase()}`}
              isLocked={item.isLocked}
            />
          ))}

          {/* Models Section Header */}
          {!collapsed && (
            <div className="pt-4 pb-2">
              <p className="font-medium text-black font-size/text-sm mb-2 px-3">
                {t("MyModels")}
              </p>
            </div>
          )}

          {/* Model Navigation Items with Skeleton Loader */}
          {isLoading && (!cacheLoaded || !cachedRecentlyUsed || cachedRecentlyUsed.length === 0)
            ? // Skeleton loader for model items (only if no cached data)
              Array(3)
                .fill(0)
                .map((_, index) => (
                  <div
                    key={`skeleton-model-${index}`}
                    className="flex items-center px-3 py-2 gap-3"
                  >
                    <Skeleton className="h-5 w-5 rounded-sm" />
                    {!collapsed && <Skeleton className="h-4 w-24" />}
                  </div>
                ))
            : // Actual model items
              processedRecentlyUsed?.map((item) => (
                <NavItemRecentlyUsed
                  key={item.slug}
                  href={item.link || "/dashboard/home"}
                  icon={renderIconRecentlyUsed(item.name)}
                  text={item.name}
                  collapsed={collapsed}
                  isActive={
                    pathname === `/dashboard/${item.name.toLowerCase()}`
                  }
                  isLocked={item.isLocked}
                  isDirectLink={
                    item.slug === "accountant-a" && userCred?.token?.accessToken
                  }
                />
              ))}
        </nav>
      </div>

      <div className=" p-4  ">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <button className="flex items-center py-2 gap-2 w-full cursor-pointer hover:bg-gray-100 rounded-md">
              {(profoileLoading || isRefetching) && !cachedProfile ? (
                // Skeleton loader for user profile
                <>
                  <Skeleton className="w-8 h-8 rounded-full" />
                  {!collapsed && <Skeleton className="h-4 w-24 flex-1" />}
                </>
              ) : (
                <>
                  {(profile?.profile?.avatar_url || cachedProfile?.profile?.avatar_url) ? (
                    <div className="w-8 h-8 relative rounded-full overflow-hidden">
                      <Image
                        src={profile?.profile?.avatar_url || cachedProfile?.profile?.avatar_url || ""}
                        alt="Profile avatar"
                        fill
                        sizes="32px"
                        className="object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center text-white">
                      {getInitials()}
                    </div>
                  )}
                  {!collapsed && (
                    <>
                      <div className="flex-1 min-w-0 text-left">
                        <p className="text-sm font-medium truncate text-black">
                          {getDisplayName()}
                        </p>
                      </div>
                      {open ? (
                        <ChevronUp size={20} />
                      ) : (
                        <ChevronDown size={20} />
                      )}
                    </>
                  )}
                </>
              )}
            </button>
          </PopoverTrigger>
          <PopoverContent
            className={`w-50 p-0 ${collapsed ? "mb-2" : ""}`}
            side={collapsed ? "right" : "top"}
            align={collapsed ? "start" : "end"}
          >
            <div className="grid gap-2">
              <div className="px-3 py-2  rounded ">
                <p className="text-xs text-gray-400">{t("OtherBusinesses")}</p>
              </div>
              {/* <div
                className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 rounded cursor-pointer"
                onClick={() => setIsModalOpen(true)}
              >
                <p className="text-xs">New Organization</p>
                <Plus size={17} />
              </div> */}

              <hr />
              <div
                className="px-3 py-2 hover:bg-gray-50 rounded cursor-pointer flex items-center justify-between gap-2 text-red-500"
                onClick={handleLogout}
              >
                <p className="text-sm">{t("LogOut")}</p>
                <LogOut size={16} />
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      <OrganizationModal open={isModalOpen} onOpenChange={setIsModalOpen} />
    </div>
  );
}

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  text: string;
  collapsed: boolean;
  isActive: boolean;
  isLocked?: boolean;
}

function NavItem({
  href,
  icon,
  text,
  collapsed,
  isActive,
  isLocked,
}: NavItemProps) {
  return (
    <Link
      href={`/dashboard/${href}` || "#"}
      className={cn(
        "flex items-center px-3 py-2 text-sm rounded-md",
        isActive
          ? "bg-[#FAFAFA] text-[#03061D]"
          : "text-[#A7A7A7] hover:bg-gray-100 hover:text-gray-900",
        isLocked && !collapsed ? "justify-between" : "gap-3"
      )}
    >
      <div className="flex items-center gap-3">
        <span className="shrink-0">{icon}</span>
        {!collapsed && <span>{text}</span>}
      </div>

      {!collapsed && isLocked && (
        <span className="text-[#A7A7A7]">
          <Image
            src="/padlock-gray.svg"
            alt="lock icon"
            height={15}
            width={15}
          />
        </span>
      )}
    </Link>
  );
}

// Update NavItemRecentlyUsed to handle direct links
interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  text: string;
  collapsed: boolean;
  isActive: boolean;
  isLocked?: boolean;
  isDirectLink?: boolean; // New prop
}

function NavItemRecentlyUsed({
  href,
  icon,
  text,
  collapsed,
  isActive,
  isLocked,
  isDirectLink,
}: NavItemProps) {
  // Use Link for internal navigation and 'a' for external links
  const LinkComponent = isDirectLink ? "a" : Link;

  return (
    <LinkComponent
      href={href}
      className={cn(
        "flex items-center px-3 py-2 text-sm rounded-md",
        isActive
          ? "bg-[#FAFAFA] text-[#03061D]"
          : "text-[#A7A7A7] hover:bg-gray-100 hover:text-gray-900",
        isLocked && !collapsed ? "justify-between" : "gap-3"
      )}
      // Add target="_blank" for external links
      {...(isDirectLink
        ? { target: "_blank", rel: "noopener noreferrer" }
        : {})}
    >
      <div className="flex items-center gap-3">
        <div className="shrink-0 rounded-full h-[25px] w-[25px] overflow-hidden">
          {icon}
        </div>
        {!collapsed && <span>{text}</span>}
      </div>

      {!collapsed && isLocked && (
        <span className="text-[#A7A7A7]">
          <Image
            src="/padlock-gray.svg"
            alt="lock icon"
            height={15}
            width={15}
          />
        </span>
      )}
    </LinkComponent>
  );
}
