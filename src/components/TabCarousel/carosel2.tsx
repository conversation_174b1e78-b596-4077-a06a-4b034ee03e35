"use client";

import { useEffect, useRef, useState, useMemo } from "react";
import { useTranslations } from 'next-intl';
import "swiper/css";
import "swiper/css/effect-coverflow";
import { A11y, Controller, Mousewheel, Autoplay } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import { Swiper as SwiperType } from "swiper/types";
import Image from "next/image";
import Link from "next/link";
import useMediaQuery from "@/hooks/useMediaQuery";

export default function CarouselTwo() {
  
  // use next-intl for i18n
  const t = useTranslations("Home");
  const tGlobal = useTranslations("global");
  const tTabs = useTranslations("Home.OurAIModels.tabs");

  const [active, setActive] = useState(0);
  const [swiper, setSwiper] = useState<SwiperType | null>(null);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [changeSource, setChangeSource] = useState<"none" | "click" | "slide">(
    "none"
  );
  
  const isMobile = useMediaQuery("(max-width: 767px)");
  const isTablet = useMediaQuery("(min-width: 768px) and (max-width: 1024px)");
  const large = useMediaQuery("(min-width: 1280px) and (max-width: 1400px)");
  const isXtraLarge = useMediaQuery("((min-width: 1600px)");
  const tabsRef = useRef<HTMLDivElement | null>(null);
  const tabRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});

  // Initialize active index
  useEffect(() => {
    const initialIndex = tabs.findIndex((tab) => tab.id === "stock");
    if (initialIndex !== -1) {
      setActive(initialIndex);
    }
  }, []);

  // Reset animation flags after animation completes
  useEffect(() => {
    if (changeSource !== "none") {
      const timer = setTimeout(() => {
        setChangeSource("none");
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [changeSource]);

  const handleTabClick = (index: number) => {
    if (index === active) return;

    setActive(index);
    setChangeSource("click");

    if (swiper) {
      swiper.slideToLoop(index, 1000);
    }
  };

  const tabs = [
      {
        id: "olympus",
        tab_label: tTabs("Olympus.tabLabel"),
        label: tTabs("Olympus.label"),
        title: tTabs("Olympus.title"),
        clip: "/olympus.svg",
        link: "/olympus",
      },
      {
        id: "market-monitor",
        tab_label: tTabs("MarketMonitor.tabLabel"),
        label: tTabs("MarketMonitor.label"),
        title: tTabs("MarketMonitor.title"),
        clip: "/hermes.svg",
        link: "/hermes",
      },
      {
        id: "equity-analyst",
        tab_label: tTabs("EquityAnalyst.tabLabel"),
        label: tTabs("EquityAnalyst.label"),
        title: tTabs("EquityAnalyst.title"),
        clip: "/orion.svg",
        link: "/orion",
      },

      {
        id: "accountant-ai",
        tab_label: tTabs("Accountant.tabLabel"),
        label: tTabs("Accountant.label"),
        title: tTabs("Accountant.title"),
        clip: "/luca.svg",
        link: "/luca",
      },

      {
        id: "hr",
        tab_label: tTabs("Recruiter.tabLabel"),
        label: tTabs("Recruiter.label"),
        title: tTabs("Recruiter.title"),
        clip: "/freddie.svg",
        link: "/freddie",
      },
      {
        id: "customer-service",
        tab_label: tTabs("CustomerService.tabLabel"),
        label: tTabs("CustomerService.label"),
        title: tTabs("CustomerService.title"),
        clip: "/Yumi front.png",
        link: "/yumi",
      }
    ];

  // Calculate responsive values based on viewport size
  const getSlidesPerView = () => {
    if (isMobile) return 1.3; // Slightly more than 1 to show a peek of the next slide
    if (isTablet) return 1.3;
    if (large) return 1.3;
    // if (isXtraLarge) return 1.5;
    return 1.4;
  };

  return (
    <div
      className="w-full  bg-white space-y-5"
      // style={{ minHeight: "50vh" }}
    >
      {/* Custom styles for transitions */}
      <style jsx global>{`
        .swiper-wrapper {
          transition-timing-function: cubic-bezier(
            0.25,
            0.1,
            0.25,
            1
          ) !important;
        }
        .swiper-slide {
          transition: transform 1.2s cubic-bezier(0.19, 1, 0.22, 1) !important; /* Ease-out cubic */
          touch-action: pan-y !important;
          will-change: transform, opacity !important;
        }
        .swiper-slide-active {
          z-index: 10 !important;
        }

        /* Tab highlight with ease-out transition */
        .tab-highlight {
          transition: transform 0.5s cubic-bezier(0, 0, 0.2, 1),
            width 0.5s cubic-bezier(0, 0, 0.2, 1) !important; /* Ease-out */
        }
      `}</style>

      {/* Tab Navigation for Desktop */}
      {!isMobile && (
        <div className="flex justify-center relative min-h-[34px] ">
          <div className="flex space-x-2 h-full rounded-[12px] relative border border-[#E4E4E7] py-1 px-2 items-center">
            {tabs.map((tab, i) => (
              <button
                key={tab.id}
                onMouseEnter={() => swiper?.autoplay.stop()}
                onMouseLeave={() => swiper?.autoplay.start()}
                onClick={() => handleTabClick(i)}
                className={`
          relative px-3 py-1 rounded-[8px] text-[16px] font-[400] 
          transition-all duration-200 cursor-pointer
          
          ${
            active === i
              ? "text-white bg-[#22263F] shadow-[0_4px_6px_-1px_rgba(3,33,127,0.3)]"
              : "text-[#A7A7A7] hover:text-gray-900 hover:bg-gray-300"
          }`}
              >
                {tab.tab_label}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Mobile Tab Display */}
      {/* {isMobile && (
        <div className="flex justify-center mb-6">
          <div className="px-4 py-2 rounded-[12px] border border-[#E4E4E7] bg-[#03217F] text-white text-base font-semibold">
            {tabs[active]?.label || ""}
          </div>
        </div>
      )} */}

      {/* Swiper Carousel */}
      <div className="flex justify-center items-center w-full">
        <div className="max-w-[1300px] mx-auto w-full overflow-visible relative">
          <Swiper
            grabCursor={true}
            centeredSlides={true}
            initialSlide={active}
            speed={1000}
            loop={true}
            mousewheel={{
              forceToAxis: true,
              sensitivity: 1,
            }}
            threshold={10}
            resistance={false}
            resistanceRatio={0}
            longSwipesRatio={0.2}
            followFinger={true}
            allowTouchMove={true}
            simulateTouch={true}
            touchRatio={1.5}
            autoplay={{
              delay: 2000, // Time between slides (ms)
              disableOnInteraction: false, // Continue autoplay after user interaction
              pauseOnMouseEnter: true,
            }}
            modules={[Controller, Mousewheel, A11y, Autoplay]}
            onSwiper={setSwiper}
            onSlideChange={(swiper) => {
              // Only update if slide changed by user action (not programmatically from tab click)
              if (changeSource !== "click") {
                setActive(swiper.realIndex);
                setChangeSource("slide");
              }
            }}
            slidesPerView={getSlidesPerView()}
            spaceBetween={25}
          >
            {tabs.map((tab, index) => (
              <SwiperSlide
                key={tab.id}
                className="pt-3 flex justify-center"
                //   style={{ width: "900px" }}
                onMouseEnter={() => swiper?.autoplay.stop()}
                onMouseLeave={() => swiper?.autoplay.start()}
              >
                <div
                  className={`${
                    active === index ? "" : "opacity-[0.5]"
                  } transition-all duration-500 ease-in-out`}
                >
                  <div
                    className={`bg-[#F6FAF3] gap-3 w-full h-[400px] sm:h-full transition-transform duration-500 ease-in-out flex justify-between flex-col md:flex-row pl-4  md:pl-4 lg:pl-7  pt-4   md:pb-0`}
                  >
                    <div className=" flex-1 text-left space-y-3 flex flex-col justify-center">
                      <h3 className="md:px-0 md:mt-0 text-[#828282] font-semibold text-[14px] md:text-[17px] lg:text-[28px]">
                        {tab.label}
                      </h3>
                      <p className="text-[18px] md:text-[25px] lg:text-[36px] font-bold text-[#22263F] leading-tight">
                        {tab.title}
                      </p>
                      <Link href={tab.link}>
                        <button
                          aria-label="Learn More"
                          className="cursor-pointer w-[94px] min-h-[28px] text-[12px] md:text-[16px] md:w-[113px] md:min-h-[35px] rounded-[3.9px] bg-[#22263F] text-white font-medium"
                        >
                          {tGlobal("LearnMore")}
                        </button>
                      </Link>
                    </div>

                    {/* Updated image container */}
                    <div className="landing-pic md:flex-[1.2] lg:flex-1 relative  h-[360px] md:h-[320px] lg:h-[450px] rounded-tl-lg sm:rounded-tl-xl md:rounded-tl-2xl rounded-br-lg sm:rounded-br-xl md:rounded-br-2xl overflow-hidden">
                      <Image
                        src={tab.clip}
                        alt=""
                        fill
                        className="object-cover"
                        priority
                      />
                    </div>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </div>
  );
}
