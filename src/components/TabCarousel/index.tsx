"use client";

import { useEffect, useRef, useState, useMemo } from "react";
import "swiper/css";
import "swiper/css/effect-coverflow";
import {
  A11y,
  Controller,
  Thumbs,
  Pagination,
  EffectCoverflow,
  Mousewheel,
} from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import { Swiper as SwiperType } from "swiper/types";
import Image from "next/image";
import Link from "next/link";
import useMediaQuery from "@/hooks/useMediaQuery";
import { douglas, hermes, olympus, orion, luca } from "@/assets/models";

export default function TabsCarousel() {
  const [active, setActive] = useState(0);
  const [swiper, setSwiper] = useState<SwiperType | null>(null);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [changeSource, setChangeSource] = useState<"none" | "click" | "slide">(
    "none"
  );
  const isMobile = useMediaQuery("(max-width: 767px)");
  const isTablet = useMediaQuery("(min-width: 768px) and (max-width: 1024px)");
  const large = useMediaQuery("(min-width: 1280px) and (max-width: 1400px)");
  const isXtraLarge = useMediaQuery("((min-width: 1600px)");
  const tabsRef = useRef<HTMLDivElement | null>(null);
  const tabRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});

  // Position states for tab highlighting
  const [activeTabPosition, setActiveTabPosition] = useState({
    x: 0,
    width: 0,
  });
  const [hoverTabPosition, setHoverTabPosition] = useState({
    x: 0,
    width: 0,
  });

  // Calculate and update positions when tabs are hovered or active
  useEffect(() => {
    const updatePositions = () => {
      if (!tabsRef.current) return;

      const containerLeft = tabsRef.current.getBoundingClientRect().left;

      // Update active tab position
      const activeTab = tabRefs.current[active];
      if (activeTab) {
        const { left, width } = activeTab.getBoundingClientRect();
        setActiveTabPosition({
          x: left - containerLeft,
          width,
        });
      }

      // Update hovered tab position if there is one
      if (hoveredIndex !== null && hoveredIndex !== active) {
        const hoverTab = tabRefs.current[hoveredIndex];
        if (hoverTab) {
          const { left, width } = hoverTab.getBoundingClientRect();
          setHoverTabPosition({
            x: left - containerLeft,
            width,
          });
        }
      }
    };

    updatePositions();
    window.addEventListener("resize", updatePositions);
    return () => window.removeEventListener("resize", updatePositions);
  }, [active, hoveredIndex]);

  // Initialize active index
  useEffect(() => {
    const initialIndex = tabs.findIndex((tab) => tab.id === "stock");
    if (initialIndex !== -1) {
      setActive(initialIndex);
    }
  }, []);

  // Reset animation flags after animation completes
  useEffect(() => {
    if (changeSource !== "none") {
      const timer = setTimeout(() => {
        setChangeSource("none");
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [changeSource]);

  const handleTabClick = (index: number) => {
    if (index === active) return;

    setActive(index);
    setChangeSource("click");

    if (swiper) {
      swiper.slideToLoop(index, 1000);
    }
  };

  const tabs = useMemo(
    () => [
      {
        id: "olympus",
        tab_label: "Market Simulation",
        label: "Olympus",
        title: "Forecasting trends and testing strategies",
        clip: "/olympus.svg",
        link: "/olympus",
      },
      {
        id: "market-monitor",
        tab_label: "Market Monitor",
        label: "Hermes",
        title: "Tracking financial market shifts 24/7",
        clip: "/hermes.svg",
        link: "/hermes",
      },
      {
        id: "equity-analyst",
        tab_label: "Equity Analyst",
        label: "Orion",
        title: "Delivering deep insights for smarter investments",
        clip: "/orion.svg",
        link: "/orion",
      },

      {
        id: "accountant-ai",
        tab_label: "Accountant",
        label: "Luca",
        title: "Automating bookkeeping with precision",
        clip: "/luca.svg",
        link: "/luca",
      },
      {
        id: "presenter",
        tab_label: "Presenter",
        label: "Doug",
        title: "Turning ideas into polished decks.",
        clip: "/doug.svg",
        link: "/doug",
      },
    ],
    []
  );

  // Calculate responsive values based on viewport size
  const getSlidesPerView = () => {
    if (isMobile) return 1.4; // Slightly more than 1 to show a peek of the next slide
    if (isTablet) return 1.3;
    if (large) return 1.3;
    if (isXtraLarge) return 1.7;
    return 1.4;
  };

  const getSpaceBetween = () => {
    // Use viewport width (vw) based calculation
    // This ensures spacing scales proportionally with the viewport
    const baseSpacing = 5; // Base percentage of viewport width

    // Calculate the space in vw units (5% of viewport width)
    // Constrain the space to reasonable min and max pixel values
    // if (typeof window !== "undefined") {
    //   const viewportWidth = window.innerWidth;
    //   const spacingInVw = viewportWidth * (baseSpacing / 100);

    //   // Minimum spacing of 20px, maximum of 100px
    //   return Math.max(40, Math.min(spacingInVw, 100));
    // }

    // Fallback for SSR
    return 30;
  };

  // slidesPerView={isMobile ? 1.2 : 2}
  // spaceBetween={isMobile ? 3 : 200}

  return (
    <div
      className="w-full overflow-hidden bg-white space-y-5"
      // style={{ minHeight: "50vh" }}
    >
      {/* Custom styles for transitions */}
      <style jsx global>{`
        .swiper-wrapper {
          transition-timing-function: cubic-bezier(
            0.25,
            0.1,
            0.25,
            1
          ) !important;
        }
        .swiper-slide {
          transition: transform 1.2s cubic-bezier(0.19, 1, 0.22, 1) !important; /* Ease-out cubic */
          touch-action: pan-y !important;
          will-change: transform, opacity !important;
        }
        .swiper-slide-active {
          z-index: 10 !important;
        }

        /* Tab highlight with ease-out transition */
        .tab-highlight {
          transition: transform 0.5s cubic-bezier(0, 0, 0.2, 1),
            width 0.5s cubic-bezier(0, 0, 0.2, 1) !important; /* Ease-out */
        }
      `}</style>

      {/* Tab Navigation for Desktop */}
      {!isMobile && (
        <div className="flex justify-center relative min-h-[44px]">
          <div className="flex space-x-2 rounded-[12px] relative border border-[#E4E4E7] p-2 items-center">
            {tabs.map((tab, i) => (
              <button
                key={tab.id}
                className={`
          relative px-4 py-2 rounded-[8px] text-[16px] font-[400] 
          transition-all duration-200 cursor-pointer
          
          ${
            active === i
              ? "text-white bg-[#22263F] shadow-[0_4px_6px_-1px_rgba(3,33,127,0.3)]"
              : "text-[#A7A7A7] hover:text-gray-900 hover:bg-gray-300"
          }`}
                onClick={() => handleTabClick(i)}
              >
                {tab.tab_label}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Mobile Tab Display */}
      {/* {isMobile && (
        <div className="flex justify-center mb-6">
          <div className="px-4 py-2 rounded-[12px] border border-[#E4E4E7] bg-[#03217F] text-white text-base font-semibold">
            {tabs[active]?.label || ""}
          </div>
        </div>
      )} */}

      {/* Swiper Carousel */}
      <div className="w-full ">
        <Swiper
          grabCursor={true}
          centeredSlides={true}
          initialSlide={active}
          speed={1000}
          loop={true}
          mousewheel={{
            forceToAxis: true,
            sensitivity: 1,
          }}
          threshold={10}
          resistance={false}
          resistanceRatio={0}
          longSwipesRatio={0.2}
          followFinger={true}
          allowTouchMove={true}
          simulateTouch={true}
          touchRatio={1.5}
          modules={[Controller, Mousewheel, A11y, Thumbs, Pagination]}
          onSwiper={setSwiper}
          onSlideChange={(swiper) => {
            // Only update if slide changed by user action (not programmatically from tab click)
            if (changeSource !== "click") {
              setActive(swiper.realIndex);
              setChangeSource("slide");
            }
          }}
          slidesPerView={getSlidesPerView()}
          spaceBetween={getSpaceBetween()}
        >
          {tabs.map((tab, index) => (
            <SwiperSlide key={tab.id} className="pt-3">
              <div className={`${active === index ? "" : "pt-0 lg:pt-10"} `}>
                <div
                  className={`bg-[#F6FAF3] gap-3 w-full  lg:h-full xl:min-h-[524px] [@media(min-width:1600px)]:h-[50vh] transition-transform duration-500 ease-in-out flex justify-between flex-col [@media(min-width:768px)]:flex-row pl-4  md:pl-6 lg:pl-10  pt-4   md:pb-0`}
                >
                  <div className="    md:w-[60%] lg:w-fit text-left space-y-3 flex flex-col justify-center ">
                    <h3 className=" md:px-0  md:mt-0 text-[#828282] font-semibold text-[11px] md:text-[17px] lg:text-[28px]">
                      {tab.label}
                    </h3>
                    <p className="text-[18px]  md:text-[25px] lg:text-[36px] font-bold  text-[#22263F] leading-tight">
                      {tab.title}
                    </p>
                    <Link href={tab.link}>
                      <button
                        aria-label="Learn More"
                        className="cursor-pointer w-[94px] min-h-[28px]  text-[12px] md:text-[16px] md:w-[113px] md:min-h-[35px] rounded-[3.9px]  bg-[#22263F] text-white font-medium"
                      >
                        Learn More
                      </button>
                    </Link>
                  </div>

                  {/* Updated image container */}
                  <div className="landing-pic  relative w-full max-w-[520px] h-full aspect-[520/514] rounded-tl-lg sm:rounded-tl-xl md:rounded-tl-2xl rounded-br-lg sm:rounded-br-xl md:rounded-br-2xl overflow-hidden">
                    <Image
                      src={tab.clip}
                      alt=""
                      fill
                      // sizes="(max-width: 640px) 100vw, (max-width: 768px) 100vw, (max-width: 1024px) 45vw, (max-width: 1536px) 520px, 30vw"
                      className="md:object-contain lg:object-cover "
                      // loading="eager"
                      priority
                    />
                  </div>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
}
