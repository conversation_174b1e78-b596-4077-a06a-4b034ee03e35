"use client"

import {useTypingEffect} from "../../hooks/useTypingEffect"
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

export const TypedBotMessage = ({ text, messageIndex }: { text: string; messageIndex: number }) => {
  const { displayedText, isComplete } = useTypingEffect(text, 15);
  
  return (
    <div className="prose prose-sm max-w-none">
      <ReactMarkdown remarkPlugins={[remarkGfm]}>
        {isComplete ? text : displayedText}
      </ReactMarkdown>
    </div>
  );
};