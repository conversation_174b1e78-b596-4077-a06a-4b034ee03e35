"use client";

import {
  useGetQ<PERSON>y,
  useGetQueryHermes,
  useGetQueryOrionBackend,
  useSubmitQuery,
} from "@/services/api_hooks";
import React, { useEffect, useState } from "react";
import { Button } from "../ui/button";
import { Send } from "lucide-react";
import { toast } from "sonner";
import { telegramBot } from "@/config/baseUrl";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
interface TelegramLinkButtonProps {
  className?: string;
}

const TelegramLinkButton: React.FC<TelegramLinkButtonProps> = ({ className }) => {
  const t = useTranslations("ConnectTelegram");
  const [isHermesEnvironment, setIsHermesEnvironment] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Check if the current URL includes "hermes" or "orion"
    const currentUrl = window.location.href.toLowerCase();
    setIsHermesEnvironment(currentUrl.includes("hermes"));
  }, []);

  const { data: profile, isLoading } = useGetQuery(
    "/auth/profile",
    ["profile"],
    {
      onError() {
        // toast.error("Could not load your profile");
      },
    }
  );

  // Call the appropriate API based on the environment
  const {
    data: hermesData,
    isLoading: hermesLoading,
    isRefetching,
    refetch: refetchHermes,
  } = useGetQueryHermes("/api/choice", ["get-choice"], {
    //   refetchOnWindowFocus: true,
    enabled: isHermesEnvironment,
  });

  const {
    data: orionData,
    isLoading: orionLoading,
    error: errorData,
    refetch: refetchOrion,
  } = useGetQuery("/api/user/telegramId", ["get-telegram-id"], {
    //   refetchOnWindowFocus: true,
    enabled: true,
    retry: false,
    onSuccess: (data: any) => {
      if (data?.telegram_id) {
        // Handle successful telegram ID retrieval
      }
    },
    onError: () => {
      console.error("Failed to fetch Telegram ID");
    },
  });

  // Determine if Telegram is linked based on the environment
  const determineIfTelegramLinked = () => {
    // If still loading, we should default to showing "Link Telegram"
    if (isHermesEnvironment) {
      if (hermesLoading) return false;
      return (
        hermesData?.telegramId !== null && hermesData?.telegramId !== undefined
      );
    } else {
      if (orionLoading) return false;
      // For Orion, we check if we have telegram_id data or if the error is NOT the specific "no telegram ID" error
      return (
        orionData?.telegram_id !== null && orionData?.telegram_id !== undefined
      );
    }
  };

  const { mutateAsync: unlinkTelegram, isPending: unlinkLoading } =
    useSubmitQuery("/api/user/de-link-telegram", "POST", {
      onSuccess() {
        toast.success(t("unlinkSuccess"), {
          position: "top-right",
          className: "p-4",
        });
        setOpenDialog(false);
        window.location.reload(); // Refresh the window to reflect changes
      },
      onError(err: any) {
        toast.error(
          err.response?.data?.message || "Failed to unlink Telegram",
          {
            position: "top-right",
            className: "p-4",
          }
        );
      },
    });

  const isTelegramLinked = determineIfTelegramLinked();
  const isDataLoading = isHermesEnvironment ? hermesLoading : orionLoading;

  useEffect(() => {
    // Refetch Hermes data when the window regains focus (user returns from Telegram)
    const handleFocus = () => {
      refetchHermes();
    };
    window.addEventListener("focus", handleFocus);
    return () => {
      window.removeEventListener("focus", handleFocus);
    };
  }, [refetchHermes]);

  return (
    <div className={className}>
      {!isTelegramLinked ? (
        <TooltipProvider delayDuration={0}>
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                disabled={isLoading || isDataLoading}
                className="bg-white hover:bg-[#0088CC1A] text-[#0088CC] flex items-center gap-2 justify-between text-sm p-[3px] rounded-full cursor-pointer border-[1.5px] border-[#0088CC] w-full"
              >
                <a
                  href={`${telegramBot}?start=link_${profile?.profile?.user_id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center text-sm gap-2"
                >
                  <p className="md:hidden">{isDataLoading ? t("checkingStatus") : t("linkTelegram")}</p>
                  <Send className="w-4 h-4 mr-[2px] mt-[2px]" color="#0088CC" />
                </a>
              </button>
            </TooltipTrigger>
            <TooltipContent className="hidden md:block">{isDataLoading ? t("checkingStatus") : t("linkTelegram")}</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ) : (
        <TooltipProvider delayDuration={0}>
          <Tooltip>
            <Dialog open={openDialog} onOpenChange={setOpenDialog}>
              <TooltipTrigger asChild> 
                <DialogTrigger asChild>
                  <button
                    className="bg-white hover:bg-[#E024244D] text-[#E02424] flex items-center gap-2 justify-center text-sm p-[3px] rounded-full cursor-pointer border-[1.5px] border-[#E024244D] w-full"
                    disabled={unlinkLoading}
                  >
                    <p className="md:hidden">{t("unlinkTelegram")}</p>
                    <Send className="w-4 h-4 mr-[2px] mt-[2px]" color="#E02424" />
                  </button>
                </DialogTrigger>
              </TooltipTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{t("unlinkDialogTitle")}</DialogTitle>
                </DialogHeader>
                <div className="py-4">{t("unlinkDialogBody")}</div>
                <DialogFooter>
                  <DialogClose asChild>
                    <Button variant="outline">{t("cancel")}</Button>
                  </DialogClose>
                  <Button
                    onClick={async () => {
                      await unlinkTelegram({});
                    }}
                    disabled={unlinkLoading}
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    {unlinkLoading ? t("unlinking") : t("confirm")}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            <TooltipContent className="hidden md:block">{t("unlinkTelegram")}</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
};

export default TelegramLinkButton;
