"use client";

import React, { useEffect } from "react";
import { SimpleWelcomeModal } from "./welcome-carousel";

const WelcomeModal = () => {
  const [showModal, setShowModal] = React.useState(false);
  useEffect(() => {
    if (!localStorage.getItem("welcome_modal_seen")) {
      setShowModal(true);
    }
  }, []);

  return <div>{showModal ? <SimpleWelcomeModal /> : null}</div>;
};

export default WelcomeModal;
