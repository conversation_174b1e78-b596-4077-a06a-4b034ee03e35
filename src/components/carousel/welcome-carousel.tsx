"use client";

import { useState, useEffect } from "react";
import { useTranslations } from 'next-intl';
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import Image from "next/image";

interface WelcomeModalProps {
  isOpen: boolean;
  setClose: () => void;
}

export function SimpleWelcomeModal() {

  // use next-intl for i18n
  const t = useTranslations("DashboardHome.Welcome");

  // Define content array
  const welcomeSlides = [
    {
      id: "welcome",
      title: t("slides.Welcome.title"),
      description: t("slides.Welcome.description"),
      icon: "/welcome-slide-one.svg",
    },
    {
      id: "get-started",
      title: t("slides.Credits.title"),
      description: t("slides.Credits.description"),
      icon: "/welcome-slide-two.svg",
    },
  ];

  const [slideIndex, setSlideIndex] = useState(0);
  const [isOpen, setClose] = useState(true);
  const [direction, setDirection] = useState(0); // -1 for left, 1 for right

  const handleContinue = () => {
    if (slideIndex === 0) {
      setDirection(1); // moving forward
      setSlideIndex(1);
    } else {
      localStorage.setItem("welcome_modal_seen", "true");
      setClose(false);
    }
  };

  const handleSlideChange = (index: number) => {
    setDirection(index > slideIndex ? 1 : -1);
    setSlideIndex(index);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setClose}>
      <DialogContent
        className="p-0 gap-0 max-w-[406px] mx-auto overflow-hidden [&>button]:hidden"
        onPointerDownOutside={(e) => e.preventDefault()} 
        
      >
        <DialogTitle className="sr-only">{t("title")}</DialogTitle>
        <div className="p-6 flex flex-col items-center">
          {/* Slide Content with Animation */}
          <div
            className="relative w-full overflow-hidden "
            style={{ height: "280px" }}
          >
            <AnimatePresence initial={false} mode="wait" custom={direction}>
              <motion.div
                key={slideIndex}
                custom={direction}
                initial={{
                  x: direction > 0 ? "70%" : direction < 0 ? "-70%" : 0,
                  opacity: 0,
                }}
                animate={{
                  x: 0,
                  opacity: 1,
                }}
                exit={{
                  x: direction < 0 ? "70%" : direction > 0 ? "-70%" : 0,
                  opacity: 0,
                }}
                transition={{
                  x: { type: "spring", stiffness: 300, damping: 30 },
                  opacity: { duration: 0.2 },
                }}
                className="absolute w-full flex flex-col items-center"
              >
                {/* Blue Illustration Area */}
                <div className="w-full  bg-blue-50 rounded-lg flex items-center justify-center mb-6">
                  <Image
                    src={welcomeSlides[slideIndex].icon}
                    alt=""
                    height={119}
                    width={358}
                    className="object-cover w-full"
                    // loading="eager"
                    priority
                  />
                </div>

                {/* Title */}
                <h2 className="text-[22px] font-[600] text-center mb-3">
                  {welcomeSlides[slideIndex].title}
                </h2>

                {/* Description */}
                <p className="text-[#7F7F81] font-[400] text-[12px] text-center px-4">
                  {welcomeSlides[slideIndex].description}
                </p>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Pagination Dots */}
          <div className="flex justify-center space-x-2 mb-6">
            {welcomeSlides.map((_, index) => (
              <span
                key={index}
                className={cn(
                  "inline-block w-4 h-2 rounded-full cursor-pointer transition-all",
                  slideIndex === index
                    ? "bg-gray-800"
                    : "bg-gray-300 opacity-50"
                )}
                onClick={() => handleSlideChange(index)}
              />
            ))}
          </div>

          {/* Continue Button */}
          <Button
            className="cursor-pointer h-[32px] px-2 py-1 bg-[#03061D] hover:bg-[#03061D]/90 text-white rounded-md font-medium"
            onClick={handleContinue}
          >
            {t("ContinueToDashboard")}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
