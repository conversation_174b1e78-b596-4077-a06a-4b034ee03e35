"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { ModelCard as ModelCardType } from "@/utils/data";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import axios from "axios";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useSubmitQuery } from "@/services/api_hooks";
import { toast } from "sonner";
import MoonLoader from "react-spinners/MoonLoader";
import { Checkbox } from "../ui/checkbox";
import SandboxSubscribeModalWrapper from "@/app/dashboard/yumi/_components/SandboxSubscribeModalWrapper";
// import { fetchRefreshToken, fetchAccessToken } from "@/services/api_hooks";

interface ModelCardProps {
  model: {
    id: string;
    name: string;
    description: string;
    img: string;
    link: string;
    is_unlocked: boolean;
    unlocked: boolean;
    slug?: string;
    label: string;
    tab_label: string;
    base_price?: number;
    acceptedTerms?: boolean;
  };
  isRecentlyUsed?: boolean;
  isYumiSandbox?: boolean;
}

export function ModelCard({ model, isRecentlyUsed }: ModelCardProps) {
  // use next-intl for i18n
  const t = useTranslations("DashboardHome.ModelCard");
  const tGlobal = useTranslations("global");

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [plan, setPlan] = useState(false);
  const [disclaimer, setDisclaimer] = useState(false);
  const [terms, setTerms] = useState(false);
  const [cardDetail, setCardDetail] = useState({});
  const router = useRouter();
  const [showYumiModal, setShowYumiModal] = useState(false);

  const { mutateAsync: recordUsage, isPending: loading } = useSubmitQuery(
    "/models/recent",
    "POST",
    {
      onSuccess(response: any) {
        // toast.success("Profile updated!", {
        //   position: "top-right",
        //   className: "p-4",
        // });
        router.push(model.link + "?show_tutorial=true");
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || t("toast.UpdateFailed"), {
          position: "top-right",
          className: "p-4",
        });
      },
    }
  );

  const { mutateAsync: acceptConditions, isPending: loadingCondition } =
    useSubmitQuery("/models/terms", "POST", {
      onSuccess(response: any) {},
      onError(err: any) {
        toast.error(err.response?.data?.error || t("toast.TermsFailed"), {
          position: "top-right",
          className: "p-4",
        });
      },
    });

  const handlePayment = async () => {
    if (terms && disclaimer) {
      const data = {
        modelId: model.id,
      };
      // acceptConditions(data)
      recordUsage(data);
    }
  };

  const handleCardClick = (arg: any) => {
    if (!model.unlocked) return;
    if (model?.acceptedTerms) {
      router.push(model.link);
    } else {
      setIsDialogOpen(true);
      setCardDetail(arg);
    }
  };

  const handleClick = () => {
    router.push(model.link);
  };

  return (
    <>
      <div className="relative">
        {/* Main card content */}
        <div
          onClick={() => handleCardClick(model)}
          className={cn(
            "bg-white rounded-lg flex-col overflow-hidden border border-[#E2E2E2] p-3 space-y-2 h-full relative cursor-pointer",
            !model.unlocked && "opacity-50"
          )}
        >
          {/* <div className="relative w-full h-40">
           
          </div> */}
          <Image
            src={model.img}
            alt={model.id}
            // fill
            width={201}
            height={133}
            className="object- rounded-[8px] place-self-center"
            priority
          />

          <div className="">
            <h3 className="font-[500] text-sm">{model.name}</h3>
            <p className="text-sm text-[#7F7F81]">{model.tab_label}</p>
          </div>
        </div>

        {/* Lock icon positioned on top, outside the opacity scope */}
        {!model.unlocked && (
          <div className="absolute inset-0 flex items-center justify-center z-50 pointer-events-none">
            <Image
              src="/padlock.svg"
              alt="lock icon"
              height={30}
              width={30}
              className="bg-white rounded-[100%] p-2 accent-accent"
            />
          </div>
        )}
      </div>

      {/* Model Dialog */}
      {!isRecentlyUsed && (
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-[406px]">
            <DialogTitle className="sr-only">Selected model dialog</DialogTitle>
            <div className="flex flex-col  space-y-3 mt-3">
              {/* Model Image */}
              <div className="relative w-full h-[240px] overflow-hidden rounded-[8px]">
                <Image
                  src={model.img}
                  alt="model"
                  fill
                  className="object-cover rounded-[8px] object- "
                  priority
                />
                {/* <div className="absolute top-4 left-4 bg-[#F0F6FF] font-[400] text-[10px] px-2 py-1 rounded-[3px] text-[#22263F] z-10">
                  7 Weeks Free Trial
                </div> */}
              </div>

              {/* Title */}
              <h2 className="text-[16px] font-[600] self-start ">
                {model.name}
              </h2>

              <p className="text-[#7F7F81]  text-sm font-[400] ">
                {model.label}
              </p>

              {/* Plan Selection Tabs with cost */}

              {/* <div className="flex flex-row items-center justify-between w-full gap-2">
                <ToggleGroup
                  type="single"
                  value={plan}
                  onValueChange={(value) => value && setPlan(value)}
                  className="flex rounded-[12px] gap-1 cursor-pointer border  p-[3px]"
                >
                  <ToggleGroupItem
                    value="standard"
                    className="flex-1 rounded-[8px] shadow-[#10182808] duration-200 cursor-pointer w-[68px] h-[26px] text-[12px] data-[state=on]:bg-[#0f172a] data-[state=on]:text-white transition-all"
                  >
                    Standard
                  </ToggleGroupItem>
                  <ToggleGroupItem
                    value="premium"
                    className="flex-1 rounded-[8px] shadow-[#10182808] cursor-pointer w-[68px] h-[26px] text-[12px] data-[state=on]:bg-[#0f172a] data-[state=on]:text-white transition-all"
                  >
                    Premium
                  </ToggleGroupItem>
                </ToggleGroup>

               
                <div className="flex items-center ">
                  <span className="text-[22px] font-[600]">
                    ${model.base_price}
                  </span>
                  <span className="ml-2 text-[12px] font-[400] text-gray-500 line-through">
                    $20/month
                  </span>
                </div>
              </div> */}

              {/* Features */}
              <div className="space-y-2 text-[#7F7F81]  text-[13px] font-[400]">
                <p>{t("IHaveAgreed")}</p>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={terms}
                    onCheckedChange={(checked) => setTerms(checked === true)}
                  />
                  <div>
                    {t("The")}{" "}
                    <Link
                      href="/terms-of-use"
                      target="_blank"
                      className="text-blue-600 underline"
                    >
                      {t("TermsOfUse")}
                    </Link>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={disclaimer}
                    onCheckedChange={(checked) =>
                      setDisclaimer(checked === true)
                    }
                  />
                  <div>
                    {t("The")}{" "}
                    <Link
                      href="/disclaimer"
                      target="_blank"
                      className="text-blue-600 underline"
                    >
                      {t("Disclaimer")}
                    </Link>
                  </div>
                </div>
              </div>

              {/* Footer Actions */}
              <div className="flex justify-between w-full mt-2">
                <Button
                  variant="ghost"
                  className="border text-[12px] rounded-md cursor-pointer"
                  onClick={() => setIsDialogOpen(false)}
                >
                  {tGlobal("Cancel")}
                </Button>

                <Button
                  onClick={() => handlePayment()}
                  className="bg-black text-white cursor-pointer text-[12px] border rounded-md font-[500] hover:bg-black/90  h-[32px]"
                  disabled={!(terms && disclaimer)}
                >
                  {t("AddToMyModel")}
                  <div className="flex">
                    <MoonLoader color="white" loading={loading} size={15} />
                  </div>
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
