import React from "react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import Image from "next/image";
import Logo from "../Logo";

export function SiteFooter() {
  // use next-intl for i18n
  const t = useTranslations("SiteFooter");

  const currentYear = new Date().getFullYear();

  return (
    <footer className="w-full border-t border-gray-200 bg-white py-10 px-[5%]">
      <div className="container mx-auto max-w-[1300px]">
        {/* Mobile and tablet view (stacked) */}
        <div className="md:hidden flex flex-col space-y-8">
          {/* Logo and copyright */}
          <div className="flex flex-col space-y-3 items-center">
            <Logo textColor="black" textSize="27px" width="40" height="45" />
            <div className="text-slate-950 text-[14px] font-normal">
              {t("copy")} © ai-wk {new Date().getFullYear()}.
            </div>
          </div>

          {/* Social icons */}
          <div className="flex justify-center space-x-4">
            <Link
              href="https://x.com/ai_wk_com"
              target="_blank"
              aria-label="Twitter"
              className="text-black hover:text-gray-900"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
              </svg>
            </Link>
            <Link
              href="https://www.youtube.com/@ai-wk"
              target="_blank"
              aria-label="YouTube"
              className="text-black hover:text-gray-900"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path>
                <polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon>
              </svg>
            </Link>
            <Link
              href="https://www.linkedin.com/company/ai-wk/about/"
              target="_blank"
              aria-label="linkedin"
              className="text-black hover:text-gray-900"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                <rect x="2" y="9" width="4" height="12"></rect>
                <circle cx="4" cy="4" r="2"></circle>
              </svg>
            </Link>
          </div>

          {/* Navigation links */}
          <div className="flex flex-wrap justify-center gap-4 text-[16px] font-[400] text-[#03061D]">
            <Link href="/terms-of-use" className="hover:text-gray-900">
              {t("nav.TermsofUse")}
            </Link>
            <Link href="/privacy-policy" className="hover:text-gray-900">
              {t("nav.PrivacyPolicy")}
            </Link>
            <Link href="/disclaimer" className="hover:text-gray-900">
              {t("nav.Disclaimer")}
            </Link>
            <Link href="/refund-policy" className="hover:text-gray-900">
              {t("nav.RefundPolicy")}
            </Link>
          </div>
        </div>

        {/* Desktop view (original layout) */}
        <div className="hidden md:flex flex-row justify-between items-center">
          <div className="flex-col space-y-3 justify-start items-start">
            <Logo textColor="black" textSize="27px" width="40" height="45" />
            <div className="self-stretch justify-start pl-1 text-slate-950 text-[14px] font-normal">
              {t("copy")} © ai-wk {new Date().getFullYear()}.
            </div>
          </div>

          <div className="flex flex-col items-end space-y-5">
            {/* Social icons */}
            <div className="flex items-center space-x-4">
              <Link
                href="https://x.com/ai_wk_com"
                target="_blank"
                aria-label="Twitter"
                className="text-black hover:text-gray-900"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                </svg>
              </Link>
              <Link
                href="https://www.youtube.com/@ai-wk"
                target="_blank"
                aria-label="YouTube"
                className="text-black hover:text-gray-900"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path>
                  <polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon>
                </svg>
              </Link>
              <Link
                href="https://www.linkedin.com/company/ai-wk/about/"
                target="_blank"
                aria-label="linkedin"
                className="text-black hover:text-gray-900"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                  <rect x="2" y="9" width="4" height="12"></rect>
                  <circle cx="4" cy="4" r="2"></circle>
                </svg>
              </Link>
            </div>

            {/* Navigation links */}
            <div className="text-[16px] flex flex-row space-x-5 font-[400] text-[#03061D]">
              <Link href="/terms-of-use" className="hover:text-gray-900">
                {t("nav.TermsofUse")}
              </Link>
              <Link href="/privacy-policy" className="hover:text-gray-900">
                {t("nav.PrivacyPolicy")}
              </Link>
              <Link href="/disclaimer" className="hover:text-gray-900">
                {t("nav.Disclaimer")}
              </Link>
              <Link href="/refund-policy" className="hover:text-gray-900">
                {t("nav.RefundPolicy")}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
