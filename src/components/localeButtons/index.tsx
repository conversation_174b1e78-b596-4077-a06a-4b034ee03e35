"use client";

import { useLocale, useTranslations } from "next-intl";
import { Locale } from "@/i18n/config";
import { getUserLocale, setUserLocale } from "@/services/locale";
import { useMemo, useState, useTransition } from "react";
import { useRef, useEffect } from "react";
import { Globe, ChevronDown, Bell, HelpCircle, Loader } from "lucide-react";
import { Button } from "../ui/button";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/Providers/LanguageProvider";

export default function LocaleButtons({ className }: any) {
  return (
    <>
      <DropDownLanguage className={className} />
    </>
  );
}

function DropDownLanguage({ className }: any) {
  const [selectedLanguage, setSelectedLanguage] = useState("ENG");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const { onChange: onChangeGeneral, currentLocale, isPending } = useLanguage();

  const languages = useMemo(
    () => [
      { code: "en", name: "ENG" },
      { code: "zh-CN", name: "中文" },
    ],
    []
  );

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLanguageSelect = (languageCode: string, name: string) => {
    setSelectedLanguage(name);
    // onChange(languageCode);
    onChangeGeneral(languageCode);
    setIsDropdownOpen(false);
  };

  useEffect(() => {
    const findLocale = languages.find((itm) => itm.code === currentLocale);
    setSelectedLanguage(findLocale?.name as string);
  }, [languages, currentLocale]);

  return (
    <div className="">
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className={cn(
              "flex items-center gap-2 text-[#000000] rounded-md p-2 transition-colors",
              className
            )}
          >
            {isPending ? (
              <Loader className="animate-spin w-4 h-4" />
            ) : (
              <Globe className="w-4 h-4" />
            )}
            <span className={cn("text-sm font-medium", className)}>
              {selectedLanguage}
            </span>
            <ChevronDown
              className={cn(
                `w-4 h-4 transition-transform ${
                  isDropdownOpen ? "rotate-180" : ""
                }`,
                className
              )}
            />
          </button>
          {isDropdownOpen && (
            <div className="absolute top-full left-0 mt-1 w-32 bg-white rounded-md shadow-lg z-50">
              {languages.map((language) => (
                <button
                  key={language.code}
                  onClick={() =>
                    handleLanguageSelect(language.code, language.name)
                  }
                  className="w-full text-left px-3 py-2 text-sm text-[#7f7f81] hover:text-[#0f172a] hover:bg-[#f9f9f9] first:rounded-t-md last:rounded-b-md transition-colors"
                >
                  {language.name}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
