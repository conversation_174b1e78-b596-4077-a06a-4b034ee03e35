// components/OrganizationModal.tsx
"use client";

import React, { useState } from "react";
import { useTranslations } from 'next-intl';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";

interface OrganizationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateOrganization?: (name: string) => void;
}

export default function OrganizationModal({
  open,
  onOpenChange,
  onCreateOrganization,
}: OrganizationModalProps) {

  // use next-intl for i18n
  const t = useTranslations("DashboardHome.DashboardHeader.Organization");
  const tGlobal = useTranslations("global");

  const [organizationName, setOrganizationName] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (organizationName.trim()) {
      onCreateOrganization?.(organizationName);
      setOrganizationName("");
      onOpenChange(false);
    }
  };

  // Reset the form when dialog closes
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      setOrganizationName("");
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-[18px] font-[500]">
            {t("title")}
          </DialogTitle>
          <DialogDescription>
            {t("description")}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 pt-2">
          <div className="space-y-2">
            <label
              htmlFor="organization-name"
              className="text-sm font-medium text-gray-700"
            >
              {t("form.name.text")}
            </label>
            <Input
              id="organization-name"
              value={organizationName}
              onChange={(e) => setOrganizationName(e.target.value)}
              className="w-full"
              autoComplete="off"
            />
          </div>

          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              {tGlobal("Back")}
            </Button>
            <Button
              type="submit"
              disabled={!organizationName.trim()}
              className="bg-gray-900 text-white hover:bg-gray-800"
            >
              {t("CreateOrganization")}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
