"use client";

import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

interface DeleteAccountModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  loading?: boolean;
  title?: string;
  description?: string;
  cancelText?: string;
  confirmText?: string;
  loadingText?: string;
}

export function SetDefualtModal({
  isOpen,
  onOpenChange,
  onConfirm,
  loading,
  title,
  description,
  cancelText,
  confirmText,
  loadingText,
}: DeleteAccountModalProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent className="w-[450px] rounded-md p-6">
        <AlertDialogHeader>
      
          <AlertDialogTitle className="text-[16px] font-medium">
            {title}
          </AlertDialogTitle>
          <AlertDialogDescription className="text-sm text-[#737384]">
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex flex-col-reverse space-y-2 space-y-reverse sm:flex-row sm:justify-between sm:space-x-2 sm:space-y-0">
          <AlertDialogCancel className="mt-2 w-full rounded-md border border-gray-200 bg-white px-4 py-2 text-sm font-normal text-gray-800 hover:bg-gray-50 sm:mt-0 sm:w-auto">
            {cancelText}
          </AlertDialogCancel>
          <Button
            onClick={onConfirm}
            disabled={loading}
            className="w-full rounded-md px-4 py-2 text-sm font-normal text-white  sm:w-auto"
          >
            {loading ? loadingText : confirmText}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
