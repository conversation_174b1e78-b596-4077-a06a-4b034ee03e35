"use client";

import React, { useEffect, useState } from "react";
import ProfileDialog from "./modal";
import { toast } from "sonner";
import { useGetQuery } from "@/services/api_hooks";

const ProfileModal = () => {
  const { data: profile, isLoading: profoileLoading } = useGetQuery(
    "/auth/profile",
    ["profile"],
    {
      // onError() {
      //   toast.error("Could not load your profile");
      // },
    }
  );

  return (
    <>{profile?.profile?.lastname === null || profile?.profile?.lastname === "" ? <ProfileDialog /> : null}</>
  );
};

export default ProfileModal;
