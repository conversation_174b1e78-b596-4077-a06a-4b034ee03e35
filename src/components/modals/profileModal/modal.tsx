// @ts-nocheck

"use client";

import React, { useState } from "react";
import { useTranslations } from 'next-intl';
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import { useSubmitQuery } from "@/services/api_hooks";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

interface FormValues {
  firstName: string;
  lastName: string;
  email: string;
}

interface ProfileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit?: (data: FormValues) => void;
}

export default function ProfileDialog(
  {
    //   open,
    //   onOpenChange,
    //   onSubmit
  }
) {

  // use next-intl for i18n
  const t = useTranslations("DashboardHome.Profile");
  const tForm = useTranslations("Form");
  const tFormSignUp = useTranslations("Form.SignUp");

  // Schema for form validation
  const profileSchema = yup.object({
    firstName: yup.string().required(tFormSignUp("firstName.required")),
    lastName: yup.string().required(tFormSignUp("lastName.required")),
    email: yup.string().email(tForm("email.error")).required(tForm("email.required"))
  }).required();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(profileSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
    }
  });
  const [dialogOpen, setDialogOpen] = useState(true);

  const handleFormSubmit = (data: FormValues) => {
    // onSubmit?.(data);
    // onOpenChange(false);
  };

  const queryClient = useQueryClient();

  // Prepare the mutation for updating
  const { mutateAsync: updateProfile, isPending: updating } = useSubmitQuery(
    "/auth/profile",
    "PUT",
    {
      onSuccess() {
        toast.success(t("toast.ProfileUpdated"), {
          position: "top-right",
          className: "p-4",
        });
        setDialogOpen(false);
        queryClient.invalidateQueries({
          queryKey: ["profile"],
        });
      },
      onError(err: any) {
        toast.error(err.response?.data?.error || t("toast.UpdateFailed"), {
          position: "top-right",
          className: "p-4",
        });
      },
    }
  );

  // Form submission handler
  const onSubmit = async (vals: any) => {
    const formData = new FormData();
    formData.append("firstname", vals.firstName);
    formData.append("lastname", vals.lastName);
    formData.append("email", vals.email);
    if (vals.password) {
      formData.append("password", vals.password);
    }
    await updateProfile(formData);
  };

  return (
    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="flex flex-col items-center text-center">
          <div className="mb-4">
            <div className="relative flex justify-center w-[387px] h-[151px]">
              <Image
                src="/profile-image.svg"
                alt="Profile setup"
                fill
                className=" "
              />
            </div>
          </div>
          <DialogTitle className="text-xl font-medium">
           {t("title")}
          </DialogTitle>
          <DialogDescription>
            {t("description")}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">{tFormSignUp("firstName.text")}</Label>
              <Input
                id="firstName"
                {...register("firstName")}
              />
              {errors.firstName && (
                <p className="text-sm text-red-500">{errors.firstName.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">{tFormSignUp("lastName.text")}</Label>
              <Input
                id="lastName"
                {...register("lastName")}
              />
              {errors.lastName && (
                <p className="text-sm text-red-500">{errors.lastName.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">{tFormSignUp("email.text")}</Label>
            <Input
              id="email"
              type="email"
              {...register("email")}
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email.message}</p>
            )}
          </div>

          <Button
            type="submit"
            className="w-full bg-gray-900 hover:bg-gray-800"
            disabled={updating}
          >
            {updating ? tForm("button.Updating") : tForm("button.SetUpProfile")}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
}