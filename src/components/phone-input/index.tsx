// @ts-nocheck

import { Button } from "@/components/ui/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@/components/ui/command";
import { Input } from "@/components/ui/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import parsePhoneNumberFromString, {
	AsYouType,
	type CarrierCode,
	type CountryCallingCode,
	type E164Number,
	type NationalNumber,
	type CountryCode,
	type NumberType,
} from "libphonenumber-js";
import { Check, ChevronsUpDown } from "lucide-react";
import * as React from "react";
import { FixedSizeList as List } from "react-window";
import { countries } from "./countries";
import { useStateHistory } from "./use-state-history";

export type Country = (typeof countries)[number];

export type PhoneData = {
	phoneNumber?: E164Number;
	countryCode?: CountryCode;
	countryCallingCode?: CountryCallingCode;
	carrierCode?: CarrierCode;
	nationalNumber?: NationalNumber;
	internationalNumber?: string;
	possibleCountries?: string;
	isValid?: boolean;
	isPossible?: boolean;
	uri?: string;
	type?: NumberType;
};

interface PhoneInputProps extends React.ComponentPropsWithoutRef<"input"> {
	value?: string;
	defaultCountry?: CountryCode;
}

export function getPhoneData(phone: string): PhoneData {
	const asYouType = new AsYouType();
	asYouType.input(phone);
	const number = asYouType.getNumber();
	return {
		phoneNumber: number?.number,
		countryCode: number?.country,
		countryCallingCode: number?.countryCallingCode,
		carrierCode: number?.carrierCode,
		nationalNumber: number?.nationalNumber,
		internationalNumber: number?.formatInternational(),
		possibleCountries: number?.getPossibleCountries().join(", "),
		isValid: number?.isValid(),
		isPossible: number?.isPossible(),
		uri: number?.getURI(),
		type: number?.getType(),
	};
}

// Country item renderer for virtualized list
const CountryItem = React.memo(({ 
	index, 
	style, 
	data 
}: { 
	index: number; 
	style: React.CSSProperties; 
	data: {
		countries: typeof countries;
		countryCode: CountryCode;
		onSelectCountry: (country: (typeof countries)[number]) => void;
	}
}) => {
	const { countries, countryCode, onSelectCountry } = data;
	const country = countries[index];
	
	return (
		<div style={style}>
			<CommandItem
				key={country.iso3}
				value={`${country.name} (+${country.phone_code})`}
				onSelect={() => onSelectCountry(country)}
				className="m-0 cursor-pointer"
			>
				<Check
					className={cn(
						"mr-2 size-4",
						countryCode === country.iso2
							? "opacity-100"
							: "opacity-0",
					)}
				/>
				{country.name}
				<span className="text-gray-11 ml-1">
					(+{country.phone_code})
				</span>
			</CommandItem>
		</div>
	);
});

CountryItem.displayName = "CountryItem";

export function PhoneCode({
	value: valueProp,
	defaultCountry = "US",
	className,
	id,
	required = true,
	...rest
}: PhoneInputProps) {
	const asYouType = new AsYouType();
	const inputRef = React.useRef<HTMLInputElement>(null);
	const listRef = React.useRef<List>(null);
	
	// Use a local state to track the current value
	const [inputValue, setInputValue] = React.useState<string>("");
	
	// State for filtering countries
	const [filterValue, setFilterValue] = React.useState<string>("");
	
	// Initialize history state
	const [, handlers, history] = useStateHistory(valueProp);
	
	// State for country code selection
	const [openCommand, setOpenCommand] = React.useState(false);
	const [countryCode, setCountryCode] = React.useState<CountryCode>(defaultCountry);

	React.useEffect(() => {
		setCountryCode(defaultCountry);
		
		// Find the selected country data for the new default country
		const country = countries.find(c => c.iso2 === defaultCountry);
		if (country) {
			// Get the new phone code
			const newPhoneCode = `+${country.phone_code}`;
			
			// Extract the national number part if any
			let nationalPart = "";
			try {
				const currentNumber = parsePhoneNumberFromString(inputValue);
				if (currentNumber?.nationalNumber) {
					nationalPart = currentNumber.nationalNumber.toString();
				}
			} catch (e) {
				// Fallback extraction logic
				// ...
			}
			
			// Combine new phone code with national part
			let newValue = newPhoneCode;
			if (nationalPart) {
				newValue = `${newPhoneCode}${nationalPart}`;
			}
			
			// Format and update
			asYouType.reset();
			const formattedValue = asYouType.input(newValue);
			setInputValue(formattedValue);
			if (inputRef.current) {
				inputRef.current.value = formattedValue;
			}
		}
	}, [defaultCountry]);
    
	
	// Get selected country data
	const selectedCountry = countries.find(
		(country) => country.iso2 === countryCode,
	);
	
	// Filter countries based on search input
	const filteredCountries = React.useMemo(() => {
		if (!filterValue) return countries;
		
		const searchTerm = filterValue.toLowerCase();
		return countries.filter(country => 
			country.name.toLowerCase().includes(searchTerm) || 
			`+${country.phone_code}`.includes(searchTerm)
		);
	}, [filterValue]);
	
	// Find the index of the selected country for scrolling
	const selectedIndex = React.useMemo(() => {
		return filteredCountries.findIndex(country => country.iso2 === countryCode);
	}, [filteredCountries, countryCode]);
	
	// Scroll to selected country when dropdown opens
	React.useEffect(() => {
		if (openCommand && selectedIndex > 0 && listRef.current) {
			listRef.current.scrollToItem(selectedIndex, "center");
		}
	}, [openCommand, selectedIndex]);
	
	// Initialize component on mount
	React.useEffect(() => {
		// If there's a value prop, use it
		if (valueProp) {
			setInputValue(valueProp);
			const parsedNumber = parsePhoneNumberFromString(valueProp);
			if (parsedNumber?.country) {
				setCountryCode(parsedNumber.country);
			}
		} else {
			// Otherwise initialize with default country code
			const defaultValue = `+${selectedCountry?.phone_code || ""}`;
			setInputValue(defaultValue);
			// Update the input reference if available
			if (inputRef.current) {
				inputRef.current.value = defaultValue;
			}
		}
	}, []);
	
	// Function to format input as phone number
	const formatPhoneNumber = (value: string): string => {
		asYouType.reset();
		let formattedValue = value;
		
		// Ensure value starts with +
		if (!formattedValue.startsWith("+")) {
			formattedValue = `+${formattedValue}`;
		}
		
		return asYouType.input(formattedValue);
	};
	
	// Handle input changes
	const handleOnInput = (event: React.FormEvent<HTMLInputElement>) => {
		const value = event.currentTarget.value;
		const formattedValue = formatPhoneNumber(value);
		
		// Update state
		setInputValue(formattedValue);
		handlers.set(formattedValue);
		
		// Check if country code should be updated based on input
		const number = asYouType.getNumber();
		if (number?.country) {
			setCountryCode(number.country);
		}
		
		// Update input value with formatted value
		event.currentTarget.value = formattedValue;
	};
	
	// Handle paste events
	const handleOnPaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
		event.preventDefault();
		
		const clipboardData = event.clipboardData;
		if (clipboardData) {
			const pastedData = clipboardData.getData("text/plain");
			const formattedValue = formatPhoneNumber(pastedData);
			
			// Update state
			setInputValue(formattedValue);
			handlers.set(formattedValue);
			
			// Check if country code should be updated
			const number = asYouType.getNumber();
			if (number?.country) {
				setCountryCode(number.country);
			}
			
			// Update input value
			if (inputRef.current) {
				inputRef.current.value = formattedValue;
			}
		}
	};
	
	// Handle keyboard shortcuts (Ctrl+Z / Cmd+Z)
	const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
		if ((event.metaKey || event.ctrlKey) && event.key === "z") {
			handlers.back();
			if (
				inputRef.current &&
				history.current > 0 &&
				history.history[history.current - 1] !== undefined
			) {
				event.preventDefault();
				const previousValue = history.history[history.current - 1] || "";
				inputRef.current.value = previousValue;
				setInputValue(previousValue);
			}
		}
	};
	
	// Handle country selection
	const handleSelectCountry = (country: (typeof countries)[number]) => {
		// Update country code state
		setCountryCode(country.iso2 as CountryCode);
		
		// Get the new phone code
		const newPhoneCode = `+${country.phone_code}`;
		
		// Update the input value - preserving any national number
		// Extract the national number part if any
		let nationalPart = "";
		try {
			const currentNumber = parsePhoneNumberFromString(inputValue);
			if (currentNumber?.nationalNumber) {
				nationalPart = currentNumber.nationalNumber.toString();
			}
		} catch (e) {
			// If parsing fails, try to extract digits after the country code
			const digits = inputValue.replace(/\D/g, '');
			// Skip the country code digits if possible
			if (digits.length > 0) {
				const oldCountry = countries.find(c => c.iso2 === countryCode);
				if (oldCountry && digits.startsWith(oldCountry.phone_code)) {
					nationalPart = digits.substring(oldCountry.phone_code.length);
				} else {
					nationalPart = digits;
				}
			}
		}
		
		// Combine new phone code with national part
		let newValue = newPhoneCode;
		if (nationalPart) {
			newValue = `${newPhoneCode}${nationalPart}`;
		}
		
		// Format the new value
		asYouType.reset();
		const formattedValue = asYouType.input(newValue);
		
		// Update state
		setInputValue(formattedValue);
		handlers.set(formattedValue);
		
		// Update input element
		if (inputRef.current) {
			inputRef.current.value = formattedValue;
			inputRef.current.focus();
		}
		
		// Reset filter and close the dropdown
		setFilterValue("");
		setOpenCommand(false);
	};
	
	// Function to format country code for display
	const getDisplayCode = (code: string) => {
		switch(code) {
			case "NG": return "NGN";
			case "US": return "USA";
			case "GB": return "UK";
			case "GH": return "GHA";
			default: return code;
		}
	};
	
	return (
		<div className={cn("flex", className)}>
			<Popover open={openCommand} onOpenChange={setOpenCommand} modal={true}>
				<PopoverTrigger asChild disabled>
					<Button
						variant="outline"
						role="combobox"
						aria-expanded={openCommand}
						className="w-[110px] justify-between whitespace-nowrap rounded-r-none border-r-0 h-[34px]"
						type="button"
						
					>
						{selectedCountry ? (
							<span className="mr-1">{getDisplayCode(selectedCountry.iso2)} (+{selectedCountry.phone_code})</span>
						) : (
							"Select country"
						)}
						<ChevronsUpDown className="ml-auto size-4 shrink-0 opacity-50" />
					</Button>
				</PopoverTrigger>
				<PopoverContent className="p-0 w-[280px]" align="start">
					<Command>
						<CommandInput 
							placeholder="Search country..." 
							value={filterValue}
							onValueChange={setFilterValue}
							className="h-9"
						/>
						<CommandList>
							{filteredCountries.length === 0 ? (
								<CommandEmpty>No country found.</CommandEmpty>
							) : (
								<List
									ref={listRef}
									height={300}
									width="100%"
									itemCount={filteredCountries.length}
									itemSize={36}
									itemData={{
										countries: filteredCountries,
										countryCode,
										onSelectCountry: handleSelectCountry
									}}
								>
									{CountryItem}
								</List>
							)}
						</CommandList>
					</Command>
				</PopoverContent>
			</Popover>
			<input
				ref={inputRef}
				type="text"
				pattern="^(\+)?[0-9\s]*$"
				name="phone"
				id={id}
				placeholder="Phone"
				value={inputValue}
				onChange={(e) => {
					// Don't need to handle value state here as it's done in onInput
					// This is just to avoid React controlled component warning
				}}
				onInput={handleOnInput}
				onPaste={handleOnPaste}
				onKeyDown={handleKeyDown}
				required={required}
				aria-required={required}
				className="rounded-l-none h-[34px] w-full border rounded-[8px] text-[14px]"
				{...rest}
			/>
		</div>
	);
}