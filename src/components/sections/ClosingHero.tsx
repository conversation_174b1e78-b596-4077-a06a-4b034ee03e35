import { FC } from "react";
import { useTranslations } from 'next-intl';
import Link from "next/link";
import Image from "next/image";

const AIWorkModelsClosingHero: FC = () => {

  // use next-intl for i18n
  const t = useTranslations("Home.AIWorkModelsClosingHero");
  const tGlobal = useTranslations("global");

  return (
    <div className="px-[5%]">
    <div className="relative w-full flex flex-col bg-primary mx-auto max-w-[1300px] justify-center min-h-[350px] md:min-h-[400px] overflow-hidden rounded-lg">
      {/* Background Image with Overlay */}
      {/* <div 
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: 'url("/closingbanner.svg")',
          backgroundBlendMode: 'multiply',
          filter: 'brightness(0.4)'
        }}
      /> */}

      <div className="absolute inset-0 z-0 bg-primary">
        <Image
          src={"/closingbanner.svg"}
          alt=""
          fill
          style={{ objectFit: "cover" }}
          priority
        />
      </div>
      <div className="absolute inset-0 z-[1] bg-black opacity-[0.3]"></div>

      {/* Content */}
      <div className="relative h-full flex flex-col items-center justify-center text-center z-10 px-4">
        <h1 className="text-3xl md:text-4xl lg:text-[52px] font-bold text-white mb-2">
          {t("title1")}
        </h1>
        <h2 className="text-3xl md:text-4xl lg:text-[52px] font-bold text-white mb-6">
          {t("title2")}
        </h2>
        <Link
          href="/sign-up"
          className="bg-white text-gray-800 rounded-[8px] px-6 py-2  font-medium hover:bg-gray-100 transition-colors"
        >
          {tGlobal("GetStartedNow")}
        </Link>
      </div>
      </div>
    </div>
  );
};

export default AIWorkModelsClosingHero;
