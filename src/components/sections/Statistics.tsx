"use client";

import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";
import { useGetQueryHermes } from "@/services/api_hooks";
import MainHermescChart from "../MainHermescChart";

interface ModelMetric {
  title: string;
  value: string;
  color?: string;
  description: React.ReactNode;
}

interface ModelData {
  id: string;
  name: string;
  leftMetric: ModelMetric;
  rightMetric: ModelMetric;
}

export default function StatisticsSection() {
  const [activeModel, setActiveModel] = useState("orion");
  const [isPaused, setIsPaused] = useState(false);

  // use next-intl for i18n
  const t = useTranslations("Home.StatisticsSection");
  const tOrion = useTranslations("Home.StatisticsSection.models.Orion");
  const tHermesX = useTranslations("Home.StatisticsSection.models.HermesX");
  const tOlympus = useTranslations("Home.StatisticsSection.models.Olympus");

  const models: ModelData[] = [
    {
      id: "orion",
      name: tOrion("name"),
      leftMetric: {
        title: tOrion("leftMetric.title"),
        description: tOrion("leftMetric.description"),
        value: "98.9%",
        color: "#F7F8FF",
      },
      rightMetric: {
        title: tOrion("rightMetric.title"),
        description: tOrion("rightMetric.description"),
        value: "91.7%",
        color: "#FFFCF3",
      },
    },
    {
      id: "hermesx",
      name: "Hermes X",
      leftMetric: {
        title: tHermesX("leftMetric.title"),
        description: tHermesX("leftMetric.description"),
        color: "#F7F8FF",
        value: tHermesX("leftMetric.value"),
      },
      rightMetric: {
        title: tHermesX("rightMetric.title"),
        description: tHermesX("rightMetric.description"),
        color: "#F6FAF3",
        value: "99.9%",
      },
    },
    {
      id: "hermesc",
      name: "Hermes C",
      leftMetric: {
        title: "",
        value: "",
        description: <></>,
      },
      rightMetric: {
        title: "",
        value: "",
        description: <></>,
      },
    },
    {
      id: "olympus",
      name: "Olympus",
      leftMetric: {
        title: tOlympus("leftMetric.title"),
        description: tOlympus("leftMetric.description"),
        color: "#F7F8FF",
        value: "98%",
      },
      rightMetric: {
        title: tOlympus("rightMetric.title"),
        description: tOlympus("rightMetric.description"),
        color: "#FFFCF3",
        value: "91.7%",
      },
    },
  ];

  const {
    data: hermesData,
    isLoading,
    isFetching,
  } = useGetQueryHermes("/api/hermesc/constant", ["get-hermesc-constant"]);

  // Auto-scroll through models every 3 seconds (when not paused)
  useEffect(() => {
    if (isPaused) return;

    const interval = setInterval(() => {
      setActiveModel((prevModel) => {
        const currentIndex = models.findIndex(
          (model) => model.id === prevModel
        );
        const nextIndex = (currentIndex + 1) % models.length;
        return models[nextIndex].id;
      });
    }, 3000);

    return () => clearInterval(interval);
  }, [isPaused]);

  // Get the active model data
  const currentModel =
    models.find((model) => model.id === activeModel) || models[0];

  return (
    <section className="px-[5%]">
      <div className="flex flex-col mx-auto max-w-[1300px]">
        <div className="mb-6">
          <p className="rounded-full w-fit bg-[#D9DEFF] text-nowrap py-1 px-3 text-[14px] font-[500] mb-2">
            {t("subTitle")}
          </p>
          <h2 className="text-[30px] lg:text-[45px] font-[600]">
            {t("title.line1")}
            <br />
            {t("title.line2")}
          </h2>
        </div>

        <div className="flex flex-col md:flex-row justify-between gap-8 min-h-[403px]">
          {/* Model tabs */}
          <div className="md:max-w-[320px] w-full flex-shrink-0">
            <div className="flex flex-col items-start space-y-2 border-b md:border-b-0 mb-6 md:mb-0">
              {models.map((model) => (
                <button
                  key={model.id}
                  onClick={() => setActiveModel(model.id)}
                  onMouseEnter={() => setIsPaused(true)}
                  onMouseLeave={() => setIsPaused(false)}
                  className={`text-lg w-full cursor-pointer px-0 py-2 text-left focus:outline-none transition-all duration-500 ease-out ${
                    activeModel === model.id
                      ? "border-b-2 border-black font-medium"
                      : "text-gray-500"
                  }`}
                >
                  {model.name}
                </button>
              ))}
            </div>
          </div>

          {/* Cards or PerformanceChart */}
          <div
            className="flw transition-all duration-700 ease-in-out w-full"
            key={activeModel}
          >
            {activeModel === "hermesc" ? (
              <MainHermescChart
                data={hermesData || []}
                isLoading={isLoading}
                isFetching={isFetching}
                setIsPaused={setIsPaused}
              />
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 animate-in fade-in slide-in-from-bottom-4 duration-700">
                <Card
                  className="relative max-w-[470px] bg-[#F7F8FF] border-none rounded-[8px] shadow-none min-h-[270px] md:min-h-[310px] transition-colors duration-700 ease-out"
                  style={{ backgroundColor: currentModel.leftMetric.color }}
                  onMouseEnter={() => setIsPaused(true)}
                  onMouseLeave={() => setIsPaused(false)}
                >
                  <Image
                    src="/left_clock.svg"
                    alt="clock"
                    width={100}
                    height={100}
                    className="absolute right-0"
                  />
                  <CardContent className="p-4 lg:p-6">
                    <div className="text-[30px] lg:text-5xl font-bold mb-4">
                      {currentModel.leftMetric.value}
                    </div>
                    <div className="mb-2 text-[18px] leading-[32px]">
                      <span className="font-[700]">
                        {currentModel.leftMetric.title}
                      </span>{" "}
                      {currentModel.leftMetric.description}
                    </div>
                  </CardContent>
                </Card>

                <Card
                  className="relative max-w-[470px] bg-[#FFFCF3] border-none rounded-[8px] shadow-none min-h-[270px] md:min-h-[310px] transition-colors duration-700 ease-out"
                  style={{ backgroundColor: currentModel.rightMetric.color }}
                  onMouseEnter={() => setIsPaused(true)}
                  onMouseLeave={() => setIsPaused(false)}
                  key={activeModel}
                >
                  <Image
                    src="/left_dollar.svg"
                    alt="clock"
                    width={100}
                    height={100}
                    className="absolute right-0"
                  />
                  <CardContent className="p-4 lg:p-6">
                    <div className="text-[30px] lg:text-5xl font-bold mb-4 text-amber-800">
                      {currentModel.rightMetric.value}
                    </div>
                    <div className="mb-2 text-[18px] leading-[32px]">
                      <span className="font-[700]">
                        {currentModel.rightMetric.title}
                      </span>{" "}
                      {currentModel.rightMetric.description}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
