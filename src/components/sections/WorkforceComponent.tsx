import React from "react";

function Workforce() {
  return (
    <section className="    px-[5%]  text-white">
      <div className="mx-auto max-w-[1300px] space-y-10">
        <div className="space-y-10">
          <div className=" text-white text-[30px] md:text-[50px] font-[600]">
            Your Future Ready Hybrid Workforce
          </div>
          <div className="text-[14px] md:text-[18px] font-[500]">
            We create scalable, AI-driven teammates that handle the heavy
            lifting, enabling human teams
            <br /> to adapt, upskill, and thrive in an evolving workplace.
          </div>
        </div>

        {/* Features grid - responsive */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-0 gap-4">
          {/* Feature 1 */}
          <div className="text-white space-y-3 w-full border-b-2 py-10 md:border-r-2 lg:pb-5 pr-0 md:pr-5">
            <div className="w-6 h-6 overflow-hidden">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M22.7 13.5L20.7005 11.5L18.7 13.5M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C15.3019 3 18.1885 4.77814 19.7545 7.42909M12 7V12L15 14"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <div className="self-stretch justify-start text-white text-[18px] md:text-[20px] font-semibold">
              Real time insights
            </div>
            <div className="self-stretch justify-start text-white text-[14px] md:text-[18px] font-medium leading-relaxed md:leading-loose">
              Stay ahead with AI-driven market analysis and financial
              forecasting.
            </div>
          </div>

          {/* Feature 2 */}
          <div className="text-white space-y-3 w-full border-b-2  py-10 lg:pb-5  md:pl-5 pr-0 md:pr-5">
            <div className="w-6 h-6 relative overflow-hidden">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M21 21H4.6C4.03995 21 3.75992 21 3.54601 20.891C3.35785 20.7951 3.20487 20.6422 3.10899 20.454C3 20.2401 3 19.9601 3 19.4V3M21 7L15.5657 12.4343C15.3677 12.6323 15.2687 12.7313 15.1545 12.7684C15.0541 12.8011 14.9459 12.8011 14.8455 12.7684C14.7313 12.7313 14.6323 12.6323 14.4343 12.4343L12.5657 10.5657C12.3677 10.3677 12.2687 10.2687 12.1545 10.2316C12.0541 10.1989 11.9459 10.1989 11.8455 10.2316C11.7313 10.2687 11.6323 10.3677 11.4343 10.5657L7 15M21 7H17M21 7V11"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <div className="self-stretch justify-start text-white text-[18px] md:text-[20px] font-semibold">
              AI Handles the Work, You Focus on Growth
            </div>
            <div className="self-stretch justify-start text-white text-[14px] md:text-[18px] font-medium leading-relaxed md:leading-loose">
              Let your agents take care of the heavy lifting while you focus on
              strategy, innovation, and what matters most.
            </div>
          </div>

          {/* Feature 3 */}
          <div className="text-white space-y-3 w-full border-b-2 py-10 lg:pb-5 lg:border-l-2 lg:pl-5 pr-0 md:pr-5">
            <div className="w-6 h-6 relative overflow-hidden">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M12 16V21M18 21L14.0486 17.7072C13.3198 17.0998 12.9554 16.7962 12.5487 16.6801C12.19 16.5778 11.81 16.5778 11.4513 16.6801C11.0446 16.7962 10.6802 17.0998 9.95141 17.7072L6 21M8 11V12M12 9V12M16 7V12M22 3H2M3 3H21V11.2C21 12.8802 21 13.7202 20.673 14.362C20.3854 14.9265 19.9265 15.3854 19.362 15.673C18.7202 16 17.8802 16 16.2 16H7.8C6.11984 16 5.27976 16 4.63803 15.673C4.07354 15.3854 3.6146 14.9265 3.32698 14.362C3 13.7202 3 12.8802 3 11.2V3Z"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <div className="self-stretch justify-start text-white text-[18px] md:text-[20px] font-semibold">
              Business Intelligence
            </div>
            <div className="self-stretch justify-start text-white text-[14px] md:text-[18px] font-medium leading-relaxed md:leading-loose">
              Make data-backed decisions with AI-optimized recommendations.
            </div>
          </div>

          {/* Feature 4 */}
          <div className="text-white space-y-3 w-full pt-2 md:pt-7 md:col-span-2 py-10 lg:col-span-1 lg:border-r-2">
            <div className="w-6 h-6 relative overflow-hidden">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M9 11.5001L11 13.5001L15.5 9.00011M20 12.0001C20 16.9086 14.646 20.4785 12.698 21.615C12.4766 21.7442 12.3659 21.8087 12.2097 21.8422C12.0884 21.8682 11.9116 21.8682 11.7903 21.8422C11.6341 21.8087 11.5234 21.7442 11.302 21.615C9.35396 20.4785 4 16.9086 4 12.0001V7.21772C4 6.4182 4 6.01845 4.13076 5.67482C4.24627 5.37126 4.43398 5.10039 4.67766 4.88564C4.9535 4.64255 5.3278 4.50219 6.0764 4.22146L11.4382 2.21079C11.6461 2.13283 11.75 2.09385 11.857 2.07839C11.9518 2.06469 12.0482 2.06469 12.143 2.07839C12.25 2.09385 12.3539 2.13283 12.5618 2.21079L17.9236 4.22146C18.6722 4.50219 19.0465 4.64255 19.3223 4.88564C19.566 5.10039 19.7537 5.37126 19.8692 5.67482C20 6.01845 20 6.4182 20 7.21772V12.0001Z"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <div className="self-stretch justify-start text-white text-[18px] md:text-[20px] font-semibold">
              Secure & Reliable
            </div>
            <div className="self-stretch justify-start text-white text-[14px] md:text-[18px] font-medium leading-relaxed md:leading-loose">
              Designed with enterprise-grade security and accuracy.
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default Workforce;
