// TransactionTable.tsx
"use client";

import { useEffect, useMemo, useState } from "react";
import { useTranslations } from "next-intl";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alog<PERSON>eader,
  <PERSON>alogTitle,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  ChevronLeft,
  ChevronRight,
  CreditCard,
  Wallet,
  Building,
  X,
  Copy,
  Loader,
} from "lucide-react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useGetQuery, useSubmitQuery } from "@/services/api_hooks";
import { toast } from "sonner";
import MoonLoader from "react-spinners/MoonLoader";
import QRCode from "react-qr-code";
import moment from "moment";
import TableComponent from "./table";

interface Transaction {
  type: string;
  timestamp: string;
  amount: string;
  reason: string;
}

const MY_WALLET_ADDRESS = "******************************************";

// Token contract addresses on Ethereum mainnet
const TOKEN_CONTRACTS: any = {
  USDT: "******************************************",
  USDC: "******************************************",
};

export default function TransactionTable({ search }: any) {
  // use next-intl for i18n
  const t = useTranslations("DashboardCredits.TransactionTable");
  const tGlobal = useTranslations("global");

  const [currentPage, setCurrentPage] = useState(1);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [numCredits, setNumCredits] = useState<string>("");
  const [amount, setAmount] = useState<string>("");
  const [dialogStep, setDialogStep] = useState<
    "topUp" | "payment" | "coinSelection" | "scanToPay" | "walletAddress"
  >("topUp");
  const [selectedCoin, setSelectedCoin] = useState<string>("");
  const [walletAddress, setWalletAddress] = useState<string>("");
  const [paymentMethod, setPaymentMethod] = useState<string>("usd");

  const [limit, setLimit] = useState<string>("10");

  const [formattedAmount, setFormattedAmount] = useState("");

  // 2. Replace the old API call with useGetQuery
  const { data, isLoading } = useGetQuery(
    `/api/payment/history?page=${currentPage}&limit=${limit}&search=${search}`,
    ["payment-history", currentPage, limit, search],
    {
      onError() {
        toast.error(t("toast.notLoadTransactionHistory"));
      },
    }
  );

  // Wallet address API call
  const { mutateAsync: saveWallet, isPending: savingWallet } = useSubmitQuery(
    "/api/payment/wallet",
    "POST",
    {
      onSuccess(response: any) {
        toast.success(t("toast.WalletAddressSavedSuccessfully"), {
          position: "top-right",
          className: "p-4",
        });
        setDialogStep("scanToPay");
      },
      onError(err: any) {
        toast.error(err.response?.data?.message || t("toast.FailedToSave"), {
          position: "top-right",
          className: "p-4",
        });
      },
    }
  );

  const handlePayment = async () => {
    // If stable coins is selected and coin isn't selected yet, show coin selection
    if (paymentMethod === "stable" && dialogStep !== "coinSelection") {
      setDialogStep("coinSelection");
      return;
    }

    const data = {
      credits_amount: numCredits,
      total_usd: numCredits,
      currency: "USD",
      type: selectedCoin || "Stable Coin",
    };

    if (paymentMethod === "stable" && selectedCoin) {
      // Instead of calling pay, proceed to wallet address screen
      setDialogStep("walletAddress");
    }
  };

  const handleCoinSelection = (coinType: string) => {
    setSelectedCoin(coinType);
  };

  const handleWalletAddressSave = async () => {
    if (!walletAddress.trim()) {
      toast.error("Please enter a wallet address", {
        position: "top-right",
        className: "p-4",
      });
      return;
    }

    await saveWallet({
      wallet_address: walletAddress,
    });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success(t("toast.AddressCopied"), {
      position: "top-right",
      className: "p-4",
    });
  };

  // Format number with dollar sign and commas
  const formatCurrency = (value: any) => {
    if (!value) return "";

    // Remove non-numeric characters
    const numericValue = value.toString().replace(/[^\d.]/g, "");

    // Format with commas and dollar sign
    const formatted = new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(numericValue);

    return formatted;
  };

  // Update formatted amount whenever numCredits changes
  useEffect(() => {
    setFormattedAmount(formatCurrency(numCredits));
  }, [numCredits]);

  // Handle input change for the amount field
  const handleAmountChange = (e: any) => {
    // Extract only numeric values
    const value = e.target.value.replace(/[^\d.]/g, "");
    setNumCredits(value);
  };

  const transactions = data?.history || [];
  const pagination = data?.pagination;

  const handlePageChange = (direction: string) => {
    if (direction === "next") {
      setCurrentPage(currentPage + 1);
    } else if (direction === "prev") {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleTopUpClick = () => {
    setIsDialogOpen(true);
    setDialogStep("topUp");
  };

  const handleProceed = () => {
    if (dialogStep === "topUp") {
      setDialogStep("payment");
    } else if (dialogStep === "coinSelection") {
      // After selecting coin and clicking proceed, move to wallet address step
      setDialogStep("walletAddress");
    } else if (dialogStep === "walletAddress") {
      // Save wallet address and proceed to QR code scan
      handleWalletAddressSave();
    } else {
      // Handle the payment completion logic here
      setIsDialogOpen(false);
      setDialogStep("topUp");
    }
  };

  const handleCancel = () => {
    if (dialogStep === "payment") {
      setDialogStep("topUp");
    } else if (dialogStep === "coinSelection") {
      setDialogStep("payment");
      setSelectedCoin("");
    } else if (dialogStep === "walletAddress") {
      setDialogStep("coinSelection");
    } else if (dialogStep === "scanToPay") {
      setDialogStep("walletAddress");
    } else {
      setIsDialogOpen(false);
    }
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setDialogStep("topUp");
    setSelectedCoin("");
    setWalletAddress("");
  };

  const handleFinish = () => {
    setIsDialogOpen(false);
    setDialogStep("topUp");
    setSelectedCoin("");
    setWalletAddress("");
    toast.success(t("toast.PaymentProcessCompleted"), {
      position: "top-right",
      className: "p-4",
    });
  };

  const generatePaymentURI = () => {
    // For ERC-20 tokens like USDT/USDC
    if (selectedCoin) {
      const tokenAddress = TOKEN_CONTRACTS[selectedCoin];
      // Format: ethereum:tokenAddress/transfer?address=receiverAddress&uint256=amount
      let uri = `ethereum:${tokenAddress}/transfer?address=${MY_WALLET_ADDRESS}`;

      // Add amount if specified (convert to token units - both USDT and USDC use 6 decimals)
      if (numCredits) {
        const amountInTokenUnits = Math.floor(
          parseFloat(numCredits) * 1000000
        ).toString();
        uri += `&uint256=${amountInTokenUnits}`;
      }

      return uri;
    }
    // For ETH
    else {
      // Format: ethereum:address?value=amountInEther
      let uri = `ethereum:${MY_WALLET_ADDRESS}`;

      if (amount) {
        uri += `?value=${amount}`;
      }

      return uri;
    }
  };

  return (
    <div className="w-full">
      <TableComponent
        transactions={transactions}
        pagination={pagination}
        isLoading={isLoading}
        onTopUpClick={handleTopUpClick}
        onPageChange={handlePageChange}
      />

      <Dialog open={isDialogOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle className="text-[18px] font-[500]">
              {dialogStep === "topUp"
                ? t("TopUpWallet")
                : dialogStep === "payment"
                ? `${t("ForPayment")} ${formattedAmount}`
                : dialogStep === "coinSelection"
                ? t("SelectCoinType")
                : dialogStep === "walletAddress"
                ? t("WalletAddress")
                : t("ScantoPay")}
            </DialogTitle>
          </DialogHeader>

          {dialogStep === "topUp" && (
            /* Top Up Step */
            <div className="space-y-6">
              <div className="bg-[#0F172A] text-white h-[116px] p-4 rounded-t-md flex flex-col justify-center">
                <p className="text-sm">{t("PayAsYouGo")}</p>
                <div className="text-xl font-medium mt-1 flex flex-row gap-1">
                  <p className="text-[32px]">{t("pay.text1")}</p>
                  <span className="text-[14px] font-[400]">
                    {t("pay.text2")}
                  </span>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <label
                    htmlFor="credits"
                    className="text-sm font-medium text-gray-700"
                  >
                    {t("NumberOfCredits")}
                  </label>
                  <Input
                    id="credits"
                    type="number"
                    value={numCredits}
                    placeholder="10"
                    onChange={(e) => setNumCredits(e.target.value)}
                    className="border-b border-t-0 border-l-0 border-r-0 rounded-none focus:outline-none focus:ring-0 focus-visible:ring-transparent outline-none ring-transparent shadow-none focus:shadow-none"
                  />
                </div>

                <div className="space-y-2">
                  <label
                    htmlFor="amount"
                    className="text-sm font-medium text-gray-700"
                  >
                    {t("EnterAmount")}
                  </label>
                  <div className="relative">
                    <span className="absolute inset-y-0 left-0 flex items-center"></span>
                    <Input
                      id="amount"
                      type="text"
                      value={formattedAmount}
                      onChange={handleAmountChange}
                      className="pl-4 border-b border-t-0 border-l-0 border-r-0 rounded-none focus:outline-none focus:ring-transparent focus-visible:ring-transparent outline-none ring-transparent shadow-none focus:shadow-none"
                      placeholder="$10"
                    />
                  </div>
                </div>
              </div>

              <Button
                className="w-full bg-[#0F172A] hover:bg-[#1E293B] text-white disabled:opacity-[0.5]"
                onClick={handleProceed}
                disabled={numCredits === "0" || !numCredits}
              >
                {tGlobal("Proceed")}
              </Button>
            </div>
          )}

          {dialogStep === "payment" && (
            <div className="space-y-6">
              <RadioGroup
                value={paymentMethod}
                onValueChange={setPaymentMethod}
                className="space-y-4"
              >
                <div
                  className={`flex items-center justify-between rounded-lg border p-4 ${
                    paymentMethod === "usd"
                      ? "border-primary"
                      : "border-gray-200"
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="bg-[#0F172A] p-2 rounded-full">
                      <CreditCard className="h-5 w-5 text-white" />
                    </div>
                    <Label htmlFor="usd" className="font-medium">
                      {t("PayWithUSDCard")}
                    </Label>
                  </div>
                  <RadioGroupItem
                    value="usd"
                    id="usd"
                    className={paymentMethod === "usd" ? " text-green-500" : ""}
                  />
                </div>

                <div
                  className={`flex items-center justify-between rounded-lg border p-4 ${
                    paymentMethod === "stable"
                      ? "border-primary"
                      : "border-gray-200"
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="bg-[#0F172A] p-2 rounded-full">
                      <Wallet className="h-5 w-5 text-white" />
                    </div>
                    <Label htmlFor="stable" className="font-medium">
                      {t("PayWithStableCoins")}
                    </Label>
                  </div>
                  <RadioGroupItem
                    value="stable"
                    id="stable"
                    className={
                      paymentMethod === "stable" ? " text-green-500" : ""
                    }
                  />
                </div>

                <div
                  className={`flex items-center justify-between rounded-lg border p-4 ${
                    paymentMethod === "bank"
                      ? "border-primary"
                      : "border-gray-200"
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="bg-[#0F172A] p-2 rounded-full">
                      <Building className="h-5 w-5 text-white" />
                    </div>
                    <Label htmlFor="bank" className="font-medium">
                      {t("PayWithBank")}
                    </Label>
                  </div>
                  <RadioGroupItem
                    value="bank"
                    id="bank"
                    className={
                      paymentMethod === "bank" ? " text-green-500" : ""
                    }
                  />
                </div>
              </RadioGroup>

              <div className="flex justify-between pt-4">
                <Button variant="outline" onClick={handleCancel}>
                  {tGlobal("Cancel")}
                </Button>
                <Button
                  className="bg-[#0F172A] hover:bg-[#1E293B] text-white px-6"
                  onClick={handlePayment}
                >
                  {tGlobal("Proceed")}
                  {/* <MoonLoader color="white" size={15} loading={loading} /> */}
                </Button>
              </div>
            </div>
          )}

          {dialogStep === "coinSelection" && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div
                  className={`flex items-center justify-between p-4 rounded-lg border ${
                    selectedCoin === "USDT"
                      ? "border-primary"
                      : "border-gray-200"
                  }`}
                  onClick={() => handleCoinSelection("USDT")}
                >
                  <div className="flex items-center space-x-3">
                    <div className="bg-black p-2 h-10 w-10 grid place-content-center rounded-full">
                      <span className="text-white font-bold">
                        {t("payUSDT.T")}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium">{t("payUSDT.text1")}</p>
                      <p className="text-sm text-gray-500">
                        {t("payUSDT.text2")}
                      </p>
                    </div>
                  </div>
                  <input
                    type="radio"
                    checked={selectedCoin === "USDT"}
                    onChange={() => {}}
                    className="h-4 w-4"
                  />
                </div>

                <div
                  className={`flex items-center justify-between p-4 rounded-lg border ${
                    selectedCoin === "USDC"
                      ? "border-primary"
                      : "border-gray-200"
                  }`}
                  onClick={() => handleCoinSelection("USDC")}
                >
                  <div className="flex items-center space-x-3">
                    <div className="bg-blue-500  p-2 h-10 w-10 grid place-content-center rounded-full">
                      <span className="text-white font-bold">
                        {t("payUSDC.C")}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium">{t("payUSDC.text1")}</p>
                      <p className="text-sm text-gray-500">
                        {t("payUSDC.text2")}
                      </p>
                    </div>
                  </div>
                  <input
                    type="radio"
                    checked={selectedCoin === "USDC"}
                    onChange={() => {}}
                    className="h-4 w-4"
                  />
                </div>

                {/* Bitcoin option removed as requested */}
              </div>

              <div className="flex justify-between mt-4">
                <Button variant="outline" onClick={handleCancel}>
                  {tGlobal("Back")}
                </Button>
                <Button
                  className="bg-[#0F172A] hover:bg-[#1E293B] text-white flex gap-1"
                  onClick={handleProceed}
                  disabled={!selectedCoin}
                >
                  {tGlobal("Proceed")}
                  <div className="flex">
                    {/* <MoonLoader color="white" size={15} loading={loading} /> */}
                  </div>
                </Button>
              </div>
            </div>
          )}

          {dialogStep === "walletAddress" && (
            <div className="space-y-6">
              <div className="space-y-2 border border-blue-100 rounded-lg p-4 bg-blue-50">
                <p className="text-sm text-gray-600">
                  {t("walletAddressDescription")}
                </p>
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="walletAddress"
                  className="text-sm font-medium text-gray-700"
                >
                  {t("WalletAddress")}
                </label>
                <Input
                  id="walletAddress"
                  value={walletAddress}
                  onChange={(e) => setWalletAddress(e.target.value)}
                  className="w-full"
                  placeholder="Enter your wallet address"
                />
              </div>

              <div className="flex justify-between pt-4">
                <Button variant="outline" onClick={handleCancel}>
                  {tGlobal("Back")}
                </Button>
                <Button
                  className="bg-[#0F172A] hover:bg-[#1E293B] text-white px-6"
                  onClick={handleProceed}
                  disabled={!walletAddress.trim()}
                >
                  {tGlobal("Continue")}
                  <MoonLoader color="white" size={15} loading={savingWallet} />
                </Button>
              </div>
            </div>
          )}

          {dialogStep === "scanToPay" && (
            <div className="space-y-6">
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  {t("scanToPayDescription")}
                </p>
              </div>

              <div className="flex justify-center p-4 bg-gray-50 rounded-lg">
                <QRCode
                  value={generatePaymentURI()}
                  size={256}
                  style={{ height: "auto", maxWidth: "100%", width: "100%" }}
                  viewBox={`0 0 256 256`}
                  level="H"
                  bgColor="#FFFFFF"
                  fgColor="#000000"
                />
              </div>

              <div className="text-center text-sm text-gray-600">
                <p>{tGlobal("ai-wk")}</p>
                <p className="text-xs mt-1 break-all">{MY_WALLET_ADDRESS}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2 flex items-center gap-1 mx-auto text-blue-700"
                  onClick={() => copyToClipboard(MY_WALLET_ADDRESS)}
                >
                  <Copy className="h-4 w-4" color="blue" /> {t("CopyAddress")}
                </Button>
              </div>

              <div className="flex justify-between pt-4">
                <Button variant="outline" onClick={handleCancel}>
                  {tGlobal("Back")}
                </Button>
                <Button
                  className="bg-[#0F172A] hover:bg-[#1E293B] text-white px-6"
                  onClick={handleFinish}
                >
                  {tGlobal("Finish")}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
