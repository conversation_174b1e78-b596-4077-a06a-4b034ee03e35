import { useState, useRef, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useBottomObserver } from "@/hooks/useBottomObserver";
import { useTranslations } from 'next-intl';
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ChevronLeft, ChevronRight } from "lucide-react";
import moment from "moment";
import { api } from "@/services/api_hooks";

interface Transaction {
  type: string;
  timestamp: string;
  amount: string;
  reason: string;
  plan?: string;
  start_date?: string;
  end_date?: string;
}

interface Pagination {
  total: number;
  totalPages: number;
  currentPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface TransactionTableProps {
  transactions: Transaction[];
  pagination?: Pagination;
  isLoading: boolean;
  onTopUpClick: () => void;
  onPageChange?: (direction: string) => void;
}

// Skeleton loader component
const TableSkeleton = () => (
  <TableBody>
    {Array.from({ length: 5 }).map((_, index) => (
      <TableRow key={index}>
        <TableCell>
          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
        </TableCell>
        <TableCell>
          <div className="h-4 bg-gray-200 rounded animate-pulse w-16"></div>
        </TableCell>
        <TableCell>
          <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
        </TableCell>
        <TableCell>
          <div className="h-4 bg-gray-200 rounded animate-pulse w-32"></div>
        </TableCell>
      </TableRow>
    ))}
  </TableBody>
);

// Simplified pagination component with only next/prev
const Pagination = ({ pagination, onPageChange }: any) => {

  const tTable = useTranslations("Table");

  const { currentPage, totalPages, hasNextPage, hasPrevPage } = pagination || {};

  if (totalPages <= 1) return null;

  return (
    <div className="flex items-center justify-between px-4 py-3 bg-white border-t">
      <div className="flex items-center text-sm text-gray-500">
        {tTable("Page")} {currentPage} {tTable("of")} {totalPages}
      </div>
      
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange("prev")}
          disabled={!hasPrevPage}
          className="flex items-center space-x-1 px-3 py-1 h-8"
        >
          <ChevronLeft className="h-4 w-4" />
          <span className="text-sm">{tTable("Previous")}</span>
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange("next")}
          disabled={!hasNextPage}
          className="flex items-center space-x-1 px-3 py-1 h-8"
        >
          <span className="text-sm">{tTable("Next")}</span>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default function TableComponent({ 
  transactions, 
  pagination, 
  isLoading, 
  onTopUpClick,
  onPageChange 
}: TransactionTableProps) {

  const t = useTranslations("DashboardCredits.TransactionTable");
  const tTable = useTranslations("Table");
  const queryClient = useQueryClient();
  const lastRowRef = useRef<HTMLTableRowElement | null>(null);

  // Prefetch next page when last row is in view and hasNextPage
  const prefetchNextPage = useCallback(() => {
    if (!pagination) return;
    queryClient.prefetchQuery({
      queryKey: ["payment-history", pagination.currentPage + 1, "10", ""],
      queryFn: async () => {
        const res = await api.get(`/api/payment/history?page=${pagination.currentPage + 1}&limit=10&search=`);
        return res.data;
      },
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 60, // 1 hour
    });
  }, [pagination, queryClient]);

  useBottomObserver(lastRowRef as React.RefObject<Element>, prefetchNextPage, Boolean(pagination && pagination.hasNextPage));

  return (
    <div className="w-full">
      <div className="rounded-md border">
        {/* Add horizontal scroll wrapper */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-[#FAFAFA]">
                <TableHead className="min-w-[150px]">{tTable("Date")}</TableHead>
                <TableHead className="min-w-[100px]">{tTable("Amount")}</TableHead>
                <TableHead className="min-w-[80px]">{tTable("Type")}</TableHead>
                <TableHead className="min-w-[200px]">{tTable("Narration")}</TableHead>
              </TableRow>
            </TableHeader>
            {isLoading ? (
              <TableSkeleton />
            ) : transactions?.length > 0 ? (
              <TableBody>
                {transactions.map((transaction, index) => (
                  <TableRow
                    key={index}
                    ref={index === transactions.length - 1 ? lastRowRef : undefined}
                  >
                    <TableCell className="min-w-[150px]">
                      {moment(transaction.timestamp).format("YYYY-MM-DD HH:mm:ss")}
                    </TableCell>
                    <TableCell className="min-w-[100px]">
                      <span className={`font-medium ${
                        transaction.amount.startsWith('-') 
                          ? 'text-red-600' 
                          : 'text-green-600'
                      }`}>
                        {transaction.amount.startsWith('-') ? '' : '+'}
                        {transaction.amount}
                      </span>
                    </TableCell>
                    <TableCell className="min-w-[80px]">
                      <span className="capitalize bg-gray-100 px-2 py-1 rounded-full text-xs">
                        {transaction.type}
                      </span>
                    </TableCell>
                    <TableCell className="min-w-[200px]">{transaction.reason}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            ) : (
              <TableBody>
                <TableRow>
                  <TableCell colSpan={4} className="text-center">
                    <div className="flex flex-col items-center justify-center py-16">
                      <h3 className="text-lg font-medium">{t("NoFundingRecord.text")}</h3>
                      <p className="text-sm text-gray-500 mt-1 mb-6">
                        {t("NoFundingRecord.description")}
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            )}
          </Table>
        </div>
        {/* Simplified Pagination */}
        {pagination && onPageChange && (
          <Pagination pagination={pagination} onPageChange={onPageChange} />
        )}
      </div>
    </div>
  );
}