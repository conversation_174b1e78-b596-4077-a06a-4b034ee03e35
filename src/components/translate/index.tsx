//@ts-nocheck

// src/components/T.tsx
'use client';

import { useTranslation } from '@/hooks/useTranslation';

interface TProps {
  children: string;
  as?: keyof JSX.IntrinsicElements;
  [key: string]: any;
}

export function T({ children, as: Element = 'span', ...props }: TProps) {
  const translatedText = useTranslation(children);
  const Component = Element as any;
  
  return <Component {...props}>{translatedText}</Component>;
}