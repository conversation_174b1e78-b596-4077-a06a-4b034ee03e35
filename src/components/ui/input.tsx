"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON>con, EyeOff, UserIcon } from "lucide-react";
import Image from "next/image";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  value?:any;
  onChange?: (e:any) => void;
  iconLeft?: string;
  iconRight?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, iconLeft, iconRight, type, label, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false);

    const paddingLeft = iconLeft ? "pl-10" : "";
    const paddingRight = iconRight ? "" : "";

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    const inputType = type === "password" && showPassword ? "text" : type;
    return (
      <div>
        <label className="text-sm text-[#5E5989] py-2">{label}</label>
        <div className="relative">
          {iconLeft && (
            <Image
              src={iconLeft}
              alt="icon"
              width={0}
              height={0}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 z-10 h-4 w-4"
            />
          )}
          {iconRight && (
            <Image
              src={iconRight}
              alt="icon"
              width={0}
              height={0}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 z-10 h-4 w-4"
            />
          )}
          <input
            type={inputType}
            className={cn(
              `flex h-10 w-full rounded-[8px] border px-3 pr-3 ${paddingLeft} ${paddingRight} py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 `,
              className
            )}
            ref={ref}
            {...props}
          />
          {type === "password" && (
            <button
              type="button"
              className="absolute right-3 top-1/2 -translate-y-1/2"
              onClick={togglePasswordVisibility}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-gray-500" />
              ) : (
                <EyeIcon className="h-4 w-4 text-gray-500" />
              )}
            </button>
          )}
        </div>
      </div>
    );
  }
);

Input.displayName = "Input";

export { Input };