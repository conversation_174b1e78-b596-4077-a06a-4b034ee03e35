// Environment-based URL configuration
const isDevelopment = process.env.NEXT_PUBLIC_NODE_ENV === "development";
const isProduction = process.env.NEXT_PUBLIC_NODE_ENV === "production";
const isStaging = !isDevelopment && !isProduction; // anything else is staging

// Main Backend URL
const backendUrl = isProduction
  ? "https://backend.ai-wk.com"
  : isDevelopment
  ? "https://ai-wk-backend-dev.vercel.app"
  : "https://ai-wk-backend-staging.vercel.app";

// Hermes Backend URLs
const hermesBackendUrl = isProduction
  ? "https://hermes-backend.ai-wk.com"
  : isDevelopment
  ? "https://hermes-x-backend-dev.vercel.app"
  : "https://hermes-x-backend-staging.vercel.app";

// Orion Backend URLs
const orionBackendUrl = isProduction
  ? "https://orion-backend.ai-wk.com"
  : isDevelopment
  ? "https://orion-backend-dev.vercel.app"
  : "https://orion-backend-staging.vercel.app";

// Yumi Backend URLs
const yumiBackendUrl = isProduction
  ? "https://yumi-backend.ai-wk.com"
  : isDevelopment
  ? "https://yumi-backend-dev.vercel.app"
  : "https://yumi-backend-staging.vercel.app";

const telegramBot = isProduction
  ? "https://t.me/ai_wk_bot"
  : isDevelopment
  ? "https://t.me/aiwk_dev_bot"
  : "https://t.me/aiwk_staging_bot";

const botName = isProduction
  ? "ai_wk_bot"
  : isDevelopment
  ? "aiwk_dev_bot"
  : "aiwk_staging_bot";

const orion_base_url = isProduction
  ? "https://orion.ai-wk.com/api"
  : isDevelopment
  ? "https://orion-dev.ai-wk.com/api"
  : "https://orion-staging.ai-wk.com/api";

const sandboxUrl = isProduction
  ? "https://yumisandboxprod-production.up.railway.app/api"
  : isDevelopment
  ? "https://yumisandbox-production.up.railway.app/api"
  : "https://yumisandboxstag-production.up.railway.app/api";

const olympusUrl = isProduction
  ? "https://olympus-dev.ai-wk.com"
  : isDevelopment
  ? "https://olympus-dev.ai-wk.com"
  : "https://olympus-dev.ai-wk.com";

const olympusUrl2 = isProduction
  ? "olympus-backend.ai-wk.com"
  : isDevelopment
  ? "https://olympus-backend-dev.vercel.app"
  : "https://olympus-backend-dev.vercel.app";

const sandboxEnv = isProduction ? "prod" : isDevelopment ? "dev" : "stag";

const freddieFrontendUrl = isProduction
  ? "https://freddie.ai-wk.com"
  : isDevelopment
  ? "https://freddie-frontend-dev.vercel.app"
  : "https://freddie-frontend-staging.vercel.app";

const callCenterUrl = isProduction
  ? "yumisandboxprod-production.up.railway.app"
  : isDevelopment
  ? "yumisandbox-production.up.railway.app"
  : "yumisandboxstag-production.up.railway.app";

export {
  backendUrl,
  hermesBackendUrl,
  orionBackendUrl,
  yumiBackendUrl,
  olympusUrl,
  olympusUrl2,
  telegramBot,
  botName,
  orion_base_url,
  sandboxUrl,
  sandboxEnv,
  freddieFrontendUrl,
  callCenterUrl,
};
