import { useEffect } from "react";

/**
 * useBottomObserver
 * Calls onIntersect when the ref element is in view and enabled is true.
 * @param ref - React ref to the DOM element to observe
 * @param onIntersect - Callback to call when element is in view
 * @param enabled - Whether to enable the observer
 */
export function useBottomObserver(
  ref: React.RefObject<Element>,
  onIntersect: () => void,
  enabled: boolean = true
) {
  useEffect(() => {
    if (!enabled) return;
    const node = ref.current;
    if (!node) return;
    const observer = new window.IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        onIntersect();
      }
    }, { threshold: 0.1 });
    observer.observe(node);
    return () => {
      observer.unobserve(node);
    };
  }, [ref, onIntersect, enabled]);
}
