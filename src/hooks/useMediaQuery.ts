import { useState, useEffect } from "react";

const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(() => window.matchMedia(query).matches);

  useEffect(() => {
    const mediaQueryList = window.matchMedia(query);
    const updateMatch = () => setMatches(mediaQueryList.matches);

    updateMatch(); // Set initial match value

    mediaQueryList.addEventListener("change", updateMatch, { passive: true });

    return () => mediaQueryList.removeEventListener("change", updateMatch);
  }, [query]);

  return matches;
};

export default useMediaQuery;
