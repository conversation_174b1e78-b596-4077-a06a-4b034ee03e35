import { useGetQuery } from "@/services/api_hooks";


interface ProfileData {
  // Define your profile data structure here
  user_id: string;
  name: string;
  email: string;
  firstname: string;
  lastname: string;
  // Add other profile fields as needed
}

interface UseProfileReturn {
  profile: ProfileData | undefined;
  isLoading: boolean;
  error: any;
  refetch: () => void;
}

export const useProfile = (): UseProfileReturn => {
  const { data: profile, isLoading, error, refetch } = useGetQuery(
    "/auth/profile",
    ["profile"],
    {
      onError() {
        // toast.error("Could not load your profile");
      },
    }
  );

  return {
    profile: profile?.profile,
    isLoading,
    error,
    refetch,
  };
};