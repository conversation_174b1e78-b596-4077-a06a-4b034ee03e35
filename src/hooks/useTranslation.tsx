// src/hooks/useTranslation.ts
'use client';

import { useState, useEffect } from 'react';
import { useLanguage } from '@/Providers/LanguageProvider';

const cache = new Map<string, string>();

export function useTranslation(text: string): string {
  const { currentLanguage } = useLanguage();
  const [translatedText, setTranslatedText] = useState(text);

  useEffect(() => {
    if (currentLanguage === 'en' || !text) {
      setTranslatedText(text);
      return;
    }

    const key = `${text}-${currentLanguage}`;
    
    if (cache.has(key)) {
      setTranslatedText(cache.get(key)!);
      return;
    }

    fetch('/api/translate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text, targetLang: currentLanguage })
    })
    .then(r => r.json())
    .then(data => {
      const translated = data.translatedText || text;
      cache.set(key, translated);
      setTranslatedText(translated);
    })
    .catch(() => setTranslatedText(text));
  }, [text, currentLanguage]);

  return translatedText;
}