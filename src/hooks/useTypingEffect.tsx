"use client"

import { useEffect, useState } from "react";

export const useTypingEffect = (text: string, speed: number = 50) => {
  const [displayedText, setDisplayedText] = useState('');
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    setDisplayedText('');
    setIsComplete(false);
    
    const words = text.split(' ');
    let index = 0;
    
    const timer = setInterval(() => {
      if (index <= words.length) {
        setDisplayedText(words.slice(0, index).join(' '));
        index++;
      } else {
        setIsComplete(true);
        clearInterval(timer);
      }
    }, speed);

    return () => clearInterval(timer);
  }, [text, speed]);

  return { displayedText, isComplete };
};