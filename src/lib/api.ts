import { sandboxUrl, yumiBackendUrl } from "@/config/baseUrl";
import axios from "axios";
import Cookies from "js-cookie";

const isProd = process.env.NEXT_PUBLIC_NODE_ENV === "production";

export const api = axios.create({
  baseURL: yumiBackendUrl,
  headers: {
    "Content-Type": "application/json",
  },
});

api.interceptors.request.use((config) => {
  const accessToken = Cookies.get("accessToken");
  if (accessToken) {
    config.headers.Authorization = `Bearer ${accessToken}`;
  }

  const refreshToken = Cookies.get("refreshToken");
  if (refreshToken) {
    config.headers["X-Refresh-Token"] = refreshToken;
  }

  return config;
});

api.interceptors.response.use(
  (response) => {
    const newAccessToken = response.headers["x-access-token"];
    const newRefreshToken = response.headers["x-refresh-token"];
    const cookieOpts = {
      path: "/",
      secure: true,
      sameSite: "None" as const,
      expires: 30,
      ...(isProd ? { domain: ".ai-wk.com" } : {}),
    };

    if (newAccessToken) {
      Cookies.set("accessToken", newAccessToken, cookieOpts);
    }
    if (newRefreshToken) {
      Cookies.set("refreshToken", newRefreshToken, cookieOpts);
    }

    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      if (typeof window !== "undefined") {
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);

export const sandboxApi = axios.create({
  baseURL: sandboxUrl,
  headers: {
    "Content-Type": "application/json",
  },
});
