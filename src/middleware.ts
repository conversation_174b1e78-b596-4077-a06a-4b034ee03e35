import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const { nextUrl: url, cookies } = request;
  const token = cookies.get('accessToken')?.value;
  const { pathname } = url;

  // Guard or auto-redirect paths
  const isDashboardPath = pathname.startsWith('/dashboard');
  const isAuthPage = ['/', '/login', '/sign-up'].includes(pathname);

  // 1️⃣ Redirect if not signed in and trying to access dashboard
  if (!token && isDashboardPath) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // 2️⃣ Redirect if signed in and trying to access auth pages
  if (token && isAuthPage) {
    return NextResponse.redirect(new URL('/dashboard/home', request.url));
  }

  // Continue if no conditions match
  return NextResponse.next();
}

export const config = {
  // Apply middleware to relevant paths
  matcher: ['/dashboard/:path*', '/', '/login', '/sign-up'],
};
