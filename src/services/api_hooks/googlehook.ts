import axios from "axios";
// import { saveToken } from "./index";
import { cookies } from "next/headers";
// Function to register user in backend
export const registerUserInBackend = async (userData: {
  email: string;
  supabaseId: string;
}) => {
  try {
    // Use environment variable or fallback to localhost
    const API_URL = "https://api.ai-wk.com";
    
    // Use axios directly instead of the api instance
    const response = await axios.post(`${API_URL}/api/auth/register`, {
      email: userData.email,
      supabaseId: userData.supabaseId,
      recaptchaToken: "oauth-registration", // Special token for OAuth registrations
    });
    const cookieStore = await cookies();
    cookieStore.set("refreshToken", response.data.refreshToken);
    cookieStore.set("accessToken", response.data.accessToken);
    return response.data;
  } catch (error) {
    console.error("Error registering user in backend:", error);
    // Don't throw error, just log it - we don't want to block the OAuth flow
    return null;
  }
};
