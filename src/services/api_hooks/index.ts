"use client";
import {
  useQuery,
  useMutation,
  MutationOptions,
  QueryOptions,
} from "@tanstack/react-query";
import axios, { AxiosError, AxiosInstance } from "axios";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import {
  TokenManager,
  createTokenManager,
} from "@/services/token-refresh-service";
import {
  backendUrl,
  hermesBackendUrl,
  orionBackendUrl,
  yumiBackendUrl,
  olympusUrl,
  olympusUrl2,
} from "@/config/baseUrl";

export const API_URL = backendUrl;
export const ORION_API = orionBackendUrl;
export const HERMES_API = hermesBackendUrl;
export const YUMI_API = yumiBackendUrl;
export const OLYMPUS_URL = olympusUrl;
export const OLYMPUS_URL2 = olympusUrl2;

const TOKEN_MAX_AGE_DAYS = 30;
const isProd = process.env.NEXT_PUBLIC_NODE_ENV === "production";

// Create token managers for both APIs
const mainTokenManager = createTokenManager(API_URL);
// Fail silently if token manager cannot be initialized
const orionTokenManager = createTokenManager(API_URL); // Using main API for auth


const storeCredentials = (data: any) => {
  const cookieOpts = {
    path: "/",
    secure: true,
    sameSite: "None" as const,
    expires: TOKEN_MAX_AGE_DAYS,
    ...(isProd ? { domain: ".ai-wk.com" } : {}),
  };

  // Also store the expiry information
  if (data.token && data.token.accessToken) {
    Cookies.set("accessToken", data.token.accessToken, cookieOpts);
  }

  if (data.token && data.token.refreshToken) {
    Cookies.set("refreshToken", data.token.refreshToken, cookieOpts);
  }

  // Store expiry timestamp if available
  if (data.token && data.token.expiresAt) {
    localStorage.setItem("tokenExpiresAt", data.token.expiresAt.toString());
  }
};

const clearCredentials = () => {
  Cookies.remove("accessToken");
Cookies.remove("refreshToken");
if (isProd) {
  Cookies.remove("accessToken", { domain: ".ai-wk.com" });
  Cookies.remove("refreshToken", { domain: ".ai-wk.com" });
}
  localStorage.removeItem("authCred");
  localStorage.removeItem("tokenExpiresAt");
  localStorage.removeItem("modelsData")
  localStorage.removeItem("profile")
  localStorage.removeItem("profileData")
  localStorage.removeItem("recentlyUsedData")
  localStorage.removeItem("recentlyUsedModels")
  window.location.href = "/login";
};

export const applyAuthInterceptors = (
  axiosInstance: AxiosInstance,
  tokenManager: TokenManager
) => {

  // Add request interceptor
  axiosInstance.interceptors.request.use(
    async (config) => {
      try {
        // Check if token is about to expire and refresh proactively
        const token = await tokenManager.getAccessToken();
        const refreshToken = tokenManager.getRefreshToken();

        if (token) {
          config.headers["Authorization"] = `Bearer ${token}`;
        }

        // Add refresh token to headers for all requests
        if (refreshToken) {
          config.headers["x-refresh-token"] = refreshToken;
        }
      } catch (error) {
        console.error("Error setting auth header:", error);
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Add response interceptor for handling 401s
  axiosInstance.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;

      if (error.response?.status === 401 || error.response?.status === 503) {
        logout();

        // return Promise.reject(
        //   new Error("Session expired. Please login again.")
        // );
      }

      return Promise.reject(error);
    }
  );

  return axiosInstance;
};

// Create API instances with appropriate token managers
export const api = mainTokenManager
  ? applyAuthInterceptors(
      axios.create({
        baseURL: API_URL,
        headers: { "Content-Type": "application/json" },
      }),
      mainTokenManager
    )
  : axios.create({
      baseURL: API_URL,
      headers: { "Content-Type": "application/json" },
    });

export const olympusApi = mainTokenManager
  ? applyAuthInterceptors(
      axios.create({
        baseURL: OLYMPUS_URL,
        headers: { "Content-Type": "application/json" },
      }),
      mainTokenManager
    )
  : axios.create({
      baseURL: OLYMPUS_URL,
      headers: { "Content-Type": "application/json" },
    });

export const olympusApi2 = mainTokenManager
  ? applyAuthInterceptors(
      axios.create({
        baseURL: OLYMPUS_URL2,
        headers: { "Content-Type": "application/json" },
      }),
      mainTokenManager
    )
  : axios.create({
      baseURL: OLYMPUS_URL2,
      headers: { "Content-Type": "application/json" },
    });

export const orionAPI = mainTokenManager
  ? applyAuthInterceptors(
      axios.create({
        baseURL: ORION_API,
        headers: { "Content-Type": "application/json" },
      }),
      mainTokenManager
    )
  : axios.create({
      baseURL: ORION_API,
      headers: { "Content-Type": "application/json" },
    });

export const hermesAPI = orionTokenManager
  ? applyAuthInterceptors(
      axios.create({
        baseURL: HERMES_API,
        headers: { "Content-Type": "application/json" },
      }),
      orionTokenManager
    )
  : axios.create({
      baseURL: HERMES_API,
      headers: { "Content-Type": "application/json" },
    });

export const yumiAPI = mainTokenManager
  ? applyAuthInterceptors(
      axios.create({
        baseURL: YUMI_API,
        headers: { "Content-Type": "application/json" },
      }),
      mainTokenManager
    )
  : axios.create({
      baseURL: YUMI_API,
      headers: { "Content-Type": "application/json" },
    });
// LOGIN REQUEST
export const useLogin = (endpoint: string, options?: any) => {
  const router = useRouter();

  return useMutation({
    mutationFn: async (data?: any) => {
      try {
        const response = await api({
          url: endpoint,
          method: "POST",
          data: data,
        });

        // Store the authentication tokens with expiry information
        if (response && response.data) {
          const sessionData = response.data.session || {};

          const credData = {
            user: response.data.user || {},
            token: {
              accessToken: sessionData.access_token,
              refreshToken: sessionData.refresh_token,
              expiresIn: sessionData.expires_in,
              expiresAt: sessionData.expires_at,
            },
          };

          storeCredentials(credData);
        }

        return response;
      } catch (error: any) {
        throw error;
      }
    },
    ...options,
  });
};

// REGISTER REQUEST
export const useRegister = (options?: any) => {
  return useMutation({
    mutationFn: async (data?: any) => {
      try {
        const response = await api.post("/auth/register", data);

        // Store the authentication tokens if they're returned immediately
        if (response.data.access_token && response.data.refresh_token) {
          const credData = {
            user: response.data.user || {},
            token: {
              accessToken: response.data.access_token,
              refreshToken: response.data.refresh_token,
            },
          };

          storeCredentials(credData);
        }

        return response;
      } catch (error: any) {
        throw error;
      }
    },
    ...options,
  });
};

export const useRegisterTelegramUser = (options?: any) => {
  return useMutation({
    mutationFn: async (data?: any) => {
      try {
        const response = await api.post("/auth/telegram", data);

        // Store the authentication tokens if they're returned immediately
        if (response.data.access_token && response.data.refresh_token) {
          const credData = {
            user: response.data.user || {},
            token: {
              accessToken: response.data.access_token,
              refreshToken: response.data.refresh_token,
            },
          };

          storeCredentials(credData);
        }

        return response;
      } catch (error: any) {
        throw error;
      }
    },
    ...options,
  });
};

// Refresh token function
export const refreshAuthToken = async () => {
  const refreshToken = Cookies.get("refreshToken");
  const accToken = Cookies.get("accessToken");

  if (!refreshToken) {
    throw new Error("No refresh token available");
  }

  try {
    const response = await axios.post(
      `${API_URL}/auth/refresh-token`,
      {
        refresh_token: refreshToken,
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accToken}`,
        },
      }
    );

    // Extract tokens correctly from response
    const accessToken = response.data.session?.access_token;
    const newRefreshToken = response.data.session?.refresh_token;

    if (!accessToken) {
      throw new Error("No access token returned from refresh endpoint");
    }

    // Update stored credentials with new tokens
    const newUserCred = {
      token: {
        accessToken: accessToken,
        refreshToken: newRefreshToken, 
      },
    };

    storeCredentials(newUserCred);

    return response.data;
  } catch (error) {
    clearCredentials();
    throw error;
  }
};

// Logout function
export const logout = () => {
  clearCredentials();

  // Optional: Make a request to invalidate the token on the server
  // await api.post("/auth/logout");
};

// Check if the user is authenticated
export const isAuthenticated = (): boolean => {
  const userCred = Cookies.get("accessToken");
  return !!userCred;
};

// Google Sign-In
export const signInWithGoogle = async () => {
  try {
    const response = await api.get("/auth/google");
    if (response.data.url) {
      window.location.href = response.data.url;
    }
  } catch (error) {
    console.error("Error initiating Google sign-in:", error);
    throw error;
  }
};

export const useSignInWithGoogle = (options?: any) => {
  return useMutation({
    mutationFn: async () => {
      try {
        const response = await api.get("/auth/google");
        if (response.data.url) {
          window.location.href = response.data.url;
        }
        return response;
      } catch (error) {
        console.error("Error initiating Google sign-in:", error);
        throw error;
      }
    },
    ...options,
  });
};

// Handle Google callback - can be used in your callback route
export const handleGoogleCallback = async (googleTokens: {
  access_token: string;
  refresh_token: string;
}) => {
  try {
    const response = await api.get(
      `/auth/session?access_token=${googleTokens?.access_token}&refresh_token=${googleTokens?.refresh_token}`
    );

    if (response) {
      const credData = {
        user: response.data.user || {},
        token: {
          accessToken: response.data.session.access_token,
          refreshToken: response.data.session.refresh_token,
        },
      };

      storeCredentials(credData);
    }

    return response.data;
  } catch (error) {
    console.error("Error with Google callback:", error);
    throw error;
  }
};

// GLOBAL GET REQUESTS - Using your existing function
export const useGetQuery = (
  endpoint: string,
  queryKey: any[],
  options?: any
): any => {
  return useQuery({
    queryKey,
    queryFn: async () => {
      try {
        const response = await api.get(endpoint);
        return response.data;
      } catch (error: any) {
        // if (error.response && error.response.status === 401) {
        //   refreshAuthToken()
        // }
        throw error;
      }
    },
    refetchOnWindowFocus: false,
    ...options,
  });
};

// GLOBAL SUBMIT REQUEST - Using your existing function
export const useSubmitQuery = <TData = any, TResponse = any>(
  endpoint: string,
  method: "POST" | "PUT" | "DELETE" = "POST",
  options?: any
) => {
  return useMutation<TResponse, Error, TData>({
    mutationFn: async (data?: TData) => {
      try {
        // ——— build axios config ———
        const config: Record<string, any> = {
          url: endpoint,
          method,
          data,
        };

        // If the payload is FormData, let Axios send it as multipart
        if (data instanceof FormData) {
          config.headers = {
            // *don’t* add a boundary yourself—Axios will
            // detect FormData and inject the proper multipart header.
            "Content-Type": "multipart/form-data",
          };
        }

        const response = await api(config);
        return response;
      } catch (error: any) {
        // if (error.response && error.response.status === 401) {
        //   refreshAuthToken()
        // }
        throw error;
      }
    },
    ...options,
  });
};

export const useSubmitQueryFormData = <TData = any, TResponse = any>(
  endpoint: string,
  method: "POST" | "PUT" | "DELETE" = "POST",
  options?: any
) => {
  return useMutation<TResponse, Error, TData>({
    mutationFn: async (data?: TData) => {
      try {
        const response = await api({
          url: endpoint,
          method,
          data: data,
          headers: {
            "Content-Type": "multipart/form-data", // ✅ This is fine — Axios will append the boundary
          },
        });
        return response;
      } catch (error: any) {
        // if (error.response && error.response.status === 401) {
        //   refreshAuthToken()
        // }
        throw error;
      }
    },
    ...options,
  });
};

export const useGetQueryOrionBackend = (
  endpoint: string,
  queryKey: any[],
  options?: any
): any => {
  return useQuery({
    queryKey,
    queryFn: async () => {
      try {
        const response = await orionAPI.get(endpoint);
        return response.data;
      } catch (error: any) {
        // if (error.response && error.response.status === 401) {
        //   refreshAuthToken()
        // }
        throw error;
      }
    },
    refetchOnWindowFocus: false,
    ...options,
  });
};

export const useSubmitQueryOrionBackend = (
  endpoint: string,
  method: "POST" | "PUT" | "DELETE" = "POST",
  options?: any
) => {
  return useMutation({
    mutationFn: async (data?: any) => {
      try {
        const response = await orionAPI({
          url: endpoint,
          method,
          data: data,
        });
        return response;
      } catch (error: any) {
        // if (error.response && error.response.status === 401) {
        //   refreshAuthToken()
        // }

        throw error;
      }
    },
    ...options,
  });
};

export const useGetQueryHermes = (
  endpoint: string,
  queryKey: any[],
  options?: any
): any => {
  return useQuery({
    queryKey,
    queryFn: async () => {
      try {
        const response = await hermesAPI.get(endpoint);
        return response.data;
      } catch (error: any) {
        // if (error.response && error.response.status === 401) {
        //   refreshAuthToken()
        // }
        throw error;
      }
    },
    refetchOnWindowFocus: false,
    ...options,
  });
};

export const useGetQueryYumi = (
  endpoint: string,
  queryKey: any[],
  options?: any
): any => {
  return useQuery({
    queryKey,
    queryFn: async () => {
      try {
        const response = await yumiAPI.get(endpoint);
        return response.data;
      } catch (error: any) {
        // if (error.response && error.response.status === 401) {
        //   refreshAuthToken()
        // }
        throw error;
      }
    },
    refetchOnWindowFocus: false,
    ...options,
  });
};

export const useSubmitQueryHermes = <TData = any, TResponse = any>(
  endpoint: string,
  method: "POST" | "PUT" | "DELETE" = "POST",
  options?: any
) => {
  return useMutation<TResponse, Error, TData>({
    mutationFn: async (data?: TData) => {
      try {
        const response = await hermesAPI({
          url: endpoint,
          method,
          data: data,
        });
        return response;
      } catch (error: any) {
        // if (error.response && error.response.status === 401) {
        //   refreshAuthToken()
        // }
        throw error;
      }
    },
    ...options,
  });
};

export async function queryChatBot(data: {
  question: string;
  overrideConfig: {};
}) {
  try {
    const response = await axios.post(
      "https://yumi.up.railway.app/api/v1/prediction/************************************",
      data,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer xJRtdAbpWrspylpib5Wx0WLSTCqRV3hNytiCpvOjxaE",
        },
      }
    );

    return response.data;
  } catch (error: any) {
    throw error;
  }
}

export async function querySandbox(data: {
  question: string;
  overrideConfig: {};
}) {
  try {
    const response = await axios.post(
      "https://yumi.up.railway.app/api/v1/prediction/************************************",
      data,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer xJRtdAbpWrspylpib5Wx0WLSTCqRV3hNytiCpvOjxaE",
        },
      }
    );

    return response.data;
  } catch (error: any) {
    throw error;
  }
}

export const useGetQueryOlympusBackend = (
  endpoint: string,
  queryKey: any[],
  options?: QueryOptions
): any => {
  return useQuery({
    queryKey,
    queryFn: async () => {
      try {
        const response = await olympusApi.get(endpoint);
        return response.data;
      } catch (error: any) {
        // if (error.response && error.response.status === 401) {
        //   refreshAuthToken()
        // }
        throw error;
      }
    },
    refetchOnWindowFocus: false,
    ...options,
  });
};

export const useSubmitQueryOlympusBackend = (
  endpoint: string,
  method: "POST" | "PUT" | "DELETE" = "POST",
  options?: MutationOptions
) => {
  return useMutation({
    mutationFn: async (data?: any) => {
      try {
        const response = await olympusApi({
          url: endpoint,
          method,
          data: data,
        });
        return response;
      } catch (error: any) {
        // if (error.response && error.response.status === 401) {
        //   refreshAuthToken()
        // }

        throw error;
      }
    },
    ...options,
  });
};

export const useGetQueryOlympusBackend2 = (
  endpoint: string,
  queryKey: any[],
  options?: QueryOptions
): any => {
  return useQuery({
    queryKey,
    queryFn: async () => {
      try {
        const response = await olympusApi2.get(endpoint);
        return response.data;
      } catch (error: any) {
        // if (error.response && error.response.status === 401) {
        //   refreshAuthToken()
        // }
        throw error;
      }
    },
    refetchOnWindowFocus: false,
    ...options,
  });
};

export const useSubmitQueryOlympusBackend2 = (
  endpoint: string,
  method: "POST" | "PUT" | "DELETE" = "POST",
  options?: MutationOptions
) => {
  return useMutation({
    mutationFn: async (data?: any) => {
      try {
        const response = await olympusApi2({
          url: endpoint,
          method,
          data: data,
        });
        return response;
      } catch (error: any) {
        // if (error.response && error.response.status === 401) {
        //   refreshAuthToken()
        // }

        throw error;
      }
    },
    ...options,
  });
};
