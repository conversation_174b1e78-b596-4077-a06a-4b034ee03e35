"use client";
import axios from "axios";
import Cookies from "js-cookie";

const TOKEN_MAX_AGE_DAYS = 30;
const TOKEN_REFRESH_THRESHOLD_MS = 5 * 60 * 1000; // Refresh 5 minutes before expiry
const isProd = process.env.NEXT_PUBLIC_NODE_ENV === "production";

const storeCredentials = (data: any) => {
  const cookieOpts = {
    path: "/",
    secure: true,
    sameSite: "None" as const,
    expires: TOKEN_MAX_AGE_DAYS,
    ...(isProd ? { domain: ".ai-wk.com" } : {}),
  };
  try {
    if (typeof window === "undefined") return;
    Cookies.set("accessToken", data.token.accessToken, cookieOpts);
    Cookies.set("refreshToken", data.token.refreshToken, cookieOpts);

    if (data.token.expiresAt) {
      localStorage.setItem("tokenExpiresAt", data.token.expiresAt.toString());
    }
  } catch (error) {
    console.error("Error storing credentials:", error);
  }
};

const clearCredentials = () => {
  if (typeof window === "undefined") return;
  Cookies.remove("accessToken");
  Cookies.remove("refreshToken");
  if (isProd) {
    Cookies.remove("accessToken", { domain: ".ai-wk.com" });
    Cookies.remove("refreshToken", { domain: ".ai-wk.com" });
  }
  localStorage.removeItem("authCred");
  localStorage.removeItem("tokenExpiresAt");
  localStorage.removeItem("modelsData");
  localStorage.removeItem("profile");
  localStorage.removeItem("profileData");
  localStorage.removeItem("recentlyUsedData");
  localStorage.removeItem("recentlyUsedModels");
  window.location.href = "/login";
};

// Class to manage token refresh
export class TokenManager {
  private refreshPromise: Promise<any> | null = null;
  private refreshClient;
  private API_URL: string;

  constructor(apiUrl: string) {
    this.API_URL = apiUrl;
    this.refreshClient = axios.create({
      baseURL: this.API_URL,
      headers: { "Content-Type": "application/json" },
    });

    if (typeof window !== "undefined") {
      this.initializeRefreshTimer();
    }
  }

  private initializeRefreshTimer() {
    setInterval(() => this.checkAndRefreshToken(), 60 * 1000);
    this.checkAndRefreshToken();
  }

  private async checkAndRefreshToken() {
    try {
      if (typeof window === "undefined") return;

      const expiresAtStr = localStorage.getItem("tokenExpiresAt");
      if (!expiresAtStr) return;

      const expiresAt = parseInt(expiresAtStr, 10);
      const now = Date.now() / 1000; // seconds

      if (expiresAt - now < TOKEN_REFRESH_THRESHOLD_MS / 1000) {
        await this.refreshAuthToken();
      }
    } catch (error) {
      console.error("Error checking token expiry:", error);
    }
  }

  public async refreshAuthToken() {
    if (this.refreshPromise) return this.refreshPromise;

    const refreshToken = Cookies.get("refreshToken");
    const accToken = Cookies.get("accessToken");

    if (!refreshToken) {
      return Promise.reject(new Error("No refresh token available"));
    }

    this.refreshPromise = (async () => {
      try {
        const response = await this.refreshClient.post(
          "/auth/refresh-token",
          {
            refresh_token: refreshToken,
          },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accToken}`,
              "x-refresh-token": refreshToken,
            },
          }
        );

        const data = response.data.session ?? response.data;
        const accessToken = data.access_token;
        const newRefreshToken = data.refresh_token;
        const expiresAt = data.expires_at;

        if (!accessToken) {
          throw new Error("No access token returned from refresh endpoint");
        }

        const newUserCred = {
          // user: userCred.user,
          token: {
            accessToken: accessToken,
            refreshToken: newRefreshToken || refreshToken,
            expiresAt: expiresAt,
          },
        };

        storeCredentials(newUserCred);
        return newUserCred;
      } catch (error) {
        clearCredentials();
        throw error;
      } finally {
        this.refreshPromise = null;
      }
    })();

    return this.refreshPromise;
  }

  public isTokenExpiringSoon(): boolean {
    try {
      if (typeof window === "undefined") return false;

      const expiresAtStr = localStorage.getItem("tokenExpiresAt");
      if (!expiresAtStr) return false;

      const expiresAt = parseInt(expiresAtStr, 10);
      const now = Date.now() / 1000;

      return expiresAt - now < TOKEN_REFRESH_THRESHOLD_MS / 1000;
    } catch (error) {
      console.error("Error checking token expiry:", error);
      return false;
    }
  }

  public async getAccessToken(): Promise<string | null> {
    if (this.isTokenExpiringSoon()) {
      await this.refreshAuthToken();
    }

    return Cookies.get("accessToken") || null;
  }

  public getRefreshToken(): string | null {
    return Cookies.get("refreshToken") || null;
  }
}

// Export the token manager factory
export const createTokenManager = (apiUrl: string) =>
  typeof window !== "undefined" ? new TokenManager(apiUrl) : null;
