import { FeatureLdg, InfoLdg, TestimonialType } from "@/types";

export const infos: InfoLdg[] = [
  {
    title: "Power Your AI Innovations",
    description:
      "Leverage cutting-edge AI models to accelerate your projects. Train, fine-tune, and deploy models effortlessly with AI-WK’s powerful infrastructure.",
      image: "/_static/illustrations/work-from-home.jpg",
    list: [
      {
        title: "Versatile",
        description: "Access a diverse range of AI models for various use cases.",
        icon: "laptop",
      },
      {
        title: "Optimized",
        description: "Utilize pre-trained models or fine-tune for better performance.",
        icon: "search",
      },
      {
        title: "Scalable",
        description:
          "Deploy AI solutions that scale with your business needs.",
        icon: "settings",
      },
    ],
  },
  {
    title: "Seamless AI Model Integration",
    description:
      "Effortlessly integrate AI models into your workflows. Connect AI-WK with your existing tools to streamline automation and enhance decision-making.",
      image: "/_static/illustrations/work-from-home.jpg",
    list: [
      {
        title: "Flexible",
        description:
          "Customize AI models to fit your specific requirements.",
          icon: "laptop",
      },
      {
        title: "Efficient",
        description: "Reduce manual effort with intelligent automation.",
        icon: "search",
      },
      {
        title: "Reliable",
        description:
          "Run AI models on a robust, high-performance infrastructure.",
          icon: "settings",
      },
    ],
  },
];

export const features: FeatureLdg[] = [
  {
    title: "Feature 1",
    description:
      "Amet praesentium deserunt ex commodi tempore fuga voluptatem. Sit, sapiente.",
    link: "/",
    icon: "laptop",
  },
  {
    title: "Feature 2",
    description:
      "Amet praesentium deserunt ex commodi tempore fuga voluptatem. Sit, sapiente.",
    link: "/",
    icon: "laptop",
  },
  {
    title: "Feature 3",
    description:
      "Amet praesentium deserunt ex commodi tempore fuga voluptatem. Sit, sapiente.",
    link: "/",
    icon: "laptop",
  },
  {
    title: "Feature 4",
    description:
      "Amet praesentium deserunt ex commodi tempore fuga voluptatem. Sit, sapiente.",
    link: "/",
    icon: "laptop",
  },
  {
    title: "Feature 5",
    description:
      "Amet praesentium deserunt ex commodi tempore fuga voluptatem. Sit, sapiente.",
    link: "/",
    icon: "laptop",
  },
  {
    title: "Feature 6",
    description:
      "Amet praesentium deserunt ex commodi tempore fuga voluptatem. Sit, sapiente.",
    link: "/",
    icon: "laptop",
  },
];

export const testimonials: TestimonialType[] = [
  {
    name: "John Doe",
    job: "Machine Learning Engineer",
    image: "https://randomuser.me/api/portraits/men/1.jpg",
    review:
      "AI-WK has completely transformed the way I work with AI models. The seamless access to multiple AI frameworks allows me to experiment and deploy solutions faster than ever. Highly recommended for AI professionals!",
  },
  {
    name: "Alice Smith",
    job: "AI Researcher",
    image: "https://randomuser.me/api/portraits/women/2.jpg",
    review:
      "AI-WK provides an incredible environment for testing and fine-tuning AI models. The intuitive UI and powerful backend make it easy to work with different AI architectures without hassle.",
  },
  {
    name: "David Johnson",
    job: "DevOps Engineer",
    image: "https://randomuser.me/api/portraits/men/3.jpg",
    review:
      "Deploying AI models has never been this simple. AI-WK streamlines the process, ensuring smooth integration and scaling with minimal effort.",
  },
  {
    name: "Michael Wilson",
    job: "Data Scientist",
    image: "https://randomuser.me/api/portraits/men/5.jpg",
    review:
      "AI-WK offers an amazing suite of AI models, making data analysis and predictive modeling incredibly efficient. The platform's speed and reliability are top-notch.",
  },
  {
    name: "Sophia Garcia",
    job: "AI Product Manager",
    image: "https://randomuser.me/api/portraits/women/6.jpg",
    review:
      "Managing AI-powered applications is so much easier with AI-WK. The platform’s flexibility and support for various AI models make it a must-have tool for AI-driven businesses.",
  },
  {
    name: "Emily Brown",
    job: "Marketing Analyst",
    image: "https://randomuser.me/api/portraits/women/4.jpg",
    review:
      "AI-WK has enabled me to leverage AI-powered insights for marketing campaigns. From customer segmentation to trend prediction, the AI models have given our team a competitive edge.",
  },
  {
    name: "Jason Stan",
    job: "AI Developer",
    image: "https://randomuser.me/api/portraits/men/9.jpg",
    review:
      "AI-WK is a game-changer for AI development. The ability to fine-tune models and deploy them with ease makes it an essential tool for AI engineers.",
  },
];
