// Remove useTranslations import and use a translation function argument instead

export const getAllVideoLinks = (t: (key: string) => string) => [
  {
    title: t("Tutorials.VideoLinks.categories.aboutAiWk"),
    videos: [
      {
        title: t("Tutorials.VideoLinks.videos.introductionToAiWk"),
        duration: t("Tutorials.VideoLinks.durations.introductionToAiWk"),
        thumbnail: "https://img.youtube.com/vi/YN4D9cFEvaI/0.jpg",
        hasCustomThumbnail: true,
        url: "https://www.youtube.com/watch?v=YN4D9cFEvaI&t=1s",
      },
    ],
  },
  {
    title: t("Tutorials.VideoLinks.categories.orion"),
    videos: [
      {
        title: t("Tutorials.VideoLinks.videos.orionHowToUse"),
        duration: t("Tutorials.VideoLinks.durations.orionHowToUse"),
        thumbnail: "https://img.youtube.com/vi/Hpv0CjRi7Zw/0.jpg",
        hasCustomThumbnail: true,
        url: "https://www.youtube.com/watch?v=Hpv0CjRi7Zw",
      },
      {
        title: t("Tutorials.VideoLinks.videos.bestUseScenarioForOrion"),
        duration: t("Tutorials.VideoLinks.durations.bestUseScenarioForOrion"),
        thumbnail: "https://img.youtube.com/vi/mo1gjeL4yX0/0.jpg",
        hasCustomThumbnail: true,
        url: "https://www.youtube.com/watch?v=mo1gjeL4yX0",
      },
    ],
  },
  {
    title: t("Tutorials.VideoLinks.categories.hermesX"),
    videos: [
      {
        title: t("Tutorials.VideoLinks.videos.hermesXHowToUse"),
        duration: t("Tutorials.VideoLinks.durations.hermesXHowToUse"),
        thumbnail: "https://img.youtube.com/vi/Uj2yIh6PBck/0.jpg",
        hasCustomThumbnail: true,
        url: "https://www.youtube.com/watch?v=Uj2yIh6PBck",
      },
      {
        title: t("Tutorials.VideoLinks.videos.bestUseScenarioForHermesX"),
        duration: t("Tutorials.VideoLinks.durations.bestUseScenarioForHermesX"),
        thumbnail: "https://img.youtube.com/vi/57edaUrRgEI/0.jpg",
        hasCustomThumbnail: true,
        url: "https://www.youtube.com/watch?v=57edaUrRgEI",
      },
    ],
  },
  {
    title: t("Tutorials.VideoLinks.categories.hermesC"),
    videos: [
      {
        title: t("Tutorials.VideoLinks.videos.hermesCHowToUse"),
        duration: t("Tutorials.VideoLinks.durations.hermesCHowToUse"),
        thumbnail: "https://img.youtube.com/vi/m6hSLNgf28M/0.jpg",
        hasCustomThumbnail: true,
        url: "https://www.youtube.com/watch?v=m6hSLNgf28M",
      },
      {
        title: t("Tutorials.VideoLinks.videos.bestUseScenarioForHermesC"),
        duration: t("Tutorials.VideoLinks.durations.bestUseScenarioForHermesC"),
        thumbnail: "https://img.youtube.com/vi/_xrOolHC9qw/0.jpg",
        hasCustomThumbnail: true,
        url: "https://www.youtube.com/watch?v=_xrOolHC9qw",
      },
    ],
  },
];

export const getOrionVideoLinks = (t: (key: string) => string) => [
  {
    title: t("Tutorials.VideoLinks.categories.orion"),
    videos: [
      {
        title: t("Tutorials.VideoLinks.videos.orionHowToUse"),
        duration: t("Tutorials.VideoLinks.durations.orionHowToUse"),
        thumbnail: "https://img.youtube.com/vi/Hpv0CjRi7Zw/0.jpg",
        hasCustomThumbnail: true,
        url: "https://www.youtube.com/watch?v=Hpv0CjRi7Zw",
      },
      {
        title: t("Tutorials.VideoLinks.videos.bestUseScenarioForOrion"),
        duration: t("Tutorials.VideoLinks.durations.bestUseScenarioForOrion"),
        thumbnail: "https://img.youtube.com/vi/mo1gjeL4yX0/0.jpg",
        hasCustomThumbnail: true,
        url: "https://www.youtube.com/watch?v=mo1gjeL4yX0",
      },
    ],
  },
  {
    title: t("Tutorials.VideoLinks.categories.aboutAiWk"),
    videos: [
      {
        title: t("Tutorials.VideoLinks.videos.introductionToAiWk"),
        duration: t("Tutorials.VideoLinks.durations.introductionToAiWk"),
        thumbnail: "https://img.youtube.com/vi/YN4D9cFEvaI/0.jpg",
        hasCustomThumbnail: true,
        url: "https://www.youtube.com/watch?v=YN4D9cFEvaI&t=1s",
      },
    ],
  },
  {
    title: t("Tutorials.VideoLinks.categories.otherModels"),
    videos: [
      {
        title: t("Tutorials.VideoLinks.videos.hermesXHowToUse"),
        duration: t("Tutorials.VideoLinks.durations.hermesXHowToUse"),
        thumbnail: "https://img.youtube.com/vi/Uj2yIh6PBck/0.jpg",
        hasCustomThumbnail: true,
        url: "https://www.youtube.com/watch?v=Uj2yIh6PBck",
      },
      {
        title: t("Tutorials.VideoLinks.videos.hermesCHowToUse"),
        duration: t("Tutorials.VideoLinks.durations.hermesCHowToUse"),
        thumbnail: "https://img.youtube.com/vi/m6hSLNgf28M/0.jpg",
        hasCustomThumbnail: true,
        url: "https://www.youtube.com/watch?v=m6hSLNgf28M",
      },
    ],
  },
];
